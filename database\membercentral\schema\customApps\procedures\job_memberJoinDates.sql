ALTER PROCEDURE dbo.job_memberJoinDates 
@itemID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	
	IF OBJECT_ID('tempdb..#tmpUDIDSubs') IS NOT NULL 
		DROP TABLE #tmpUDIDSubs;
	IF OBJECT_ID('tempdb..#subJoinDatesTmp') IS NOT NULL 
		DROP TABLE #subJoinDatesTmp;
	IF OBJECT_ID('tempdb..#subJoinDatesTmp2') IS NOT NULL 
		DROP TABLE #subJoinDatesTmp2;
	IF OBJECT_ID('tempdb..#subJoinDates') IS NOT NULL 
		DROP TABLE #subJoinDates;
	IF OBJECT_ID('tempdb..#mc_PartialMemImport') IS NOT NULL
		DROP TABLE #mc_PartialMemImport;

	CREATE TABLE #tmpUDIDSubs (subscriptionID int PRIMARY KEY);
	CREATE TABLE #subJoinDatesTmp (activeMemberID int PRIMARY KEY, theDate dateTime);
	CREATE TABLE #subJoinDatesTmp2 (activeMemberID int PRIMARY KEY);
	CREATE TABLE #subJoinDates (row int identity(1,1) PRIMARY KEY, activeMemberID int, joinDate dateTime, 
		dropDate dateTime, rejoinDate dateTime, paidThruDate dateTime, renewalDate dateTime, [action] varchar(1));
	
	declare @queueTypeID int, @readyToProcessStatusID int, @processingStatusID int, @udid int, @siteID int, @orgID int, 
		@siteCode varchar(10), @orgShortName varchar(20), @joinColumnID int, @compareDate datetime, @joinFieldName varchar(128), 
		@dropFieldName varchar(128), @rejoinFieldName varchar(128), @paidthrudatefieldname varchar(128), 
		@renewalDateFieldName varchar(128), @droppedColumnID int, @rejoinColumnID int, @paidThruColumnID int, 
		@renewalColumnID int, @emailSubject varchar(50), @AStatusID int, @EStatusID int, @XStatusID int, 
		@DStatusID int, @PStatusID int, @OStatusID int, @IStatusID int, @PPayStatusID int, @importResult xml, 
		@errCount int = 0, @runByMemberID int, @errorSubject VARCHAR(100), @errmsg NVARCHAR(2048);
	declare @tblMemberJoinDates table (memberID int, joinDate dateTime, dropDate dateTime, rejoinDate dateTime, 
		paidThruDate dateTime, renewalDate dateTime, oldJoinDate datetime, olddropDate dateTime, oldrejoinDate dateTime, 
		oldpaidThruDate dateTime, oldrenewalDate dateTime);

	SELECT @queueTypeID = queueTypeID 
	FROM platformQueue.dbo.tblQueueTypes 
	WHERE queueType = 'memberJoinDates';
	
	SELECT @readyToProcessStatusID = queueStatusID 
	FROM platformQueue.dbo.tblQueueStatuses 
	WHERE queueTypeID = @queueTypeID 
	AND queueStatus = 'readyToProcess';

	SELECT @processingStatusID = queueStatusID 
	FROM platformQueue.dbo.tblQueueStatuses 
	WHERE queueTypeID = @queueTypeID 
	AND queueStatus = 'processing';

	SELECT @udid = udid
	FROM platformQueue.dbo.queue_memberJoinDates
	WHERE itemID = @itemID
	AND statusID = @readyToProcessStatusID;

	IF @udid IS NULL
		GOTO on_done;

	UPDATE platformQueue.dbo.queue_memberJoinDates
	SET statusID = @processingStatusID,
		dateUpdated = GETDATE()
	WHERE itemID = @itemID;

	select @siteCode = sitecode, @compareDate = isnull(lastSuccessDate,'1/1/1900'),
		@joinFieldName = joinDateFieldName, @dropFieldName = droppedDateFieldName,
		@rejoinFieldName = rejoinDateFieldName, @paidthrudatefieldname = paidThruDateFieldName,
		@renewalDateFieldName = renewalDateFieldName
	from dbo.schedTask_memberJoinDates
	where udid = @udid;

	select @siteID = s.siteID, @orgID = o.orgID, @orgShortName = oi.organizationShortName
	from membercentral.dbo.sites as s
	inner join membercentral.dbo.organizations as o on o.orgID = s.orgID
	inner join membercentral.dbo.orgIdentities as oi on oi.orgID = o.orgID and oi.orgIdentityID = o.defaultOrgIdentityID
	where s.siteCode = @siteCode;

	SELECT @runByMemberID = membercentral.dbo.fn_ams_getOrgSystemMemberID(@orgID);

	set @emailSubject = @orgShortName + ' Member Join Dates Update Report';

	select @joinColumnID=columnID from membercentral.dbo.ams_memberDataColumns where orgID = @orgID and columnName = @joinFieldName;
	select @droppedColumnID=columnID from membercentral.dbo.ams_memberDataColumns where orgID = @orgID and columnName = @dropFieldName;
	select @rejoinColumnID=columnID from membercentral.dbo.ams_memberDataColumns where orgID = @orgID and columnName = @rejoinFieldName;
	select @paidThruColumnID=columnID from membercentral.dbo.ams_memberDataColumns where orgID = @orgID and columnName = @paidthrudatefieldname;
	select @renewalColumnID=columnID from membercentral.dbo.ams_memberDataColumns where orgID = @orgID and columnName = @renewalDateFieldName;
	select @AStatusID = statusID from membercentral.dbo.sub_statuses where statusCode = 'A';
	select @EStatusID = statusID from membercentral.dbo.sub_statuses where statusCode = 'E';
	select @IStatusID = statusID from membercentral.dbo.sub_statuses where statusCode = 'I';
	select @XStatusID = statusID from membercentral.dbo.sub_statuses where statusCode = 'X';
	select @DStatusID = statusID from membercentral.dbo.sub_statuses where statusCode = 'D';
	select @PStatusID = statusID from membercentral.dbo.sub_statuses where statusCode = 'P';
	select @OStatusID = statusID from membercentral.dbo.sub_statuses where statusCode = 'O';
	select @PPayStatusID = statusID from membercentral.dbo.sub_paymentStatuses where statusCode = 'P';

	-- get all the subscriptions we are considering from the udid types/subs
	insert into #tmpUDIDSubs (subscriptionID)
	select distinct s.subscriptionID 
	from dbo.schedTask_memberJoinDateSubTypes as mjdst
	inner join membercentral.dbo.sub_types as t on t.siteID = @siteID 
		and t.[uid] = mjdst.subscriptionTypeUID
	inner join membercentral.dbo.sub_subscriptions as s on s.orgID = @orgID 
		and s.typeID = t.typeID
		and isnull(mjdst.subscriptionUID,s.[uid]) = s.[uid]
	where mjdst.memberJoinDateUDID = @udid;

	-- get rows that do not have a join date but do have an active subscription
	IF @joinColumnID IS NOT NULL BEGIN
		insert into #subJoinDatesTmp (activeMemberID, theDate)
		select m.activeMemberID, max(sh.updateDate)
		from membercentral.dbo.ams_members as m 
		inner join membercentral.dbo.sub_subscribers as s on s.orgID = @orgID and s.memberID = m.memberID
		inner join #tmpUDIDSubs as subs on subs.subscriptionID = s.subscriptionID
		inner join membercentral.dbo.sub_statusHistory as sh on sh.orgID = @orgID and sh.subscriberID = s.subscriberID 
			and sh.statusID = @AStatusID
		left outer join membercentral.dbo.sub_paymentStatusHistory as psh on psh.orgID = @orgID
			and psh.subscriberID = s.subscriberID
			and psh.paymentStatusID IN (1,2)
		where m.orgID = @orgID
		and (sh.updateDate > @compareDate or psh.updateDate > @compareDate)
		group by m.activeMemberID;

		insert into #subJoinDates (activeMemberID, joinDate, [action])
		select tmp.activeMemberID, tmp.theDate, 'J'
		from #subJoinDatesTmp as tmp
		left outer join membercentral.dbo.ams_memberData as md 
			inner join membercentral.dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @joinColumnID and mdcv.valueID = md.valueID
			on md.memberID = tmp.activeMemberID
		where mdcv.columnvalueDate is null;

		TRUNCATE TABLE #subJoinDatesTmp;
	END

	-- get rows that have been expired, deleted, or had an offer expired and do not have an active, pending, or offered Status
	IF @droppedColumnID IS NOT NULL BEGIN
		insert into #subJoinDatesTmp (activeMemberID, theDate)
		select m.activeMemberID, max(sh.updateDate)
		from membercentral.dbo.ams_members as m 
		inner join membercentral.dbo.sub_subscribers as s on s.orgID = @orgID and s.memberID = m.memberID
		inner join #tmpUDIDSubs as subs on subs.subscriptionID = s.subscriptionID
		inner join membercentral.dbo.sub_statusHistory as sh on sh.orgID = @orgID and sh.subscriberID = s.subscriberID 
			and sh.statusID in (@EStatusID,@XStatusID,@DStatusID)
		where m.orgID = @orgID
		and sh.updateDate > @compareDate
		group by m.activeMemberID;

		delete from #subJoinDatesTmp
		where activeMemberID in (
			select distinct m.activeMemberID
			from membercentral.dbo.ams_members as m 
			inner join #subJoinDatesTmp as sd on sd.activeMemberID = m.activeMemberID
			inner join membercentral.dbo.sub_subscribers as s on s.orgID = @orgID 
				and s.memberID = m.memberID
				and s.statusID in (@AStatusID,@PStatusID,@OStatusID)
			inner join #tmpUDIDSubs as subs on subs.subscriptionID = s.subscriptionID
			where m.orgID = @orgID
		);

		insert into #subJoinDates (activeMemberID, dropDate, [action])
		select tmp.activeMemberID, tmp.theDate, 'D'
		from #subJoinDatesTmp as tmp;

		TRUNCATE TABLE #subJoinDatesTmp;	
	END

	-- get people who have been moved to active, have a drop date, and rejoin date is null or before drop date
	IF @rejoinColumnID is not null BEGIN
		insert into #subJoinDatesTmp (activeMemberID, theDate)
		select m.activeMemberID, max(sh.updateDate)
		from membercentral.dbo.ams_members as m 
		inner join membercentral.dbo.sub_subscribers as s on s.orgID = @orgID 
			and s.memberID = m.memberID
		inner join #tmpUDIDSubs as subs on subs.subscriptionID = s.subscriptionID
		inner join membercentral.dbo.sub_statusHistory as sh on sh.orgID = @orgID 
			and sh.subscriberID = s.subscriberID 
			and sh.statusID = @AStatusID
		left outer join membercentral.dbo.sub_paymentStatusHistory as psh on psh.orgID = @orgID
			and psh.subscriberID = s.subscriberID
			and psh.paymentStatusID IN (1,2)
		where m.orgID = @orgID
		and (sh.updateDate > @compareDate or psh.updateDate > @compareDate)
		group by m.activeMemberID;

		insert into #subJoinDates (activeMemberID, rejoinDate, [action])
		select tmp.activeMemberID, tmp.theDate, 'R'
		from #subJoinDatesTmp as tmp
		inner join membercentral.dbo.ams_memberData as mdD on mdD.memberID = tmp.activeMemberID
		inner join membercentral.dbo.ams_memberDataColumnValues as mdcvD on mdcvD.columnID = @droppedColumnID and mdcvD.valueID = mdD.valueID
		left outer join membercentral.dbo.ams_memberData as mdR 
			inner join membercentral.dbo.ams_memberDataColumnValues as mdcvR on mdcvR.columnID = @rejoinColumnID and mdcvR.valueID = mdR.valueID
			on mdR.memberID = tmp.activeMemberID
		where mdcvD.columnvalueDate is not null
		and (mdcvR.columnvalueDate is null or mdcvR.columnvalueDate < mdcvD.columnvalueDate);
		
		TRUNCATE TABLE #subJoinDatesTmp;
	END

	-- find members with subscription activity and add those needing paidThrudate changes
	-- only consider if the current status is one that implies that the subscription was or will be active at some point 
	IF @paidThruColumnID IS NOT NULL BEGIN
		insert into #subJoinDatesTmp2 (activeMemberID)
		select distinct m.activeMemberID
		from membercentral.dbo.ams_members as m 
		inner join membercentral.dbo.sub_subscribers as s on s.orgID = @orgID 
			and s.memberID = m.memberID
		inner join #tmpUDIDSubs as subs on subs.subscriptionID = s.subscriptionID
		inner join membercentral.dbo.sub_statusHistory as sh on sh.orgID = @orgID 
			and sh.subscriberID = s.subscriberID 
		left outer join membercentral.dbo.sub_paymentStatusHistory as psh on psh.orgID = @orgID
			and psh.subscriberID = s.subscriberID
			and psh.paymentStatusID IN (1,2)
		where m.orgID = @orgID
		and (sh.updateDate > @compareDate or psh.updateDate > @compareDate);

		insert into #subJoinDatesTmp (activeMemberID, theDate)
		select tmp.activeMemberID, max(latestActivatedS.subenddate)
		from #subJoinDatesTmp2 as tmp
		inner join membercentral.dbo.ams_members as allm on allm.orgID = @orgID
			and allm.activeMemberID = tmp.activeMemberID
		inner join membercentral.dbo.sub_subscribers as latestActivatedS on latestActivatedS.orgID = @orgID
			and latestActivatedS.memberID = allm.memberID
			and latestActivatedS.statusID in (@AStatusID,@IStatusID,@PStatusID,@EStatusID)
			and latestActivatedS.paymentStatusID = @PPayStatusID
		inner join #tmpUDIDSubs as subs on subs.subscriptionID = latestActivatedS.subscriptionID
		group by tmp.activeMemberID;

		insert into #subJoinDates (activeMemberID, paidThruDate, [action])
		select tmp.activeMemberID, tmp.theDate, 'P'
		from #subJoinDatesTmp as tmp
		left outer join membercentral.dbo.ams_memberData as md 
			inner join membercentral.dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = @paidThruColumnID and mdcv.valueID = md.valueID
			on md.memberID = tmp.activeMemberID
		where (mdcv.columnvalueDate is null or mdcv.columnvalueDate <> tmp.theDate);

		TRUNCATE TABLE #subJoinDatesTmp;
	END

	-- get people who have been moved to active or accepted, have a join date, updatedate is after join date, and updatedate is after renewal date or renewaldate is null
	IF @renewalColumnID is not null BEGIN
		insert into #subJoinDatesTmp (activeMemberID, theDate)
		select m.activeMemberID, max(psh.updateDate)
		from membercentral.dbo.ams_members as m 
		inner join membercentral.dbo.sub_subscribers as s on s.orgID = @orgID 
			and s.memberID = m.memberID
			and s.statusID in (@AStatusID,@PStatusID)
			and s.paymentStatusID = @PPayStatusID
		inner join #tmpUDIDSubs as subs on subs.subscriptionID = s.subscriptionID
		inner join membercentral.dbo.sub_rateFrequencies as rf on rf.rfid = s.rfid
		inner join membercentral.dbo.sub_rates as r on r.rateID = rf.rateID
			and r.isRenewalRate = 1
			and r.status = 'A'
		inner join membercentral.dbo.sub_paymentStatusHistory as psh on psh.orgID = @orgID
			and psh.subscriberID = s.subscriberID
			and psh.paymentStatusID IN (1,2)
		where m.orgID = @orgID
		and psh.updateDate > @compareDate
		group by m.activeMemberID;	

		insert into #subJoinDates (activeMemberID, renewalDate, [action])
		select tmp.activeMemberID, tmp.theDate, 'W'
		from #subJoinDatesTmp as tmp
		inner join membercentral.dbo.ams_memberData as mdJ on mdJ.memberID = tmp.activeMemberID
		inner join membercentral.dbo.ams_memberDataColumnValues as mdcvJ on mdcvJ.columnID = @joinColumnID and mdcvJ.valueID = mdJ.valueID
		left outer join membercentral.dbo.ams_memberData as mdR
			inner join membercentral.dbo.ams_memberDataColumnValues as mdcvR on mdcvR.columnID = @renewalColumnID and mdcvR.valueID = mdR.valueID
			on mdR.memberID = tmp.activeMemberID
		where mdcvJ.columnvalueDate is not null
		and (mdcvR.columnvalueDate is null or tmp.theDate > mdcvR.columnvalueDate)
		and tmp.theDate > mdcvJ.columnvalueDate;

		TRUNCATE TABLE #subJoinDatesTmp;
	END

	-- if no members to update, get out now
	IF not exists (select top 1 activeMemberID from #subJoinDates)
		GOTO on_done;

	-- get unique active members that are not deleted
	insert into @tblMemberJoinDates (memberID)
	select distinct jd.activeMemberID
	from #subJoinDates as jd
	inner join membercentral.dbo.ams_members as m on m.memberID = jd.activeMemberID
	where m.[status] in ('A','I');

	-- flatten table
	update tmp
	set tmp.joinDate = jd.joinDate,
		tmp.dropDate = dd.dropDate,
		tmp.rejoinDate = rd.rejoinDate,
		tmp.paidThruDate = pd.paidThruDate,
		tmp.renewalDate = wd.renewalDate
	from @tblMemberJoinDates as tmp
	left outer join #subJoinDates as jd on jd.activeMemberID = tmp.memberID and jd.[action] = 'J'
	left outer join #subJoinDates as dd on dd.activeMemberID = tmp.memberID and dd.[action] = 'D'
	left outer join #subJoinDates as rd on rd.activeMemberID = tmp.memberID and rd.[action] = 'R'
	left outer join #subJoinDates as pd on pd.activeMemberID = tmp.memberID and pd.[action] = 'P'
	left outer join #subJoinDates as wd on wd.activeMemberID = tmp.memberID and wd.[action] = 'W';

	-- get existing values
	update tmp 
	set tmp.oldjoinDate = mdcv.columnvalueDate
	from @tblMemberJoinDates as tmp
	inner join memberCentral.dbo.ams_memberData as md on md.memberID = tmp.memberID
	inner join memberCentral.dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
	where mdcv.columnID = @joinColumnID;

	update tmp 
	set tmp.olddropDate = mdcv.columnvalueDate
	from @tblMemberJoinDates as tmp
	inner join memberCentral.dbo.ams_memberData as md on md.memberID = tmp.memberID
	inner join memberCentral.dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
	where mdcv.columnID = @droppedColumnID;

	update tmp 
	set tmp.oldrejoinDate = mdcv.columnvalueDate
	from @tblMemberJoinDates as tmp
	inner join memberCentral.dbo.ams_memberData as md on md.memberID = tmp.memberID
	inner join memberCentral.dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
	where mdcv.columnID = @rejoinColumnID;
	
	update tmp 
	set tmp.oldpaidThruDate = mdcv.columnvalueDate
	from @tblMemberJoinDates as tmp
	inner join memberCentral.dbo.ams_memberData as md on md.memberID = tmp.memberID
	inner join memberCentral.dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
	where mdcv.columnID = @paidThruColumnID;

	update tmp 
	set tmp.oldrenewalDate = mdcv.columnvalueDate
	from @tblMemberJoinDates as tmp
	inner join memberCentral.dbo.ams_memberData as md on md.memberID = tmp.memberID
	inner join memberCentral.dbo.ams_memberDataColumnValues as mdcv on mdcv.valueID = md.valueID
	where mdcv.columnID = @renewalColumnID;

	-- force times to midnight
	update @tblMemberJoinDates
	set joinDate = DATEADD(dd, DATEDIFF(dd,0,joinDate), 0),
		dropDate = DATEADD(dd, DATEDIFF(dd,0,dropDate), 0),
		rejoinDate = DATEADD(dd, DATEDIFF(dd,0,rejoinDate), 0),
		paidThruDate = DATEADD(dd, DATEDIFF(dd,0,paidThruDate), 0),
		renewalDate = DATEADD(dd, DATEDIFF(dd,0,renewalDate), 0),
		oldjoinDate = DATEADD(dd, DATEDIFF(dd,0,oldjoinDate), 0),
		olddropDate = DATEADD(dd, DATEDIFF(dd,0,olddropDate), 0),
		oldrejoinDate = DATEADD(dd, DATEDIFF(dd,0,oldrejoinDate), 0),
		oldpaidThruDate = DATEADD(dd, DATEDIFF(dd,0,oldpaidThruDate), 0),
		oldrenewalDate = DATEADD(dd, DATEDIFF(dd,0,oldrenewalDate), 0);

	-- set the date fields if they are null to use existing values
	update @tblMemberJoinDates set joinDate = oldJoinDate where joinDate is null;
	update @tblMemberJoinDates set rejoinDate = oldRejoinDate where rejoinDate is null;
	update @tblMemberJoinDates set paidThruDate = oldpaidThruDate where paidThruDate is null;
	update @tblMemberJoinDates set renewalDate = oldrenewalDate where renewalDate is null;
	update @tblMemberJoinDates set dropDate = olddropDate where dropDate is null;

	-- delete records with no changes
	delete from @tblMemberJoinDates
	where isnull(joinDate,'1/1/1900') = isnull(oldJoinDate,'1/1/1900')
	AND isnull(rejoinDate,'1/1/1900') = isnull(oldRejoinDate,'1/1/1900')
	AND isnull(paidThruDate,'1/1/1900') = isnull(oldpaidThruDate,'1/1/1900')
	AND isnull(renewalDate,'1/1/1900') = isnull(oldrenewalDate,'1/1/1900')
	AND isnull(dropDate,'1/1/1900') = isnull(olddropDate,'1/1/1900');

	IF not exists (select top 1 memberID from @tblMemberJoinDates)
		GOTO on_done;

	select m.memberNumber, tmp.joinDate, tmp.dropDate, tmp.rejoinDate, tmp.paidThruDate, tmp.renewalDate,
		ROW_NUMBER() over (order by m.memberNumber asc) as rowID
	into #mc_PartialMemImport
	from @tblMemberJoinDates as tmp
	inner join memberCentral.dbo.ams_Members as m on m.memberID = tmp.memberID;

	IF @joinFieldName is not null
		BEGIN TRY
			EXEC tempdb..sp_rename '#mc_PartialMemImport.joinDate', @joinFieldName, 'COLUMN';
		END TRY
		BEGIN CATCH
			SET @errorSubject = 'Error Running Member Join Dates Nightly Job';
			SET @errmsg = 'The job could not be run because the column UID could not be identified for the column '+@joinFieldName+'.';
			EXEC platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=0;	 
			THROW;
		END CATCH
	ELSE
		ALTER TABLE #mc_PartialMemImport DROP COLUMN joinDate;
	
	IF @dropFieldName is not null
		BEGIN TRY
			EXEC tempdb..sp_rename '#mc_PartialMemImport.dropDate', @dropFieldName, 'COLUMN';;
		END TRY
		BEGIN CATCH
			SET @errorSubject = 'Error Running Member Join Dates Nightly Job';
			SET @errmsg = 'The job could not be run because the column UID could not be identified for the column '+@dropFieldName+'.';
			EXEC platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=0;	 
			THROW;
		END CATCH
	ELSE
		ALTER TABLE #mc_PartialMemImport DROP COLUMN dropDate;

	IF @rejoinFieldName is not null
		BEGIN TRY
			EXEC tempdb..sp_rename '#mc_PartialMemImport.rejoinDate', @rejoinFieldName, 'COLUMN';
		END TRY
		BEGIN CATCH
			SET @errorSubject = 'Error Running Member Join Dates Nightly Job';
			SET @errmsg = 'The job could not be run because the column UID could not be identified for the column '+@rejoinFieldName+'.';
			EXEC platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=0;	 
			THROW;
		END CATCH
	ELSE
		ALTER TABLE #mc_PartialMemImport DROP COLUMN rejoinDate;

	IF @paidthrudatefieldname is not null
		BEGIN TRY
			EXEC tempdb..sp_rename '#mc_PartialMemImport.paidThruDate', @paidthrudatefieldname, 'COLUMN';
		END TRY
		BEGIN CATCH
			SET @errorSubject = 'Error Running Member Join Dates Nightly Job';
			SET @errmsg = 'The job could not be run because the column UID could not be identified for the column '+@paidthrudatefieldname+'.';
			EXEC platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=0;	 
			THROW;
		END CATCH
	ELSE
		ALTER TABLE #mc_PartialMemImport DROP COLUMN paidThruDate;

	IF @renewalDateFieldName is not null
		BEGIN TRY
			EXEC tempdb..sp_rename '#mc_PartialMemImport.renewalDate', @renewalDateFieldName, 'COLUMN';
		END TRY
		BEGIN CATCH
			SET @errorSubject = 'Error Running Member Join Dates Nightly Job';
			SET @errmsg = 'The job could not be run because the column UID could not be identified for the column '+@renewalDateFieldName+'.';
			EXEC platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=0;	 
			THROW;
		END CATCH
	ELSE
		ALTER TABLE #mc_PartialMemImport DROP COLUMN renewalDate;

	EXEC membercentral.dbo.ams_importPartialMemberData_autoconfirm @orgID=@orgID, @importTitle='Member Join Dates Nightly Job', 
		@runByMemberID=null, @activateIncMembers=0, @inactivateNonIncMembers=0, @thresholdLimit=0, @bypassRO=1, 
		@finalMSGHeader='MemberCentral automatically', @emailSubject=@emailSubject,
		@environmentName=null, @importResult=@importResult OUTPUT, @errCount=@errCount OUTPUT;
	
	on_done:
	IF @errCount = 0
		update dbo.schedTask_memberJoinDates
		set lastSuccessDate = getDate()	
		where udid = @udid;

	-- delete from queue
	IF @itemID IS NOT NULL
		DELETE FROM platformQueue.dbo.queue_memberJoinDates
		WHERE itemID = @itemID;

	IF OBJECT_ID('tempdb..#tmpUDIDSubs') IS NOT NULL 
		DROP TABLE #tmpUDIDSubs;
	IF OBJECT_ID('tempdb..#subJoinDatesTmp') IS NOT NULL 
		DROP TABLE #subJoinDatesTmp;
	IF OBJECT_ID('tempdb..#subJoinDatesTmp2') IS NOT NULL 
		DROP TABLE #subJoinDatesTmp2;
	IF OBJECT_ID('tempdb..#subJoinDates') IS NOT NULL 
		DROP TABLE #subJoinDates;
	IF OBJECT_ID('tempdb..#mc_PartialMemImport') IS NOT NULL
		DROP TABLE #mc_PartialMemImport;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
