ALTER PROC dbo.queue_depoDocumentsUnzip_grabForProcessing

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpQueueItems') IS NOT NULL 
		DROP TABLE #tmpQueueItems;
	CREATE TABLE #tmpQueueItems(itemID int);

	declare @queueTypeID int, @statusReady int, @statusGrabbed int;

	select @queueTypeID = queueTypeID from dbo.tblQueueTypes where queueType = 'depoDocumentsUnzip';
	select @statusReady = queueStatusID from dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'readyToProcess';
	select @statusGrabbed = queueStatusID from dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'grabbedForProcessing';

	-- dequeue
	UPDATE qi WITH (UPDLOCK, READPAST)
	SET qi.statusID = @statusGrabbed,
		qi.dateUpdated = getdate()
		OUTPUT inserted.itemID INTO #tmpQueueItems
	FROM dbo.queue_depoDocumentsUnzip as qi
	INNER JOIN (
		SELECT top 1 qi2.itemID 
		FROM dbo.queue_depoDocumentsUnzip as qi2
		WHERE qi2.statusID = @statusReady
		ORDER BY qi2.dateAdded, qi2.itemID
	) as batch on batch.itemID = qi.itemID
	WHERE qi.statusID = @statusReady;

	SELECT qid.itemID, qid.depoMemberDataID, qid.pathToZip, qid.[State], qid.DepoAmazonBucks,
		qid.DepoAmazonBucksFullName, qid.DepoAmazonBucksEmail, qid.uploadSourceID
	FROM #tmpQueueItems as qi
	INNER JOIN dbo.queue_depoDocumentsUnzip as qid on qid.itemID = qi.itemID;
	
	IF OBJECT_ID('tempdb..#tmpQueueItems') IS NOT NULL 
		DROP TABLE #tmpQueueItems;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
END CATCH
GO
