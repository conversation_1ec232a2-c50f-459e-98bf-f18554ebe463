ALTER PROC dbo.tr_importTransactionsOption1_import
@siteID int,
@recordedByMemberID int,
@importResult xml OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @queueTypeID int, @statusReady int, @orgID int, @itemGroupUID uniqueidentifier, @xmlMessage xml;

	select @queueTypeID = queueTypeID from platformQueue.dbo.tblQueueTypes where queueType = 'importAcctOpt1';
	select @statusReady = queueStatusID from platformQueue.dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'readyToProcess';
	select @orgID = orgID from dbo.sites where siteID = @siteID;
	set @importResult = null;

	-- all imported transactions get the same ItemGroupUID
	select @itemGroupUID = NEWID();

	BEGIN TRY
		insert into platformQueue.dbo.queue_acctOption1Import (itemGroupUID, recordedByMemberID, orgID, siteID, memberID, 
			invoiceProfileID, GLAccountID, saleDate, saleDescription, saleAmount, statusID, dateAdded, dateUpdated, dueDate)
		select @itemGroupUID, @recordedByMemberID, @orgID, @siteID, MCMemberID, MCInvoiceProfileID, MCRevenueGLAID, 
			saleDate, saleDescription, saleAmount, @statusReady, getdate(), getdate(), dueDate
		from #mc_Acct1Import;

		-- resume task
		EXEC dbo.sched_resumeTask @name='Accounting Option 1 Queue', @engine='BERLinux';

		-- send message to service broker to create all the individual messages
		select @xmlMessage = isnull((
			select 'importAcctOpt1Load' as t, cast(@itemGroupUID as varchar(60)) as u
			FOR XML RAW('mc'), TYPE
		),'<mc/>');
		EXEC platformQueue.dbo.queue_DataImport_sendMessage @xmlMessage=@xmlMessage;
	END TRY
	BEGIN CATCH
		IF @@trancount > 0 ROLLBACK TRANSACTION;

		INSERT INTO #tblAcctErrors (msg)
		VALUES ('Unable to add transactions to the queue.');

		INSERT INTO #tblAcctErrors (msg)
		VALUES (left(error_message(),300));
	END CATCH


	select @importResult = (
		select getdate() as "@date",
			isnull((select top 100 PERCENT dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg"
			from #tblAcctErrors
			order by rowid
			FOR XML path('error'), root('errors'), type),'<errors/>')
		for xml path('import'), TYPE);
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
