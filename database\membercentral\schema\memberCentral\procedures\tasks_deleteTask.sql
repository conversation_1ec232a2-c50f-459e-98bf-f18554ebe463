ALTER PROCEDURE dbo.tasks_deleteTask
@siteID int,
@taskIDList varchar(max)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpTaskIDs') IS NOT NULL 
		DROP TABLE #tmpTaskIDs;
	CREATE TABLE #tmpTaskIDs (taskID int PRIMARY KEY, siteResourceID int);

	DECLARE @orgID int, @taskSiteResourceID int, @queueTypeID int, @statusReady int, @xmlMessage xml,
		@itemGroupUID uniqueidentifier = NEWID();

	SELECT @orgID = dbo.fn_getOrgIDFromSiteID(@siteID);

	select @queueTypeID = queueTypeID from platformQueue.dbo.tblQueueTypes where queueType = 'taskDelete';
	select @statusReady = queueStatusID from platformQueue.dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'readyToProcess';

	INSERT INTO #tmpTaskIDs (taskID, siteResourceID)
	SELECT DISTINCT t.taskID, t.siteResourceID
	FROM dbo.fn_intListToTable(@taskIDList,',') AS tmp
	INNER JOIN dbo.tasks_tasks AS t ON t.taskID = tmp.listitem
	INNER JOIN dbo.cms_siteResources AS sr ON sr.siteID = @siteID
		AND sr.siteResourceID = t.siteResourceID 
		AND sr.siteResourceStatusID = 1;

	IF @@ROWCOUNT = 0 
		GOTO on_done;

	INSERT INTO platformQueue.dbo.queue_taskDelete (itemGroupUID, siteID, orgID, taskID, taskSiteResourceID, statusID, dateAdded, dateUpdated)
	SELECT @itemGroupUID, @siteID, @orgID, tmp.taskID, tmp.siteResourceID, @statusReady, GETDATE(), GETDATE()
	FROM #tmpTaskIDs AS tmp
	LEFT OUTER JOIN platformQueue.dbo.queue_taskDelete AS qid ON qid.taskID = tmp.taskID
	WHERE qid.itemID IS NULL;

	-- send message to service broker to create all the individual messages
	select @xmlMessage = isnull((
		select 'taskDeleteLoad' as t, cast(@itemGroupUID as varchar(60)) as u
		FOR XML RAW('mc'), TYPE
	),'<mc/>');
	EXEC platformQueue.dbo.queue_DataImport_sendMessage @xmlMessage=@xmlMessage;

	on_done:

	IF OBJECT_ID('tempdb..#tmpTaskIDs') IS NOT NULL 
		DROP TABLE #tmpTaskIDs;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
