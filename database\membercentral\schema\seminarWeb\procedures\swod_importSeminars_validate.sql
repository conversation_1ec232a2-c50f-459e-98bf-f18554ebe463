ALTER PROC dbo.swod_importSeminars_validate
@orgCode varchar(10), 
@importResult xml OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @participantID int, @siteID int, @queueTypeID int, @mincol varchar(255), @good bit, @dynSQL nvarchar(max);
	DECLARE @tblCols TABLE (columnName varchar(255) PRIMARY KEY);

	SET @participantID = dbo.fn_getParticipantIDFromOrgCode(@orgCode);
	SET @siteID = memberCentral.dbo.fn_getSiteIDFromSiteCode(@orgcode);

	SELECT @queueTypeID = queueTypeID
	FROM platformQueue.dbo.tblQueueTypes
	WHERE queueType = 'importSWODPrograms';

	SET @importResult = null;

	-- ***********
	-- clean table 
	-- ***********
	BEGIN TRY
		-- delete empty rows
		DELETE FROM #mc_SWODImport WHERE SeminarTitle IS NULL AND [Description] IS NULL;
	END TRY
	BEGIN CATCH
		INSERT INTO #tblSWODErrors (msg)
		VALUES ('Unable to clean import table.');

		INSERT INTO #tblSWODErrors (msg)
		VALUES (left(error_message(),300));

		GOTO on_done;
	END CATCH

	IF NOT EXISTS (SELECT 1 FROM #mc_SWODImport) BEGIN
		INSERT INTO #tblSWODErrors (msg) VALUES('No data rows found.')
		GOTO on_done;
	END

	-- ****************
	-- required columns 
	-- ****************
	BEGIN TRY
		-- no blank SeminarTitle
		UPDATE #mc_SWODImport SET SeminarTitle = '' WHERE SeminarTitle IS NULL;

		INSERT INTO #tblSWODErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has a missing SeminarTitle.'
		FROM #mc_SWODImport
		WHERE SeminarTitle = ''
		ORDER BY rowID;
			IF @@ROWCOUNT > 0 GOTO on_done;

		-- SeminarTitles must be at or under 250 chars
		INSERT INTO #tblSWODErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has an invalid SeminarTitle. SeminarTitle must be 250 characters or less.'
		FROM #mc_SWODImport
		WHERE len(SeminarTitle) > 250 
		ORDER BY rowID;
			IF @@ROWCOUNT > 0 GOTO on_done;

		-- no blank Description
		UPDATE #mc_SWODImport SET [Description] = '' WHERE [Description] IS NULL;

		INSERT INTO #tblSWODErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has a missing Description.'
		FROM #mc_SWODImport
		WHERE [Description] = ''
		ORDER BY rowID;
			IF @@ROWCOUNT > 0 GOTO on_done;

		-- ensure OrigPublished is datetime (allow nulls for this check)
		BEGIN TRY
			ALTER TABLE #mc_SWODImport ALTER COLUMN OrigPublished datetime null;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblSWODErrors (msg)
			VALUES ('The column OrigPublished contains invalid dates.');

			GOTO on_done;
		END CATCH

		-- check for null OrigPublished
		BEGIN TRY
			ALTER TABLE #mc_SWODImport ALTER COLUMN OrigPublished datetime not null;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblSWODErrors (msg)
			SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' is missing the required OrigPublished.'
			FROM #mc_SWODImport
			WHERE OrigPublished IS NULL
			ORDER BY rowID;
				IF @@ROWCOUNT > 0 GOTO on_done;
		END CATCH

		-- no blank PlayerMode
		UPDATE #mc_SWODImport SET PlayerMode = '' WHERE PlayerMode IS NULL;

		INSERT INTO #tblSWODErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has a missing PlayerMode.'
		FROM #mc_SWODImport
		WHERE PlayerMode = ''
		ORDER BY rowID;
			IF @@ROWCOUNT > 0 GOTO on_done;

		UPDATE tmp 
		set tmp.MCLayoutID = l.layoutID
		from dbo.tblSeminarsSWODLayouts as l
		inner join #mc_SWODImport as tmp on isnull(tmp.PlayerMode,'') = l.layout;

		-- check for missing player mode
		BEGIN TRY
			ALTER TABLE #mc_SWODImport ALTER COLUMN MCLayoutID int not null;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblSWODErrors (msg)
			SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ': ' + isnull(PlayerMode,'') + ' does not match an existing player mode.'
			FROM #mc_SWODImport
			WHERE MCLayoutID IS NULL
			ORDER BY rowID;
				IF @@ROWCOUNT > 0 GOTO on_done;
		END CATCH

		-- no blank PlayerQATab
		UPDATE #mc_SWODImport set [PlayerQATab] = '' where [PlayerQATab] is null;
		UPDATE #mc_SWODImport set [PlayerQATab] = '1' where [PlayerQATab] in ('Yes','Y','TRUE');
		UPDATE #mc_SWODImport set [PlayerQATab] = '0' where [PlayerQATab] in ('No','N','FALSE');
			
		INSERT INTO #tblSWODErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has a blank value in the PlayerQATab column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.'
		FROM #mc_SWODImport
		WHERE [PlayerQATab] = ''
		ORDER BY rowID;
			IF @@ROWCOUNT > 0 GOTO on_done;

		BEGIN TRY
			ALTER TABLE #mc_SWODImport ALTER COLUMN [PlayerQATab] bit NULL;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblSWODErrors (msg)
			VALUES ('There are invalid values in the PlayerQATab column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.');

			GOTO on_done;
		END CATCH

		-- no blank BlankPlayer
		UPDATE #mc_SWODImport set [BlankPlayer] = '' where [BlankPlayer] is null;
		UPDATE #mc_SWODImport set [BlankPlayer] = '1' where [BlankPlayer] in ('Yes','Y','TRUE');
		UPDATE #mc_SWODImport set [BlankPlayer] = '0' where [BlankPlayer] in ('No','N','FALSE');
			
		INSERT INTO #tblSWODErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has a blank value in the BlankPlayer column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.'
		FROM #mc_SWODImport
		WHERE [BlankPlayer] = ''
		ORDER BY rowID;
			IF @@ROWCOUNT > 0 GOTO on_done;

		BEGIN TRY
			ALTER TABLE #mc_SWODImport ALTER COLUMN [BlankPlayer] bit NULL;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblSWODErrors (msg)
			VALUES ('There are invalid values in the BlankPlayer column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.');

			GOTO on_done;
		END CATCH

		-- no blank CompleteTime
		UPDATE #mc_SWODImport set CompleteTime = '' where CompleteTime is null;
			
		INSERT INTO #tblSWODErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has a blank value in the CompleteTime column. This column supports only whole numbers.'
		FROM #mc_SWODImport
		WHERE CompleteTime = ''
		ORDER BY rowID;
			IF @@ROWCOUNT > 0 GOTO on_done;

		BEGIN TRY
			ALTER TABLE #mc_SWODImport ALTER COLUMN CompleteTime INT NULL;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblSWODErrors (msg)
			VALUES ('There are invalid values in the CompleteTime. This column supports only whole numbers.');

			GOTO on_done;
		END CATCH

		-- no blank Certificate
		UPDATE #mc_SWODImport set [Certificate] = '' where [Certificate] is null;
		UPDATE #mc_SWODImport set [Certificate] = '1' where [Certificate] in ('Yes','Y','TRUE');
		UPDATE #mc_SWODImport set [Certificate] = '0' where [Certificate] in ('No','N','FALSE');
			
		INSERT INTO #tblSWODErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has a blank value in the Certificate column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.'
		FROM #mc_SWODImport
		WHERE [Certificate] = ''
		ORDER BY rowID;
			IF @@ROWCOUNT > 0 GOTO on_done;

		BEGIN TRY
			ALTER TABLE #mc_SWODImport ALTER COLUMN [Certificate] bit NULL;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblSWODErrors (msg)
			VALUES ('There are invalid values in the Certificate column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.');

			GOTO on_done;
		END CATCH

		-- no blank SellInCatalog
		UPDATE #mc_SWODImport set SellInCatalog = '' where SellInCatalog is null;
		UPDATE #mc_SWODImport set SellInCatalog = '1' where SellInCatalog in ('Yes','Y','TRUE');
		UPDATE #mc_SWODImport set SellInCatalog = '0' where SellInCatalog in ('No','N','FALSE');
			
		INSERT INTO #tblSWODErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has a blank value in the SellInCatalog column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.'
		FROM #mc_SWODImport
		WHERE SellInCatalog = ''
		ORDER BY rowID;
			IF @@ROWCOUNT > 0 GOTO on_done;

		BEGIN TRY
			ALTER TABLE #mc_SWODImport ALTER COLUMN SellInCatalog bit NULL;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblSWODErrors (msg)
			VALUES ('There are invalid values in the SellInCatalog column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.');

			GOTO on_done;
		END CATCH

		UPDATE #mc_SWODImport set SellInCatalog = '' where SellInCatalog is null;

		-- ensure StartSale is datetime (allow nulls for this check)
		BEGIN TRY
			ALTER TABLE #mc_SWODImport ALTER COLUMN StartSale datetime null;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblSWODErrors (msg)
			VALUES ('The column StartSale contains invalid dates.');

			GOTO on_done;
		END CATCH

		-- check for null StartSale when SellInCatalog = 1
		INSERT INTO #tblSWODErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' is missing the required StartSale.'
		FROM #mc_SWODImport
		WHERE SellInCatalog = 1
		AND StartSale IS NULL
		ORDER BY rowID;
			IF @@ROWCOUNT > 0 GOTO on_done;

		-- ensure EndSale is datetime (allow nulls for this check)
		BEGIN TRY
			ALTER TABLE #mc_SWODImport ALTER COLUMN EndSale datetime null;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblSWODErrors (msg)
			VALUES ('The column EndSale contains invalid dates.');

			GOTO on_done;
		END CATCH

		-- check for null EndSale when SellInCatalog = 1
		INSERT INTO #tblSWODErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' is missing the required EndSale.'
		FROM #mc_SWODImport
		WHERE SellInCatalog = 1
		AND EndSale IS NULL
		ORDER BY rowID;
			IF @@ROWCOUNT > 0 GOTO on_done;

		-- check dates
		INSERT INTO #tblSWODErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has a StartSale after the EndSale.'
		FROM #mc_SWODImport
		WHERE SellInCatalog = 1
		AND StartSale > EndSale
		ORDER BY rowID;
			IF @@ROWCOUNT > 0 GOTO on_done;

		-- no blank Credit
		UPDATE #mc_SWODImport set Credit = '' where Credit is null;
		UPDATE #mc_SWODImport set Credit = '1' where Credit in ('Yes','Y','TRUE');
		UPDATE #mc_SWODImport set Credit = '0' where Credit in ('No','N','FALSE');
			
		INSERT INTO #tblSWODErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has a blank value in the Credit column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.'
		FROM #mc_SWODImport
		WHERE Credit = ''
		ORDER BY rowID;
			IF @@ROWCOUNT > 0 GOTO on_done;

		BEGIN TRY
			ALTER TABLE #mc_SWODImport ALTER COLUMN Credit bit NULL;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblSWODErrors (msg)
			VALUES ('There are invalid values in the Credit column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.');

			GOTO on_done;
		END CATCH

		-- no blank AutoCreateTitle
		UPDATE #mc_SWODImport set AutoCreateTitle = '' where AutoCreateTitle is null;
		UPDATE #mc_SWODImport set AutoCreateTitle = '1' where AutoCreateTitle in ('Yes','Y','TRUE');
		UPDATE #mc_SWODImport set AutoCreateTitle = '0' where AutoCreateTitle in ('No','N','FALSE');
			
		INSERT INTO #tblSWODErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has a blank value in the AutoCreateTitle column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.'
		FROM #mc_SWODImport
		WHERE AutoCreateTitle = ''
		ORDER BY rowID;
			IF @@ROWCOUNT > 0 GOTO on_done;

		BEGIN TRY
			ALTER TABLE #mc_SWODImport ALTER COLUMN AutoCreateTitle bit NULL;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblSWODErrors (msg)
			VALUES ('There are invalid values in the AutoCreateTitle column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.');

			GOTO on_done;
		END CATCH

	END TRY
	BEGIN CATCH
		INSERT INTO #tblSWODErrors (msg)
		VALUES ('Unable to validate data in required columns.');

		INSERT INTO #tblSWODErrors (msg)
		VALUES (left(error_message(),300));

		GOTO on_done;
	END CATCH

	-- ****************
	-- optional columns 
	-- ****************
	BEGIN TRY
		IF NOT EXISTS (select COLUMN_NAME from #tblColsAdded where COLUMN_NAME = 'ProgramCode') BEGIN
			UPDATE #mc_SWODImport SET ProgramCode = '' WHERE ProgramCode IS NULL;

			-- ProgramCode must be at or under 15 chars
			INSERT INTO #tblSWODErrors (msg)
			SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ': ' + ProgramCode + ' is an invalid ProgramCode. ProgramCodes must be 15 characters or less.'
			FROM #mc_SWODImport
			WHERE len(ProgramCode) > 15 
			ORDER BY rowID;
				IF @@ROWCOUNT > 0 GOTO on_done;

			-- ProgramCode must be unique in file
			INSERT INTO #tblSWODErrors (msg)
			SELECT TOP 100 PERCENT 'ProgramCode ' + ProgramCode + ' appears in the file multiple times. ProgramCodes must be unique.'
			FROM #mc_SWODImport
			WHERE ProgramCode <> ''
			GROUP BY ProgramCode
			HAVING COUNT(*) > 1
			ORDER BY ProgramCode;
				IF @@ROWCOUNT > 0 GOTO on_done;

			-- match on ProgramCode
			UPDATE tmp 
			SET tmp.MCSeminarID = s.seminarID
			FROM #mc_SWODImport as tmp 
			INNER JOIN dbo.tblSeminars as s on s.programCode = tmp.ProgramCode
				and s.isDeleted = 0
				and s.participantID = @participantID
			WHERE tmp.ProgramCode <> '';
		END

		-- bit columns provided in the original upload need a value for each row
		IF NOT EXISTS (select COLUMN_NAME from #tblColsAdded where COLUMN_NAME = 'Featured') BEGIN
			UPDATE #mc_SWODImport set Featured = '0' where Featured is null;
			UPDATE #mc_SWODImport set Featured = '1' where Featured in ('Yes','Y','TRUE');
			UPDATE #mc_SWODImport set Featured = '0' where Featured in ('No','N','FALSE');

			BEGIN TRY
				ALTER TABLE #mc_SWODImport ALTER COLUMN Featured bit NULL;
			END TRY
			BEGIN CATCH
				INSERT INTO #tblSWODErrors (msg)
				VALUES ('There are invalid values in the Featured column. This column supports YES/Y/TRUE/1 or NO/N/FALSE/0.');

				GOTO on_done;
			END CATCH
		END

		IF NOT EXISTS (select COLUMN_NAME from #tblColsAdded where COLUMN_NAME = 'SeminarSubtitle') BEGIN
			-- SeminarSubtitle must be at or under 15 chars
			INSERT INTO #tblSWODErrors (msg)
			SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' has an invalid SeminarSubtitle. SeminarSubtitle must be 250 characters or less.'
			FROM #mc_SWODImport
			WHERE len(isnull(SeminarSubtitle,'')) > 250
			ORDER BY rowID;
				IF @@ROWCOUNT > 0 GOTO on_done;
		END

		IF NOT EXISTS (SELECT COLUMN_NAME FROM #tblColsAdded where COLUMN_NAME = 'Subjects') BEGIN
			IF OBJECT_ID('tempdb..#mc_SWODImport_subjects') IS NOT NULL
				DROP TABLE #mc_SWODImport_subjects;

			CREATE TABLE #mc_SWODImport_subjects(autoID int identity(1,1), rowID int, [subject] varchar(max), MCCategoryID int);

			INSERT INTO #mc_SWODImport_subjects (rowID, [subject], MCCategoryID)
			SELECT tmp.rowID, tmpSubject.listitem, c.categoryID
			FROM #mc_SWODImport AS tmp
			OUTER APPLY membercentral.dbo.fn_varcharListToTable(tmp.Subjects,'|') AS tmpSubject
			LEFT OUTER JOIN dbo.tblCategories AS c
				INNER JOIN dbo.tblParticipants AS p ON c.participantID = p.participantID
					AND p.participantID = @participantID
				ON c.categoryName = tmpSubject.listitem
			WHERE ISNULL(tmp.Subjects,'') <> '';

			-- invalid subjects
			INSERT INTO #tblSWODErrors (msg)
			SELECT 'Row ' + cast(rowID as varchar(10)) + ' has invalid subjects: ' + STRING_AGG([subject],'|') WITHIN GROUP (ORDER BY [subject] ASC)
			FROM #mc_SWODImport_subjects
			WHERE MCCategoryID IS NULL
			GROUP BY rowID;

			IF @@ROWCOUNT > 0 GOTO on_done;

			IF EXISTS(SELECT 1 FROM #mc_SWODImport_subjects WHERE MCCategoryID IS NOT NULL) BEGIN
				UPDATE tmp
				SET tmp.MCCategoryIDList = tmp2.MCCategoryIDList
				FROM #mc_SWODImport AS tmp
				INNER JOIN (
					SELECT rowID, STRING_AGG(MCCategoryID,'|') AS MCCategoryIDList
					FROM #mc_SWODImport_subjects
					WHERE MCCategoryID IS NOT NULL
					GROUP BY rowID
				) tmp2 ON tmp2.rowID = tmp.rowID;
			END

			IF OBJECT_ID('tempdb..#mc_SWODImport_subjects') IS NOT NULL
				DROP TABLE #mc_SWODImport_subjects;
		END

		-- validate rate amounts
		INSERT INTO @tblCols (columnName)
		VALUES ('RateAmount1'),('RateAmount2'),('RateAmount3'),('RateAmount4'),('RateAmount5');

		SELECT @mincol = min(columnName) FROM @tblCols;
		WHILE @mincol IS NOT NULL BEGIN
			SET @good = 1;
			SET @dynSQL = '
				set @good = 1;
				BEGIN TRY
					ALTER TABLE #mc_SWODImport ALTER COLUMN ' + quotename(@mincol) + ' varchar(max) NULL;
					UPDATE #mc_SWODImport SET ' + quotename(@mincol) + ' = NULL WHERE ' + quotename(@mincol) + ' = '''';
					ALTER TABLE #mc_SWODImport ALTER COLUMN ' + quotename(@mincol) + ' decimal(9,2) NULL;
				END TRY
				BEGIN CATCH
					set @good = 0;
				END CATCH';
				EXEC sp_executesql @dynSQL, N'@good bit output', @good output;
			IF @good = 0
				INSERT INTO #tblSWODErrors (msg)
				VALUES ('The column ' + replace(quotename(@mincol),'''','''''') + ' contains invalid decimal values.');

			SELECT @mincol = min(columnName) FROM @tblCols WHERE columnname > @mincol;
		END
		
		IF NOT EXISTS (SELECT COLUMN_NAME FROM #tblColsAdded where COLUMN_NAME = 'Evaluation') BEGIN
			IF OBJECT_ID('tempdb..#mc_SWODImport_evaluation') IS NOT NULL
				DROP TABLE #mc_SWODImport_evaluation;

			CREATE TABLE #mc_SWODImport_evaluation(autoID int identity(1,1), rowID int, [evaluation] varchar(max), MCFormID int);

			INSERT INTO #mc_SWODImport_evaluation (rowID, [evaluation], MCFormID)
			SELECT tmp.rowID, tmpEvaluation.listitem, f.formID
			FROM #mc_SWODImport AS tmp
			OUTER APPLY membercentral.dbo.fn_varcharListToTable(tmp.Evaluation,'|') AS tmpEvaluation
			LEFT OUTER JOIN formbuilder.dbo.tblForms as f
				INNER JOIN formbuilder.dbo.tblFormTypes as ft on ft.formTypeID = f.formTypeID AND ft.formTypeAbbr = 'S'
				ON f.formTitle = tmpEvaluation.listitem AND f.isDeleted = 0 AND f.siteID = @siteID
			WHERE ISNULL(tmp.Evaluation,'') <> '';
			
			-- invalid evaluation
			INSERT INTO #tblSWODErrors (msg)
			SELECT 'Row ' + cast(rowID as varchar(10)) + ' has invalid evaluation: ' + STRING_AGG([evaluation],'|') WITHIN GROUP (ORDER BY [evaluation] ASC)
			FROM #mc_SWODImport_evaluation
			WHERE MCFormID IS NULL
			GROUP BY rowID;

			IF @@ROWCOUNT > 0 GOTO on_done;

			IF EXISTS(SELECT 1 FROM #mc_SWODImport_evaluation WHERE MCFormID IS NOT NULL) BEGIN
				UPDATE tmp
				SET tmp.MCFormIDList = tmp2.MCFormIDList
				FROM #mc_SWODImport AS tmp
				INNER JOIN (
					SELECT rowID, STRING_AGG(MCFormID,'|') AS MCFormIDList
					FROM #mc_SWODImport_evaluation
					WHERE MCFormID IS NOT NULL
					GROUP BY rowID
				) tmp2 ON tmp2.rowID = tmp.rowID;
			END

			IF OBJECT_ID('tempdb..#mc_SWODImport_evaluation') IS NOT NULL
				DROP TABLE #mc_SWODImport_evaluation;
		END

		IF EXISTS (SELECT 1 FROM #tblSWODErrors)
			GOTO on_done;
		
	END TRY
	BEGIN CATCH
		INSERT INTO #tblSWODErrors (msg)
		VALUES ('Unable to validate data in optional columns.');

		INSERT INTO #tblSWODErrors (msg)
		VALUES (left(error_message(),300));

		GOTO on_done;
	END CATCH

	-- ************************
	-- generate result xml file 
	-- ************************
	on_done:
	select @importResult = (
		select getdate() as "@date",
			isnull((select top 100 PERCENT membercentral.dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg"
			from #tblSWODErrors
			order by rowid
			FOR XML path('error'), root('errors'), type),'<errors/>')
		for xml path('import'), TYPE);

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
