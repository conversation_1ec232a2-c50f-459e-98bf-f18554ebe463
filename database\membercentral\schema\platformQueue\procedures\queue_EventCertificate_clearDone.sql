ALTER PROC dbo.queue_EventCertificate_clearDone
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @queueTypeID int, @statusDone int;
	
	select @queueTypeID = queueTypeID from dbo.tblQueueTypes where queueType = 'eventCertificate';
	select @statusDone = queueStatusID from dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'done';

	IF OBJECT_ID('tempdb..#tmpitemGroupUIDs') IS NOT NULL 
		DROP TABLE #tmpitemGroupUIDs;
	CREATE TABLE #tmpitemGroupUIDs (itemGroupUID uniqueidentifier);

	insert into #tmpitemGroupUIDs
	select distinct itemGroupUID
	from dbo.queue_eventCertificate
	where statusID = @statusDone
		except
	select distinct itemGroupUID
	from dbo.queue_eventCertificate
	where statusID <> @statusDone;

	IF (select count(*) from #tmpitemGroupUIDs) = 0
		GOTO on_done;

	DELETE qi
	FROM dbo.queue_eventCertificate as qi
	INNER JOIN #tmpitemGroupUIDs as tmp on tmp.itemGroupUID = qi.itemGroupUID;

	on_done:
	IF OBJECT_ID('tempdb..#tmpitemGroupUIDs') IS NOT NULL 
		DROP TABLE #tmpitemGroupUIDs;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
