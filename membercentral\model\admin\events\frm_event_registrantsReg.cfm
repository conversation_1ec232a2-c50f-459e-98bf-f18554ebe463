<cfsavecontent variable="local.registrantJS">
	<cfoutput>
	<script language="javascript">
	var #ToScript(local.exportRegistrantsFormResponsesLink,'link_exportevformresponses')#
	var mcma_hasrights_refundpmt = "1"; /* Bypassing the JS method; a read-only form will be shown in this case */
	var #ToScript(local.refundPaymentURL,"mcma_link_refpayment")#
	let evRegistrantsTable;

	function initializeEventRegistrantsTable(){
		let domString = "<'row'<'col-sm-12 col-md-5'<'row'<'col-auto'l><'col-sm-12 col-md pl-md-0'i>>><'col-sm-12 col-md-7'p>>" + "<'row'<'col-sm-12'tr>>" + "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>";

		evRegistrantsTable = $('##evRegistrantsTable').DataTable({
			"processing": true,
			"serverSide": true,
			"pageLength": 50,
			"lengthMenu": [ 10, 25, 50, 100 ],
			"dom": domString,
			"language": {
				"lengthMenu": "_MENU_"
			},
			"ajax": { 
				"url": "#local.eventRegistrantsLink#",
				"type": "post",
				"data": function(d) {
					$.each($('##frmFilter').serializeArray(),function() {
						d[this.name] = (typeof d[this.name] != "undefined" ? d[this.name] + ',' : '') + this.value || '';
					});
				}
			},
			"autoWidth": false,
			"columns": [
				{
					"data": null,
					"render": function (data, type) {
						let renderData = '';
						if (type === 'display') {
							if(data.isflagged) renderData += '<i class="fa-solid fa-flag text-danger mr-1"></i>';
							renderData += '<a href="'+data.editmemberlink+'">'+data.lastname+', '+data.firstname+' ('+data.membernumber+')</a>';
							if(data.company.length) renderData += '<div class="small text-dim">'+data.company+'</div>';
							if(data.regroles.length) renderData += '<div class="small text-dim">'+data.regroles+'</div>';
						}
						return type === 'display' ? renderData : data;
					},
					"width": "35%",
					'className': 'align-top'
				},
				{
					"data": null, 
					"render": function (data, type) {
						return type === 'display' ? '<span title="'+data.registereddatetime+'">'+data.registereddate+'</span>' : data;
					},
					"width": "10%",
					'className': 'align-top'
				},
				{ "data": "attendedinfo", "width": "10%", 'className': 'align-top', "orderable": false },
				{ "data": "totalregfee", "width": "10%", 'className': 'align-top' },
				{ "data": "amountduedisplay", "width": "10%", 'className': 'align-top' },
				{ 
					"data": null,
					"render": function (data, type, row, meta) {
						let renderData = '';
						if (type === 'display') {
							var arrGridAction = [
								{ title:"Pay for Registration", btnClass:"btn-outline-green", iconClass: "fa-money-bill", isVisible:data.amountdue > 0, onClickFnCall:'addPayment(\''+data.addpaymentencstring+'\')' },
								{ title:"Edit This Registrant's Credit", btnClass:"btn-outline-warning", iconClass: "fa-file-certificate", isVisible:data.eventstatus == 'A', onClickFnCall:'manageSubEventAC('+data.registrantid+')' },
								<cfif local.qryEventForms.recordCount>
									{ title:"Submit Evaluation", btnClass:"btn-outline-primary", iconClass: "fa-tasks", isVisible:data.showeval, onClickFnCall:'showSubmitEvaluationForm('+data.registrantid+')' },
								</cfif>
								{ title:"View This Registrant's Certificates", btnClass:"btn-outline-warning", iconClass: "fa-certificate", isVisible:data.showcert, onClickFnCall:'viewCertificate('+data.registrantid+')' },
								{ title:"Print This Registration", btnClass:"btn-outline-dark", iconClass: "fa-print", isVisible:true, onClickFnCall:'printRegistrant('+data.registrantid+','+data.memberid+')' },
								{ title:"Resend Email Confirmation", btnClass:"btn-outline-primary", iconClass: "fa-envelope", isVisible:true, onClickFnCall:'sendConfirmation('+data.registrantid+','+data.memberid+')' },
								{ title:"Email Materials", btnClass:"btn-outline-primary", iconClass: "fa-envelope-open-text", isVisible:data.showemailmaterials, onClickFnCall:'emailRegistrantMaterials('+data.registrantid+','+data.memberid+')' },
								{ title:"Edit This Registration", btnClass:"btn-outline-primary", iconClass: "fa-pencil", isVisible:true, onClickFnCall:'editRegistrant('+data.registrantid+','+data.memberid+')' },
								<cfif local.badgePrinterEnabled AND local.hasBadgeDevices AND local.qryBadgeTemplates.recordcount>
									{ title:"Print This Registrant's Badge", btnClass:"btn-outline-dark", iconClass: "fa-badge", isVisible:true, onClickFnCall:'printBadgeRegistrant('+data.registrantid+')' },
								</cfif>
								{ title:"Remove This Registration", btnClass:"btn-outline-danger", iconClass: "fa-circle-minus", isVisible:true, onClickFnCall:'removeRegistrant('+data.registrantid+')' }
							];

							if (!data.canedit) {
								arrGridAction.forEach(obj => {
									obj.isVisible = false;
									obj.onClickFnCall = '';
								});
							}
							if (data.cancleanupinvoices) {
								arrGridAction[arrGridAction.length - 1] = { title:"Cleanup Invoices", btnClass:"btn-outline-danger", iconClass: "fa-file-invoice-dollar", isVisible:true, onClickFnCall:'cleanupRegInvoices('+data.registrantid+')' };
							}

							$.each(arrGridAction, function(index, item) {
								renderData += '<a href="##" class="btn btn-xs '+(item.isVisible ? item.btnClass : '')+' px-1 m-1'+ (!item.isVisible ? ' text-muted disabled' : '') +'" '+ (item.isVisible ? 'onclick="'+item.onClickFnCall+';return false;" title="'+item.title+'"' : '') +'><i class="fa-solid '+item.iconClass+'"></i></a>';
							});
						}
						return type === 'display' ? renderData : data;
					},
					"width": "25%",
					"className": "text-center",
					"orderable": false
				}
			],
			"order": [[1, 'desc']],
			"searching": false,
			"createdRow": function(row, data, index) {
				if (data.status == "D") $(row).addClass('table-danger');
			}
		});
	}
	function filterEV() {
		if (!$('##divFilterForm').is(':visible')) {
			$('div.divEVTool').hide();
			$('##divFilterForm').show();
		}
	}
	function exportRegistrants() {
		if (validateFields()) {
			$('div.divEVTool').hide();
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'lg',
				title: 'Download Filtered Registrants',
				iframe: true,
				contenturl: '#local.exportRegPromptLink#',
				strmodalfooter: {
					classlist: 'text-right',
					showclose: false,
					showextrabutton: true,
					extrabuttonclass: 'btn-primary',
					extrabuttononclickhandler: 'exportRegistrantsPromptButtonHandler',
					extrabuttonlabel: 'Download CSV',
					extrabuttoniconclass: 'fa-light fa-file-csv'
				}
			});
		}
	}
	function exportEventResponses() {
		if(evRegistrantsTable.page.info().recordsTotal == 0) {
			alert('There are no registrants to act upon.');
		} else if ($('##divFormResponseDownloadForm').hasClass('d-none')) {
			$('div.divEvRegistrantsTool').addClass('d-none');
			$('##divFormResponseDownloadForm').removeClass('d-none');
		}
	}
	function exportRegistrantsPromptButtonHandler(){
		$('##MCModalBodyIframe')[0].contentWindow.fnDnReg();
	}
	function getExportRegistrantsLink(fsid,swcfid,qid,rqid) {
		var exportRegLink = '#local.exportRegLink#&fsid=' + fsid + (qid ? '&qid=' + qid : '') + (swcfid ? '&swcfid=' + swcfid : '') + (rqid ? '&rqid=' + rqid : '') + '&' + $('##frmFilter').serialize();
		return exportRegLink;
	}
	function exportTransactions() {
		if (validateFields()) {
			$('div.divEVTool').hide();
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'lg',
				title: 'Download Transactions for Filtered Registrants',
				iframe: true,
				contenturl: '#local.exportRegTransPromptLink#',
				strmodalfooter: {
					classlist: 'text-right',
					showclose: false,
					showextrabutton: true,
					extrabuttonclass: 'btn-primary',
					extrabuttononclickhandler: 'exportTransactionPromptButtonHandler',
					extrabuttonlabel: 'Download CSV',
					extrabuttoniconclass: 'fa-light fa-file-csv'
				}
			});
		}
	}
	function exportTransactionPromptButtonHandler(){
		$('##MCModalBodyIframe')[0].contentWindow.fnDnRegTrans();
	}
	function doExportRegistrantTrans(fsid) {
		window.setTimeout(function(){ MCModalUtils.hideModal(); },4000);
		top.location.href='#local.exportRegTransLink#&fsid=' + fsid + '&' + $('##frmFilter').serialize();
	}
	<cfif arguments.event.getValue('isOnlineMeeting') is 1>
		function exportAccessLog() {
			if (validateFields()) {
				$('div.divEVTool').hide();
				MCModalUtils.showModal({
					isslideout: true,
					modaloptions: {
						backdrop: 'static',
						keyboard: false
					},
					size: 'lg',
					title: 'Download Access Log for Registrants',
					iframe: true,
					contenturl: '#local.exportOnlineMeetingLogPromptLink#',
					strmodalfooter: {
						classlist: 'text-right',
						showclose: false,
						showextrabutton: true,
						extrabuttonclass: 'btn-primary',
						extrabuttononclickhandler: 'exportOnlineMeetingLogPromptButtonHandler',
						extrabuttonlabel: 'Download CSV',
						extrabuttoniconclass: 'fa-light fa-file-csv'
					}
				});
			}
		}
		function exportOnlineMeetingLogPromptButtonHandler(){
			$('##MCModalBodyIframe')[0].contentWindow.fnDnAccessLog();
		}
		function doExportAccessLog(fsid) {
			window.setTimeout(function(){ MCModalUtils.hideModal(); },4000);
			top.location.href='#local.exportOnlineMeetingLogLink#&fsid=' + fsid + '&' + $('##frmFilter').serialize();
		}
	</cfif>

	<cfif local.qryTickets.recordcount>
		function exportTicket() {
			if (validateFields()) {
				$('div.divEVTool').hide();
				MCModalUtils.showModal({
					isslideout: true,
					modaloptions: {
						backdrop: 'static',
						keyboard: false
					},
					size: 'lg',
					title: 'Download Ticket',
					iframe: true,
					contenturl: '#local.exportTicketPromptLink#',
					strmodalfooter: {
						classlist: 'text-right',
						showclose: false,
						showextrabutton: true,
						extrabuttonclass: 'btn-primary',
						extrabuttononclickhandler: 'exportTicketPromptButtonHandler',
						extrabuttonlabel: 'Download CSV',
						extrabuttoniconclass: 'fa-light fa-file-csv'
					}
				});
			}
		}
		function exportTicketPromptButtonHandler(){
			$('##MCModalBodyIframe')[0].contentWindow.fnDnTix();
		}
		function doExportTicket(tid,fsid) {
			window.setTimeout(function(){ MCModalUtils.hideModal(); },4000);
			top.location.href='#local.exportTicketLink#&tid=' + tid + '&fsid=' + fsid;
		}
	</cfif>

	<cfif local.badgePrinterEnabled>
		function printBadgeRegistrant(rid){
			MCModalUtils.showModal({
					isslideout: true,
					size: 'lg',
					modaloptions: {
						backdrop: 'static',
						keyboard: false
					},
					title: 'Print Registrant Badge',
					iframe: true,
					contenturl: '#this.link.printBadgeRegistrant#&eID=#arguments.event.getValue('eID')#&registrantID=' + rid,
					strmodalfooter: {
						classlist: 'd-none'
					}
				});

		}
	</cfif>
	function removeRegistrant(id) {
		MCModalUtils.showModal({
			isslideout: true,
			modaloptions: {
				backdrop: 'static',
				keyboard: false
			},
			size: 'lg',
			title: 'Remove Registrant Confirmation',
			iframe: true,
			contenturl: '#this.link.removeRegistrant#&mode=direct&registrantID=' + id,
			strmodalfooter: {
				classlist: 'd-flex',
				showclose: true,
				showextrabutton: true,
				extrabuttonclass: 'btn-primary ml-auto',
				extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.doCallRemoveReg',
				extrabuttonlabel: 'Remove Registrant'
			}
		});
	}
	function doRemoveReg(objParams) {
		var removeData = function(r) {
			if (r.success && r.success.toLowerCase() == 'true'){
				reloadPage();
				if(r.showrefund && r.showrefund.toString() == 'true'){
					showRefundPaymentOnRegRemoveSuccess(objParams.registrantMemberID);
				} else{
					MCModalUtils.hideModal();
				}
			} else {
				alert('Unable to Remove Registrant - We were unable to remove this registrant. Contact MemberCentral for assistance.');
			}
		};
		
		TS_AJX('ADMINEVENT','removeRegistrant',objParams,removeData,removeData,10000,removeData);
	}
	function cleanupRegInvoices(rid) {
		MCModalUtils.showModal({
			isslideout: true,
			size: 'lg',
			modaloptions: {
				backdrop: 'static',
				keyboard: false
			},
			title: 'Cleanup Invoices For Registrant',
			iframe: true,
			contenturl: '#local.cleanupInvoicesRegLink#&rid='+rid,
			strmodalfooter : {
				classlist: 'd-flex',
				showclose: true,
				showextrabutton: true,
				extrabuttonclass: 'btn-primary ml-auto',
				extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.$("##frmRegInvCleanup :submit").click',
				extrabuttonlabel: 'Continue',
			}
		});
	}
	function showRefundPaymentOnRegRemoveSuccess(memberID){
		$('##MCModal').on('hidden.bs.modal', function() { refundPayment(memberID); });
		MCModalUtils.hideModal();
	}
	function refundPayment(mid,ptid) {
		if (Number(mcma_hasrights_refundpmt) == 1) {
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'lg',
				title: 'Issue Refund',
				iframe: true,
				contenturl: mcma_link_refpayment+'&mid=' + mid + (typeof ptid == 'undefined' ? '' : '&ptid=' + ptid)
			});
		}
	}
	function closeBox() { MCModalUtils.hideModal(); }
	function reloadPage(retainPaging) { evRegistrantsTable.draw(retainPaging ? false : true); }
	function reloadEventGrid() { reloadPage(); }

	function sendConfirmation(rid,mid) {
		MCModalUtils.showModal({
			isslideout: true,
			modaloptions: {
				backdrop: 'static',
				keyboard: false
			},
			size: 'lg',
			title: 'Resend Registration Confirmation',
			iframe: true,
			contenturl: '#this.link.sendConfirmation#&cID=#arguments.event.getValue('cID')#&eID=#arguments.event.getValue('eID')#&mid=' + mid + '&registrantID=' + rid,
			strmodalfooter: {
				classlist: 'd-flex',
				showclose: true,
				showextrabutton: true,
				extrabuttonclass: 'btn-primary ml-auto',
				extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.$("##frmSendConf :submit").click',
				extrabuttonlabel: 'Resend Confirmation',
				extrabuttoniconclass: 'fa-light fa-share'
			}
		});
	}
	function printRegistrant(rid,mid) {
		MCModalUtils.showModal({
				isslideout: true,
				size: 'lg',
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				title: 'Print Registrant',
				iframe: true,
				contenturl: '#this.link.printReg#&cID=#arguments.event.getValue('cID')#&eID=#arguments.event.getValue('eID')#&mid=' + mid + '&registrantID=' + rid
			});
	}
	function addRegistrant() {
		MCModalUtils.showModal({
			isslideout: true,
			size: 'xl',
			modaloptions: {
				backdrop: 'static',
				keyboard: false
			},
			title: '#encodeForJavascript(local.strEvent.qryEventMeta.eventContentTitle)#',
			iframe: true,
			contenturl: '#this.link.addReg#&cID=#arguments.event.getValue('cID')#&eID=#arguments.event.getValue('eID')#&mode=direct'
		});
	}
	function editRegistrant(rid,mid) {
		MCModalUtils.showModal({
			isslideout: true,
			size: 'xl',
			modaloptions: {
				backdrop: 'static',
				keyboard: false
			},
			title: '#encodeForJavascript(local.strEvent.qryEventMeta.eventContentTitle)#',
			iframe: true,
			contenturl: '#this.link.addReg#&cID=#arguments.event.getValue('cID')#&eID=#arguments.event.getValue('eID')#&mode=direct&mid=' + mid + '&registrantID=' + rid,
			strmodalfooter : {
				classlist: 'd-none'
			}
		});
	}
	function closeUpdateRegistration() { 
		reloadPage(true); 
		MCModalUtils.hideModal();
		
	}
	function returnToRegistration(cid,eid,rid,mid){
		MCModalUtils.hideModal();
		$('.modal-backdrop').remove();
		if(mid > 0){
			editRegistrant(rid,mid);
		}
		else {
			addRegistrant();
		}

				
	}
	<!--- pay for reg --->
	function addPayment(po) {
		mca_addPayment(po,'#this.link.addPayment#');
	}
	function closeAddPayment(po) { 
		reloadPage(true); 
		<cfif local.myRightsTransactionsAdmin.transAllocatePayment is not 1>
			MCModalUtils.hideModal();
		<cfelse>
			allocIndivPayment(po);
		</cfif>
	}
	function allocIndivPayment(po) {
		mca_allocIndivPayment(po,'#this.link.allocatePayment#');
	}
	function closeAllocPayment() { reloadPage(true); MCModalUtils.hideModal(); }
	function manageAC(rid) {
		if (validateFields()) {
			MCModalUtils.showModal({
				isslideout: true,
				size: 'lg',
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				title: 'Manage Attendance and Credits',
				iframe: true,
				contenturl: '#this.link.manageAttendanceCredit#&' +  $('##frmFilter').serialize() + '&eid=#arguments.event.getValue('eID')#' + (rid ? '&_rid=' + rid : '')
			});
		}
	}	
	function manageSubEventAC(rid) {
		MCModalUtils.showModal({
				isslideout: true,
				size: 'lg',
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				title: 'Manage Attendance and Credits',
				iframe: true,
				contenturl: '#this.link.manageSubEventAttendanceCredit#&eid=#arguments.event.getValue('eID')#' + (rid ? '&_rid=' + rid : '')
			});
	}
	function viewCertificate(rid) {
		MCModalUtils.showModal({
				isslideout: true,
				size: 'lg',
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				title: 'Registrant Certificate',
				iframe: true,
				contenturl: '#this.link.viewCertificate#&eid=#arguments.event.getValue('eID')#&rid=' + rid
			});
	}
	function showSubmitEvaluationForm(rid) {
		MCModalUtils.showModal({
			isslideout: true,
			size: 'xl',
			modaloptions: {
				backdrop: 'static',
				keyboard: false
			},
			title: 'Submit Evaluation',
			iframe: true,
			contenturl: '#local.showSubmitEvaluationFormLink#&eid=#arguments.event.getValue('eID')#&rid=' + rid,
			strmodalfooter: {
				classlist: 'd-flex',
				showclose: true,
				showextrabutton: false
			}
		});
	}
	
	<cfif now() gt arguments.event.getValue('eventEndTime')>
		function emailCerts() {
			if (validateFields()) {
				var emailLink = '#this.link.emailCertificates#&eid=#arguments.event.getValue('eID')#&' + $('##frmFilter').serialize();
				$('div.divEVTool').hide();
				MCModalUtils.showModal({
					isslideout: true,
					modaloptions: {
						backdrop: 'static',
						keyboard: false
					},
					size: 'lg',
					title: 'E-mail Filtered Certificates',
					iframe: true,
					contenturl: emailLink,
					strmodalfooter: {
						classlist: 'd-none'
					}
				});
			}
		}
	</cfif>
	function resendConfEmail() {
		if (validateFields()) {
			$('div.divEVTool').hide();
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'lg',
				title: 'Resend Confirmations Emails to Filtered Registrants',
				iframe: true,
				contenturl: '#this.link.emailRegistrations#&' +  $('##frmFilter').serialize() + '&eid=#arguments.event.getValue('eID')#',
				strmodalfooter: {
					classlist: 'd-flex',
					showclose: true,
					showextrabutton: true,
					extrabuttonclass: 'btn-primary ml-auto',
					extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.$("##frmEmailRegs :submit").click',
					extrabuttonlabel: 'Send',
					extrabuttoniconclass: 'fa-light fa-share'
				}
			});
		}
	}
	function importRegistrants() {
		$('div.divEVTool').hide();
		MCModalUtils.showModal({
				isslideout: true,
				size: 'lg',
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				title: 'Import Registrants',
				iframe: true,
				contenturl: '#local.importRegPromptLink#'
			});
	}	
	function filterRegistrantsGrid() {
		if (validateFields()) {
			evRegistrantsTable.draw();
		}
	}
	#local.strFieldFilters.fieldSelectJS#

	function selectMemberInvFilter() {
		var selhref = '#local.memSelectGotoLink#&mode=direct&fldName=rAssociatedMemberID&retFunction=top.updateField&dispTitle=';
		MCModalUtils.showModal({
			isslideout: true,
			modaloptions: {
				backdrop: 'static',
				keyboard: false
			},
			size: 'lg',
			title: 'Filter Registrants by Member',
			iframe: true,
			contenturl: selhref,
			strmodalfooter: {
				classlist: 'd-none'
			}
		});
	}
	function selectGroupInvFilter() {
		var selhref = '#local.grpSelectGotoLink#&mode=direct&fldName=rAssociatedGroupID&retFunction=top.updateGroupField&dispTitle=' + escape('Filter Registrants by Group');

		MCModalUtils.showModal({
			isslideout: true,
			modaloptions: {
				backdrop: 'static',
				keyboard: false
			},
			size: 'xl',
			title: 'Filter Registrants by Group',
			iframe: true,
			contenturl: selhref,
			strmodalfooter: {
				classlist: 'd-none'
			}
		});
	}
	
	function updateField(fldID, mID, mNum, mName) {
		var fld = $('##'+fldID);
		var fldName = $('##rAssociatedVal');
		fld.val(mID);
		if ((mName.length > 0) && (mNum.length > 0)) {
			fldName.html('<b>' + mName + ' (' + mNum + ')</b>&nbsp;');
			$('##rAssociatedGroupID').val("");
			$('##divAssociatedVal').show();
		} else {
			fldName.html('');
			$('##divAssociatedVal').hide();
		}
	}
	function updateGroupField(fldID,gID,gPath) {
		var fld = $('##'+fldID);
		var fldName = $('##rAssociatedVal');
		fld.val(gID);
		if (gPath.length > 0) {
			var newgPath = gPath.split("\\");
				newgPath.shift();
				newgPath = newgPath.join(" \\ ");
			fldName.html('<b>' + newgPath + '</b>&nbsp;');
			$('##rAssociatedMemberID').val("");
			$('##divAssociatedVal').show();
		} else {
			fldName.html('');
			$('##divAssociatedVal').hide();
		}
	}
	function changeAssocType() {
		var assocType = $('input:radio[name=rAssocType]:checked').val();
		if ( assocType != undefined) {
			if (assocType == "group") selectGroupInvFilter();
			else selectMemberInvFilter();
		}
	}
	function clearAssocType() {
		$(".rAssocType").each(function(){
			$(this).attr("checked",false);
		});
		$('##rAssociatedVal').html("");
		$('##rAssociatedMemberID').val("");
		$('##rAssociatedGroupID').val("");
		$('##divAssociatedVal').hide();
	}
	function rFormatCurrency(el) {
		if (el.val() != '') el.val(formatCurrency(el.val()));
	}	
	function emailRegistrants() {
		if(evRegistrantsTable.page.info().recordsTotal == 0) {
			alert('There are no registrants to act upon.');
			return false;
		}
		var emailLink = '#local.massEmailRegistrants#&eID=#arguments.event.getValue('eID')#&' + $('##frmFilter').serialize();
		MCModalUtils.showModal({
			isslideout: true,
			modaloptions: {
				backdrop: 'static',
				keyboard: false
			},
			size: 'xl',
			title: 'E-mail Filtered Registrants',
			iframe: true,
			contenturl: emailLink,
			strmodalfooter: {
				classlist: 'd-none'
			}
		});
	}
	function printBadges() {
		if(evRegistrantsTable.page.info().recordsTotal == 0) {
			alert('There are no registrants to act upon.');
		}else{
		
			var printBadgesLink = '#local.massPrintBadges#&eID=#arguments.event.getValue('eID')#&' + $('##frmFilter').serialize();
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'lg',
				title: 'Print Badges for Filtered Registrants',
				iframe: true,
				contenturl: printBadgesLink,
				strmodalfooter: {
					classlist: 'd-none'
				}
			});
		}
	}
	function getGenerateSigninSheetLink(fsid) {
		return '#local.doGenerateSigninSheetLink#' + ( fsid ? '&fsid=' + fsid : '' ) + '&' + $('##frmFilter').serialize();
	}
	function printSignInSheet() {
		if(evRegistrantsTable.page.info().recordsTotal == 0) {
			alert('There are no registrants to act upon.');
		} else {
			<cfif NOT local.hasCustomSigninSheet>
				MCModalUtils.showModal({
					isslideout: true,
					size: 'lg',
					modaloptions: {
						backdrop: 'static',
						keyboard: false
					},
					title: 'Sign-In Sheet',
					iframe: true,
					contenturl: '#local.viewSigninsheetPromptLink#',
					strmodalbody : {
						classlist: 'p-2'
					},
					strmodalfooter: {
						classlist: 'text-right',
						showclose: false,
						showextrabutton: true,
						extrabuttonclass: 'btn-primary',
						extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.doGenerateSigninSheet',
						extrabuttonlabel: 'Generate Sign-In Sheet',
						extrabuttoniconclass: 'fa-light fa-file-csv'
					}
				});
			<cfelse>
				MCModalUtils.showModal({
					isslideout: true,
					size: 'lg',
					modaloptions: {
						backdrop: 'static',
						keyboard: false
					},
					title: 'Sign-In Sheet',
					iframe: true,
					contenturl: getGenerateSigninSheetLink(),
					strmodalbody : { classlist: 'p-2' },
					strmodalfooter: { classlist: 'd-none' }
				});
			</cfif>
		}
	}
	function massUpdateRegistrants() {
		if (!$('##divMassUpdateReg').is(':visible')) {
			$('div.divEVTool').hide();
			$('##divMassUpdateReg').show();
		}
	}
	function showMassUpdateRegUploadLoading() {
		if ($('##uploadFileName').val() == '') {
			$('##ev_regmassupdate_err_div').html('Select the CSV or XLS file from your computer.').addClass('alert alert-danger').show();
			return false;
		}

		$('##ev_regmassupdate_err_div').html('').hide();
		$('##divMassUpdateRegForm').hide();
		$('##divMassUpdateRegUploadLoading').show(300);
		return true;
	}
	function emailRegistrantMaterials(rid,mid) {
		MCModalUtils.showModal({
			isslideout: true,
			modaloptions: {
				backdrop: 'static',
				keyboard: false
			},
			size: 'lg',
			title: 'Email Registrant Materials',
			iframe: true,
			contenturl: '#this.link.sendRegistrantMaterials#&cID=#arguments.event.getValue('cID')#&eID=#arguments.event.getValue('eID')#&mid=' + mid + '&registrantID=' + rid,
			strmodalfooter: {
				classlist: 'd-flex',
				showclose: true,
				showextrabutton: true,
				extrabuttonclass: 'btn-primary ml-auto',
				extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.$("##frmSendMaterials :submit").click',
				extrabuttonlabel: 'Send Materials'
			}
		});
	}
	function emailEventMaterials() {
		if (validateFields()) {
			if(evRegistrantsTable.page.info().recordsTotal == 0) {
				alert('There are no registrants to act upon.');
				return false;
			}

			$('div.divEVTool').hide();
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'lg',
				title: 'Email Registrant Materials to Filtered Registrants',
				iframe: true,
				contenturl: '#this.link.massEmailRegistrantMaterials#&' +  $('##frmFilter').serialize() + '&eid=#arguments.event.getValue('eID')#',
				strmodalfooter: {
					classlist: 'd-flex',
					showclose: true,
					showextrabutton: true,
					extrabuttonclass: 'btn-primary ml-auto',
					extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.$("##frmSendMaterials :submit").click',
					extrabuttonlabel: 'Send Materials'
				}
			});
		}
	}
	function doExportEVFormResponses(type){
		mca_hideAlert('err_downloadformresponses');
		var formID = $('##formID').val();
		if (typeof formID == "undefined" || formID == 0) {
			mca_showAlert('err_downloadformresponses', 'Select an Evaluation');
		} else {
			$('##divFormResponseDownloadFormArea').addClass('d-none');
			$('##divFormResponseDownloadFormAreaLoading').removeClass('d-none').html('<h5>Download Evaluation Responses</h5>' + mca_getLoadingHTML());
			$('##divFormResponseDownloadFormAreaLoading').load(link_exportevformresponses + '&exportType=' + type + '&formID=' + formID + '&' + $('##frmFilter').serialize());
		}
	}
	function downloadEVFormResponses(u) {
		self.location.href = '/tsdd/' + u;
		$('##divFormResponseDownloadFormArea').removeClass('d-none');
		$('div.divEVRegistrantsTool, ##divFormResponseDownloadFormAreaLoading').addClass('d-none');
	}
	
	$(function(){
		mca_setupDatePickerRangeFields('rDateFrom','rDateTo');
		mca_setupCalendarIcons('frmFilter');
		mca_setupCustomFileControls('frmMassUpdateReg');
		mca_setupSelect2();
		<cfif local.hasEventDocs>
			$('##emailMaterialLink').removeClass('d-none');
		</cfif>
		$('##registrantsTab').on('click', function(){
			var chkEventHasDocuments = function(r) {
				if (r == true) {
					$('##emailMaterialLink').removeClass('d-none');
				} else {
					if(!$('##emailMaterialLink').hasClass('d-none'))
						$('##emailMaterialLink').addClass('d-none');
				}
			};
			var objParams = { eventID:#arguments.event.getValue('eID')# };
			TS_AJX('ADMINEVENT','hasEventDocumentsForAnEvent',objParams,chkEventHasDocuments,chkEventHasDocuments,10000,chkEventHasDocuments);
		});
	});
	</script>
	<style type="text/css">
		span.rfinvalid { color:##f00; font-weight:bold; padding-left:10px; }
		div##evRegistrantsTable_wrapper div##evRegistrantsTable_info { padding-top: 0.25em; }
	</style>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.registrantJS#">

<cfoutput>
<!--- button bar --->
<div class="toolButtonBar">
	<cfif XMLSearch(local.xmlRights,"string(//right[@functionName='EditRegistrants']/@allowed)") is 1>
		<div><a href="javascript:addRegistrant();" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to add a new registrant."><i class="fa-regular fa-user-plus"></i> Add Registrant</a></div>
	</cfif>
	<div><a href="javascript:filterEV();" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to filter registrants."><i class="fa-regular fa-filter"></i> Filter Registrants</a></div>
	<div><a href="javascript:manageAC();" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to manage attendance/credits."><i class="fa-regular fa-envelope-circle-check"></i> Manage Attendance/Credits</a></div>
	<div><a href="javascript:exportRegistrants();" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to download registrants."><i class="fa-regular fa-download"></i> Download Registrants</a></div>
	<cfif local.qryEventForms.recordCount>
		<div><a href="javascript:exportEventResponses();" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to download evaluation responses."><i class="fa-regular fa-download"></i> Download Evaluation Responses</a></div>
	</cfif>
	<div><a href="javascript:resendConfEmail();" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to resend confirmation e-mails."><i class="fa-regular fa-envelope-circle-check"></i> Resend Confirmations</a></div>
	<div id="emailMaterialLink" class="d-none"><a href="##" onclick="emailEventMaterials();return false;" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to email materials for filtered registrants."><i class="fa-regular fa-envelope-circle-check"></i> Email Materials</a></div>
	<cfif now() gt arguments.event.getValue('eventEndTime')>
		<div><a href="javascript:emailCerts();" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to email certificates."><i class="fa-regular fa-medal text-warning"></i> E-mail Certificates</a></div>
	</cfif>
	<cfif local.qryTickets.recordcount>
		<div><a href="javascript:exportTicket();" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to download registrant tickets."><i class="fa-regular fa-download"></i> Download Tickets</a></div>
	</cfif>	
	<div><a href="##" onclick="emailRegistrants();return false;" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to email filtered registrants."><i class="fa-regular fa-envelope"></i> Email Registrants</a></div>
	<div><a href="javascript:exportTransactions();" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to download registrant transactions."><i class="fa-regular fa-download"></i> Download Transactions</a></div>
	<cfif arguments.event.getValue('isOnlineMeeting') is 1>
		<div><a href="javascript:exportAccessLog();" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to download registrant access log."><i class="fa-regular fa-download"></i> Download Access Log</a></div>
	</cfif>
	<div><a href="javascript:importRegistrants();" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to import registrants."><i class="fa-regular fa-upload"></i> Import Registrants</a></div>
	<div><a href="javascript:printSignInSheet();" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to Generate Sign-In Sheet."><i class="fa-regular fa-table-cells"></i> Generate Sign-In Sheet</a></div>
	<cfif local.badgePrinterEnabled>
		<cfif local.hasBadgeDevices AND local.qryBadgeTemplates.recordcount>
			<div><a href="javascript:printBadges();" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to print badges."><i class="fa-regular fa-print"></i> Print Badges</a></div>
		<cfelse>
			<div><a href="##" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="There are no badge templates or badge printers setup." class="text-muted disabled"><i class="fa-regular fa-print"></i> Print Badges</a></div>
		</cfif>	
	</cfif>
	<div><a href="##" onclick="massUpdateRegistrants();return false;" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to mass update registrants via file upload."><i class="fa-regular fa-pen-to-square"></i> Mass Update Registrants via Upload</a></div>
</div>
<cfsavecontent variable="local.associatedWithFilterHTML">
	<cfoutput>
		<div class="form-group row mt-2">
			<div class="col-auto pr-1">Associated With:</div>
			<div class="col">
				<div class="d-inline-block pr-1">
					<input class="rAssocType" type="radio" name="rAssocType" id="rAssocTypeMember" value="member" onclick="changeAssocType();">
					<label for="rAssocTypeMember">A Specific Member</label>
				</div>
				<div class="d-inline-block">
					<input class="rAssocType" type="radio" name="rAssocType" id="rAssocTypeGroup" value="group" onclick="changeAssocType();">
					<label for="rAssocTypeGroup">A Specific Group</label>
				</div>
				<div id="divAssociatedVal" style="display:none;">
					<span id="rAssociatedVal"></span> &nbsp; <a href="javascript:clearAssocType();" id="aClearAssocType" class="text-danger"><i class="fa-solid fa-circle-xmark"></i></a>
				</div>
				<input type="hidden" name="rAssociatedMemberID" id="rAssociatedMemberID" value="">
				<input type="hidden" name="rAssociatedGroupID" id="rAssociatedGroupID" value="">
			</div>
		</div>
	</cfoutput>
</cfsavecontent>

<div id="divFilterForm" class="divEVTool" style="display:none;">
	<div class="card card-box">
		<div class="card-header py-1 bg-light">
			<div class="card-header--title font-weight-bold font-size-md">Registrant Filters</div>
		</div>
		<div class="card-body">
			<form name="frmFilter" id="frmFilter" onsubmit="filterRegistrantsGrid();return false;">
			<div class="row">
				<div class="col-sm-6 col-xs-12 pr-sm-2">
					<div class="form-row">
						<div class="col-xl-6 col-md-12">
							<div class="form-group">
								<div class="form-label-group mb-2">
									<div class="input-group dateFieldHolder">
										<input type="text" name="rDateFrom" id="rDateFrom" value="" class="form-control dateControl">
										<div class="input-group-append">
											<span class="input-group-text cursor-pointer calendar-button" data-target="rDateFrom"><i class="fa-solid fa-calendar"></i></span>
											<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('rDateFrom');"><i class="fa-solid fa-circle-xmark"></i></a></span>
										</div>
										<label for="rDateFrom">Registered From</label>
									</div>
								</div>
							</div>
						</div>

						<div class="col-xl-6 col-md-12">
							<div class="form-group">
								<div class="form-label-group mb-2">
									<div class="input-group dateFieldHolder">
										<input type="text" name="rDateTo" id="rDateTo" value="" class="form-control dateControl">
										<div class="input-group-append">
											<span class="input-group-text cursor-pointer calendar-button" data-target="rDateTo"><i class="fa-solid fa-calendar"></i></span>
											<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('rDateTo');"><i class="fa-solid fa-circle-xmark"></i></a></span>
										</div>
										<label for="rDateTo">Registered To</label>
									</div>
								</div>
							</div>
						</div>
					</div>

					<div class="form-row">
						<div class="col-xl-6 col-md-12">
							<div class="form-group">
								<div class="form-label-group mb-2">
									<select name="rAttended" id="rAttended" class="form-control">
										<option value="">Attended or Not Attended</option>
										<option value="1">Attended</option>
										<option value="0">Not Attended</option>
									</select>
									<label for="rAttended">Attended?</label>
								</div>
							</div>
						</div>
						<div class="col-xl-6 col-md-12">
							<div class="form-group">
								<div class="form-label-group mb-2">
									<input type="text" name="rCompany" id="rCompany" value="" class="form-control">
									<label for="rCompany">Company Name Contains...</label>
								</div>
							</div>
						</div>
					</div>
					<div class="form-row">
						<div class="col-xl-6 col-md-12 mb-2">
							<div class="input-group flex-nowrap">
								<div class="input-group-prepend">
									<span class="input-group-text px-3">$</span>
								</div>
								<div class="form-label-group flex-grow-1 mb-0">
									<input type="text" name="rBillFrom" id="rBillFrom" value="" class="form-control amtBox"  onBlur="rFormatCurrency($(this));">
									<label for="rBillFrom">Billed Amt From</label>
								</div>
							</div>
						</div>

						<div class="col-xl-6 col-md-12 mb-2">
							<div class="input-group flex-nowrap">
								<div class="input-group-prepend">
									<span class="input-group-text px-3">$</span>
								</div>
								<div class="form-label-group flex-grow-1 mb-0">
									<input type="text" name="rBillTo" id="rBillTo" value="" class="form-control amtBox"  onBlur="rFormatCurrency($(this));">
									<label for="rBillTo">Billed Amt To</label>
								</div>
							</div>
						</div>
					</div>

					<div class="form-row">
						<div class="col-xl-6 col-md-12 mb-2">
							<div class="input-group flex-nowrap">
								<div class="input-group-prepend">
									<span class="input-group-text px-3">$</span>
								</div>
								<div class="form-label-group flex-grow-1 mb-0">
									<input type="text" name="rDuesFrom" id="rDuesFrom" value="" class="form-control amtBox"  onBlur="rFormatCurrency($(this));">
									<label for="rDuesFrom">Due Amt From</label>
								</div>
							</div>
						</div>
						<div class="col-xl-6 col-md-12 mb-2">
							<div class="input-group flex-nowrap">
								<div class="input-group-prepend">
									<span class="input-group-text px-3">$</span>
								</div>
								<div class="form-label-group flex-grow-1 mb-0">
									<input type="text" name="rDuesTo" id="rDuesTo" value="" class="form-control amtBox"  onBlur="rFormatCurrency($(this));">
									<label for="rDuesTo">Due Amt To</label>
								</div>
							</div>
						</div>
					</div>
					<cfif local.qryEventForms.recordCount>
						#local.associatedWithFilterHTML#
					</cfif>
				</div>

				<div class="col-sm-6 col-xs-12 pl-sm-2">
					<div class="form-row">
						<div class="col-xl-6 col-md-12">
							<div class="form-group">
								<div class="form-label-group mb-2">
									<select name="rCreditType" id="rCreditType" class="form-control">
										<option value="">Awarded Any or No Credit</option>
										<cfloop query="local.qryCreditTypes">
											<option value="#local.qryCreditTypes.typeID#">Awarded #local.qryCreditTypes.authorityName#: #local.qryCreditTypes.creditType#</option>
										</cfloop>
										<option value="0">No Credit Awarded</option>
									</select>
									<label for="rCreditType">Awarded Credit</label>
								</div>
							</div>
						</div>

						<div class="col-xl-6 col-md-12 ">
							<div class="form-group">
								<div class="form-label-group mb-2">
									<select name="rStatus" id="rStatus" class="form-control">
										<option value="0">Hide deleted registrants</option>
										<option value="1">Show deleted registrants</option>
									</select>
									<label for="rStatus">Registrant Status</label>
								</div>
							</div>
						</div>
					</div>
					<div class="form-row">
						<div class="col-sm-12">
							<div class="form-group">
								<div class="form-label-group mb-2">
									<select name="rEvRate" id="rEvRate" class="form-control form-control-sm" multiple="yes" data-toggle="custom-select2" >
										<cfloop query="local.qryDistinctEventRates">
											<option value="#local.qryDistinctEventRates.rateID#">#local.qryDistinctEventRates.rateName#</option>
										</cfloop>
									</select>
									<label for="rEvRate">Rate</label>
								</div>
							</div>
						</div>
					</div>
					<cfif local.qryEventRoles.recordcount>
						<div class="form-row">
							<div class="col-sm-12">
								<div class="form-group">
									<div class="form-label-group mb-2">
										<select name="rEvRole" id="rEvRole" class="form-control form-control-sm" multiple="yes" data-toggle="custom-select2">
											<option value="0">No Role</option>
											<cfloop query="local.qryEventRoles">
												<option value="#local.qryEventRoles.categoryID#">#local.qryEventRoles.categoryName#</option>
											</cfloop>
										</select>
										<label for="rEvRole">Role</label>
									</div>
								</div>
							</div>
						</div>
					<cfelse>
						<input type="hidden" name="rEvRole" id="rEvRole" value="">
					</cfif>
					<cfif local.qryEventForms.recordCount>
						<div class="form-row">
							<div class="col-sm-12">
								<div class="form-group">
									<div class="form-label-group mb-2">
										<select name="rEvFormCompletion" id="rEvFormCompletion" class="form-control form-control-sm" multiple="yes" data-toggle="custom-select2" >
											<cfloop query="local.qryEventForms">
												<option value="#local.qryEventForms.eventFormID#|1">Completed #local.qryEventForms.formTitle#</option>
												<option value="#local.qryEventForms.eventFormID#|0">Not Completed #local.qryEventForms.formTitle#</option>
											</cfloop>
										</select>
										<label for="rEvFormCompletion">Evaluation Completed?</label>
									</div>
								</div>
							</div>
						</div>
					<cfelse>
						#local.associatedWithFilterHTML#
					</cfif>
				</div>
			</div>
			<cfif local.strFieldFilters.qryFields.recordcount>
				<div class="form-group row">
					<div class="col-sm-12 font-weight-bold mt-4 mb-2">Registrant Field Filters</div>
					<div class="col-sm-12">
						#local.strFieldFilters.fieldSelectArea#
					</div>
				</div>
			</cfif>
			<div class="mt-4">
				<button type="submit" class="btn btn-sm btn-primary"><i class="fa-light fa-filter"></i> Show Registrants</button>
			</div>
			</form>
		</div>
	</div>
</div>

<div id="divMassUpdateReg" class="divEVTool" style="display:none;">
	<div id="divMassUpdateRegArea" class="mt-3 mb-4">
		<div id="divMassUpdateRegForm">
			<div id="ev_regmassupdate_err_div" class="mb-1" style="display:none;"></div>
			<div class="row">
				<div class="col-auto text-center">
					<h5>Mass Update Registrants<br/>via File Upload</h5>
					<br/>
					<div><img src="/assets/common/images/fileExts/xls.png" hspace="2" width="16" height="16"><a href="#local.sampleMassUpdateRegTemplate#">Template</a></div>
				</div>
				<div class="col">
					<div id="divMassUpdateRegForm">
						<div class="mb-2">Upload your CSV or XLS file with information to mass update registrants.</div>
						<div style="mb-1">The file does not need to match the template. You will have the opportunity to map your update columns to the required columns in the next step.</div>
						<form name="frmMassUpdateReg" id="frmMassUpdateReg" action="#local.preProcessMassUpdateLink#" method="post" enctype="multipart/form-data" onsubmit="return showMassUpdateRegUploadLoading();">
							<div class="input-group mt-2">
								<div class="custom-file form-control-sm">
									<input type="file" class="custom-file-input" name="uploadFileName" id="uploadFileName">
									<label class="custom-file-label" for="uploadFileName">Select File</label>
								</div>
								<button type="submit" class="btn btn-sm btn-primary ml-2">Upload</button>
							</div>
						</form>
					</div>
				</div>
			</div>
		</div>
		<div id="divMassUpdateRegUploadLoading" style="display:none;">
			<h5>Mass Update Registrants via File Upload</h5>
			<div class="mt-4 text-center"><div class="spinner-border" role="status"></div><div class="mt-2">Uploading data. Please wait...</div></div>			
		</div>
	</div>
</div>
<cfif local.qryEventForms.recordCount>
	<div id="divFormResponseDownloadForm" class="d-none divEVRegistrantsTool">
		<div id="divFormResponseDownloadFormArea">
			<div class="card card-box">
				<div class="card-header bg-light">
					<div class="card-header--title font-weight-bold font-size-md">Download Evaluation Responses</div>
				</div>
				<div class="card-body p-3">
					<div id="err_downloadformresponses" class="alert alert-danger mb-3 d-none"></div>
					Select either CSV or PDF to download evaluation/exam responses for the filtered registrants.
					<br/><br/>
					<cfif local.qryEventForms.recordCount eq 1>
						<cfif LCase(local.qryEventForms.loadpoint) eq 'evaluation'>
							There is one evaluation attached to this event:<br/>
						<cfelse>
							There is one exam attached to this event:<br/>
						</cfif>
						<b>#local.qryEventForms.loadpoint#</b> - <b>#local.qryEventForms.formTitle#</b>
						<br/><br/>
						<input type="hidden" name="formID" id="formID" value="#local.qryEventForms.formID#">
					<cfelseif local.qryEventForms.recordCount gt 1>
						<div class="form-group row">
							<label for="formID" class="col-auto col-form-label-sm font-size-md">Select Evaluation</label>
							<div class="col">
								<select name="formID" id="formID" class="form-control form-control-sm">
									<option value="0">Select Evaluation</option>
									<cfoutput query="local.qryEventForms" group="loadpoint">
										<optgroup label="#local.qryEventForms.loadpoint#">
										<cfoutput>
											<option value="#local.qryEventForms.formID#">#local.qryEventForms.formTitle#</option>
										</cfoutput>
										</optgroup>
									</cfoutput>
								</select>
							</div>
						</div>
					</cfif>
					<div class="mb-2">
						<button type="button" class="btn btn-sm btn-secondary" onclick="doExportEVFormResponses('csv');" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to download the individual responses as a CSV.">
							<i class="fa-light fa-file-csv"></i> CSV
						</button>
						<span class="ml-2">This will include individual responses as a CSV.</span>
					</div>
					<div>
						<button type="button" class="btn btn-sm btn-secondary" onclick="doExportEVFormResponses('pdf');" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to download the summary of responses as a PDF.">
							<i class="fa-light fa-file-pdf"></i> PDF
						</button>
						<span class="ml-2">This will be a summary of responses as a PDF.</span>
					</div>
				</div>
			</div>
		</div>
		<div id="divFormResponseDownloadFormAreaLoading" class="d-none"></div>
	</div>
</cfif>

<div class="mt-2" style="min-width:690px;">
	<h5>Registrants</h5>
</div>
<table id="evRegistrantsTable" class="table table-sm table-striped table-bordered" style="width:100%">
	<thead>
		<tr>
			<th>Registrant</th>
			<th>Registered</th>
			<th>Attendance</th>
			<th>Billed</th>
			<th>Due</th>
			<th>Actions</th>
		</tr>
	</thead>
</table>
<div id="divCustomFilterFieldsHolder" class="d-none">
	#local.strFieldFilters.fieldSelectControls#
</div>
</cfoutput>