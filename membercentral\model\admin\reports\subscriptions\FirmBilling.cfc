<cfcomponent extends="model.admin.reports.report" output="no">
	<cfset variables.defaultEvent = 'controller'>
	<cfset variables.runformats = [ 'screen','pdf' ]>
	<cfset variables.AllowScheduling = false>

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
		var local = structNew();

		// call common report controller
		reportController(event=arguments.event);

		local.methodToRun = this[arguments.event.getValue('mca_ta')];
		return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>

	<cffunction name="showReport" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		
		<cfif arguments.event.getValue('qryReportInfo').reportID gt 0>
			<cfset local.strSubsWidgetData = { title='Define Subscription Filter',
				description='Filter the root subscriptions for individuals appearing on this report using the defined criteria below.', 
				gridext="#this.siteResourceID#_1", gridwidth=660, gridheight=150, gridClassList='mb-5 stepDIV', initGridOnLoad=true,
				controllingSRID=this.siteResourceID, reportID=arguments.event.getValue('qryReportInfo').reportID, filterMode=1,
				excludeSteps='SS,PS,PM,D', showIcons=1 }>
			<!--- hide icons if unable to change report --->
			<cfif NOT hasReportEditRights(event=arguments.event)>
				<cfset local.strSubsWidgetData.showIcons = 0>
			</cfif>
			<cfset local.strSubscriptionWidget = createObject("component","model.admin.common.modules.subscriptionWidget.subscriptionWidget").renderWidget(strWidgetData=local.strSubsWidgetData)>

			<cfset local.qrySubStatuses = getSubscriptionStatuses()>
			
			<cfset local.frmRT = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmrt/text())")>
			<cfset local.frmRTRT = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmrtrt/text())")>
			<cfset local.frmGroupID = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmgroupid/text())")>
			
			<cfif val(local.frmGroupID) neq 0>
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySelectedGroup">
					SET XACT_ABORT, NOCOUNT ON;
					BEGIN TRY
						SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

						select groupPathExpanded 
						from dbo.ams_groups 
						where groupID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.frmGroupID#">;

						SET TRANSACTION ISOLATION LEVEL READ COMMITTED;		
					END TRY
					BEGIN CATCH
						IF @@trancount > 0 ROLLBACK TRANSACTION;
						SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
						EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
					END CATCH
				</cfquery>
			</cfif>

			<cfset local.grpSelectLink = buildLinkToTool(toolType='MemberSelectorAdmin',mca_ta='listGroups')>
			<cfset local.qryOrgRecordTypes = application.objOrgInfo.getOrgRecordTypes(orgID=arguments.event.getValue('mc_siteinfo.orgID'))>

			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryRoles">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					DECLARE @orgID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteinfo.orgID')#">;

					select rt.recordTypeID, rtrt.recordTypeRelationshipTypeID, linkingRT.recordTypeName as linkingRecordTypeName, rrt.relationshipTypeName
					from dbo.ams_recordTypes as rt
					inner join dbo.ams_recordTypesRelationshipTypes as rtrt on rtrt.childRecordTypeID = rt.recordTypeID
					inner join dbo.ams_recordRelationshipTypes as rrt on rrt.relationshipTypeID = rtrt.relationshipTypeID
					inner join dbo.ams_recordTypes as linkingRT on rtrt.masterRecordTypeID = linkingRT.recordTypeID
					where rt.orgID = @orgID
					and rtrt.isActive = 1
						union all
					select rt.recordTypeID, rtrt.recordTypeRelationshipTypeID, linkingRT.recordTypeName as linkingRecordTypeName, rrt.relationshipTypeName
					from dbo.ams_recordTypes as rt
					inner join dbo.ams_recordTypesRelationshipTypes as rtrt on rtrt.masterRecordTypeID = rt.recordTypeID
					inner join dbo.ams_recordRelationshipTypes as rrt on rrt.relationshipTypeID = rtrt.relationshipTypeID
					inner join dbo.ams_recordTypes as linkingRT on rtrt.childRecordTypeID = linkingRT.recordTypeID
					where rt.orgID = @orgID
					and rtrt.isActive = 1
					order by rt.recordTypeID, linkingRecordTypeName, rrt.relationshipTypeName;

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;		
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
			<cfsavecontent variable="local.dataHead">
				<cfoutput>
				<script language="javascript">
					function selGroup(fldID) {
						var selhref = '#local.grpSelectLink#&mode=direct&retFunction=top.doSelectGroup&fldName=' + fldID;
						MCModalUtils.showModal({
							isslideout: true,
							modaloptions: {
								backdrop: 'static',
								keyboard: false
							},
							size: 'xl',
							title: 'Select Group',
							iframe: true,
							contenturl: selhref,
							strmodalfooter: {
								classlist: 'd-none'
							}
						});
					}
					function doSelectGroup(fldID,gID,gPath,bypass) {
						bypass = (typeof bypass === 'undefined') ? '0' : bypass;
						$('input##frmGroupID').val(gID);
						var newgPath = gPath.split("\\");
							if (bypass == 0) newgPath.shift();
							newgPath = newgPath.join(" \\ ");
						$('span##spanGroupInfo').html(newgPath);
						$('span##spanGroupClear').show();
						$('span##spanGroupSelect a').html('Select Other Group');
						return false;
					}
					function clearSelGroup(fldID) {
						$('input##frmGroupID').val('');
						$('span##spanGroupInfo').html('');
						$('span##spanGroupClear').hide();
						$('span##spanGroupSelect a').html('Select Group');
					}
					function closeBox() { MCModalUtils.hideModal(); }

					function frmRTRTChange() {
						$('##divReportShowScreenLoading, div.tbodyrightrtrt').hide();
						$('##divReportShowScreen').html('').hide();
						$('div.tbodyrightrtrt select').attr('disabled',true);

						var currSelRT = $('##frmRT').val() || '';
						for (var i=0, tot=currSelRT.length; i < tot; i++) {
							$('div##tbodyRightRTRT'+currSelRT[i]+' select').attr('disabled',false);
							$('div##tbodyRightRTRT'+currSelRT[i]).show();
						}
					}
					function forceSubs() {
						var feResult = false;

						var renewalStatementTitle = $('##renewalStatementTitle').val();			
						if (renewalStatementTitle.length == 0 && $('##frmView').val() == "2") {
							rptShowAlert('You must enter a Subscriber Detail Sheet Title.');
							return feResult;
						}

						var forceSubsResult = function(s) {
							if (s.success && s.success.toLowerCase() == 'true' && s.subcount > 0) feResult=true; 
							else { rptShowAlert('This report requires you to select one or more subscriptions in the Subscription Filter.'); feResult=false; }
						};
						var objParams = { rptid:#val(arguments.event.getValue('qryReportInfo').reportID)#, csrid:#this.siteResourceID# };
						TS_AJX_SYNC('SUBSWIDGET','checkSubscriptionFilterLength',objParams,forceSubsResult,forceSubsResult,5000,forceSubsResult);

						if (feResult) {
							if ( $('##frmView').val() == "2" ) {
								$('##reportDefs ##reportAction').val('outputToScreen');
							}
						}

						return feResult;
					}

					function frmReportViewChange() {
						if ( $('##frmView').val() == "2" ) {
							$('##btnReportBarscreen').hide();
							$('.divFullReportViewOnly').show();
							
						}
						else {
							$('##btnReportBarscreen').show();
							$('.divFullReportViewOnly').hide();
						}
					}

					function frmCoversheetChange() {
						var calendarCount = $('##frmInvoiceProfileID').val().length;
						if ($('##frmInvoiceProfileID').val()){
							calendarCount = $('##frmInvoiceProfileID').val().length;
						}
						if ( !calendarCount ){
							$('##profileImage').hide();	
						}
						else{
							$('##profileImageSrc').attr("src", '/userassets/common/invoices/' + $('##frmInvoiceProfileID').val());
							$('##profileImage').show();
						}
					}

					$(function() {
						$('.tbody_ovfsimg,.tbody_ovfsmn,.tbody_ovfsmc').hide();

						setupRptFilterDateRange('frmStartFrom','frmStartTo');
						setupRptFilterDateRange('frmEndFrom','frmEndTo');
						mca_setupCalendarIcons('frmReport');
						mca_setupSelect2(); 
						frmReportViewChange();
						frmRTRTChange();

						$('span##spanGroupClear').hide();
						<cfif val(local.frmGroupID) neq 0>
							doSelectGroup('',#local.frmGroupID#,'#JSStringFormat(local.qrySelectedGroup.groupPathExpanded)#',1)
						</cfif>
					});				
				</script>
				#local.strSubscriptionWidget.js#
				<style type="text/css">
				span##spanGroupInfo { font-weight:bold; display:block; margin-bottom:4px; }
				</style>
				</cfoutput>
			</cfsavecontent>
			<cfhtmlhead text="#local.dataHead#">

			<cfquery name="local.qryInvoiceProfiles" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					select profileID, profileName, imageExt
					from dbo.tr_invoiceProfiles
					where orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteinfo.orgid')#">
					and status <> 'D'
					and imageExt is not null
					order by profileName;

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;		
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<div id="reportDefs">
				#showCommonTop(event=arguments.event)#
				
				<cfif arguments.event.getValue('qryReportInfo').reportID gt 0>
					<cfset local.frmStartFrom = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmstartfrom/text())")>
					<cfset local.frmStartTo = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmstartto/text())")>
					<cfset local.frmEndFrom = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmendfrom/text())")>
					<cfset local.frmEndTo = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmendto/text())")>
					<cfset local.frmOrgSize = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmorgsize/text())")>
					<cfset local.frmSubStatusID = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmsubstatusid/text())")>
					<cfset local.frmView = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmview/text())")>
					<cfset local.frmInvoiceProfileID = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frminvoiceprofileid/text())")>
					<cfset local.frmCoverSheetTitle = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmcoversheettitle/text())")>
					<cfset local.frmBalanceTitle = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmbalancetitle/text())")>
					<cfset local.frmBalanceDescription = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmbalancedescription/text())")>
					<cfset local.frmPaidFullTitle = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmpaidfulltitle/text())")>
					<cfset local.frmPaidFullDescription = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmpaidfulldescription/text())")>
					<cfset local.renewalStatementTitle = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/renewalstatementtitle/text())")>
					<cfset local.frmSubscriberTopContent = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmsubscribertopcontent/text())")>
					<cfset local.frmRenewOnlineOptions = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmrenewonlineoptions/text())")>
					<cfset local.frmDisplayZeroDollarAddOns = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmdisplayzerodollaraddons/text())")>
					<cfset local.frmSubscriberBottomContent = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmsubscriberbottomcontent/text())")>
					<cfset local.frmIncludeSection = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmincludesection/text())")>
					<cfset local.frmSectionTitle = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmsectiontitle/text())")>
					<cfset local.frmSectionDescription = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmsectiondescription/text())")>

					<cfif not arraylen(xmlparse(arguments.event.getValue('qryReportInfo').otherXML).report.extra.xmlChildren)>
						<cfset local.frmCoverSheetTitle = "Consolidated Subscription Statement">
						<cfset local.frmBalanceTitle = "Individuals with Balance Remaining">
						<cfset local.frmBalanceDescription = "The following individuals have a balance remaining.">
						<cfset local.frmPaidFullTitle = "Individuals Paid In Full">
						<cfset local.frmPaidFullDescription = "The following individuals are paid in full.">
						<cfset local.renewalStatementTitle = "Renewal Statement">
						<cfset local.frmSubscriberTopContent = "">
						<cfset local.frmRenewOnlineOptions = 0>
						<cfset local.frmDisplayZeroDollarAddOns = 1>
						<cfset local.frmSubscriberBottomContent = "">
						<cfset local.frmIncludeSection = 1>
						<cfset local.frmSectionTitle = "Individuals without Subscription">
						<cfset local.frmSectionDescription = "The following individuals do not have any active, offered, or accepted subscriptions of this type.">
					</cfif>
					
					<cfform name="frmReport"  id="frmReport" method="post">
					<input type="hidden" name="reportAction" id="reportAction" value="">
					
					#local.strSubscriptionWidget.html#
					#showStepMemberCriteria(event=arguments.event, title="Define Optional Firm Filter", desc="Optionally filter the firm records on this report using the defined criteria below.")#
					
					<div class="mb-5 stepDIV">
						<h5>Define Filter for Individuals Linked to Firm(s)</h5>
						<div class="row mt-2">
							<div class="col-sm-12">
								<div class="form-group row">
									<label for="frmGroupID" class="col-md-4 col-sm-12 col-form-label">Members of</label>
									<div class="col-md-8 col-sm-12">
										<span id="spanGroupInfo"></span>
										<span id="spanGroupSelect"><a href="javascript:void(0);" onclick="selGroup('frmGroupID')">Select Group</a></span>
										<span id="spanGroupClear">&bull; <a href="javascript:void(0);" onclick="clearSelGroup('frmGroupID')">Clear Selection</a></span>
										<cfinput type="hidden" name="frmGroupID" id="frmGroupID" value="">
									</div>
								</div>
								<div class="form-group row">
									<label for="frmRT" class="col-md-4 col-sm-12 col-form-label">Record Types</label>
									<div class="col-md-8 col-sm-12">
										<select name="frmRT" id="frmRT" onChange="frmRTRTChange()" class="form-control form-control-sm" multiple="yes" data-toggle="custom-select2" placeholder="Select options">
											<cfloop query="local.qryOrgRecordTypes">
												<option value="#local.qryOrgRecordTypes.recordTypeID#" <cfif listFind(local.frmRT,local.qryOrgRecordTypes.recordTypeID)>selected</cfif>>#local.qryOrgRecordTypes.recordTypeName#</option>
											</cfloop>
										</select>
									</div>
								</div>
								<cfloop query="local.qryOrgRecordTypes">
									<cfquery name="local.qryRolesRT" dbtype="query">
										select recordTypeRelationshipTypeID, linkingRecordTypeName + ': ' + relationshipTypeName as role
										from [local].qryRoles
										where recordTypeID = #local.qryOrgRecordTypes.recordTypeID#
										order by linkingRecordTypeName, relationshipTypeName
									</cfquery>

									<cfset local.thisSelectedRecTypeRolesList = "">
									<cfset local.thisRecTypeIndex = ListFind(local.frmRT, local.qryOrgRecordTypes.recordTypeID)>
									<cfif local.thisRecTypeIndex gt 0 and ListIndexExists(local.frmRTRT, local.thisRecTypeIndex, "|")>
										<cfset local.thisSelectedRecTypeRolesList = ListGetAt(local.frmRTRT, local.thisRecTypeIndex, "|")>
										<cfif local.thisSelectedRecTypeRolesList eq "ALL">
											<cfset local.thisSelectedRecTypeRolesList = "">
										</cfif>
									</cfif>

									<div class="form-group row tbodyrightrtrt" id="tbodyRightRTRT#local.qryOrgRecordTypes.recordTypeID#" style="display:none;">
										<label for="frmRTRT_#local.qryOrgRecordTypes.recordTypeID#" class="col-md-4 col-sm-12 col-form-label">Limit to roles of #local.qryOrgRecordTypes.recordTypeName#</label>
										<div class="col-md-8 col-sm-12">
											<select name="frmRTRT_#local.qryOrgRecordTypes.recordTypeID#" id="frmRTRT_#local.qryOrgRecordTypes.recordTypeID#" class="form-control form-control-sm" multiple="yes" data-toggle="custom-select2" placeholder="All roles">
												<cfloop query="local.qryRolesRT">
													<option value="#local.qryRolesRT.recordTypeRelationshipTypeID#" <cfif listFind(local.thisSelectedRecTypeRolesList, local.qryRolesRT.recordTypeRelationshipTypeID)>selected</cfif>>#local.qryRolesRT.role#</option>
												</cfloop>
											</select>
										</div>
									</div>
								</cfloop>
							</div>
						</div>
					</div>

					<div class="mb-5 stepDIV">
						<h5>Define Extra Options</h5>
						<div class="row mt-2">
							<div class="col-sm-12">
								<div class="form-group row">
									<label for="frmStartFrom" class="col-md-4 col-sm-12 col-form-label">Subscription Start Date between</label>
									<div class="col-md-8 col-sm-12">
										<div class="row">
											<div class="col-md col-sm-12 pr-md-0">
												<div class="input-group input-group-sm">
													<input type="text" name="frmStartFrom" id="frmStartFrom" value="#local.frmStartFrom#" mcrdtxt="Subscription Start Date Start" class="form-control form-control-sm dateControl rolldate" placeholder="Date From">
													<div class="input-group-append">
														<span class="input-group-text cursor-pointer calendar-button" data-target="frmStartFrom"><i class="fa-solid fa-calendar"></i></span>
													</div>
												</div>
											</div>
											<div class="col-md-auto px-md-2 d-flex align-items-center">and</div>
											<div class="col-md col-sm-12 px-md-0">
												<div class="input-group input-group-sm">
													<input type="text" name="frmStartTo" id="frmStartTo" value="#local.frmStartTo#" mcrdtxt="Subscription Start Date End" class="form-control form-control-sm dateControl rolldate" placeholder="Date To">
													<div class="input-group-append">
														<span class="input-group-text cursor-pointer calendar-button" data-target="frmStartTo"><i class="fa-solid fa-calendar"></i></span>
													</div>
												</div>
											</div>
											<div class="col-md-auto pl-md-2">
												<button type="button" class="btn btn-pill btn-secondary btn-sm btn-clear-dates" onclick="clearDateRangeFields('frmStartFrom','frmStartTo');">clear</button>
											</div>
										</div>
									</div>
								</div>
								<div class="form-group row">
									<label for="frmEndFrom" class="col-md-4 col-sm-12 col-form-label">Subscription End Date between</label>
									<div class="col-md-8 col-sm-12">
										<div class="row">
											<div class="col-md col-sm-12 pr-md-0">
												<div class="input-group input-group-sm">
													<input type="text" name="frmEndFrom" id="frmEndFrom" value="#local.frmEndFrom#" mcrdtxt="Subscription End Date Start" class="form-control form-control-sm dateControl rolldate" placeholder="Date From">
													<div class="input-group-append">
														<span class="input-group-text cursor-pointer calendar-button" data-target="frmEndFrom"><i class="fa-solid fa-calendar"></i></span>
													</div>
												</div>
											</div>
											<div class="col-md-auto px-md-2 d-flex align-items-center">and</div>
											<div class="col-md col-sm-12 px-md-0">
												<div class="input-group input-group-sm">
													<input type="text" name="frmEndTo" id="frmEndTo" value="#local.frmEndTo#" mcrdtxt="Subscription End Date End" class="form-control form-control-sm dateControl rolldate" placeholder="Date To">
													<div class="input-group-append">
														<span class="input-group-text cursor-pointer calendar-button" data-target="frmEndTo"><i class="fa-solid fa-calendar"></i></span>
													</div>
												</div>
											</div>
											<div class="col-md-auto pl-md-2">
												<button type="button" class="btn btn-pill btn-secondary btn-sm btn-clear-dates" onclick="clearDateRangeFields('frmEndFrom','frmEndTo');">clear</button>
											</div>
										</div>
									</div>
								</div>
								<div class="form-group row">
									<label for="frmOrgSize" class="col-md-4 col-sm-12 col-form-label">Minimum Org Size</label>
									<div class="col-md-8 col-sm-12">
										<input type="text" name="frmOrgSize" id="frmOrgSize" value="#local.frmOrgSize#" class="form-control form-control-sm">
									</div>
								</div>
								<div class="form-group row">
									<label for="frmSubStatusID" class="col-md-4 col-sm-12 col-form-label">Subscription Statuses</label>
									<div class="col-md-8 col-sm-12">
										<select name="frmSubStatusID" id="frmSubStatusID" multiple="yes" class="form-control form-control-sm" data-toggle="custom-select2" placeholder="Select to Limit (Billed, Accepted, and Active by Default)">
											<cfloop query="local.qrySubStatuses">
												<option value="#local.qrySubStatuses.statusID#" <cfif listFind(local.frmSubStatusID,local.qrySubStatuses.statusID) gt 0>selected</cfif>>#local.qrySubStatuses.statusName#</option>
											</cfloop>
										</select>
									</div>
								</div>
								<div class="form-group row">
									<label for="frmView" class="col-md-4 col-sm-12 col-form-label">Report View</label>
									<div class="col-md-8 col-sm-12">
										<select name="frmView" id="frmView" onchange="frmReportViewChange()" class="form-control form-control-sm">
											<option value="1" <cfif local.frmView EQ "1">selected</cfif>>Summary - View list of firms and matching subscribers</option>
											<option value="2" <cfif local.frmView EQ "2">selected</cfif>>Full - Produce PDF for each matching firm</option>
										</select>
									</div>
								</div>
							</div>
						</div>
					</div>

					<div class="mb-5 stepDIV divFullReportViewOnly">
						<h5>Define Section Labels</h5>
						<div class="row mt-2">
							<div class="col-sm-12">
								<div class="form-group row">
									<label for="frmInvoiceProfileID" class="col-md-4 col-sm-12 col-form-label">Coversheet Header</label>
									<div class="col-md-8 col-sm-12">
										<select name="frmInvoiceProfileID" id="frmInvoiceProfileID" onchange="frmCoversheetChange()" class="form-control form-control-sm">
											<option value="">None</option>
											<cfloop query="local.qryInvoiceProfiles">
												<option value="#local.qryInvoiceProfiles.profileID#.#imageExt#" <cfif local.frmInvoiceProfileID eq "#local.qryInvoiceProfiles.profileID#.#imageExt#">selected</cfif>>#local.qryInvoiceProfiles.profileName#</option>
											</cfloop>
										</select>	
										<div id="profileImage" <cfif local.frmInvoiceProfileID EQ "">style="display:none;"</cfif>>
											<img id="profileImageSrc" src="/userassets/common/invoices/#local.frmInvoiceProfileID#">
										</div>
									</div>
								</div>
								<div class="form-group row">
									<label for="frmCoverSheetTitle" class="col-md-4 col-sm-12 col-form-label">Coversheet Title</label>
									<div class="col-md-8 col-sm-12">
										<input type="text" name="frmCoverSheetTitle" id="frmCoverSheetTitle" value="#local.frmCoverSheetTitle#" class="form-control form-control-sm">
									</div>
								</div>
								<div class="form-group row">
									<label for="frmBalanceTitle" class="col-md-4 col-sm-12 col-form-label">Individuals with balance Title</label>
									<div class="col-md-8 col-sm-12">
										<input type="text" name="frmBalanceTitle" id="frmBalanceTitle" value="#local.frmBalanceTitle#" class="form-control form-control-sm">
									</div>
								</div>
								<div class="form-group row">
									<label for="frmBalanceDescription" class="col-md-4 col-sm-12 col-form-label">Individuals with balance Description</label>
									<div class="col-md-8 col-sm-12">
										<textarea name="frmBalanceDescription" id="frmBalanceDescription" rows="4" class="form-control form-control-sm">#local.frmBalanceDescription#</textarea>
									</div>
								</div>
								<div class="form-group row">
									<label for="frmPaidFullTitle" class="col-md-4 col-sm-12 col-form-label">Individuals Paid in Full Title</label>
									<div class="col-md-8 col-sm-12">
										<input type="text" name="frmPaidFullTitle" id="frmPaidFullTitle" value="#local.frmPaidFullTitle#" class="form-control form-control-sm">
									</div>
								</div>
								<div class="form-group row">
									<label for="frmPaidFullDescription" class="col-md-4 col-sm-12 col-form-label">Individuals Paid in Full Description</label>
									<div class="col-md-8 col-sm-12">
										<textarea name="frmPaidFullDescription" id="frmPaidFullDescription" rows="4" class="form-control form-control-sm">#local.frmPaidFullDescription#</textarea>
									</div>
								</div>
							</div>
						</div>
					</div>
					
					<div class="mb-5 stepDIV divFullReportViewOnly">
						<h5>Subscriber Detail Sheet</h5>
						<div class="row mt-2">
							<div class="col-sm-12">	
								<div class="form-group row">
									<label for="renewalStatementTitle" class="col-md-4 col-sm-12 col-form-label">Title</label>
									<div class="col-md-8 col-sm-12">
										<input type="text" name="renewalStatementTitle" id="renewalStatementTitle" value="#local.renewalStatementTitle#" maxlength="25" class="form-control form-control-sm">
									</div>
								</div>
								<div class="form-group row">
									<label for="frmSubscriberTopContent" class="col-md-4 col-sm-12 col-form-label">Top Content</label>
									<div class="col-md-8 col-sm-12">
										<textarea name="frmSubscriberTopContent" id="frmSubscriberTopContent" rows="4" class="form-control form-control-sm">#local.frmSubscriberTopContent#</textarea>
									</div>
								</div>
								<div class="form-group row">
									<label for="frmDisplayZeroDollarAddOns" class="col-md-4 col-sm-12 col-form-label">Display $0.00 Add-Ons</label>
									<div class="col-md-8 col-sm-12">
										<div class="form-check">
											<input class="form-check-input" type="radio" name="frmDisplayZeroDollarAddOns" id="frmDisplayZeroDollarAddOns_1" value="1" <cfif len(local.frmDisplayZeroDollarAddOns) eq 0 OR val(local.frmDisplayZeroDollarAddOns) eq 1>checked</cfif>>
											<label class="form-check-label" for="frmDisplayZeroDollarAddOns_1">Yes</label>
										</div>
										<div class="form-check">
											<input class="form-check-input" type="radio" name="frmDisplayZeroDollarAddOns" id="frmDisplayZeroDollarAddOns_0" value="0" <cfif len(local.frmDisplayZeroDollarAddOns) and val(local.frmDisplayZeroDollarAddOns) eq 0>checked</cfif>>
											<label class="form-check-label" for="frmDisplayZeroDollarAddOns_0">No</label>
										</div>
									</div>
								</div>
								<div class="form-group row">
									<label for="frmRenewOnlineOptions" class="col-md-4 col-sm-12 col-form-label">Renew Online Options</label>
									<div class="col-md-8 col-sm-12">
										<div class="form-check">
											<input class="form-check-input" type="radio" name="frmRenewOnlineOptions" id="frmRenewOnlineOptions_0" value="0" <cfif local.frmRenewOnlineOptions neq "1">checked</cfif>>
											<label class="form-check-label" for="frmRenewOnlineOptions_0">Do Not Display on Individual Statements</label>
										</div>
										<div class="form-check">
											<input class="form-check-input" type="radio" name="frmRenewOnlineOptions" id="frmRenewOnlineOptions_1" value="1" <cfif local.frmRenewOnlineOptions eq "1">checked</cfif>>
											<label class="form-check-label" for="frmRenewOnlineOptions_1">Display renew Link and Renewal Code</label>
										</div>
									</div>
								</div>
								<div class="form-group row">
									<label for="frmSubscriberBottomContent" class="col-md-4 col-sm-12 col-form-label">Bottom Content</label>
									<div class="col-md-8 col-sm-12">
										<textarea name="frmSubscriberBottomContent" id="frmSubscriberBottomContent" rows="4" class="form-control form-control-sm">#local.frmSubscriberBottomContent#</textarea>
									</div>
								</div>
							</div>
						</div>
					</div>
					
					<div class="mb-5 stepDIV divFullReportViewOnly">
						<h5>Individuals without Specified Subscription(s)</h5>
						<div class="row mt-2">
							<div class="col-sm-12">
								<div class="form-group row">
									<label for="frmIncludeSection" class="col-md-4 col-sm-12 col-form-label">Do you want this Section Included?</label>
									<div class="col-md-8 col-sm-12">
										<div class="form-check">
											<input class="form-check-input" type="radio" name="frmIncludeSection" id="frmIncludeSection_1" value="1" <cfif local.frmIncludeSection eq "1">checked</cfif>>
											<label class="form-check-label" for="frmIncludeSection_1">Yes</label>
										</div>
										<div class="form-check">
											<input class="form-check-input" type="radio" name="frmIncludeSection" id="frmIncludeSection_0" value="0" <cfif local.frmIncludeSection eq "0">checked</cfif>>
											<label class="form-check-label" for="frmIncludeSection_0">No</label>
										</div>
									</div>
								</div>
								<div class="form-group row">
									<label for="frmSectionTitle" class="col-md-4 col-sm-12 col-form-label">Section Title</label>
									<div class="col-md-8 col-sm-12">
										<input type="text" name="frmSectionTitle" id="frmSectionTitle" value="#local.frmSectionTitle#" class="form-control form-control-sm">
									</div>
								</div>
								<div class="form-group row">
									<label for="frmSectionDescription" class="col-md-4 col-sm-12 col-form-label">Section Description</label>
									<div class="col-md-8 col-sm-12">
										<textarea name="frmSectionDescription" id="frmSectionDescription" rows="4" class="form-control form-control-sm">#local.frmSectionDescription#</textarea>
									</div>
								</div>
							</div>
						</div>

						<div class="mt-5">
							#showStepFieldsets(event=arguments.event)#
						</div>
					</div>

					#showStepRollingDates(event=arguments.event)#
					#showButtonBar(event=arguments.event,validateFunction='forceSubs')#
					</cfform>
				</cfif>
			</div>
			</cfoutput>
		</cfsavecontent>
	
		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>

	<cffunction name="qrySummaryReport" access="private" output="false" returntype="struct">
		<cfargument name="strSQLPrep" type="struct" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="frmRT" type="string" required="true">
		<cfargument name="frmRTRT" type="string" required="true">
		<cfargument name="frmGroupID" type="string" required="true">
		<cfargument name="orgSize" type="numeric" required="true">
		<cfargument name="subStatusIDList" type="string" required="true">
		<cfargument name="frmStartFrom" type="date" required="true">
		<cfargument name="frmStartTo" type="date" required="true">
		<cfargument name="frmEndFrom" type="date" required="true">
		<cfargument name="frmEndTo" type="date" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct=structNew()>
 		<cfset local.startFrom = dateformat(arguments.frmStartFrom,'mm/dd/yyyy')>
		<cfset local.startTo = dateformat(arguments.frmStartTo,'mm/dd/yyyy') & " 23:59:59.997">
		<cfset local.endFrom = dateformat(arguments.frmEndFrom,'mm/dd/yyyy')>
		<cfset local.endTo = dateformat(arguments.frmEndTo,'mm/dd/yyyy') & " 23:59:59.997">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.returnStruct.qryData" result="local.returnStruct.qryDataResult">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				<cfif len(arguments.strSQLPrep.ruleSQL)>#PreserveSingleQuotes(arguments.strSQLPrep.ruleSQL)#</cfif>

				declare @orgID int, @siteID int, @orgSize int, @subStatusList varchar(300), @startFrom datetime, @startTo datetime, @endFrom datetime, @endTo datetime;
				set @siteID = #arguments.siteID#;
				set @orgSize = #arguments.orgSize#;
				set @subStatusList = '#arguments.subStatusIDList#';
				set @startFrom = <cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.startFrom#">;
				set @startTo = <cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.startTo#">;
				set @endFrom = <cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.endFrom#">;
				set @endTo = <cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.endTo#">;
				select @orgID = orgID from dbo.sites where siteID = @siteID;

				-- split subscription status IDs
				DECLARE @tblSubStatusID table (subStatusID int);
				INSERT INTO @tblSubStatusID (subStatusID)
				select tmp.listitem
				from dbo.fn_intListToTable(@subStatusList,',') as tmp;

				declare @qryAllFirmMembers table (masterMemberID int, masterName varchar(1000), childMemberID int, childName varchar(1000), 
					firstName varchar(250), lastName varchar(250), relationshipTypeName varchar(1000));

				declare @qrySubs table (
					subscriberID int,
					memberID int,
					memberName varchar(256),
					subscriptionID int,
					typeID int,
					typeName varchar(100),
					subscriptionName varchar(300),
					status varchar(1),
					statusName varchar(50),
					subStartDate datetime,
					subEndDate datetime,
					graceEndDate datetime,
					parentSubscriberID int,
					rfid int,
					thePath varchar(max),
					thePathExpanded varchar(max),
					masterMemberID int,
					masterName varchar(1000),
					childMemberID int,
					childName varchar(1000),
					relationshipList varchar(1000),
					rowNum int
				);

				insert into @qryAllFirmMembers
				select distinct masterMemberID, masterName, childMemberID, childName, m2.firstname, m2.lastname, relationshipList 
				from (
					select masterMemberID, masterName, childMemberID, childName, STRING_AGG(relationshipTypeName,', ') as relationshipList, 
						count(*) over (partition by masterMemberID) as masterCount
					from (
						select distinct m.memberID as masterMemberID, mChild.memberID as childMemberID,
							  case when rtMaster.isOrganization = 1 then m.company else m.lastName + ', ' + m.firstName end as masterName,
							  case when rtChild.isOrganization = 1 then mChild.company else mChild.lastName + ', ' + mChild.firstName end as childName,
							  rrt.relationshipTypeName
						from dbo.ams_recordTypesRelationshipTypes rtrt
						inner join dbo.ams_recordRelationships rr on rr.orgID = @orgID and rr.recordTypeRelationshipTypeID = rtrt.recordTypeRelationshipTypeID and rr.isActive = 1
						inner join dbo.ams_recordRelationshipTypes rrt on rrt.relationshipTypeID = rtrt.relationshipTypeID and rrt.orgID = @orgID
						inner join dbo.ams_recordTypes rtMaster on rtMaster.recordTypeID = rtrt.masterRecordTypeID  and rtMaster.orgID = @orgID
						inner join dbo.ams_recordTypes rtChild on rtChild.recordTypeID = rtrt.childRecordTypeID and rtChild.orgID = @orgID
						inner join dbo.ams_members mMaster on mMaster.memberID = rr.masterMemberID and mMaster.orgID = @orgID
						inner join dbo.ams_members m on m.memberID = mMaster.memberID and m.orgID = @orgID 
							and m.isProtected = 0
							and m.status <> 'D'
						<cfset local.joinNoS = ReplaceNoCase(arguments.strSQLPrep.JOINSQLNOMEMBERDATA,"inner join @tblS as tblS on tblS.subscriberID = s.subscriberID","")>
						<cfif len(local.joinNoS)>#PreserveSingleQuotes(local.joinNoS)#</cfif>
						inner join dbo.ams_members mChild on mChild.memberID = rr.childMemberID 
							and mChild.orgID = @orgID 
							and mChild.memberID = mChild.activeMemberID 
							and mChild.isProtected = 0
							and mChild.status <> 'D'
						<cfif val(arguments.frmGroupID)>
							inner join dbo.cache_members_groups as cmg on cmg.memberID = mChild.memberID 
								and cmg.groupID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(arguments.frmGroupID)#">
						</cfif>
						where rtrt.isActive = 1 
						<cfif listlen(arguments.frmRT) and NOT listlen(arguments.frmRTRT)>
							and rtChild.recordTypeID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="yes" value="#arguments.frmRT#">)
						<cfelseif listlen(arguments.frmRT) and listlen(arguments.frmRTRT)>
							and (
								<cfloop list="#arguments.frmRT#" item="local.thisRecType" index="local.recTypeIndex">
									<cfset local.thisRecTypeRolesList = ListGetAt(arguments.frmRTRT, local.recTypeIndex, "|")>
									<cfif local.recTypeIndex gt 1>OR</cfif>
									(
										rtChild.recordTypeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisRecType#">
										<cfif local.thisRecTypeRolesList neq 'ALL'>
											AND rtrt.recordTypeRelationshipTypeID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="yes" value="#local.thisRecTypeRolesList#">)
										</cfif>
									)
								</cfloop>
							)
						</cfif>
						) x
					group by masterName, childName, masterMemberID, childMemberID
					) as outertbl
				inner join dbo.ams_members m2 on m2.memberid = outertbl.childMemberID
				where masterCount >= @orgSize
				order by masterName, childName;

				insert into @qrySubs
				select subscriberID, memberID, memberName, subscriptionID, typeID, typeName, subscriptionName, [status], statusName,
					subStartDate, subEndDate, graceEndDate, parentSubscriberID, rfid, 
					CONVERT(varchar, memberID) + '.' + CAST(RIGHT('100000'+theRow,4) as varchar(max)) AS thePath,
					CAST(subscriptionName as varchar(max)) as thePathExpanded,
					masterMemberID, masterName, childMemberID, childName, relationshipTypeName as relationshipList,
					ROW_NUMBER() OVER (ORDER BY fm.masterName, fm.childName, CONVERT(varchar, memberID) + '.' + CAST(RIGHT('100000'+theRow,4) as varchar(max))) as rowNum
				FROM (
					select distinct s.subscriberID, activeMember.memberID, RTRIM(activeMember.lastname + ' ' + isnull(activeMember.suffix, '')) + ', ' + activeMember.firstname + isnull(' (' + activeMember.membernumber + ')','') AS memberName,
						s.subscriptionID, t.typeID, t.typeName, sub.subscriptionName, st.statusCode as status, st.statusName, s.subStartDate, s.subEndDate, s.graceEndDate, s.rfid, s.parentSubscriberID,
						ROW_NUMBER() OVER (ORDER BY activeMember.lastname, activeMember.firstname, activeMember.memberID, s.parentSubscriberID, s.subStartDate, s.subscriberID) AS theRow
					from dbo.sub_subscribers s
					inner join dbo.sub_subscriptions sub on sub.subscriptionID = s.subscriptionID
						and s.parentSubscriberID is null
						and s.subscriptionID = s.subscriptionID
					<cfset local.joinNoM = ReplaceNoCase(arguments.strSQLPrep.JOINSQLNOMEMBERDATA,"inner join ##tmpVGRMembers as tblM on tblM.memberID = m.memberID","")>
					<cfif len(local.joinNoM)>#PreserveSingleQuotes(local.joinNoM)#</cfif>
					inner join dbo.sub_types as t on sub.typeiD = t.typeID and t.siteID = @siteID
					inner join dbo.ams_members as m on s.memberID = m.memberID and m.orgID = @orgID
					inner join dbo.ams_members as activeMember on activeMember.memberID = m.activeMemberID and activeMember.orgID = @orgID
					inner join @qryAllFirmMembers fm on fm.childMemberID = activeMember.memberID
					inner join dbo.sub_statuses st on st.statusID = s.statusID 
						and st.statusID in (select subStatusID from @tblSubStatusID)
					inner join dbo.sub_rateFrequencies rf on rf.rfid = s.rfid
					inner join dbo.sub_rates r on r.rateID = rf.rateID 
						and r.rateID = isnull(null, r.rateID)
					where s.subStartDate between @startFrom and @startTo
					and s.subEndDate between @endFrom and @endTo
				) as x
				inner join @qryAllFirmMembers fm on fm.childMemberID = x.memberID;
				
				-- delete firms that had zero subscribers
				delete fm 
				from @qryAllFirmMembers fm
				left outer join @qrySubs qs on fm.childMemberID = qs.memberID
				where qs.memberID is null;

				-- result: subscriptions for the firms selected
				select * from @qrySubs 
				order by rowNum;

				<cfif len(arguments.strSQLPrep.ruleSQL)>
					IF OBJECT_ID('tempdb..##tmpVGRMembers') IS NOT NULL
						DROP TABLE ##tmpVGRMembers;
				</cfif>

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;		
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="qryFullReport" access="private" output="false" returntype="struct">
		<cfargument name="strSQLPrep" type="struct" required="true">
		<cfargument name="qryReportInfo" type="query" required="true">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="siteCode" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct=structNew()>
		<cfset local.tempTableName = "rpt#getTickCount()#">		
		<cfset local.otherXML = XMLParse(arguments.qryReportInfo.otherXML)>
		<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(arguments.siteCode)>

		<!--- extra option fields --->
		<cfset local.frmRT = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmrt/text())")>
		<cfset local.frmRTRT = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmrtrt/text())")>
		<cfset local.frmGroupID = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmgroupid/text())")>
		<cfset local.orgSize = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmorgsize/text())")>
		<cfset local.frmSubStatusID = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmsubstatusid/text())")>
		<cfif not len(local.frmSubStatusID)>
			<cfset local.qrySubStatuses = getSubscriptionStatuses()>
			<cfset local.frmSubStatusID = valueList(local.qrySubStatuses.statusID)>
		</cfif>

		<cfset local.startFrom = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmstartfrom/text())")>
		<cfset local.startTo = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmstartto/text())")>
		<cfset local.endFrom = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmendfrom/text())")>
		<cfset local.endTo = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmendto/text())")>
		<cfif not len(local.startFrom)>
			<cfset local.startFrom = '1/1/1960'>
		</cfif>
		<cfif not len(local.startTo)>
			<cfset local.startTo = '12/31/2499'>
		</cfif>
		<cfif not len(local.endFrom)>
			<cfset local.endFrom = '1/1/1960'>
		</cfif>
		<cfif not len(local.endTo)>
			<cfset local.endTo = '12/31/2499'>
		</cfif>
 		<cfset local.startFrom = dateformat(local.startFrom,'mm/dd/yyyy')>
		<cfset local.startTo = dateformat(local.startTo,'mm/dd/yyyy') & " 23:59:59.997">
		<cfset local.endFrom = dateformat(local.endFrom,'mm/dd/yyyy')>
		<cfset local.endTo = dateformat(local.endTo,'mm/dd/yyyy') & " 23:59:59.997">

		<cfset local.frmInvoiceProfileID = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frminvoiceprofileid/text())")>
		<cfset local.frmCoverSheetTitle = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmcoversheettitle/text())")>
		<cfset local.frmBalanceTitle = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmbalancetitle/text())")>
		<cfset local.frmBalanceDescription = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmbalancedescription/text())")>
		<cfset local.frmPaidFullTitle = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmpaidfulltitle/text())")>
		<cfset local.frmPaidFullDescription = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmpaidfulldescription/text())")>
		<cfset local.renewalStatementTitle = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/renewalstatementtitle/text())")>
		<cfset local.frmSubscriberTopContent = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmsubscribertopcontent/text())")>
		<cfset local.frmRenewOnlineOptions = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmrenewonlineoptions/text())")>
		<cfset local.frmDisplayZeroDollarAddOns = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmdisplayzerodollaraddons/text())")>
		<cfset local.frmSubscriberBottomContent = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmsubscriberbottomcontent/text())")>
		<cfset local.frmIncludeSection = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmincludesection/text())")>
		<cfset local.frmSectionTitle = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmsectiontitle/text())")>
		<cfset local.frmSectionDescription = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmsectiondescription/text())")>

		<!--- --------------- --->
		<!--- PREP FIELDSETS  --->
		<!--- --------------- --->
		<cfset local.arrFS = arrayNew(1)>
		<cfloop array="#xmlsearch(local.otherXML,"/report/fieldsets/fieldset")#" index="local.thisFS">
			<cfset arrayAppend(local.arrFS,local.thisFS.xmlAttributes.uid)>
		</cfloop>

		<cfif application.MCEnvironment eq "production">
			<cfset local.thisHostname = local.mc_siteInfo.scheme & "://" & local.mc_siteInfo.mainhostname>
		<cfelse>
			<cfset local.thisHostname = local.mc_siteInfo.scheme & "://" & application.objPlatform.getCurrentHostname()>
		</cfif>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.returnStruct.qryData" result="local.returnStruct.qryDataResult">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				<cfif len(arguments.strSQLPrep.ruleSQL)>#PreserveSingleQuotes(arguments.strSQLPrep.ruleSQL)#</cfif>

				declare @orgID int, @siteID int, @orgSize int, @subStatusList varchar(300), 
						@startFrom datetime, @startTo datetime, @endFrom datetime, @endTo datetime, @recordedByMemberID int;

				set @siteID = #local.mc_siteInfo.siteid#;
				set @orgID = #arguments.orgID#;
				set @orgSize = <cfqueryparam cfsqltype="cf_sql_integer" value="#val(local.orgSize)#">;
				set @subStatusList = <cfqueryparam cfsqltype="cf_sql_varchar" value="#local.frmSubStatusID#">;
				set @startFrom = <cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.startFrom#">;
				set @startTo = <cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.startTo#">;
				set @endFrom = <cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.endFrom#">;
				set @endTo = <cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.endTo#">;
				set @recordedByMemberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#session.cfcuser.memberdata.memberID#">;

				-- split subscription status IDs
				DECLARE @tblSubStatusID table (subStatusID int);
				INSERT INTO @tblSubStatusID (subStatusID)
				select tmp.listitem
				from dbo.fn_intListToTable(@subStatusList,',') as tmp;

				-- put extra option fields into temp table
				declare @settings TABLE (name varchar(100), value varchar(max));
				insert into @settings (name, value) values ('frminvoiceprofileid', <cfqueryparam cfsqltype="cf_sql_longvarchar" value="#local.frminvoiceprofileid#">);
				insert into @settings (name, value) values ('frmcoversheettitle', <cfqueryparam cfsqltype="cf_sql_longvarchar" value="#local.frmcoversheettitle#">);
				insert into @settings (name, value) values ('frmbalancetitle', <cfqueryparam cfsqltype="cf_sql_longvarchar" value="#local.frmbalancetitle#">);
				insert into @settings (name, value) values ('frmbalancedescription', <cfqueryparam cfsqltype="cf_sql_longvarchar" value="#local.frmbalancedescription#">);
				insert into @settings (name, value) values ('frmpaidfulltitle', <cfqueryparam cfsqltype="cf_sql_longvarchar" value="#local.frmpaidfulltitle#">);
				insert into @settings (name, value) values ('frmpaidfulldescription', <cfqueryparam cfsqltype="cf_sql_longvarchar" value="#local.frmpaidfulldescription#">);
				insert into @settings (name, value) values ('renewalstatementtitle', <cfqueryparam cfsqltype="cf_sql_longvarchar" value="#local.renewalstatementtitle#">);
				insert into @settings (name, value) values ('frmsubscribertopcontent', <cfqueryparam cfsqltype="cf_sql_longvarchar" value="#local.frmsubscribertopcontent#">);
				insert into @settings (name, value) values ('frmrenewonlineoptions', <cfqueryparam cfsqltype="cf_sql_longvarchar" value="#local.frmrenewonlineoptions#">);
				insert into @settings (name, value) values ('frmdisplayzerodollaraddons', <cfqueryparam cfsqltype="cf_sql_longvarchar" value="#local.frmdisplayzerodollaraddons#">);
				insert into @settings (name, value) values ('frmsubscriberbottomcontent', <cfqueryparam cfsqltype="cf_sql_longvarchar" value="#local.frmsubscriberbottomcontent#">);
				insert into @settings (name, value) values ('frmincludesection', <cfqueryparam cfsqltype="cf_sql_longvarchar" value="#local.frmincludesection#">);
				insert into @settings (name, value) values ('frmsectiontitle', <cfqueryparam cfsqltype="cf_sql_longvarchar" value="#local.frmsectiontitle#">);
				insert into @settings (name, value) values ('frmsectiondescription', <cfqueryparam cfsqltype="cf_sql_longvarchar" value="#local.frmsectiondescription#">);
				insert into @settings (name, value) values ('invoiceheaderimgurl', '#replacenocase(application.paths.internalPlatform.url,'*SITECODE*','mc')#userassets/common/invoices/');
				insert into @settings (name, value) values ('renewalsuburl', '#local.thisHostname#/renewsub');
				insert into @settings (name, value) values ('showtax', 0);

				declare @qryAllFirmMembers table (masterMemberID int, childMemberID int);
				declare @tblFirmSubscribers TABLE (masterMemberID int, childMemberID int, rootSubscriberID int);

				insert into @qryAllFirmMembers (masterMemberID, childMemberID)
				select distinct masterMemberID, childMemberID
				from (
					select masterMemberID, childMemberID, count(*) over (partition by masterMemberID) as masterCount
					from (
						select distinct rr.masterMemberID as masterMemberID, mChild.memberID as childMemberID
						from dbo.ams_recordTypesRelationshipTypes rtrt
						inner join dbo.ams_recordRelationships rr on rr.orgID = @orgID and rr.recordTypeRelationshipTypeID = rtrt.recordTypeRelationshipTypeID and rr.isActive = 1
						inner join dbo.ams_recordRelationshipTypes rrt on rrt.relationshipTypeID = rtrt.relationshipTypeID and rrt.orgID = @orgID
						inner join dbo.ams_recordTypes rtMaster on rtMaster.recordTypeID = rtrt.masterRecordTypeID  and rtMaster.orgID = @orgID
						inner join dbo.ams_recordTypes rtChild on rtChild.recordTypeID = rtrt.childRecordTypeID and rtChild.orgID = @orgID
						inner join dbo.ams_members mMaster on mMaster.memberID = rr.masterMemberID and mMaster.orgID = @orgID
						inner join dbo.ams_members m on m.memberID = mMaster.memberID and m.isProtected = 0 and m.orgID = @orgID
						<cfset local.joinNoS = ReplaceNoCase(arguments.strSQLPrep.JOINSQLNOMEMBERDATA,"inner join @tblS as tblS on tblS.subscriberID = s.subscriberID","")>
						<cfif len(local.joinNoS)>#PreserveSingleQuotes(local.joinNoS)#</cfif>
						inner join dbo.ams_members mChild on mChild.memberID = rr.childMemberID 
							and mChild.orgID = @orgID 
							and mChild.memberID = mChild.activeMemberID 
							and mChild.isProtected = 0
							and mChild.status <> 'D'
						<cfif val(local.frmGroupID)>
							inner join dbo.cache_members_groups as cmg on cmg.memberID = mChild.memberID 
								and cmg.groupID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(local.frmGroupID)#">
						</cfif>
						where rtrt.isActive = 1 
						<cfif listlen(local.frmRT) and NOT listlen(local.frmRTRT)>
							and rtChild.recordTypeID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="yes" value="#local.frmRT#">)
						<cfelseif listlen(local.frmRT) and listlen(local.frmRTRT)>
							and (
								<cfloop list="#local.frmRT#" item="local.thisRecType" index="local.recTypeIndex">
									<cfset local.thisRecTypeRolesList = ListGetAt(local.frmRTRT, local.recTypeIndex, "|")>
									<cfif local.recTypeIndex gt 1>OR</cfif>
									(
										rtChild.recordTypeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisRecType#">
										<cfif local.thisRecTypeRolesList neq 'ALL'>
											AND rtrt.recordTypeRelationshipTypeID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="yes" value="#local.thisRecTypeRolesList#">)
										</cfif>
									)
								</cfloop>
							)
						</cfif>
						) x
					group by masterMemberID, childMemberID
					) as outertbl
				inner join dbo.ams_members m2 on m2.memberid = outertbl.childMemberID
				where masterCount >= @orgSize;

				insert into @tblFirmSubscribers (masterMemberID, childMemberID,rootSubscriberID)
				select fm.masterMemberID, activeMember.memberID, s.subscriberID
				from dbo.sub_subscribers as s 
				<cfset local.joinNoM = ReplaceNoCase(arguments.strSQLPrep.JOINSQLNOMEMBERDATA,"inner join ##tmpVGRMembers as tblM on tblM.memberID = m.memberID","")>
				<cfif len(local.joinNoM)>#PreserveSingleQuotes(local.joinNoM)#</cfif>
				inner join @tblSubStatusID as tblSt on tblSt.subStatusID = s.statusID
				inner join dbo.ams_members as m on s.memberID = m.memberID and m.orgID = @orgID
				inner join dbo.ams_members as activeMember on activeMember.memberID = m.activeMemberID
				inner join @qryAllFirmMembers as fm on fm.childMemberID = activeMember.memberID
				where s.parentSubscriberID is null
				and s.subStartDate between @startFrom and @startTo
				and s.subEndDate between @endFrom and @endTo;

				-- delete firms that had zero subscribers
				delete fm 
				from @qryAllFirmMembers fm
				left outer join @tblFirmSubscribers qs on fm.masterMemberID = qs.masterMemberID
				where qs.masterMemberID is null;

				-- create UID for each firm
				declare @tblFirms TABLE (masterMemberID int PRIMARY KEY, uid uniqueIdentifier NOT NULL default newid());
				insert into @tblFirms (masterMemberID)
				select distinct masterMemberID
				from @qryAllFirmMembers;

				declare @queueTypeID int, @insertingQueueStatusID int,@readyQueueStatusID int, @jobUID uniqueIdentifier;
				select @queueTypeID = queueTypeID from platformQueue.dbo.tblQueueTypes where queueType='FirmSubStatements';
				select @jobUID = newid();

				select @insertingQueueStatusID = queueStatusID
					from platformQueue.dbo.tblQueueStatuses
					where queueTypeID=@queueTypeID and queueStatus = 'insertingItems';
				select @readyQueueStatusID = queueStatusID
					from platformQueue.dbo.tblQueueStatuses
					where queueTypeID=@queueTypeID and queueStatus = 'readyToProcess';

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;		

				insert into platformQueue.dbo.tblQueueItems (itemUID, queueStatusID, dateAdded, dateUpdated)
				select tf.uid as itemUID, @insertingQueueStatusID, getdate(), getdate()
				from @tblFirms tf;

				insert into platformQueue.dbo.tblQueueItemData (itemGroupUID,recordedByMemberID,siteID,itemUID, columnID, dataKey, columnValueInteger)
				select @jobUID, @recordedByMemberID,@siteID,tf.uid as itemUID, dc.columnID, tf.masterMemberID as dataKey, fs.rootSubscriberID as columnValueInteger
				from @tblFirmSubscribers fs
				inner join @tblFirms tf on fs.masterMemberID = tf.masterMemberID
				inner join platformQueue.dbo.tblQueueTypeDataColumns dc on dc.queueTypeID = @queueTypeID
					and dc.columnname = 'FirmChildSub';

				insert into platformQueue.dbo.tblQueueItemData (itemGroupUID,recordedByMemberID,siteID,itemUID, columnID, dataKey, columnValueInteger)
				select @jobUID, @recordedByMemberID,@siteID,tf.uid as itemUID, dc.columnID, tf.masterMemberID as dataKey, temp.childMemberID as columnValueInteger
				from (
					select masterMemberID, childMemberID
					from @qryAllFirmMembers
						except
					select masterMemberID, childMemberID
					from @tblFirmSubscribers
				) temp
				inner join @tblFirms as tf on temp.masterMemberID = tf.masterMemberID
				inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID
					and dc.columnname = 'FirmChildNoSub';

				insert into platformQueue.dbo.tblQueueItemData (itemGroupUID,recordedByMemberID,siteID,itemUID, columnID, dataKey, columnValueString)
				select @jobUID, @recordedByMemberID, @siteID, tf.uid as itemUID, dc.columnID, s.name as dataKey, s.value as columnValueString
				from platformQueue.dbo.tblQueueTypes as qt
				inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = qt.queueTypeID
					and qt.queueType = 'FirmSubStatements'
					and dc.columnname = 'configParam'
				inner join @settings as s on s.name in ('frmcoversheettitle','frminvoiceprofileid','frmbalancetitle','frmpaidfulltitle','frmincludesection','frmsectiontitle','renewalstatementtitle','frmrenewonlineoptions','frmdisplayzerodollaraddons','invoiceheaderimgurl','renewalsuburl','showtax')
				cross join @tblFirms as tf;

				insert into platformQueue.dbo.tblQueueItemData (itemGroupUID,recordedByMemberID,siteID,itemUID, columnID, dataKey, columnValueText)
				select @jobUID, @recordedByMemberID,@siteID, tf.uid as itemUID, dc.columnID, s.name as dataKey, s.value as columnValueText
				from platformQueue.dbo.tblQueueTypes as qt
				inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = qt.queueTypeID
					and qt.queueType = 'FirmSubStatements'
					and dc.columnname = 'configText'
				inner join @settings as s on s.name in ('frmbalancedescription','frmpaidfulldescription', 'frmsectiondescription','frmsubscribertopcontent','frmsubscriberbottomcontent')
				cross join @tblFirms as tf;

				<cfloop array="#local.arrFS#" index="local.fsUID">
					insert into platformQueue.dbo.tblQueueItemData (itemGroupUID,recordedByMemberID,siteID,itemUID, columnID, dataKey, columnValueString)
					select @jobUID, @recordedByMemberID, @siteID, tf.uid as itemUID, dc.columnID, 'fieldset' as dataKey, '#local.fsUID#' as columnValueString
					from platformQueue.dbo.tblQueueTypes as qt
					inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = qt.queueTypeID
						and qt.queueType = 'FirmSubStatements'
						and dc.columnname = 'fieldset'
					cross join @tblFirms as tf;
				</cfloop>

				-- resume task
				EXEC dbo.sched_resumeTask @name='Firm Billing Report Queue', @engine='BERLinux';

				update qi 
				set queueStatusID = @readyQueueStatusID,
					dateUpdated = getdate()
				from platformQueue.dbo.tblQueueItems as qi
				inner join platformQueue.dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
					and qid.itemGroupUID = @jobUID 
					and queueStatusID = @insertingQueueStatusID;

				select count(*) as firmCount
				from @tblFirms;

				<cfif len(arguments.strSQLPrep.ruleSQL)>
					IF OBJECT_ID('tempdb..##tmpVGRMembers') IS NOT NULL
						DROP TABLE ##tmpVGRMembers;
				</cfif>

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
 
		<cfreturn local.returnStruct>
	</cffunction>	

	<cffunction name="screenReport" access="private" output="false" returntype="struct">
		<cfargument name="reportAction" type="string" required="true">
		<cfargument name="qryReportInfo" type="query" required="true">
		<cfargument name="siteCode" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = { data="", success=true, errMsg="", isReportEmpty=false }>
		<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(arguments.siteCode)>
		
		<cftry>
			<cfset local.memberLink = buildLinkToTool(toolType='MemberAdmin',mca_ta='edit')>
			<cfset local.strSQLPrep = prepSQL(orgID=local.mc_siteInfo.orgID, siteID=local.mc_siteInfo.siteID,
				reportRuleID=arguments.qryReportInfo.ruleID, reportOtherXML=arguments.qryReportInfo.otherXML,
				existingFields="m_lastname,m_firstname,m_membernumber,m_company", existingAliases="m")>
			<cfif local.strSQLPrep.ruleErr>
				<cfthrow message="There was an error in the report criteria.">
			</cfif>
			
			<cfset local.frmRT = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmrt/text())")>
			<cfset local.frmRTRT = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmrtrt/text())")>
			<cfset local.frmGroupID = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmgroupid/text())")>
			<cfset local.frmStartFrom = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmstartfrom/text())")>
			<cfset local.frmStartTo = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmstartto/text())")>
			<cfset local.frmEndFrom = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmendfrom/text())")>
			<cfset local.frmEndTo = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmendto/text())")>
			<cfset local.frmOrgSize = val(XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmorgsize/text())"))>
			<cfset local.frmSubStatusID = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmsubstatusid/text())")>
			<cfif not len(local.frmStartFrom)>
				<cfset local.frmStartFrom = '1/1/1960'>
			</cfif>
			<cfif not len(local.frmStartTo)>
				<cfset local.frmStartTo = '12/31/2499'>
			</cfif>
			<cfif not len(local.frmEndFrom)>
				<cfset local.frmEndFrom = '1/1/1960'>
			</cfif>
			<cfif not len(local.frmEndTo)>
				<cfset local.frmEndTo = '12/31/2499'>
			</cfif>
			<cfif not len(local.frmSubStatusID)>
				<cfset local.frmSubStatusID = valueList(local.qrySubStatuses.statusID)>
			</cfif>

			<cfset local.qrySubStatuses = getSubscriptionStatuses()>
			<cfset local.qryReport = qrySummaryReport(strSQLPrep=local.strSQLPrep, siteID=local.mc_siteInfo.siteID, frmRT=local.frmRT, frmRTRT=local.frmRTRT, frmGroupID=local.frmGroupID, 
				orgSize=local.frmOrgSize, subStatusIDList=local.frmSubStatusID, frmStartFrom=local.frmStartFrom, frmStartTo=local.frmStartTo, frmEndFrom=local.frmEndFrom, frmEndTo=local.frmEndTo)>

			<cfsavecontent variable="local.strReturn.data">
				<cfoutput>
				<div id="screenreport">
					#showReportHeader(siteID=local.mc_siteInfo.siteID, reportAction=arguments.reportAction, reportName=arguments.qryReportInfo.reportName)#
				</cfoutput>
	
				<cfif local.qryReport.qryData.recordcount is 0>
					<cfset local.strReturn.isReportEmpty = true>
					<cfoutput><div>No results to report.</div></cfoutput>
				<cfelse>
					<cfoutput>
					<table class="table table-sm table-borderless">
					<thead>
						<tr>
							<th class="text-left">Subscription Name</th>
							<th class="text-left">Type</th>
							<th class="text-left">Status</th>
							<th class="text-left">Start Date</th>
							<th class="text-left">End Date</th>
						</tr>
					</thead>
					<tbody>
					</cfoutput>						
					<cfoutput query="local.qryReport.qryData" group="masterMemberID">
						<tr>
							<td colspan="5">
								<cfif arguments.reportAction eq "screen">
									<a href="#local.memberLink#&memberid=#local.qryReport.qryData.masterMemberID#" target="_blank"><b>#local.qryReport.qryData.masterName#</b></a><br/>
								<cfelse>
									<b>#local.qryReport.qryData.masterName#</b>
								</cfif>
							</td>
						</tr>
						<cfoutput group="childMemberID">
							<tr>
								<td colspan="5">
									<div class="ml-3">
										<cfif arguments.reportAction eq "screen">
											<a href="#local.memberLink#&memberid=#local.qryReport.qryData.memberID#" target="_blank">#local.qryReport.qryData.memberName#</a><br/>
										<cfelse>
											#local.qryReport.qryData.memberName#
										</cfif>
									</div>
								</td>
							</tr>
							<cfoutput>
								<tr>
									<td class="align-text-bottom"><div class="ml-5">#local.qryReport.qryData.subscriptionName#</div></td>
									<td class="align-text-bottom">#local.qryReport.qryData.typeName#</td>
									<td class="align-text-bottom">#local.qryReport.qryData.statusName#</td>
									<td class="align-text-bottom">#DateFormat(local.qryReport.qryData.subStartDate, "m/d/yy")#</td>
									<td class="align-text-bottom">#DateFormat(local.qryReport.qryData.subEndDate, "m/d/yy")#</td>
								</tr>
							</cfoutput>
						</cfoutput>
					</cfoutput>
					<cfoutput>
					</tbody>
					</table>
					</cfoutput>
				</cfif>

				<cfoutput>
				#showReportFooter(reportAction=arguments.reportAction, defaultTimeZoneID=local.mc_siteInfo.defaultTimeZoneID)#
				#showRawSQL(reportAction=arguments.reportAction, qryName="local.qryReport.qryData", strQryResult=local.qryReport.qryDataResult)#
				</div>
				</cfoutput>
			</cfsavecontent>
		
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.strReturn.success = false>
			<cfset local.strReturn.data = "">
		</cfcatch>
		</cftry>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="pdfReport" access="private" output="false" returntype="struct">
		<cfargument name="reportAction" type="string" required="true">
		<cfargument name="qryReportInfo" type="query" required="true">
		<cfargument name="siteCode" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = { data="", success=true, errMsg="", isReportEmpty=false }>

		<cfset local.frmView = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmview/text())")>

		<cftry>
			<cfif local.frmView eq "1">
				<cfreturn SUPER.pdfReport(reportAction=arguments.reportAction, qryReportInfo=arguments.qryReportInfo, siteCode=arguments.siteCode)>
			<cfelse>
				<cfreturn pdfFullReport(reportAction=arguments.reportAction, qryReportInfo=arguments.qryReportInfo, siteCode=arguments.siteCode)>
			</cfif>
		<cfcatch type="any">
			<cfset local.strReturn.success = false>
			<cfset local.strReturn.data = "">
		</cfcatch>
		</cftry>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="pdfFullReport" access="private" output="false" returntype="struct">
		<cfargument name="reportAction" type="string" required="true">
		<cfargument name="qryReportInfo" type="query" required="true">
		<cfargument name="siteCode" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = { data="", success=true, errMsg="", format="json", isReportEmpty=false }>
		<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(arguments.siteCode)>

		<cftry>
			<cfset local.strSQLPrep = prepSQL(orgID=local.mc_siteInfo.orgID, siteID=local.mc_siteInfo.siteID,
				reportRuleID=arguments.qryReportInfo.ruleID, reportOtherXML=arguments.qryReportInfo.otherXML,
				existingFields="m_lastname,m_firstname,m_membernumber,m_company", existingAliases="m")>
			<cfif local.strSQLPrep.ruleErr>
				<cfthrow message="There was an error in the report criteria.">
			</cfif>

			<!--- Use the queueing mechanism --->
			<cfset local.qryReport = qryFullReport(strSQLPrep=local.strSQLPrep, qryReportInfo=arguments.qryReportInfo, orgID=local.mc_siteInfo.orgID, siteCode=arguments.siteCode)>

			<cfsavecontent variable="local.strReturn.data">
				<cfoutput>
				<div id="screenreport">
					<cfif local.qryReport.qryData.firmCount>
						<h4>Firm Billing Has Been Scheduled</h4>
						<div>
							The firm billing report containing #local.qryReport.qryData.firmCount# firm(s) has been scheduled and will begin shortly.<br/>
							You will be sent an e-mail with the results with the report.<br/><br/>
							Please wait until you receive the emailed report before contacting MemberCentral with any questions.
						</div>
					<cfelse>
						<cfset local.strReturn.isReportEmpty = true>
						<h4>No Matching Firms Found</h4>
						<div>
							There are no matches for the combination of subscription, firm filters, and other report options that you selected.<br/>
							You can use Summary Report View to see spot check the firm list after any changes.
						</div>
					</cfif>
				</div>
				</cfoutput>
			</cfsavecontent>
		
		<cfcatch type="any">
			<cfset local.strReturn.success = false>
			<cfset local.strReturn.data = "">
		</cfcatch>
		</cftry>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="saveReportExtra" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		
		local.strFields = structNew();		
		local.strFields.frmgroupid = { label="Members of Group", value=arguments.event.getValue('frmGroupID','') };
		local.strFields.frmrt = { label="Record Types", value=arguments.event.getValue('frmRT','') };
		local.strFields.frmstartfrom = { label="Subscription Start Date Start", value=arguments.event.getValue('frmStartFrom','') };
		local.strFields.frmstartto = { label="Subscription Start Date End", value=arguments.event.getValue('frmStartTo','') };
		local.strFields.frmendfrom = { label="Subscription End Date Start", value=arguments.event.getValue('frmEndFrom','') };
		local.strFields.frmendto = { label="Subscription End Date End", value=arguments.event.getValue('frmEndTo','') };
		local.strFields.frmorgsize = { label="Minimum Org Size", value=arguments.event.getValue('frmOrgSize','') };
		local.strFields.frmsubstatusid = { label="Subscription Statuses", value=arguments.event.getValue('frmSubStatusID',0) };
		local.strFields.frmview = { label="Report View", value=arguments.event.getValue('frmView','') };
		local.strFields.frminvoiceprofileid = { label="Coversheet Header", value=arguments.event.getValue('frmInvoiceProfileID','') };
		local.strFields.frmcoversheettitle = { label="Coversheet Title", value=arguments.event.getValue('frmCoverSheetTitle','') };
		local.strFields.frmbalancetitle = { label="Individuals with balance Title", value=arguments.event.getValue('frmBalanceTitle','') };
		local.strFields.frmbalancedescription = { label="Individuals with balance Description", value=arguments.event.getValue('frmBalanceDescription','') };
		local.strFields.frmpaidfulltitle = { label="Individuals Paid in Full Title", value=arguments.event.getValue('frmPaidFullTitle','') };
		local.strFields.frmpaidfulldescription = { label="Individuals Paid in Full Description", value=arguments.event.getValue('frmPaidFullDescription','') };
		local.strFields.renewalstatementtitle = { label="Subscriber Detail Sheet - Title", value=arguments.event.getValue('renewalStatementTitle','') };
		local.strFields.frmsubscribertopcontent = { label="Subscriber Detail Sheet - Top Content", value=arguments.event.getValue('frmSubscriberTopContent','') };
		local.strFields.frmrenewonlineoptions = { label="Subscriber Detail Sheet - Renew Online Options", value=arguments.event.getValue('frmRenewOnlineOptions',0) };
		local.strFields.frmdisplayzerodollaraddons = { label="Subscriber Detail Sheet - Display $0.00 Add-Ons", value=arguments.event.getValue('frmDisplayZeroDollarAddOns',0) };
		local.strFields.frmsubscriberbottomcontent = { label="Subscriber Detail Sheet - Bottom Content", value=arguments.event.getValue('frmSubscriberBottomContent','') };
		local.strFields.frmincludesection = { label="Individuals without Specified Subscription(s) - Include Section", value=arguments.event.getValue('frmIncludeSection',0) };
		local.strFields.frmsectiontitle = { label="Individuals without Specified Subscription(s) - Section Title", value=arguments.event.getValue('frmSectionTitle','') };
		local.strFields.frmsectiondescription = { label="Individuals without Specified Subscription(s) - Section Description", value=arguments.event.getValue('frmSectionDescription','') };
		</cfscript>
		
		<cfset local.recordTypeRoles = structNew()>
		<cfloop collection="#arguments.event.getCollection()#" item="local.thisEl">
			<cfif left(local.thisEl,8) eq "frmRTRT_">
				<cfif not structKeyExists(local.recordTypeRoles,GetToken(local.thisEl,2,'_'))>
					<cfset structInsert(local.recordTypeRoles, GetToken(local.thisEl,2,'_'), arguments.event.getValue(local.thisEl))>
				</cfif>
			</cfif>
		</cfloop>

		<cfset local.strFields.frmrtrt = { label="Roles of Record Type", value="" }>
		<cfif NOT StructIsEmpty(local.recordTypeRoles)>
			<cfloop list="#local.strFields.frmrt.value#" item="local.thisRecType">
				<cfif StructKeyExists(local.recordTypeRoles, local.thisRecType)>
					<cfset local.strFields.frmrtrt.value = listAppend(local.strFields.frmrtrt.value, local.recordTypeRoles[local.thisRecType], "|")>
				<cfelse>
					<cfset local.strFields.frmrtrt.value = listAppend(local.strFields.frmrtrt.value, "ALL", "|")>
				</cfif>
			</cfloop>
		</cfif>
		
		<cfset reportSaveReportExtra(qryReportInfo=arguments.event.getValue("qryReportInfo"), strFields=local.strFields, event=arguments.event)>
		<cfreturn returnAppStruct('','echo')>
	</cffunction>

	<cffunction name="getSubscriptionStatuses" access="private" output="false" returntype="query">
		<cfset var qryStatus = "">
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryStatus">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select statusID, statusName, statusCode
			from dbo.sub_statuses
			where statusName in ('Active','Billed','Accepted')
			order by statusName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;		
		</cfquery>

		<cfreturn qryStatus>
	</cffunction>

</cfcomponent>