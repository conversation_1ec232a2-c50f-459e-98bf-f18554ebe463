ALTER PROC dbo.queue_memberPhotoThumb_grabForProcessing
@batchSize int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @queueTypeID int, @statusReady int, @statusGrabbed int;

	select @queueTypeID = queueTypeID from dbo.tblQueueTypes where queueType = 'memberPhotoThumb';
	select @statusReady = queueStatusID from dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'readyToProcess';
	select @statusGrabbed = queueStatusID from dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'grabbedForProcessing';

	IF OBJECT_ID('tempdb..#tmpThumbs') IS NOT NULL 
		DROP TABLE #tmpThumbs;
	CREATE TABLE #tmpThumbs (itemID int PRIMARY KEY);

	update qi WITH (UPDLOCK, READPAST)
	set qi.statusID = @statusGrabbed,
		qi.dateUpdated = getdate()
		OUTPUT inserted.itemID
		INTO #tmpThumbs
	from dbo.queue_memberPhotoThumb as qi
	inner join (
		select top(@BatchSize) itemID
		from dbo.queue_memberPhotoThumb
		where statusID = @statusReady
		order by dateAdded, itemID
		) as batch on batch.itemID = qi.itemID
	where qi.statusID = @statusReady;

	select qidd.itemID, qidd.orgcode, qidd.memberid, qidd.membernumber, qidd.width, qidd.height
	from #tmpThumbs as qid
	inner join dbo.queue_memberPhotoThumb as qidd on qidd.itemID = qid.itemID
	order by qid.itemID;

	IF OBJECT_ID('tempdb..#tmpThumbs') IS NOT NULL 
		DROP TABLE #tmpThumbs;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
END CATCH
GO
