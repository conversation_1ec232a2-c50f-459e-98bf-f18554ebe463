<cfcomponent output="false" extends="model.scheduledTasks.scheduledTasks">

	<cffunction name="runTask" access="public" returntype="void" output="false">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structnew()>

		<cfsetting requesttimeout="600">
		
		<cfscript>
			local.arrTaskFields = [ { name="BatchSize", type="INTEGER", desc="Batch Size", value="50" } ];
			local.strTaskFields = setTaskCustomFields(siteID=application.objSiteInfo.getSiteInfo('MC').siteID, siteResourceID=arguments.strTask.siteResourceID, arrTaskFields=local.arrTaskFields);
		</cfscript>

		<cfset local.itemCount = getQueueItemCount()>

		<cfif local.itemCount GT 0>
			<cfset local.success = processQueue(batchSize=local.strTaskFields.batchSize)>
			<cfif NOT local.success>
				<cfthrow message="Error running processQueue()">
			</cfif>
		</cfif>

		<cfset local.historyID = (arguments.strTask.historyID ?: 0)>
		<cfset addBatchIdentifierToHistory(historyID=local.historyID, batchIdentifier="", itemCount=local.itemCount)>
	</cffunction>

	<cffunction name="processQueue" access="private" output="false" returntype="boolean">
		<cfargument name="batchSize" type="numeric" required="true">
		
		<cfset var local = structnew()>
		<cfset local.success = true>

		<cftry>
			<!--- ---------------------- --->
			<!--- grab and process queue --->
			<!--- ---------------------- --->
			<cfstoredproc procedure="queue_TSDepoDocumentReport_grabForProcessing" datasource="#application.dsn.platformQueue.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.batchSize#">
				<cfprocresult name="local.qryDepoMembers" resultset="1">
			</cfstoredproc>

			<cfif local.qryDepoMembers.recordCount>
				<cfsavecontent variable="local.emailContent">
					<cfoutput>
					<!DOCTYPE html>
					<html xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office" lang="en">
					<head>
						<title></title>
						<meta charset="UTF-8">
						<meta name="viewport" content="width=device-width,initial-scale=1">
						<!--[if mso]><xml><o:OfficeDocumentSettings><o:PixelsPerInch>96</o:PixelsPerInch><o:AllowPNG/></o:OfficeDocumentSettings></xml><![endif]-->
						<style>*{box-sizing:border-box}body{margin:0;padding:0}th.column{padding:0}a[x-apple-data-detectors]{color:inherit!important;text-decoration:inherit!important}##MessageViewBody a{color:inherit;text-decoration:none}p{line-height:inherit}@media (max-width:700px){.row-content{width:100%!important}.stack .column{width:100%;display:block}}</style>
					</head>
					<body style="background-color:##fff;margin:0;padding:0;-webkit-text-size-adjust:none;text-size-adjust:none">
						<table class="nl-container" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:##fff">
							<tbody>
								<tr>
									<td>
										<table class="row row-1" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:##fff">
											<tbody>
												<tr>
													<td>
														<table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;background-color:##fff" width="680">
															<tbody>
																<tr>
																	<th class="column" width="100%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;padding-top:5px;padding-bottom:5px">
																		<table class="image_block" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0">
																			<tr>
																				<td style="width:100%;padding-top:10px;padding-bottom:10px;padding-right:0;padding-left:0">
																					<div align="center" style="line-height:10px"><a href="https://www.trialsmith.com" target="_blank" style="outline:0" tabindex="-1"><img src="https://www.trialsmith.com/images/TS_288x28.png" style="display:block;height:auto;border:0;width:288px;max-width:100%" width="288" alt="TrialSmith" title="TrialSmith"></a></div>
																				</td>
																			</tr>
																		</table>
																		<table class="text_block" width="100%" border="0" cellpadding="10" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word">
																			<tr>
																				<td>
																					<div style="font-family:Verdana,sans-serif">
																						<div style="font-size:16px;font-family:Verdana,Geneva,sans-serif;color:##555;line-height:1.2">
																							<p style="margin:0;mso-line-height-alt:16.8px">&nbsp;</p>
																							<p style="margin:0">Dear [[TSDepoMemberName]]:</p><br>
																							<p style="margin:0">Last week, <strong>[[TSDepoCountWeek]]</strong> of your recent deposition contribution(s) were reviewed[[TSDepoPurchaseCreditAmountWeekText]].</p><br>
																							[[TSDepoAmazonBucksCreditAmountWeek]]
																							<p style="margin:0">Your current total document purchase credit balance is now <strong>[[TSDepoPurchaseCreditAmountTotal]]</strong>.</p><br>
																							[[TSDepoPendingDepositionsText]]
																							<p style="margin:0">Thanks for helping the plaintiff bar!!</p><br>
																						</div>
																					</div>
																				</td>
																			</tr>
																		</table>
																		<table class="button_block" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0">
																			<tr>
																				<td style="text-align:center;padding-top:10px;padding-right:10px;padding-bottom:15px;padding-left:10px">
																					<div align="center">
																						<!--[if mso]>
																						<v:roundrect xmlns:v="urn:schemas-microsoft-com:vml" xmlns:w="urn:schemas-microsoft-com:office:word" href="https://www.trialsmith.com/?pg=uploadDocuments&ul=1&mk=[[MEMBERKEY]]" style="height:40px;v-text-anchor:middle;width:350px;" arcsize="10%" stroke="f" fillcolor="##0068a5">
																						<w:anchorlock/>
																						<center style="color:##ffffff;font-family:sans-serif;font-size:16px;font-weight:bold;">
																						Upload and Earn Credits for [[TSDepoMemberName]]
																						</center>
																						</v:roundrect>
																						<![endif]-->
																						<![if !mso]>
																						<table cellspacing="0" cellpadding="0" align="center"><tr> 
																						<td align="center" width="350" height="40" bgcolor="##0068a5" style="-webkit-border-radius: 5px; -moz-border-radius: 5px; border-radius: 5px; color: ##ffffff; display: block;">
																						<a href="https://www.trialsmith.com/?pg=uploadDocuments&ul=1&mk=[[MEMBERKEY]]" style="font-size:16px; font-weight: bold; font-family:sans-serif; text-decoration: none; line-height:40px; width:100%; display:inline-block">
																						<span style="color: ##ffffff;">Upload and Earn Credits for [[TSDepoMemberName]]</span>
																						</a>
																						</td> 
																						</tr></table>
																						<![endif]>
																					</div>
																					<div style="font-family:Verdana,sans-serif;padding-top:15px;">
																						<div style="font-size:16px;font-family:Verdana,Geneva,sans-serif;color:##555;line-height:1.2">
																							<p style="margin:0">Or, email your transcripts to <a href="mailto:<EMAIL>?subject=Deposition%20from%20[[TSEncodedDepoMemberName]]%20-%20DepoID-[[TSDepoMemberDataID]]&body=Attach%20up%20to%2010%20Depositions%20to%20this%20email%20-%20documents%20will%20be%20added%20to%20TrialSmith%20and%20document%20purchase%20credits%20posted%20to%20your%20Account."><EMAIL></a>.</p>
																						</div>
																					</div>
																				</td>
																			</tr>
																		</table>
																	</th>
																</tr>
															</tbody>
														</table>
													</td>
												</tr>
											</tbody>
										</table>
										<br><br>
										<table class="row row-2" align="center" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0">
											<tbody>
												<tr>
													<td>
														<table class="row-content stack" align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;border-radius:0;color:##000;width:500px" width="500">
															<tbody>
																<tr>
																	<td class="column column-1" width="25%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0">
																		<table class="image_block block-2" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0">
																			<tr>
																				<td class="pad" style="width:100%;padding-right:0;padding-left:0;padding-top:5px;padding-bottom:5px">
																					<div class="alignment" align="center" style="line-height:10px"><img src="https://www.trialsmith.com/userassets/MC/TS/userimages/brettwurdemanheadshot118px.png" style="display:block;height:92px;border:0;width:72px;max-width:100%" width="72"></div>
																				</td>
																			</tr>
																		</table>
																	</td>
																	<td class="column column-2" width="75%" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;border-top:0;border-right:0;border-bottom:0;border-left:0">
																		<table class="paragraph_block block-2" width="100%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word">
																			<tr>
																				<td class="pad" style="padding-bottom:15px;padding-left:10px;padding-right:10px;padding-top:15px">
																					<div style="color:##000;direction:ltr;font-family:Arial,Helvetica Neue,Helvetica,sans-serif;font-size:16px;font-weight:400;letter-spacing:0;line-height:120%;text-align:left;mso-line-height-alt:16.8px">
																						<p style="margin:0;margin-bottom:10px"><strong>Brett Wurdeman</strong></p>
																						<p style="margin:0;margin-bottom:10px">Director, Trialsmith</p>
																						<p style="margin:0"> ************ | <a href="mailto:<EMAIL>" target="_blank" title="<EMAIL>" style="text-decoration: underline; color: ##0068a5;" rel="noopener"><EMAIL></a></p>
																					</div>
																				</td>
																			</tr>
																		</table>
																	</td>
																</tr>
															</tbody>
														</table>
													</td>
												</tr>
											</tbody>
										</table>
										<table class="row row-4" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0" width="100%" cellspacing="0" cellpadding="0" border="0" align="center">
											<tbody>
												<tr>
													<td>
														<table class="row-content stack" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0" width="500" cellspacing="0" cellpadding="0" border="0" align="center">
															<tbody>
																<tr>
																	<th class="column" style="mso-table-lspace:0;mso-table-rspace:0;font-weight:400;text-align:left;vertical-align:top;padding-top:5px;padding-bottom:5px;border-top:0;border-right:0;border-bottom:0;border-left:0" width="100%">
																		<table class="divider_block" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0" width="100%" cellspacing="0" cellpadding="10" border="0">
																			<tbody>
																				<tr>
																					<td>
																						<div align="center">
																							<table role="presentation" style="mso-table-lspace:0;mso-table-rspace:0" width="100%" cellspacing="0" cellpadding="0" border="0">
																								<tbody>
																									<tr>
																										<td class="divider_inner" style="font-size:1px;line-height:1px;border-top:1px solid ##bbb"><span></span>
																										</td>
																									</tr>
																								</tbody>
																							</table>
																						</div>
																					</td>
																				</tr>
																			</tbody>
																		</table>
																		<table class="text_block" role="presentation" style="mso-table-lspace:0;mso-table-rspace:0;word-break:break-word" width="100%" cellspacing="0" cellpadding="10" border="0">
																			<tbody>
																				<tr>
																					<td>
																						<div style="font-family:sans-serif">
																							<div style="font-size:12px;color:##555;line-height:1.2;font-family:Arial,'Helvetica Neue',Helvetica,sans-serif">
																								<p style="margin:0;font-size:12px;text-align:center">
																									<a href="mailto:<EMAIL>?subject=Unsubscribe%20From%20Credit%20Notifications&body=I%20would%20like%20to%20unsubscribe%20from%20the%20credit%20notifications.%0D%0AName:[[TSEncodedDepoMemberName]]%0D%0AEmail%20Address:[[TSDepoMemberEmailEncoded]]" target="_blank" title="Unsubscribe From Credit Notifications" style="text-decoration:underline;color:##0068a5" rel="noopener">Unsubscribe</a>
																								</p>
																							</div>
																						</div>
																					</td>
																				</tr>
																			</tbody>
																		</table>
																	</th>
																</tr>
															</tbody>
														</table>
													</td>
												</tr>
											</tbody>
										</table>
									</td>
								</tr>
							</tbody>
						</table>
					</body>
					</html>
					</cfoutput>
				</cfsavecontent>

				<cfset local.mc_siteinfo = application.objSiteInfo.getSiteInfo('TS')>                

                <cfquery name="local.qryDepoDetailsWeek" datasource="#application.dsn.platformQueue.dsn#">
                    SET NOCOUNT ON;

                    SELECT DISTINCT qddr.itemID, dd.DocumentID, dd.DepoAmazonBucksFullName, pc.PurchaseCreditAmount, pc.AmazonBucksCreditAmount
                    FROM dbo.queue_TSDepoDocumentReport AS qddr
                    INNER JOIN trialsmith.dbo.depoDocuments AS dd ON dd.depomemberdataID = qddr.depomemberdataID
                    INNER JOIN trialsmith.dbo.depoDocumentStatusHistory AS docSH ON docSH.documentID = dd.DocumentID
                    INNER JOIN trialsmith.dbo.depoDocumentStatuses AS ds ON ds.statusID = docSH.statusID
						AND ds.statusName = 'Approved'
                    INNER JOIN trialsmith.dbo.PurchaseCredits AS pc ON pc.DocumentID = dd.DocumentID
                    WHERE dd.disabled = 'N'
                    AND docSH.dateEntered BETWEEN DATEADD(DAY,-7,GETDATE()) AND GETDATE() 
                    AND qddr.itemID in (<cfqueryparam value="#valueList(local.qryDepoMembers.itemID)#" cfsqltype="CF_SQL_INTEGER" list="yes">);
                </cfquery>

				<cfloop query="local.qryDepoMembers">
					<!--- item must still be in the grabbedForProcessing state for this job. else skip it. --->
					<cfquery name="local.qryCheckItemID" datasource="#application.dsn.platformQueue.dsn#">
						SELECT COUNT(qi.itemID) AS itemCount
						FROM dbo.queue_TSDepoDocumentReport as qi
						INNER JOIN dbo.tblQueueStatuses AS qs ON qs.queueStatusID = qi.statusID 
						WHERE qi.itemID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qryDepoMembers.itemID#">
						AND qs.queueStatus = 'grabbedForProcessing'
					</cfquery>

					<cfif local.qryCheckItemID.itemCount>
						<cfquery name="local.qryUpdateToProcessingItem" datasource="#application.dsn.platformQueue.dsn#">
							SET NOCOUNT ON;

							DECLARE @queueTypeID int, @statusProcessing int;
							SELECT @queueTypeID = queueTypeID FROM dbo.tblQueueTypes WHERE queueType = 'TSDepoDocumentReport';
							SELECT @statusProcessing = queueStatusID FROM dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID AND queueStatus = 'processingItem';

							UPDATE dbo.queue_TSDepoDocumentReport
							SET statusID = @statusProcessing,
								dateUpdated = getdate()
							WHERE itemID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qryDepoMembers.itemID#">;
						</cfquery>

                        <cfquery name="local.qryDepoMemberDetailsWeek" dbtype="query">
                            SELECT COUNT(1) AS depoCountWeek, SUM(local.qryDepoDetailsWeek.PurchaseCreditAmount) AS purchaseCreditAmount, SUM(local.qryDepoDetailsWeek.AmazonBucksCreditAmount) AS amazonBucksCreditAmount, DepoAmazonBucksFullName
                            FROM local.qryDepoDetailsWeek
                            WHERE local.qryDepoDetailsWeek.itemID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qryDepoMembers.itemID#">
                            GROUP BY itemID, DepoAmazonBucksFullName
                        </cfquery>

                        <cfquery name="local.qryPurchaseCreditBalance" datasource="#application.dsn.tlasites_trialsmith.dsn#">
							SELECT balance 
							FROM dbo.fn_Documents_getPurchaseCredits(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryDepoMembers.depoMemberDataID#">)
                        </cfquery>

						<cfquery name="local.qryPendingDepoDocs" datasource="#application.dsn.tlasites_trialsmith.dsn#">
							select count(*) as pendingCount
							from trialsmith.dbo.depoDocuments
							where uploadStatus = 1
							and reviewFlag = 0
							and disabled = 'N'
							and DepomemberdataID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryDepoMembers.depoMemberDataID#">
						</cfquery>

                        <cfset local.depoCountWeek = 0/>
                        <cfset local.purchaseCreditAmountText = "">
                        <cfset local.amazonBucksCreditAmountText = ""/>
                        <cfset local.DepoAmazonBucksFullName = ""/>

						<cfset local.pendingDepositionsText = ""/>
						<cfif local.qryPendingDepoDocs.recordCount and local.qryPendingDepoDocs.pendingCount gt 0>
							<cfset local.pendingDepositionsText = "<p style='margin:0'>Additionally, <strong>#val(local.qryPendingDepoDocs.pendingCount)#</strong> deposition(s) are currently pending approval. We are working diligently to review them and will notify you as soon as they are processed.</p><br>"/>
						</cfif>
                        
                        <cfif local.qryDepoMemberDetailsWeek.RecordCount>
                            <cfset local.depoCountWeek = val(local.qryDepoMemberDetailsWeek.depoCountWeek)/>
                            <cfif val(local.qryDepoMemberDetailsWeek.purchaseCreditAmount) GT 0>
                                <cfset local.purchaseCreditAmount = DollarFormat(val(local.qryDepoMemberDetailsWeek.purchaseCreditAmount))/>
                                <cfset local.purchaseCreditAmountText = ", and we added <strong>" & local.purchaseCreditAmount & "</strong> in purchase credits to your account"/>
                            </cfif>
                            <cfif val(local.qryDepoMemberDetailsWeek.amazonBucksCreditAmount) GT 0>
                                <cfset local.amazonBucksCreditAmount = DollarFormat(val(local.qryDepoMemberDetailsWeek.amazonBucksCreditAmount))/>
                                <cfset local.amazonBucksCreditAmountText = "<p style='margin:0'>You have <strong>" & local.amazonBucksCreditAmount & "</strong> in Amazon Gift Cards!"/>
                                <cfif len(trim(local.qryDepoMemberDetailsWeek.DepoAmazonBucksFullName))>
                                    <cfset local.DepoAmazonBucksFullName = " (We'll send the gift card separately to " & encodeForHTMLAttribute(local.qryDepoMemberDetailsWeek.DepoAmazonBucksFullName) & " next month )"/>
                                    <cfset local.amazonBucksCreditAmountText = local.amazonBucksCreditAmountText & local.DepoAmazonBucksFullName>
                                </cfif>
								 <cfset local.amazonBucksCreditAmountText = local.amazonBucksCreditAmountText & "</p><br>">
                            </cfif>
                        </cfif>
						<cfset local.MCMemberID = application.objMember.getMemberIDByMemberNumber(memberNumber="TSDEPOID_#local.qryDepoMembers.depomemberdataID#", orgID=local.mc_siteinfo.orgID)>
						<cfset local.memberKeyForTS = application.objMergeCodes.generateMemberKey(orgcode=local.mc_siteinfo.orgcode, membernumber='TSDEPOID_#local.qryDepoMembers.depomemberdataID#')>
						<cfif local.MCMemberID eq 0>
							<cfset local.MCMemberID = local.mc_siteinfo.sysMemberID>
							<cfset local.memberKeyForTS = "">
						</cfif>

						<cfset local.thisEmailContent = local.emailContent>
						<cfset local.thisEmailContent = replaceNoCase(local.thisEmailContent,'[[TSDepoMemberName]]','#local.qryDepoMembers.FirstName# #local.qryDepoMembers.LastName#', 'all')>
						<cfset local.thisEmailContent = replaceNoCase(local.thisEmailContent,'[[TSEncodedDepoMemberName]]',encodeForHTMLAttribute("#local.qryDepoMembers.FirstName# #local.qryDepoMembers.LastName#"), 'all')>
						<cfset local.thisEmailContent = replaceNoCase(local.thisEmailContent,'[[TSDepoMemberEmailEncoded]]',local.qryDepoMembers.Email, 'all')>
						<cfset local.thisEmailContent = replaceNoCase(local.thisEmailContent,'[[TSDepoMemberDataID]]',local.qryDepoMembers.depomemberdataID, 'all')>
                        <cfset local.thisEmailContent = replaceNoCase(local.thisEmailContent,'[[TSDepoCountWeek]]',local.depoCountWeek, 'all')>
						<cfset local.thisEmailContent = replaceNoCase(local.thisEmailContent,'[[TSDepoPurchaseCreditAmountWeekText]]',local.purchaseCreditAmountText, 'all')>
                        <cfset local.thisEmailContent = replaceNoCase(local.thisEmailContent,'[[TSDepoAmazonBucksCreditAmountWeek]]',local.amazonBucksCreditAmountText, 'all')>
                        <cfset local.thisEmailContent = replaceNoCase(local.thisEmailContent,'[[TSDepoPurchaseCreditAmountTotal]]',DollarFormat(val(local.qryPurchaseCreditBalance.balance)), 'all')>
						<cfset local.thisEmailContent = replaceNoCase(local.thisEmailContent,'[[TSDepoPendingDepositionsText]]',local.pendingDepositionsText, 'all')>
						<cfif len(local.memberKeyForTS)>
							<cfset local.thisEmailContent = replaceNoCase(local.thisEmailContent,'[[MEMBERKEY]]',local.memberKeyForTS, 'all')>
						<cfelse>
							<cfset local.thisEmailContent = replaceNoCase(local.thisEmailContent,'&mk=[[MEMBERKEY]]','', 'all')>
						</cfif>

						<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
							emailfrom={ name="TrialSmith", email="<EMAIL>" },
							emailto=[{ name:'#local.qryDepoMembers.FirstName# #local.qryDepoMembers.LastName#', email:local.qryDepoMembers.Email }],
							emailreplyto=local.mc_siteinfo.supportProviderEmail,
							emailsubject="Your Depositions Were Received - Credits Approved",
							emailtitle="Depo Document Report",
							emailhtmlcontent=local.thisEmailContent,
							siteID=local.mc_siteinfo.siteID,
							memberID=local.MCMemberID,
							messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="TSDEPOREPORT"),
							sendingSiteResourceID=local.mc_siteinfo.siteSiteResourceID,
							doWrapEmail=false
						)>

						<cfquery name="local.qryDeleteQueueItem" datasource="#application.dsn.platformQueue.dsn#">
							DELETE FROM dbo.queue_TSDepoDocumentReport
							WHERE itemID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qryDepoMembers.itemID#">
						</cfquery>
					</cfif>
				</cfloop>
			</cfif>

		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.success = false>
		</cfcatch>
		</cftry>			
					
		<cfreturn local.success>
	</cffunction>

	<cffunction name="getQueueItemCount" access="private" output="false" returntype="numeric">
		<cfset var qryQueueItems = "">

		<cfquery name="qryQueueItems" datasource="#application.dsn.platformQueue.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT COUNT(itemID) AS itemCount
			FROM dbo.queue_TSDepoDocumentReport;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryQueueItems.itemCount>
	</cffunction>

</cfcomponent>