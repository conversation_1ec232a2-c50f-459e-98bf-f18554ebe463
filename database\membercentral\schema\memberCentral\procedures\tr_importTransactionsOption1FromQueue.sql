ALTER PROC dbo.tr_importTransactionsOption1FromQueue
@itemID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @queueTypeID int, @recordedByMemberID int, @siteID int, @rowID int, @orgID int, @MCMemberID int, 
		@MCInvoiceProfileID int, @MCRevenueGLAID int, @saleDate date, @saleDescription varchar(255), 
		@saleAmount decimal(18,2), @invoiceID int, @invoiceNumber varchar(18), @transactionID int, 
		@statusReady int, @itemStatus int, @statusProcessing int, @statusNotify int, @dueDate date;

	select @queueTypeID = queueTypeID from platformQueue.dbo.tblQueueTypes where queueType = 'importAcctOpt1';
	select @statusReady = queueStatusID from platformQueue.dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'readyToProcess';
	select @statusProcessing = queueStatusID from platformQueue.dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'processingItem';
	select @statusNotify = queueStatusID from platformQueue.dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'readyToNotify';

	select @recordedByMemberID=recordedByMemberID, @orgID=orgID, @siteID=siteID, @MCMemberID=memberID, 
		@MCInvoiceProfileID=invoiceProfileID, @MCRevenueGLAID=GLAccountID, @saleDate=saleDate, 
		@saleDescription=saleDescription, @saleAmount=saleAmount, @itemStatus=statusID, @dueDate = dueDate
	from platformQueue.dbo.queue_acctOption1Import
	where itemID = @itemID;

	IF @itemStatus <> @statusReady OR @MCMemberID is null
		GOTO on_done;

	UPDATE platformQueue.dbo.queue_acctOption1Import
	set statusID = @statusProcessing,
		dateUpdated = getdate()
	where itemID = @itemID;

	BEGIN TRAN;
		EXEC dbo.tr_createInvoice @invoiceProfileID=@MCInvoiceProfileID, @enteredByMemberID=@recordedByMemberID, 
			@assignedToMemberID=@MCMemberID, @dateBilled=@saleDate, @dateDue=@dueDate, 
			@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT;

		EXEC dbo.tr_createTransaction_sale @ownedByOrgID=@orgID, @recordedOnSiteID=@siteID, @assignedToMemberID=@MCMemberID, 
			@recordedByMemberID=@recordedByMemberID, @statsSessionID=null, @status='Active', @detail=@saleDescription, 
			@parentTransactionID=null, @amount=@saleAmount, @transactionDate=@saleDate,  
			@creditGLAccountID=@MCRevenueGLAID, @invoiceID=@invoiceID, @stateIDForTax=0, @zipForTax='', @taxAmount=null, 
			@bypassTax=1, @bypassInvoiceMessage=1, @bypassAccrual=1, @xmlSchedule=null, @transactionID=@transactionID OUTPUT;
		
		EXEC dbo.tr_closeInvoice @orgID=@orgID, @enteredByMemberID=@recordedByMemberID, @invoiceIDList=@invoiceID;
	COMMIT TRAN;

	UPDATE platformQueue.dbo.queue_acctOption1Import
	set statusID = @statusNotify,
		dateUpdated = getdate()
	where itemID = @itemID;

	on_done:
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
