ALTER PROC dbo.queue_monthBillEmailIndiv_clearDone
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @queueTypeID int, @statusDone int;
	declare @tblBillingPeriods TABLE (billingPeriodID int);
	select @queueTypeID = queueTypeID from dbo.tblQueueTypes where queueType = 'monthBillEmailIndiv';
	select @statusDone = queueStatusID from dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'done';

	INSERT INTO @tblBillingPeriods (billingPeriodID)
	select distinct billingPeriodID
	from dbo.queue_monthBillEmailIndiv
	where statusID = @statusDone
		except
	select distinct billingPeriodID
	from dbo.queue_monthBillEmailIndiv
	where statusID <> @statusDone;

	IF @@ROWCOUNT = 0
		GOTO on_done;
		
	BEGIN TRAN;
		delete q
		from platformQueue.dbo.queue_monthBillTSA as q
		inner join @tblBillingPeriods as tmp on tmp.billingPeriodID = q.billingPeriodID
		where q.isOrg = 0;

		delete q
		from platformQueue.dbo.queue_monthBillEmailIndiv as q
		inner join @tblBillingPeriods as tmp on tmp.billingPeriodID = q.billingPeriodID;
	COMMIT TRAN;

	on_done:
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
