ALTER PROC dbo.queue_SubscriptionAdd_grabForNotification
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @queueTypeID int, @statusReady int, @statusGrabbed int;

	select @queueTypeID = queueTypeID from dbo.tblQueueTypes where queueType = 'addSubscribers';
	select @statusReady = queueStatusID from dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'readyToNotify';
	select @statusGrabbed = queueStatusID from dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'grabbedForNotifying';

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	CREATE TABLE #tmpNotify (itemGroupUID uniqueidentifier);

	-- dequeue. 
	; WITH itemGroupUIDs AS (
		select distinct itemGroupUID
		from dbo.queue_subscriptionAdd
		where statusID = @statusReady
			except
		select distinct itemGroupUID
		from dbo.queue_subscriptionAdd
		where statusID <> @statusReady
	)
	UPDATE qi
	SET qi.statusID = @statusGrabbed,
		qi.dateUpdated = GETDATE()
		OUTPUT itemGroupUIDs.itemGroupUID
		INTO #tmpNotify
	FROM dbo.queue_subscriptionAdd as qi
	INNER JOIN itemGroupUIDs on itemGroupUIDs.itemGroupUID = qi.itemGroupUID
	where qi.statusID = @statusReady;

	-- return itemGroupUIDs that can be marked as done
	select distinct tmpN.itemGroupUID, me.email as reportEmail, s.siteID, s.siteName, s.siteCode, mActive.firstname, mActive.lastname, mActive.membernumber, mActive.memberID
	from (select distinct itemGroupUID from #tmpNotify) as tmpN
	INNER JOIN dbo.queue_subscriptionAdd as qid ON qid.itemGroupUID = tmpN.itemGroupUID
	INNER JOIN membercentral.dbo.sites as s on s.siteID = qid.siteID
	INNER JOIN membercentral.dbo.ams_members as m on m.orgID in (s.orgID,1) and m.memberID = qid.recordedByMemberID
	INNER JOIN membercentral.dbo.ams_members as mActive on mActive.orgID = m.orgID and mActive.memberid = m.activeMemberID
	INNER JOIN membercentral.dbo.ams_memberEmailTags as metag on metag.orgID = m.orgID and metag.memberID = mActive.memberID
	INNER JOIN membercentral.dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = m.orgID 
		and metagt.emailTagTypeID = metag.emailTagTypeID
		and metagt.emailTagType = 'Primary'
	INNER JOIN membercentral.dbo.ams_memberEmails as me on me.orgID = m.orgID
		and me.memberID = metag.memberID
		and me.emailTypeID = metag.emailTypeID
	order by tmpN.itemGroupUID;

	-- return report information for failures
	select tmpN.itemGroupUID, 
		m2.lastname + ', ' + m2.firstname + isnull(' ' + nullif(m2.middlename,''),'') + ' (' + m2.membernumber + ')' as subscriberName,
		qid.treeCode, sub.subscriptionName,
		RANK() OVER (ORDER BY tmpN.itemGroupUID, m2.lastname + ', ' + m2.firstname + isnull(' ' + nullif(m2.middlename,''),'') + ' (' + m2.membernumber + ')', qid.treecode) as rowID1,
		ROW_NUMBER() OVER (PARTITION BY tmpN.itemGroupUID, m2.lastname + ', ' + m2.firstname + isnull(' ' + nullif(m2.middlename,''),'') + ' (' + m2.membernumber + ')', qid.itemUID ORDER BY qidd.rowID) as rowID2
	from (select distinct itemGroupUID from #tmpNotify) as tmpN
	INNER JOIN dbo.queue_subscriptionAdd as qid ON qid.itemGroupUID = tmpN.itemGroupUID
	INNER JOIN dbo.queue_subscriptionAddDetail as qidd on qidd.itemUID = qid.itemUID
	INNER JOIN membercentral.dbo.ams_members as m on m.memberID = qid.memberID
	INNER JOIN membercentral.dbo.ams_members as m2 on m2.memberid = m.activeMemberID
	INNER JOIN membercentral.dbo.sub_subscriptions as sub on sub.subscriptionID = qidd.subscriptionID
	WHERE qidd.subscriberID is null
	order by rowID1, rowID2;

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
