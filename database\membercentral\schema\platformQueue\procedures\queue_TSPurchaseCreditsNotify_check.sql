ALTER PROC dbo.queue_TSPurchaseCreditsNotify_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @issueCount int, @timeToUse datetime, @queueTypeID int, @queueStatusID int, @nextQueueStatusID int,
		@errorSubject VARCHAR(400), @errorTitle VARCHAR(400);
	select @queueTypeID = queueTypeID from dbo.tblQueueTypes where queueType = 'TSPurchaseCreditsNotify';

	-- queue_TSPurchaseCreditsNotify / grabbedForProcessing autoreset to readyToProcess
	set @issueCount = 0;
	set @timeToUse = DATEADD(minute, -10, GETDATE());
	select @queueStatusID = queuestatusID from dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'grabbedForProcessing';
	select @nextQueueStatusID = queuestatusID from dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'readyToProcess';
	SELECT @issueCount = count(itemID) FROM dbo.queue_TSPurchaseCreditsNotify WHERE statusID = @queueStatusID and dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		update dbo.queue_TSPurchaseCreditsNotify
		set statusID = @nextQueueStatusID,
			dateUpdated = getdate()
		WHERE statusID = @queueStatusID
		and dateUpdated < @timeToUse;

		SET @errorTitle = 'TSPurchaseCreditsNotify Queue Issue';
		SET @errorSubject = 'TSPurchaseCreditsNotify queue moved items from grabbedForProcessing to readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- queue_TSPurchaseCreditsNotify / processingItem autoreset to readyToProcess
	set @issueCount = 0;
	set @timeToUse = DATEADD(minute, -10, GETDATE());
	select @queueStatusID = queuestatusID from dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'processingItem';
	select @nextQueueStatusID = queuestatusID from dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'readyToProcess';
	SELECT @issueCount = count(itemID) FROM dbo.queue_TSPurchaseCreditsNotify WHERE statusID = @queueStatusID and dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		update dbo.queue_TSPurchaseCreditsNotify
		set statusID = @nextQueueStatusID,
			dateUpdated = getdate()
		WHERE statusID = @queueStatusID
		and dateUpdated < @timeToUse;

		SET @errorTitle = 'TSPurchaseCreditsNotify Queue Issue';
		SET @errorSubject = 'TSPurchaseCreditsNotify queue moved items from processingItem to readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- queue_TSPurchaseCreditsNotify catchall
	set @issueCount = 0;
	set @timeToUse = DATEADD(hour, -4, GETDATE())
	SELECT @issueCount = count(itemID) FROM dbo.queue_TSPurchaseCreditsNotify WHERE dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'TSPurchaseCreditsNotify Queue Issue';
		SET @errorSubject = 'TSPurchaseCreditsNotify queue has items last updated more than 4 hours ago';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
