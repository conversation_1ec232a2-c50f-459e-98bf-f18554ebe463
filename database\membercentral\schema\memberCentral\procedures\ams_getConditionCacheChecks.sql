ALTER PROC dbo.ams_getConditionCacheChecks
@itemCount int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpActiveOrgs') IS NOT NULL
		DROP TABLE #tmpActiveOrgs;
	CREATE TABLE #tmpActiveOrgs (orgID int PRIMARY KEY);

	DECLARE @orgID int, @queueTypeID int, @queueStatusID int, @xmlMessage xml, @nowDate datetime = GETDATE();
	SELECT @queueTypeID = queueTypeID FROM platformQueue.dbo.tblQueueTypes WHERE queueType = 'conditionCacheCheck';
	SELECT @queueStatusID = queueStatusID FROM platformQueue.dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID AND queueStatus = 'readyToProcess';

	SET @itemCount = NULL;

	INSERT INTO #tmpActiveOrgs (orgID)
	SELECT DISTINCT s.orgID
	FROM dbo.sites AS s
	INNER JOIN dbo.cms_siteResources AS sr ON sr.siteID = s.siteID
		AND sr.siteResourceID = s.siteResourceID
		AND sr.siteResourceStatusID = 1;

	INSERT INTO platformQueue.dbo.queue_conditionCacheCheck (orgID, statusID, dateAdded, dateUpdated)
	SELECT tmp.orgID, @queueStatusID, @nowDate, @nowDate
	FROM #tmpActiveOrgs AS tmp
	LEFT OUTER JOIN platformQueue.dbo.queue_conditionCacheCheck AS qid ON qid.orgID = tmp.orgID
	WHERE qid.itemID IS NULL;

	SET @itemCount = @@ROWCOUNT;

	SELECT @orgID = MIN(orgID) FROM #tmpActiveOrgs;
	WHILE @orgID IS NOT NULL BEGIN
		SET @xmlMessage = NULL;

		SELECT @xmlMessage = isnull((
			SELECT @orgID as o
			FOR XML RAW('mc'), TYPE
		),'<mc/>');

		EXEC platformQueue.dbo.queue_conditionCacheCheck_sendMessage @xmlMessage=@xmlMessage;

		SELECT @orgID = MIN(orgID) FROM #tmpActiveOrgs WHERE orgID > @orgID;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
