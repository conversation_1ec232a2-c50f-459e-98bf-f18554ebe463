<cfsavecontent variable="local.remreghead">
	<cfoutput>
	<script language="javascript">
		function checkAROptChanges() {
			if ($('input[name="rdoAR"]:checked').val() == 'A' && $('##deallocUsingPaidAmt').is(':disabled')) {
				$('##deallocUsingPaidAmt').prop('disabled',false);
			} else if ($('input[name="rdoAR"]:checked').val() != 'A' && !$('##deallocUsingPaidAmt').is(':disabled')) {
				$('##deallocUsingPaidAmt').prop('disabled',true).prop('checked',false);
			}
		}
		function doCallRemoveReg() {
			if ($('input[name="rdoAR"]:checked').val() == null){
				$('##ev_remreg_err_div').html('Select an accounts receivable option.').show();
				return false;
			}
			else $('##ev_remreg_err_div').html('').hide();

			top.$('##btnMCModalSave').text("Removing...").prop('disabled',true);
			$('##divRemoveRegLoading').show();
			$('##divRemoveRegForm').hide();

			var objParams = { registrantID:#val(local.qryRegLookup.registrantID)#, eventSRID:#val(local.qryRegLookup.eventSRID)#,
				calendarSRID:#val(local.qryRegLookup.calendarSRID)#, AROption:$('input[name="rdoAR"]:checked').val(),
				cancellationFee:mca_stripCurrency(formatCurrency($('##cancellationFee').val())),
				GLAccountID:$('##GLAccountID').val(), deallocUsingPaidAmt:$('##deallocUsingPaidAmt').is(':checked'), registrantMemberID:#val(local.qryRegLookup.memberID)# };
			top.doRemoveReg(objParams);
		}
		function selectGLAccount() {
			$('##divRemoveRegForm').hide();
			top.$('##MCModalFooter').removeClass('d-flex').addClass('d-none');
			toggleGLASelectorGridArea(true);
			$('##divGLAA_back').removeClass('d-none');
		}
		function selectGLAccountResult(objGL) {
			if (objGL.thepathexpanded.length > 0) {
				$('##GLAccountPath').html(objGL.thepathexpanded + '&nbsp;');
				$('##GLAccountID').val(objGL.glaccountid);
			} else { 
				var msg = 'There was a problem selecting the GL Account.<br/>Try again; if the issue persists, contact MemberCentral for assistance.';
				$('##divGLerr').html(msg).show();
			}
			$('##divRemoveRegForm').show();
			toggleGLASelectorGridArea(false,true);
			$('##divGLAA_back').addClass('d-none');
		}
		function goBackGLAccount() {
			top.$('##MCModalFooter').removeClass('d-none').addClass('d-flex');
			$('##divRemoveRegForm').show();
			toggleGLASelectorGridArea(false,true);
			$('##divGLAA_back').addClass('d-none');
		}
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.remreghead)#">

<cfoutput>
<div id="divRemoveRegForm" class="px-3 mt-2">
	<div id="ev_remreg_err_div" class="alert alert-danger mb-3" style="display:none;"></div>
	<form name="frmRemoveReg" id="frmRemoveReg">
		
		<cfinclude template="dsp_AROptions.cfm">

		<h5 class="mt-3">Cancellation Fee</h5>
		<div>Charge a cancellation fee while removing this registration by entering the fee amount below:</div>
		<div class="d-flex align-items-center mt-2">
			<div>
				<div class="input-group input-group-sm">
					<div class="input-group-prepend">
						<span class="input-group-text">$</span>
					</div>
					<input type="text" name="cancellationFee" id="cancellationFee" value="" onblur="this.value=formatCurrency(this.value);" class="form-control form-control-sm">									
				</div>
			</div>
			<span class="mx-2">#local.defaultCurrencyType# to</span>
			<div>
				<input type="hidden" name="GLAccountID" id="GLAccountID" value="#val(arguments.event.getValue('GLAccountID'))#">
				<span id="GLAccountPath" class="font-weight-bold">#arguments.event.getValue('GLAccountPath')#</span>
				<a href="javascript:selectGLAccount();">Choose GL Account</a> &nbsp; 
				<div id="divGLerr" class="alert alert-danger" style="display:none;"></div>
			</div>
		</div>				
		<div class="d-flex align-items-center mt-2">
			<input type="checkbox" name="deallocUsingPaidAmt" id="deallocUsingPaidAmt" value="1" class="align-self-center" disabled="disabled">
			<label for="deallocUsingPaidAmt" class="align-self-center mb-0 ml-1">Apply deallocated payments to this cancellation fee, if applicable</label>
		</div>
	</form>
</div>
<div id="divRemoveRegLoading" class="mt-4" style="display:none;">
	<div class="mt-4">
		<div class="text-center">
			<div class="spinner-border" role="status"></div>
			<h4 class="mt-2">Removing Registrant...</h4>
		</div>
	</div>
</div>
#local.showGLSelector.data#
</cfoutput>