ALTER PROC dbo.queue_payInvoices_grabForProcessing
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @batchSize int, @queueTypeID int, @statusReady int, @statusGrabbed int;

	set @batchSize = 40;
	select @queueTypeID = queueTypeID from dbo.tblQueueTypes where queueType = 'payInvoices';
	select @statusReady = queueStatusID from dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'readyToProcess';
	select @statusGrabbed = queueStatusID from dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'grabbedForProcessing';

	IF OBJECT_ID('tempdb..#tmpPayments') IS NOT NULL 
		DROP TABLE #tmpPayments;
	IF OBJECT_ID('tempdb..#tmpInvStateZipForTax') IS NOT NULL 
		DROP TABLE #tmpInvStateZipForTax;
	IF OBJECT_ID('tempdb..#tmpReturn') IS NOT NULL 
		DROP TABLE #tmpReturn;
	CREATE TABLE #tmpPayments (itemID int, orgID int, merchantProfileID int, memberPaymentProfileID int);
	CREATE TABLE #tmpInvStateZipForTax (invoiceID int, stateIDForTax int, zipForTax varchar(25));

	-- dequeue in order of dateAdded. get @batchsize payments
	UPDATE qi WITH (UPDLOCK, READPAST)
	SET qi.statusID = @statusGrabbed,
		qi.dateUpdated = getdate()
		OUTPUT inserted.itemID, inserted.orgID, inserted.MPProfileID, inserted.memberPaymentProfileID 
		INTO #tmpPayments (itemID, orgID, merchantProfileID, memberPaymentProfileID)
	FROM dbo.queue_payInvoices as qi
	INNER JOIN (
		SELECT top(@BatchSize) qi2.itemID 
		from dbo.queue_payInvoices as qi2
		WHERE qi2.statusID = @statusReady
		ORDER BY qi2.dateAdded, qi2.itemID
	) as batch on batch.itemID = qi.itemID
	WHERE qi.statusID = @statusReady;

	-- return payment and invoice information
	-- left outer amount in case the invoice has been acted upon since it was added to the queue
	-- status of mp_profile doesnt matter here - if not active, payment will fail.
	select qid.itemID, o.orgID, qid.memberPaymentProfileID, qidd.invoiceid, 
		qidd.payProcessFee, qidd.processFeePercent, mpp.customerProfileID, 
		mpp.paymentProfileID, b.routingNumber, b.accountNumber, b.acctType, s.siteID, mp.gatewayID, g.gatewayType, mp.profileID, 
		m.activeMemberID as memberID, o.useBatches, s.sitename, mp.profileCode, 
		mp.processFeeDonationRenevueGLAccountID as processFeeRevGlAccountID, 
		mp.processFeeDonationRevTransDesc as processFeeRevTransDesc,
		cast(mpp.otherFields as varchar(4000)) as otherFields,
		mpp.surchargeEligible, mp.enableSurcharge, mp.surchargePercent, mp.surchargeRevenueGLAccountID, mp.processingFeeLabel,
		o.orgcode + membercentral.dbo.fn_tr_padInvoiceNumber(i.invoiceNumber) as invoiceNumber,
		sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount-it.cache_pendingPaymentAllocatedAmount) as InvDueAmount,
		cast(0 as decimal(18,2)) as additionalPaymentFee, cast(0 as decimal(18,2)) as paymentAmount, cast(0 as tinyint) as paymentFeeTypeID
	into #tmpReturn
	from #tmpPayments as qid
	inner join dbo.queue_payInvoicesDetail as qidd on qidd.itemID = qid.itemID
	inner join membercentral.dbo.tr_invoices as i on i.invoiceID = qidd.invoiceID
	inner join membercentral.dbo.ams_memberPaymentProfiles as mpp on mpp.payProfileID = qid.memberPaymentProfileID
	inner join membercentral.dbo.mp_profiles as mp on mp.profileID = qid.merchantProfileID
	inner join membercentral.dbo.mp_gateways as g on g.gatewayID = mp.gatewayID
	inner join membercentral.dbo.sites as s on s.siteID = mp.siteID
	inner join membercentral.dbo.organizations as o on o.orgID = s.orgID
	inner join membercentral.dbo.ams_members as m on m.memberid = mpp.memberid
	left outer join membercentral.dbo.tr_invoiceTransactions as it on it.orgID = o.orgID and it.invoiceID = i.invoiceid
	left outer join membercentral.dbo.tr_bankAccounts as b on b.MPPPayProfileID = mpp.payProfileID
	group by qid.itemID, o.orgID, qid.memberPaymentProfileID, qidd.invoiceid, 
		qidd.payProcessFee, qidd.processFeePercent, mpp.customerProfileID, 
		mpp.paymentProfileID, b.routingNumber, b.accountNumber, b.acctType, s.siteID, mp.gatewayID, g.gatewayType, mp.profileID,
		m.activeMemberID, o.useBatches, s.sitename, mp.profileCode, 
		mp.processFeeDonationRenevueGLAccountID, mp.processFeeDonationRevTransDesc, 
		cast(mpp.otherFields as varchar(4000)), o.orgcode + membercentral.dbo.fn_tr_padInvoiceNumber(i.invoiceNumber),
		mpp.surchargeEligible, mp.enableSurcharge, mp.surchargePercent, mp.surchargeRevenueGLAccountID, mp.processingFeeLabel
	order by qid.itemID;

	-- processing fees
	UPDATE #tmpReturn
	SET additionalPaymentFee = ROUND(InvDueAmount * processFeePercent / 100,2),
		paymentFeeTypeID = 1
	WHERE payProcessFee = 1
	AND processFeePercent > 0
	AND processFeeRevGlAccountID IS NOT NULL;

	-- surcharge
	UPDATE #tmpReturn
	SET additionalPaymentFee = ROUND(InvDueAmount * surchargePercent / 100,2),
		paymentFeeTypeID = 2
	WHERE enableSurcharge = 1
	AND surchargePercent > 0
	AND surchargeRevenueGLAccountID IS NOT NULL
	AND surchargeEligible = 1;


	-- get payment amounts in the final total
	update tmp
	set tmp.paymentAmount = innersum.paymentAmount
	from #tmpReturn as tmp
	inner join (
		select itemID, sum(InvDueAmount + isnull(additionalPaymentFee,0)) as paymentAmount
		from #tmpReturn
		group by itemID
	) as innersum on innersum.itemID = tmp.itemID;

	-- update queue_payInvoices for the notification report
	update qid
	set qid.paymentAmount = innersum.paymentAmount
	from dbo.queue_payInvoices as qid
	inner join (
		select distinct itemID, paymentAmount
		from #tmpReturn
	) as innersum on innersum.itemID = qid.itemID;

	update qidd
	set qidd.invoiceDueAmount = tmp.InvDueAmount,
		qidd.additionalPaymentFee = tmp.additionalPaymentFee
	from dbo.queue_payInvoicesDetail as qidd
	inner join #tmpReturn as tmp on tmp.itemID = qidd.itemID and tmp.invoiceID = qidd.invoiceID;

	INSERT INTO #tmpInvStateZipForTax (invoiceID, stateIDForTax, zipForTax)
	SELECT tmp.invoiceID, MIN(ts.stateIDForTax), MIN(ts.zipForTax)
	FROM #tmpReturn AS tmp
	INNER JOIN membercentral.dbo.tr_invoiceTransactions AS it ON it.orgID = tmp.orgID
		AND it.invoiceID = tmp.invoiceID
	INNER JOIN membercentral.dbo.tr_transactionSales AS ts ON ts.orgID = tmp.orgID
		AND ts.transactionID = it.transactionID
	GROUP BY tmp.invoiceID;

	-- final data
	select tmp.itemID, tmp.orgID, tmp.siteID, tmp.memberPaymentProfileID, tmp.invoiceid, tmp.customerProfileID, 
		tmp.paymentProfileID, tmp.routingNumber, tmp.accountNumber, tmp.acctType, tmp.gatewayID, tmp.gatewayType, 
		tmp.profileID, tmp.memberID, tmp.useBatches, tmp.sitename, tmp.profileCode, tmp.otherFields, tmp.invoiceNumber, 
		tmp.InvDueAmount, tmp.additionalPaymentFee, tmp.paymentAmount, tmp.processFeePercent, tmp.processFeeRevGlAccountID, 
		tmp.processFeeRevTransDesc,tmp.enableSurcharge, tmp.surchargePercent, tmp.surchargeRevenueGLAccountID, 
		tmp.paymentFeeTypeID, sz.stateIDForTax, sz.zipForTax, tmp.surchargeEligible, 
		case tmp.paymentFeeTypeID when 1 then tmp.processingFeeLabel when 2 then 'Surcharge' else null end as additionalFeeLabel
	from #tmpReturn AS tmp
	left outer join #tmpInvStateZipForTax AS sz ON sz.invoiceID = tmp.invoiceID
	order by tmp.itemID;

	IF OBJECT_ID('tempdb..#tmpPayments') IS NOT NULL 
		DROP TABLE #tmpPayments;
	IF OBJECT_ID('tempdb..#tmpInvStateZipForTax') IS NOT NULL 
		DROP TABLE #tmpInvStateZipForTax;
	IF OBJECT_ID('tempdb..#tmpReturn') IS NOT NULL 
		DROP TABLE #tmpReturn;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
