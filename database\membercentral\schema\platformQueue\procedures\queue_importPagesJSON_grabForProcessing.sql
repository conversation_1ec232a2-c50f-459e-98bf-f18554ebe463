ALTER PROC dbo.queue_importPagesJSON_grabForProcessing
@batchSize int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @statusReady int, @statusGrabbed int, @totalItemCount int;
	SELECT @queueTypeID = queueTypeID FROM dbo.tblQueueTypes WHERE queueType = 'importPagesJSON';
	SELECT @statusReady = queuestatusID FROM dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID and queueStatus = 'ReadyToProcess';
	SELECT @statusGrabbed = queuestatusID FROM dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID and queueStatus = 'GrabbedForProcessing';

	IF OBJECT_ID('tempdb..#tmpImportPagesJSON') IS NOT NULL
		DROP TABLE #tmpImportPagesJSON;
	CREATE TABLE #tmpImportPagesJSON (itemID int);

	-- dequeue in order of dateAdded. get @batchsize entries
	UPDATE qid WITH (UPDLOCK, READPAST)
	SET qid.statusID = @statusGrabbed,
		qid.dateUpdated = getdate()
		OUTPUT inserted.itemID
		INTO #tmpImportPagesJSON
	FROM dbo.queue_importPagesJSON AS qid
	INNER JOIN (
		SELECT TOP (@batchSize) itemID 
		FROM dbo.queue_importPagesJSON
		WHERE statusID = @statusReady
		ORDER BY dateAdded, itemID
	) AS batch ON batch.itemID = qid.itemID
	WHERE qid.statusID = @statusReady;

	SELECT @totalItemCount = COUNT(itemID)
	FROM #tmpImportPagesJSON;

	SELECT DISTINCT qid.itemID, qid.itemGroupUID, qid.siteID, qid.submittedMemberID, qid.pageJSON, qid.errorMessage, @totalItemCount as totalItemCount
	FROM #tmpImportPagesJSON AS tmp
	INNER JOIN dbo.queue_importPagesJSON AS qid ON qid.itemID = tmp.itemID
	ORDER BY qid.itemID;

	IF OBJECT_ID('tempdb..#tmpImportPagesJSON') IS NOT NULL
		DROP TABLE #tmpImportPagesJSON;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
