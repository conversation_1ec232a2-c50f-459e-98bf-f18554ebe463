ALTER PROC dbo.cp_importContributors_validate
@siteID int,
@programID int,
@siteResourceID int,
@importResult xml OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	set @importResult = null;

	DECLARE @orgID int, @colList varchar(max), @minColID int, @mincol varchar(255), @good bit, @reqMsg varchar(800), 
		@isRequired bit, @customTypeID int, @offerAmount bit, @offerQTY bit, @dataTypeCode varchar(12), 
		@displayTypeCode varchar(12), @dynSQL nvarchar(max), @queueTypeID int;
	DECLARE @tblDistCols TABLE (columnName varchar(255) PRIMARY KEY);
	DECLARE @tblCustomCols TABLE (fieldID int  PRIMARY KEY, columnName varchar(128), isRequired bit, requiredMsg varchar(800), dataTypeCode varchar(12), displayTypeCode varchar(12));

	select @orgID = dbo.fn_getOrgIDFromSiteID(@siteID);
	select @queueTypeID = queueTypeID from platformQueue.dbo.tblQueueTypes where queueType = 'importContributions';

	BEGIN TRY
		-- no blank membernumbers
		update #cp_ContributorImport set MemberNumber = '' where MemberNumber is null;
		
		INSERT INTO #tblImportErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' is missing MemberNumber.'
		FROM #cp_ContributorImport
		WHERE MemberNumber = ''
		ORDER BY rowID;
			IF @@ROWCOUNT > 0 GOTO on_done;

		-- match on membernumber
		UPDATE tmp
		SET tmp.MCMemberID = m.memberID
		FROM #cp_ContributorImport as tmp
		INNER JOIN dbo.ams_members as m on m.orgID = @orgID
		AND m.membernumber = tmp.memberNumber
		AND m.memberID = m.activeMemberID
		AND m.status in ('A','I');

		-- invalid memberumber
		INSERT INTO #tblImportErrors (msg)
		SELECT TOP 100 PERCENT 'MemberNumber "' + cast(isnull(MemberNumber,'') as varchar(50)) + '" doesn''t match any valid member accounts.'
		FROM #cp_ContributorImport
		WHERE MCMemberID is null
		ORDER BY rowID;
			IF @@ROWCOUNT > 0 GOTO on_done;

		-- validate campaign names
		update #cp_ContributorImport set CampaignName = '' where CampaignName is null;

		-- match on campaignName
		UPDATE tmp
		SET tmp.campaignID = cpc.campaignID
		FROM #cp_ContributorImport as tmp
		INNER JOIN dbo.cp_campaigns as cpc on cpc.programID = @programID
		AND cpc.campaignName = tmp.CampaignName;

		-- invalid campaignName
		INSERT INTO #tblImportErrors (msg)
		SELECT TOP 100 PERCENT 'CampaignName "' + cast(isnull(CampaignName,'') as varchar(50)) + '" doesn''t match any valid campaign names under this program.'
		FROM #cp_ContributorImport
		WHERE campaignID is null
		AND CampaignName <> ''
		ORDER BY rowID;
			IF @@ROWCOUNT > 0 GOTO on_done;

		-- distribution columns
		insert into @tblDistCols (columnName)
		select distCode
		from dbo.cp_distributions
		where programID = @programID;

		select @mincol = min(columnName) from @tblDistCols;
		while @mincol is not null begin
			set @good = 1;
			set @dynSQL = '
				set @good = 1
				BEGIN TRY
					UPDATE #cp_ContributorImport SET ' + quotename(@mincol) + ' = replace(' + quotename(@mincol) + ','','','''');
					UPDATE #cp_ContributorImport SET ' + quotename(@mincol) + ' = null where ' + quotename(@mincol) + ' = '''';
					ALTER TABLE #cp_ContributorImport ALTER COLUMN ' + quotename(@mincol) + ' decimal(18,2) not null;
				END TRY
				BEGIN CATCH
					set @good = 0
				END CATCH';
				exec sp_executesql @dynSQL, N'@good bit output', @good output;
			IF @good = 0 
				INSERT INTO #tblImportErrors (msg)
				VALUES ('The column ' + @mincol + ' contains invalid amount.');

			select @mincol = min(columnName) from @tblDistCols where columnName > @mincol;
		end

		if exists (select 1 from #tblImportErrors)
			GOTO on_done;

		set @colList = null;
		select @colList = COALESCE(@colList + '+', '') + quotename(columnName) from @tblDistCols;
		if @colList is not null begin
			set @dynSQL = '
				update #cp_ContributorImport
				set installmentAmount = ' + @colList + ' ';
			exec(@dynSQL);

			-- match on rate
			UPDATE tmp
			set tmp.rateID = r.rateID
			from #cp_ContributorImport as tmp
			inner join dbo.cp_rates as r on r.rateName = tmp.RateName
			where r.programID = @programID;

			-- invalid rate
			INSERT INTO #tblImportErrors (msg)
			SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' Rate Name "' + cast(isnull(RateName,'') as varchar(100)) + '" doesn''t match any program rates.'
			FROM #cp_ContributorImport
			WHERE rateID is null
			ORDER BY rowID;
				IF @@ROWCOUNT > 0 GOTO on_done;
		end

		-- match on frequency
		UPDATE tmp
		set tmp.frequencyID = f.frequencyID
		from #cp_ContributorImport as tmp
		inner join dbo.cp_frequencies as f on f.frequency = tmp.Frequency
		inner join dbo.cp_programFrequencies as pf on pf.frequencyID = f.frequencyID
		where pf.programID = @programID;

		-- invalid frequency
		INSERT INTO #tblImportErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' Frequency "' + cast(isnull(Frequency,'') as varchar(100)) + '" doesn''t match any program frequencies.'
		FROM #cp_ContributorImport
		WHERE frequencyID is null
		ORDER BY rowID;
			IF @@ROWCOUNT > 0 GOTO on_done;

		-- match on status
		UPDATE tmp
		set tmp.CPStatusID = cps.statusID
		from #cp_ContributorImport as tmp
		inner join dbo.cp_statuses as cps on cps.statusName = tmp.[status];

		-- invalid status
		INSERT INTO #tblImportErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' Status "' + cast(isnull([Status],'') as varchar(100)) + '" doesn''t match any status.'
		FROM #cp_ContributorImport
		WHERE CPStatusID is null
		ORDER BY rowID;
			IF @@ROWCOUNT > 0 GOTO on_done;

		-- start date
		BEGIN TRY
			UPDATE #cp_ContributorImport SET StartDate = null WHERE StartDate = '';
			ALTER TABLE #cp_ContributorImport ALTER COLUMN StartDate date null;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblImportErrors (msg) VALUES ('Start Date contains invalid dates.');
			GOTO on_done;
		END CATCH

		-- end date
		BEGIN TRY
			UPDATE #cp_ContributorImport SET EndDate = null WHERE EndDate = '';
			ALTER TABLE #cp_ContributorImport ALTER COLUMN EndDate date null;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblImportErrors (msg) VALUES ('End Date contains invalid dates.');
			GOTO on_done;
		END CATCH

		-- end date if defined must be greater than start date
		INSERT INTO #tblImportErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' End Date "' + convert(varchar, endDate, 103) + '" must be greater than start date.'
		FROM #cp_ContributorImport
		WHERE endDate is not null
		AND startDate > endDate
		ORDER BY rowID;
			IF @@ROWCOUNT > 0 GOTO on_done;

		BEGIN TRY
			UPDATE #cp_ContributorImport SET isAnonymous = '1' where isAnonymous IN ('TRUE','YES');
			UPDATE #cp_ContributorImport SET isAnonymous = '0' where isAnonymous IN ('FALSE','NO','');
			IF EXISTS (select 1 from #cp_ContributorImport where isAnonymous not in ('1','0'))
				RAISERROR('bad values',16,1);
			ALTER TABLE #cp_ContributorImport ALTER COLUMN isAnonymous bit null;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblImportErrors (msg) VALUES ('Is Anonymous column contains invalid boolean values.');
			GOTO on_done;
		END CATCH

		-- amount already paid
		BEGIN TRY
			UPDATE #cp_ContributorImport SET amountAlreadyPaid = null WHERE amountAlreadyPaid = '';
			ALTER TABLE #cp_ContributorImport ALTER COLUMN amountAlreadyPaid decimal(14,2) null;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblImportErrors (msg) VALUES ('Amount Already Paid column contains invalid amounts.');
			GOTO on_done;
		END CATCH

		-- validate program and cross program custom fields
		set @minColID = null;

		declare @usageID int;
		select @usageID = dbo.fn_cf_getUsageID('ContributionAdmin','Role',NULL);

		insert into @tblCustomCols (fieldID, columnName, isRequired, requiredMsg, dataTypeCode, displayTypeCode)
		select f.fieldID, f.fieldReference, f.isRequired, f.requiredMsg, ft.dataTypeCode, ft.displayTypeCode
		from dbo.cf_fields as f 
		inner join dbo.cf_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID
		inner join dbo.cf_fieldUsages as fu on fu.usageID = f.usageID 
		inner join dbo.cp_programs as cp on cp.programID = f.detailID and f.controllingSiteResourceID = cp.siteResourceID
		where cp.programID = @programID
		and f.isActive = 1
		and len(f.fieldReference) > 0
		and ft.displayTypeCode <> 'LABEL'
		and 1 = case 
				when ft.displayTypeCode in ('SELECT','RADIO','CHECKBOX') then case when exists(select 1 from dbo.cf_fieldValues where fieldID = f.fieldID) then 1 else 0 end 
				else 1 end
			union all
		select f.fieldID, f.fieldReference, f.isRequired, f.requiredMsg, ft.dataTypeCode, ft.displayTypeCode
		from dbo.cf_fields as f
		inner join dbo.cf_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID
		where f.controllingSiteResourceID = @siteResourceID
		and f.usageID = @usageID
		and f.isActive = 1
		and len(f.fieldReference) > 0
		and ft.displayTypeCode <> 'LABEL'
		and 1 = case 
				when ft.displayTypeCode in ('SELECT','RADIO','CHECKBOX') then case when exists(select 1 from dbo.cf_fieldValues where fieldID = f.fieldID) then 1 else 0 end 
				else 1 end;

		select @minColID = min(fieldID) from @tblCustomCols;
		while @minColID is not null begin
			select @mincol=null, @isRequired=null, @reqMsg = null, @dataTypeCode=null, @displayTypeCode=null;

			select @mincol=columnName, @isRequired=isRequired, @dataTypeCode=dataTypeCode, @displayTypeCode=displayTypeCode
			from @tblCustomCols 
			where fieldID = @minColID;

			set @reqMsg = '"' + @mincol + '" data is missing.'

			-- required custom column
			IF @isRequired = 1 BEGIN
				set @dynSQL = '
					SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' Column ' + @reqMsg + ' ''
					FROM #cp_ContributorImport
					WHERE len(ltrim(rtrim(isnull(' + quotename(@mincol) + ','''')))) = 0
					ORDER BY rowID';

				INSERT INTO #tblImportErrors (msg)
				EXEC(@dynSQL)
					IF @@ROWCOUNT > 0 GOTO on_done;
			END

			IF @displayTypeCode in ('SELECT','RADIO','CHECKBOX') BEGIN
				set @reqMsg = 'Column "' + @mincol + '" option "';
				
				-- bit columns support only select box and radio buttons
				IF @dataTypeCode = 'BIT' BEGIN 
					set @good = 1;
					set @dynSQL = '
						set @good = 1
						BEGIN TRY
							UPDATE #cp_ContributorImport SET ' + quotename(@mincol) + ' = ''1'' where ' + quotename(@mincol) + ' IN (''TRUE'',''YES'');
							UPDATE #cp_ContributorImport SET ' + quotename(@mincol) + ' = ''0'' where ' + quotename(@mincol) + ' IN (''FALSE'',''NO'');
							UPDATE #cp_ContributorImport SET ' + quotename(@mincol) + ' = null where ' + quotename(@mincol) + ' = '''';
							IF EXISTS (select 1 from #cp_ContributorImport where ' + quotename(@mincol) + ' is not null and ' + quotename(@mincol) + ' not in (''1'',''0''))
								RAISERROR(''invalid values'',16,1);
							ALTER TABLE #cp_ContributorImport ALTER COLUMN ' + quotename(@mincol) + ' bit null;
						END TRY
						BEGIN CATCH
							set @good = 0
						END CATCH';
						exec sp_executesql @dynSQL, N'@good bit output', @good output;
					IF @good = 0 BEGIN
						INSERT INTO #tblImportErrors (msg)
						VALUES ('The column ' + @mincol + ' contains invalid boolean values.');	

						GOTO on_done;
					END
				END

				set @dynSQL = '
					SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ' + @reqMsg + ''' + tbl.listitem  + ''" is invalid.''
					from #cp_ContributorImport
					cross apply dbo.fn_varcharListToTableInline(' + quotename(@mincol) + ',''|'') as tbl
					inner join dbo.cf_fields as f on f.fieldID = ' + cast(@minColID as varchar(10)) + ' 
					inner join dbo.cf_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID
					left outer join dbo.cf_fieldValues as fv on fv.fieldID = f.fieldID
						and case when ft.dataTypeCode  = ''DATE'' then cast(cast(tbl.listitem as date) as varchar(15)) else tbl.listitem end
							 = case when ft.dataTypeCode = ''STRING'' then cast(fv.valueString as varchar(max))
									 when ft.dataTypeCode = ''DECIMAL2'' then cast(fv.valueDecimal2 as varchar(15))
									 when ft.dataTypeCode = ''INTEGER'' then cast(fv.valueInteger as varchar(10))
									 when ft.dataTypeCode = ''BIT'' then cast(fv.valueBit as varchar(1))
									 when ft.dataTypeCode = ''DATE'' then cast(fv.valueDate as varchar(15))
								else '''' end
					where fv.valueID is null
					and len(ltrim(rtrim(isnull(' + quotename(@mincol) + ','''')))) > 0
					ORDER BY rowID';

				INSERT INTO #tblImportErrors (msg)
				EXEC(@dynSQL)
					IF @@ROWCOUNT > 0 GOTO on_done;
			END

			IF @dataTypeCode ='INTEGER' and @displayTypeCode = 'TEXTBOX' BEGIN 
				set @good = 1;
				set @dynSQL = '
					set @good = 1
					BEGIN TRY
						UPDATE #cp_ContributorImport SET ' + quotename(@mincol) + ' = replace(' + quotename(@mincol) + ','','','''');
						UPDATE #cp_ContributorImport SET ' + quotename(@mincol) + ' = null where ' + quotename(@mincol) + ' = '''';
						ALTER TABLE #cp_ContributorImport ALTER COLUMN ' + quotename(@mincol) + ' int null;
					END TRY
					BEGIN CATCH
						set @good = 0
					END CATCH';
					exec sp_executesql @dynSQL, N'@good bit output', @good output;
				IF @good = 0
					INSERT INTO #tblImportErrors (msg)
					VALUES ('The column "' + @mincol + '" contains invalid whole numbers.');
			END

			IF @dataTypeCode ='DECIMAL2' and @displayTypeCode = 'TEXTBOX' BEGIN 
				set @good = 1;
				set @dynSQL = '
					set @good = 1
					BEGIN TRY
						UPDATE #cp_ContributorImport SET ' + quotename(@mincol) + ' = replace(' + quotename(@mincol) + ','','','''');
						UPDATE #cp_ContributorImport SET ' + quotename(@mincol) + ' = null where ' + quotename(@mincol) + ' = '''';
						ALTER TABLE #cp_ContributorImport ALTER COLUMN ' + quotename(@mincol) + ' decimal(14,2) null;
					END TRY
					BEGIN CATCH
						set @good = 0
					END CATCH';
					exec sp_executesql @dynSQL, N'@good bit output', @good output;
				IF @good = 0
					INSERT INTO #tblImportErrors (msg)
					VALUES ('The column "' + @mincol + '" contains invalid decimal values.');
			END

			IF @dataTypeCode ='DATE' and @displayTypeCode = 'DATE' BEGIN 
				set @good = 1;
				set @dynSQL = '
					set @good = 1
					BEGIN TRY
						UPDATE #cp_ContributorImport SET ' + quotename(@mincol) + ' = replace(' + quotename(@mincol) + ','','','''');
						UPDATE #cp_ContributorImport SET ' + quotename(@mincol) + ' = null where ' + quotename(@mincol) + ' = '''';
						ALTER TABLE #cp_ContributorImport ALTER COLUMN ' + quotename(@mincol) + ' date null;
					END TRY
					BEGIN CATCH
						set @good = 0
					END CATCH';
					exec sp_executesql @dynSQL, N'@good bit output', @good output;
				IF @good = 0
					INSERT INTO #tblImportErrors (msg)
					VALUES ('The column "' + @mincol + '" contains invalid dates.');
			END

			if exists (select 1 from #tblImportErrors)
				GOTO on_done;

			select @minColID = min(fieldID) from @tblCustomCols where fieldID > @minColID;
		end

	END TRY
	BEGIN CATCH
		INSERT INTO #tblImportErrors (msg) VALUES ('Unable to validate import data for import.');
		INSERT INTO #tblImportErrors (msg) VALUES (left(error_message(),300));
		GOTO on_done;
	END CATCH
	
	-- ensure distribution codes are included in the queue tables
	BEGIN TRY
		insert into platformQueue.dbo.tblQueueTypeDataColumns (queueTypeID, columnName, dataTypeID) 
		select @queueTypeID, distCode, 2 as dataTypeID
		from dbo.cp_distributions
		where programID = @programID
			except 
		select queueTypeID, columnName, dataTypeID
		from platformQueue.dbo.tblQueueTypeDataColumns
		where queueTypeID = @queueTypeID;
	END TRY
	BEGIN CATCH
		INSERT INTO #tblImportErrors (msg) VALUES ('Unable to add distributions to the queue tables.');
		INSERT INTO #tblImportErrors (msg) VALUES (left(error_message(),300));
		GOTO on_done;
	END CATCH

	-- ensure custom columns are included in the queue tables
	BEGIN TRY
		insert into platformQueue.dbo.tblQueueTypeDataColumns (queueTypeID, columnName, dataTypeID) 
		select @queueTypeID, tmp.columnName, tmp.dataTypeID
		from (
			select tmp.columnName, dt.dataTypeID
			from @tblCustomCols as tmp
			inner join platformQueue.dbo.tblQueueTypesDataColumnDataTypes as dt on dt.dataTypeCode = tmp.dataTypeCode
		) as tmp
			except 
		select queueTypeID, columnName, dataTypeID
		from platformQueue.dbo.tblQueueTypeDataColumns
		where queueTypeID = @queueTypeID;
	END TRY
	BEGIN CATCH
		INSERT INTO #tblImportErrors (msg) VALUES ('Unable to add program fields to the queue tables.');
		INSERT INTO #tblImportErrors (msg) VALUES (left(error_message(),300));
		GOTO on_done;
	END CATCH

	
	-- ************************
	-- generate result xml file 
	-- ************************
	on_done:
	select @importResult = (
		select getdate() as "@date",
			isnull((select top 100 PERCENT dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg"
			from #tblImportErrors
			order by rowid
			FOR XML path('error'), root('errors'), type),'<errors/>')
		for xml path('import'), TYPE);
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
