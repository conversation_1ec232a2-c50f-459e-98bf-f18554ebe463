ALTER PROC dbo.queue_NJFirmSubStatements_grabForNotification
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @statusReady int, @statusGrabbed int, @queueTypeID int, @siteID int, @orgID int;
	select @queueTypeID = queueTypeID from dbo.tblQueueTypes where queueType = 'NJFirmSubStatements';
	select @statusReady = queueStatusID from dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'readyToNotify';
	select @statusGrabbed = queueStatusID from dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'grabbedForNotifying';

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	CREATE TABLE #tmpNotify (itemGroupUID uniqueidentifier);

	SELECT @siteID = siteID, @orgID = orgID
	FROM membercentral.dbo.sites
	WHERE siteCode = 'NJ';

	-- dequeue. 
	; WITH itemGroupUIDs AS (
		select distinct itemGroupUID
		from dbo.queue_NJFirmSubStatements
		where statusID = @statusReady
			except
		select distinct itemGroupUID
		from dbo.queue_NJFirmSubStatements
		where statusID <> @statusReady
	)
	UPDATE qi
	SET qi.statusID = @statusGrabbed,
		qi.dateUpdated = GETDATE()
		OUTPUT itemGroupUIDs.itemGroupUID
		INTO #tmpNotify
	FROM dbo.queue_NJFirmSubStatements as qi
	INNER JOIN itemGroupUIDs on itemGroupUIDs.itemGroupUID = qi.itemGroupUID
	where qi.statusID = @statusReady;

	-- return itemGroupUIDs that can be marked as done
	SELECT DISTINCT tmpN.itemGroupUID, me.email as reportEmail, s.siteID, s.siteName, s.siteCode, o.orgID, o.orgCode,
		mActive.memberID, mActive.firstname, mActive.lastname, mActive.memberNumber
	FROM (SELECT DISTINCT itemGroupUID FROM #tmpNotify) as tmpN
	INNER JOIN dbo.queue_NJFirmSubStatements as qid on qid.itemGroupUID = tmpN.itemGroupUID
	INNER JOIN membercentral.dbo.sites as s on s.siteID = @siteID
	INNER JOIN membercentral.dbo.organizations as o on o.orgID = s.orgID
	INNER JOIN membercentral.dbo.ams_members as m on m.orgID in (@orgID,1)
		and m.memberID = qid.recordedByMemberID
	INNER JOIN membercentral.dbo.ams_members as mActive on mActive.orgID = m.orgID and mActive.memberID = m.activeMemberID
	INNER JOIN membercentral.dbo.ams_memberEmailTags as metag on metag.orgID = m.orgID and metag.memberID = mActive.memberID
	INNER JOIN membercentral.dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = m.orgID
		and metagt.emailTagTypeID = metag.emailTagTypeID
		and metagt.emailTagType = 'Primary'
	INNER JOIN membercentral.dbo.ams_memberEmails as me on me.orgID = m.orgID
		and me.memberID = metag.memberID
		and me.emailTypeID = metag.emailTypeID
	ORDER BY tmpN.itemGroupUID;

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL 
		DROP TABLE #tmpNotify;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
