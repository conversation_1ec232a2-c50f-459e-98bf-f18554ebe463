<cfcomponent extends="model.admin.reports.report" output="no">
	<cfset variables.defaultEvent = 'controller'>
	<cfset variables.runformats = [ 'screen','pdf' ]>
	<cfset variables.AllowScheduling = false>

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
		var local = structNew();

		// call common report controller
		reportController(event=arguments.event);

		this.appInstanceSettings = super.getInstanceSettings(this.appInstanceID);
		
		local.arrNodes = XMLSearch(application.adminNavigationXML,"/navitems/navitem[@navName='Events']");
		this.showEventsTab = structKeyExists(session.mcastruct.strNavKeys, local.arrNodes[1].xmlAttributes.navKey);
		
		local.methodToRun = this[arguments.event.getValue('mca_ta')];
		return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>		

	<cffunction name="showReport" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		
		<cfif arguments.event.getValue('qryReportInfo').reportID gt 0>

			<!--- hide icons if unable to change report --->
			<cfset local.canEditReport = hasReportEditRights(event=arguments.event)>

			<!--- SUBS Widget --->
			<cfset local.strSubsWidgetData = { title='', description='Select the subscriptions you would like to include in this report.',
				gridext="#this.siteResourceID#_1", gridwidth=620, gridheight=100, gridClassList='mb-0 stepDIV', initGridOnLoad=true, 
				controllingSRID=this.siteResourceID, reportID=arguments.event.getValue('qryReportInfo').reportID, filterMode=2, excludeSteps='', showIcons=1 }>
			<cfif NOT local.canEditReport>
				<cfset local.strSubsWidgetData.showIcons = 0>
			</cfif>
			<cfset local.strSubscriptionWidget = createObject("component","model.admin.common.modules.subscriptionWidget.subscriptionWidget").renderWidget(strWidgetData=local.strSubsWidgetData)>

			<!--- GL Widget --->
			<cfset local.objGLAccountWidget = createObject("component","model.admin.common.modules.glAccountWidget.glAccountWidget")>
			<cfset local.arrGLAccountWidgetData = [ 
				{ title='', description='Select the revenue accounts you''d like to include in this report.',
				gridext="#this.siteResourceID#_1", gridwidth=620, gridheight=100, gridClassList='mb-0 stepDIV', initGridOnLoad=true,
				controllingSRID=this.siteResourceID, reportID=arguments.event.getValue('qryReportInfo').reportID,
				glatid=3, extraNodeName='givegllist', showIcons=1, widgetMode='report' }, 
				{ title='', description='Select the revenue accounts you''d like to include in this report.',
				gridext="#this.siteResourceID#_2", gridwidth=620, gridheight=100, gridClassList='mb-0 stepDIV', initGridOnLoad=true,
				controllingSRID=this.siteResourceID, reportID=arguments.event.getValue('qryReportInfo').reportID,
				glatid=3, extraNodeName='paygllist', showIcons=1, widgetMode='report' } 
			]>
			<cfif NOT local.canEditReport>
				<cfset local.arrGLAccountWidgetData[1].showIcons = 0>
				<cfset local.arrGLAccountWidgetData[2].showIcons = 0>
			</cfif>
			<cfset local.strGivHistGLAccountWidget = local.objGLAccountWidget.renderWidget(strWidgetData=local.arrGLAccountWidgetData[1])>
			<cfset local.strPmtSummGLAccountWidget = local.objGLAccountWidget.renderWidget(strWidgetData=local.arrGLAccountWidgetData[2])>

			<!--- Event Widget --->
			<cfset local.strEventWidgetData = { title='Event History', description='Select the events you''d like to include in this report.',
				gridext="#this.siteResourceID#_1", gridClassList='mb-0 stepDIV', 
				initGridOnLoad=true, controllingSRID=this.siteResourceID, reportID=arguments.event.getValue('qryReportInfo').reportID,
				filterMode=2, showIcons=1 }>
			<cfif NOT local.canEditReport>
				<cfset local.strEventWidgetData.showIcons = 0>
			</cfif>
			<cfset local.strEventWidget = createObject("component","model.admin.common.modules.eventWidget.eventWidget").renderWidget(strWidgetData=local.strEventWidgetData)>

			<!--- mh widgets --->
			<cfset local.objMHWidget = createObject("component","model.admin.common.modules.memberHistoryWidget.memberHistoryWidget")>
			<cfset local.mhTypeList = 'Member History,Relationships,Notes'>
			<cfset local.strMHWidget = structNew()>
			<cfif arguments.event.getValue('mc_siteInfo.sf_relationships') eq 1>
				<cfset local.typeIDList = "1,2,3">
			<cfelse>
				<cfset local.typeIDList = "1,3">
			</cfif>
			<cfloop list="#local.typeIDList#" index="local.typeID">
				<cfset local.strMHWidgetData = { title=listGetAt(local.mhTypeList,local.typeID), description='Select the #listGetAt(local.mhTypeList,local.typeID)# you''d like to include in this report.',
					gridext="#this.siteResourceID#_#local.typeID#", gridClassList='mb-5 stepDIV', 
					initGridOnLoad=true, controllingSRID=this.siteResourceID, reportID=arguments.event.getValue('qryReportInfo').reportID,
					typeID=local.typeID, showIcons=1 }>
				<cfif NOT local.canEditReport>
					<cfset local.strMHWidgetData.showIcons = 0>
				</cfif>
				<cfset local.strMHWidget[local.typeID] = local.objMHWidget.renderWidget(strWidgetData=local.strMHWidgetData)>
			</cfloop>

			<cfsavecontent variable="local.dataHead">
				<cfoutput>
				#local.strSubscriptionWidget.js#
				#local.strGivHistGLAccountWidget.js#
				#local.strPmtSummGLAccountWidget.js#
				#local.strEventWidget.js#
				<cfloop list="#local.typeIDList#" index="local.typeID">
					#local.strMHWidget[local.typeID].js#
				</cfloop>

				<script language="javascript">
					function forceFilter() {
						var feResult = false;

						var forceMemberResult = function(s) {
							if (s.success && s.success.toLowerCase() == 'true' && s.filtercount > 0) feResult=true; 
							else { rptShowAlert('This report requires you to define one or more filters in the Member Filter.'); feResult=false; }
						};
						var forceGiveGLResult = function(s) {
							if (s.success && s.success.toLowerCase() == 'true' && s.glcount > 0) {
								if ($('##frmgivinghistoryfrom').val() == '' || $('##frmgivinghistoryto').val() == '') {
									rptShowAlert('Select a date range in the Giving History section.'); 
									feResult=false; 
								} else 	if ($('##frmgivinghistoryfrom').val() != '' && $('##frmgivinghistoryto').val() != '') {
									var start = moment($('##frmgivinghistoryfrom').val());
									var end = moment($('##frmgivinghistoryto').val());
									var dayDiff = end.diff(start, "day");

									if ( dayDiff > 365){
										if (!confirm("You have selected a date range greater than 365 days for Giving History. This can make comparing accounting periods difficult.")){
											feResult=false;
											$("##btnReportBarpdf").attr("disabled", false);
										}
									}
								} else {
									feResult=true; 
								}
							}
						};
						var forcePayGLResult = function(s) {
							if (s.success && s.success.toLowerCase() == 'true' && s.glcount > 0) {
								if ($('##frmpaymentsummaryfrom').val() == '' || $('##frmpaymentsummaryto').val() == '') {
									rptShowAlert('Select a date range in the Payment Summary section.'); 
									feResult=false; 
								} else {
									feResult=true; 
								}
							}
						};

						var objParams = { rptId:#val(arguments.event.getValue('qryReportInfo').reportID)#, rptTT:'#arguments.event.getValue('mca_tt')#', csrID:#this.siteResourceID#, rvID:$('##useRuleVersionID_#val(arguments.event.getValue('qryReportInfo').ruleID)#').val() };
						TS_AJX_SYNC('ADMREPORTS','checkMemberFilterLength',objParams,forceMemberResult,forceMemberResult,5000,forceMemberResult);

						if (feResult == true) {
							var objParams = { rptId:#val(arguments.event.getValue('qryReportInfo').reportID)#, csrID:#this.siteResourceID#, extraNodeName:'givegllist', widgetMode:'report' };
							TS_AJX_SYNC('GLWIDGET','checkGLFilterLength',objParams,forceGiveGLResult,forceGiveGLResult,5000,forceGiveGLResult);
						}
						if (feResult == true) {
							var objParams = { rptId:#val(arguments.event.getValue('qryReportInfo').reportID)#, csrID:#this.siteResourceID#, extraNodeName:'paygllist', widgetMode:'report' };
							TS_AJX_SYNC('GLWIDGET','checkGLFilterLength',objParams,forcePayGLResult,forcePayGLResult,5000,forcePayGLResult);
						}

						if (feResult) {
							if ( $('##frmView').val() == "1" ) {
								$('##reportDefs ##reportAction').val('outputToScreen');
							}
						}

						return feResult;
					}

					function frmReportViewChange() {
						$('##divReportShowScreen').html('');
						if ( $('##frmView').val() == "1" ) {
							$('##btnReportBarscreen').hide();
							$('.divFullReportViewOnly').show();
						}
						else {
							$('##btnReportBarscreen').show();
							$('.divFullReportViewOnly').hide();
						}
					}

					/* function for field set selector widget */
					function getReportFieldSetsJSONForMemberCallSheet(onCompleteFunc){
						TS_AJX('ADMREPORTS','getAvailableReportFieldSetsJSON',{},onCompleteFunc,onCompleteFunc,60000,onCompleteFunc);
					}
					
					$(function() {
						setupRptFilterDateRange('frmDRFrom','frmDRTo');
						setupRptFilterDateRange('frmgivinghistoryfrom','frmgivinghistoryto');
						setupRptFilterDateRange('frmpaymentsummaryfrom','frmpaymentsummaryto');
						mca_setupDatePickerField('frmAsOfBD');
						mca_setupCalendarIcons('frmReport');

						$('.divFullReportViewOnly').hide();
						frmReportViewChange();

						$('div.rptFieldSetOptionsContainer').hide();
					});
				</script>
				<style type="text/css">
					fieldset { border-color:##cecece; }
				</style>
				</cfoutput>
			</cfsavecontent>
			<cfhtmlhead text="#application.objCommon.minText(local.dataHead)#">
		</cfif>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<div id="reportDefs">
				#showCommonTop(event=arguments.event)#
				<cfif arguments.event.getValue('qryReportInfo').reportID gt 0>
					<cfset local.qryAvailableFieldSetsTop = getAvailableReportFieldSets(siteID=arguments.event.getValue('mc_siteInfo.siteID'))>
					<cfset local.qryAvailableGroupSets = CreateObject("component","model.admin.MemberGroupSets.MemberGroupSets").getGroupSets(orgID=arguments.event.getValue('mc_siteInfo.orgID'))>
			
					<cfset local.reportInfoXML = arguments.event.getValue('qryReportInfo').otherXML>

					<cfif this.showEventsTab>
						<cfset local.sectionnameevents = XMLSearch(local.reportInfoXML,"string(/report/extra/sectionnameevents/text())")>
						<cfif NOT len(local.sectionnameevents)>
							<cfset local.sectionnameevents = "Event History">
						</cfif>
					</cfif>
					<cfif arguments.event.getValue('mc_siteinfo.sf_subscriptions')>
						<cfset local.sectionnamesubscriptions = XMLSearch(local.reportInfoXML,"string(/report/extra/sectionnamesubscriptions/text())")>
						<cfif NOT len(local.sectionnamesubscriptions)>
							<cfset local.sectionnamesubscriptions = "Subscriptions">
						</cfif>
						<cfset local.frmIncludePastDue = XMLSearch(local.reportInfoXML,"string(/report/extra/frmincludepastdue/text())")>
						<cfset local.frmIncludeDueNow = XMLSearch(local.reportInfoXML,"string(/report/extra/frmincludeduenow/text())")>
						<cfset local.frmIncludeFutureDue = XMLSearch(local.reportInfoXML,"string(/report/extra/frmincludefuturedue/text())")>
						<cfset local.frmDRFrom = XMLSearch(local.reportInfoXML,"string(/report/extra/frmdrfrom/text())")>
						<cfset local.frmDRTo = XMLSearch(local.reportInfoXML,"string(/report/extra/frmdrto/text())")>
						<cfif not len(local.frmDRFrom) and not len(local.frmDRTo)>
							<cfset local.frmDRFrom = "#month(now())#/1/#year(now())#">
							<cfset local.frmDRTo = dateformat(now(),"m/d/yyyy")>
						</cfif>
						<cfset local.frmAsOfBD = XMLSearch(local.reportInfoXML,"string(/report/extra/frmasofbd/text())")>
						<cfif not len(local.frmAsOfBD)>
							<cfset local.frmAsOfBD = dateformat(now(),"m/d/yyyy")>
						</cfif>
					</cfif>
					<cfset local.sectionnamegivinghistory = XMLSearch(local.reportInfoXML,"string(/report/extra/sectionnamegivinghistory/text())")>
					<cfif NOT len(local.sectionnamegivinghistory)>
						<cfset local.sectionnamegivinghistory = "Giving History">
					</cfif>
					<cfset local.sectionnamepaymentsummary = XMLSearch(local.reportInfoXML,"string(/report/extra/sectionnamepaymentsummary/text())")>
					<cfif NOT len(local.sectionnamepaymentsummary)>
						<cfset local.sectionnamepaymentsummary = "Total Payments Over Time">
					</cfif>
					
					<cfset local.fieldsetattop = XMLSearch(local.reportInfoXML,"string(/report/extra/fieldsetattop/text())")>
					<cfset local.groupsetattop = XMLSearch(local.reportInfoXML,"string(/report/extra/groupsetattop/text())")>
					<cfset local.frmgivinghistoryfrom = XMLSearch(local.reportInfoXML,"string(/report/extra/frmgivinghistoryfrom/text())")>
					<cfset local.frmgivinghistoryto = XMLSearch(local.reportInfoXML,"string(/report/extra/frmgivinghistoryto/text())")>
					<cfset local.frmpaymentsummaryfrom = XMLSearch(local.reportInfoXML,"string(/report/extra/frmpaymentsummaryfrom/text())")>
					<cfset local.frmpaymentsummaryto = XMLSearch(local.reportInfoXML,"string(/report/extra/frmpaymentsummaryto/text())")>
					<cfset local.frmlinkalloctype = XMLSearch(local.reportInfoXML,"string(/report/extra/frmlinkalloctype/text())")>					
					<cfset local.frmView = XMLSearch(local.reportInfoXML,"string(/report/extra/frmview/text())")>
					<cfset local.frmShowPhotos = XMLSearch(local.reportInfoXML,"string(/report/extra/frmshowphotos/text())")>
					
					<cfset local.sectionnamefieldsets = XMLSearch(local.reportInfoXML,"string(/report/extra/sectionnamefieldsets/text())")>
					<cfif NOT len(local.sectionnamefieldsets)>
						<cfset local.sectionnamefieldsets = "Additional Fieldsets">
					</cfif>
				
					<cfset local.sectionnamegroupsets = XMLSearch(local.reportInfoXML,"string(/report/extra/sectionnamegroupsets/text())")>
					<cfif NOT len(local.sectionnamegroupsets)>
						<cfset local.sectionnamegroupsets = "Additional Groupsets">
					</cfif>
					
					<cfif len(local.fieldsetattop)>
						<cfset local.selectedTopFieldSetID = CreateObject("component","model.admin.MemberFieldSets.MemberFieldSets").getFieldSetIDByUID(uid=local.fieldsetattop)>
					<cfelse>
						<cfset local.selectedTopFieldSetID = 0>
					</cfif>
					<cfset local.strFieldSetSelector = createObject("component","model.admin.common.modules.fieldSetSelector.fieldSetSelector").getFieldSetSelector(
						siteID=arguments.event.getValue('mc_siteInfo.siteID'), selectorID="fieldSetToShowAtTop", selectedValue=local.selectedTopFieldSetID,
						qryFieldSets=local.qryAvailableFieldSetsTop, getFieldSetDataFunc="getReportFieldSetsJSONForMemberCallSheet", usageMode="fsWidgetSavedRpt")>
					
					<cfform name="frmReport" id="frmReport" method="post">
						<input type="hidden" name="reportAction" id="reportAction" value="">
						#showStepMemberCriteria(event=arguments.event, title="Define Member Filter", desc="Filter the members appearing on this report using the defined criteria below.")#

						<div class="mb-5 stepDIV">
							<h5>Select Your Report View</h5>
							<div class="row mt-2">
								<div class="col-sm-12">
									<div class="form-group row">
										<label for="frmView" class="col-md-4 col-sm-12 col-form-label">Report View</label>
										<div class="col-md-8 col-sm-12">
											<select name="frmView" id="frmView" onchange="frmReportViewChange()" class="form-control form-control-sm">
												<option value="1" <cfif local.frmView EQ "1">selected</cfif>>Full - Produce Full Report for each member</option>
												<option value="2" <cfif local.frmView EQ "2">selected</cfif>>Summary - View simple list of members</option>
											</select>
										</div>
									</div>
								</div>
							</div>
						</div>

						<div class="mb-5 stepDIV divFullReportViewOnly">
							<h5>Define Information That Appears in Full Report</h5>
							<div class="row mt-2">
								<div class="col-sm-12">
									<div class="form-group row align-items-center mb-2">
										<div class="col-md-4 col-sm-12">Member Field Set at top of report</div>
										<div class="col-md-8 col-sm-12">
											#local.strFieldSetSelector.html#
										</div>
									</div>
									<div class="form-group row">
										<label for="groupsetattop" class="col-md-4 col-sm-12 col-form-label">Member Group Set at top of report</label>
										<div class="col-md-8 col-sm-12">
											<select name="groupsetattop" id="groupsetattop" class="form-control form-control-sm">
												<option value=""></option>
												<cfloop query="local.qryAvailableGroupSets">
													<option value="#local.qryAvailableGroupSets.groupsetID#" <cfif local.qryAvailableGroupSets.groupsetID eq local.groupsetattop>selected</cfif>>#local.qryAvailableGroupSets.groupsetName#</option>			
												</cfloop>
											</select>
										</div>
									</div>
									<div class="form-group row">
										<label for="frmShowPhotos" class="col-md-4 col-sm-12 col-form-label">Include Member Photos</label>
										<div class="col-md-8 col-sm-12">
											<select name="frmShowPhotos" id="frmShowPhotos" class="form-control form-control-sm">
												<option value="0" <cfif local.frmShowPhotos eq 0>selected</cfif>>No</option>
												<option value="1" <cfif local.frmShowPhotos eq 1>selected</cfif>>Yes</option>
											</select>
										</div>
									</div>
								</div>
							</div>
						</div>

						<div class="stepDIV divFullReportViewOnly">
							<cfif arguments.event.getValue('mc_siteinfo.sf_subscriptions')>
								<h5>Subscription History</h5>
								#local.strSubscriptionWidget.html#
								<div class="row mt-2 mb-5">
									<div class="col-sm-12">
										<div class="form-group row">
											<label for="sectionnamesubscriptions" class="col-md-4 col-sm-12 col-form-label">Heading at top of section</label>
											<div class="col-md-8 col-sm-12">
												<input type="text" name="sectionnamesubscriptions" id="sectionnamesubscriptions" value="#local.sectionnamesubscriptions#" class="form-control form-control-sm">
											</div>
										</div>
										<div class="form-group row">
											<label class="col-md-4 col-sm-12 col-form-label">
												Limit Subscriptions:<br/>
												<small>Subscriptions must satisfy one OR more of the following:</small>
											</label>
											<div class="col-md-8 col-sm-12">
												<div class="form-check">
													<input type="checkbox" name="frmIncludePastDue" id="frmIncludePastDue" value="1" class="form-check-input" <cfif local.frmIncludePastDue is 1>checked</cfif>>
													<label class="form-check-label" for="frmIncludePastDue">Only include subscriptions with an Amount Past Due</label>
												</div>
												<div class="form-check">
													<input type="checkbox" name="frmIncludeDueNow" id="frmIncludeDueNow" value="1" class="form-check-input" <cfif local.frmIncludeDueNow is 1>checked</cfif>>
													<label class="form-check-label" for="frmIncludeDueNow">Only include subscriptions with an Amount Due Now</label>
												</div>
												<div class="form-check">
													<input type="checkbox" name="frmIncludeFutureDue" id="frmIncludeFutureDue" value="1" class="form-check-input" <cfif local.frmIncludeFutureDue is 1>checked</cfif>>
													<label class="form-check-label" for="frmIncludeFutureDue">Only include subscriptions with an Amount Due In Future</label>
												</div>
											</div>
										</div>
										<div class="form-group row">
											<label for="frmDRFrom" class="col-md-4 col-sm-12 col-form-label">Invoice Due Date between</label>
											<div class="col-md-8 col-sm-12">
												<div class="row">
													<div class="col-md col-sm-12 pr-md-0">
														<div class="input-group input-group-sm">
															<input type="text" name="frmDRFrom" id="frmDRFrom" value="#local.frmDRFrom#" mcrdtxt="Subscription History Invoice Due Start Date" class="form-control form-control-sm dateControl rolldate" placeholder="Date From">
															<div class="input-group-append">
																<span class="input-group-text cursor-pointer calendar-button" data-target="frmDRFrom"><i class="fa-solid fa-calendar"></i></span>
															</div>
														</div>
													</div>
													<div class="col-md-auto px-md-2 d-flex align-items-center">and</div>
													<div class="col-md col-sm-12 pl-md-0">
														<div class="input-group input-group-sm">
															<input type="text" name="frmDRTo" id="frmDRTo" value="#local.frmDRTo#" mcrdtxt="Subscription History Invoice Due End Date" class="form-control form-control-sm dateControl rolldate" placeholder="Date To">
															<div class="input-group-append">
																<span class="input-group-text cursor-pointer calendar-button" data-target="frmDRTo"><i class="fa-solid fa-calendar"></i></span>
															</div>
														</div>
													</div>
												</div>
											</div>
										</div>
										<div class="form-group row">
											<label for="frmAsOfBD" class="col-md-4 col-sm-12 col-form-label">As of Batch Deposit Date</label>
											<div class="col-md-8 col-sm-12">
												<div class="input-group input-group-sm">
													<input type="text" name="frmAsOfBD" id="frmAsOfBD" value="#local.frmAsOfBD#" mcrdtxt="Subscription History As Of Batch Deposit Date" class="form-control form-control-sm dateControl rolldate">
													<div class="input-group-append">
														<span class="input-group-text cursor-pointer calendar-button" data-target="frmDRFrom"><i class="fa-solid fa-calendar"></i></span>
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>
							</cfif>

							<h5>Giving History</h5>
							#local.strGivHistGLAccountWidget.html#
							<div class="row mt-2 mb-5">
								<div class="col-sm-12">
									<div class="form-group row">
										<label for="sectionnamegivinghistory" class="col-md-4 col-sm-12 col-form-label">Heading at top of section</label>
										<div class="col-md-8 col-sm-12">
											<input type="text" name="sectionnamegivinghistory" id="sectionnamegivinghistory" value="#local.sectionnamegivinghistory#" class="form-control form-control-sm">
										</div>
									</div>
									<div class="form-group row">
										<label for="frmgivinghistoryfrom" class="col-md-4 col-sm-12 col-form-label">Date range</label>
										<div class="col-md-8 col-sm-12">
											<div class="row">
												<div class="col-md col-sm-12 pr-md-0">
													<div class="input-group input-group-sm">
														<input type="text" name="frmgivinghistoryfrom" id="frmgivinghistoryfrom" value="#local.frmgivinghistoryfrom#" mcrdtxt="Giving History Start Date" class="form-control form-control-sm dateControl rolldate" placeholder="Date From">
														<div class="input-group-append">
															<span class="input-group-text cursor-pointer calendar-button" data-target="frmgivinghistoryfrom"><i class="fa-solid fa-calendar"></i></span>
														</div>
													</div>
												</div>
												<div class="col-md-auto px-md-2 d-flex align-items-center">and</div>
												<div class="col-md col-sm-12 px-md-0">
													<div class="input-group input-group-sm">
														<input type="text" name="frmgivinghistoryto" id="frmgivinghistoryto" value="#local.frmgivinghistoryto#" mcrdtxt="Giving History End Date" class="form-control form-control-sm dateControl rolldate" placeholder="Date To">
														<div class="input-group-append">
															<span class="input-group-text cursor-pointer calendar-button" data-target="frmgivinghistoryto"><i class="fa-solid fa-calendar"></i></span>
														</div>
													</div>
												</div>
												<div class="col-md-auto pl-md-2">
													<button type="button" class="btn btn-pill btn-secondary btn-sm btn-clear-dates" onclick="clearDateRangeFields('frmgivinghistoryfrom','frmgivinghistoryto');">clear</button>
												</div>
											</div>
											<div class="row">
												<div class="col-sm-12"><i>(We'll compare to the same range in previous years.)</i></div>
											</div>
										</div>
									</div>
								</div>
							</div>

							<h5>Total Payments Over Time</h5>
							#local.strPmtSummGLAccountWidget.html#
							<div class="row mt-2 mb-5">
								<div class="col-sm-12">
									<div class="form-group row">
										<label for="sectionnamepaymentsummary" class="col-md-4 col-sm-12 col-form-label">Name of Payment Summary Section</label>
										<div class="col-md-8 col-sm-12">
											<input type="text" name="sectionnamepaymentsummary" id="sectionnamepaymentsummary" value="#local.sectionnamepaymentsummary#" class="form-control form-control-sm">
										</div>
									</div>
									<div class="form-group row">
										<label for="frmpaymentsummaryfrom" class="col-md-4 col-sm-12 col-form-label">Batch Deposit Date</label>
										<div class="col-md-8 col-sm-12">
											<div class="row">
												<div class="col-md col-sm-12 pr-md-0">
													<div class="input-group input-group-sm">
														<input type="text" name="frmpaymentsummaryfrom" id="frmpaymentsummaryfrom" value="#local.frmpaymentsummaryfrom#" mcrdtxt="Payment Summary Batch Deposit Start Date" class="form-control form-control-sm dateControl rolldate" placeholder="Date From">
														<div class="input-group-append">
															<span class="input-group-text cursor-pointer calendar-button" data-target="frmpaymentsummaryfrom"><i class="fa-solid fa-calendar"></i></span>
														</div>
													</div>
												</div>
												<div class="col-md-auto px-md-2 d-flex align-items-center">and</div>
												<div class="col-md col-sm-12 px-md-0">
													<div class="input-group input-group-sm">
														<input type="text" name="frmpaymentsummaryto" id="frmpaymentsummaryto" value="#local.frmpaymentsummaryto#" mcrdtxt="Payment Summary Batch Deposit End Date" class="form-control form-control-sm dateControl rolldate" placeholder="Date To">
														<div class="input-group-append">
															<span class="input-group-text cursor-pointer calendar-button" data-target="frmpaymentsummaryto"><i class="fa-solid fa-calendar"></i></span>
														</div>
													</div>
												</div>
												<div class="col-md-auto pl-md-2">
													<button type="button" class="btn btn-pill btn-secondary btn-sm btn-clear-dates" onclick="clearDateRangeFields('frmpaymentsummaryfrom','frmpaymentsummaryto');">clear</button>
												</div>
											</div>
										</div>
									</div>
									<div class="form-group row">
										<label for="frmlinkalloctype" class="col-md-4 col-sm-12 col-form-label">Link Allocation Type</label>
										<div class="col-md-8 col-sm-12">
											<select name="frmlinkalloctype" id="frmlinkalloctype" class="form-control form-control-sm">
												<option value="LinkAllocCash" <cfif local.frmLinkAllocType eq "LinkAllocCash">selected</cfif>>Members Linked to Allocation's Cash</option>
												<option value="LinkAllocRevenue" <cfif local.frmLinkAllocType eq "LinkAllocRevenue">selected</cfif>>Members Linked to Allocation's Revenue</option>	
											</select>
										</div>
									</div>
								</div>
							</div>

							<cfif this.showEventsTab>
								#local.strEventWidget.html#
								<div class="row mt-2 mb-5">
									<div class="col-sm-12">
										<div class="form-group row">
											<label for="sectionnameevents" class="col-md-4 col-sm-12 col-form-label">Heading at top of section</label>
											<div class="col-md-8 col-sm-12">
												<input type="text" name="sectionnameevents" id="sectionnameevents" value="#local.sectionnameevents#" class="form-control form-control-sm">
											</div>
										</div>
									</div>
								</div>
							</cfif>

							<!--- Member History --->
							#local.strMHWidget[1].html#
							<cfif arguments.event.getValue('mc_siteInfo.sf_relationships') eq 1>
								#local.strMHWidget[2].html#
							</cfif>
							<!--- Notes --->
							#local.strMHWidget[3].html#
						</div>

						<div class="mb-5 stepDIV divFullReportViewOnly">
							<h5>Additional Member Field Sets</h5>
							<div class="mt-2">
								#showStepFieldsets(event=arguments.event, title="", desc="Select the additional member field sets you'd like to include in this report.")#
							</div>
							<div class="row mt-1">
								<div class="col-sm-12">
									<div class="form-group row">
										<label for="sectionnamefieldsets" class="col-md-4 col-sm-12 col-form-label">Heading at top of section</label>
										<div class="col-md-8 col-sm-12">
											<input type="text" name="sectionnamefieldsets" id="sectionnamefieldsets" value="#local.sectionnamefieldsets#" class="form-control form-control-sm">
										</div>
									</div>
								</div>
							</div>
						</div>

						<div class="mb-5 stepDIV divFullReportViewOnly">
							<h5>Additional Member Group Sets</h5>
							<div class="mt-2">
								#showStepGroupsets(event=arguments.event, title="", desc="Select the additional member group sets you'd like to include in this report.", gridwidth="270", gridheight="200")#
							</div>
							<div class="row mt-1">
								<div class="col-sm-12">
									<div class="form-group row">
										<label for="sectionnamegroupsets" class="col-md-4 col-sm-12 col-form-label">Heading at top of section</label>
										<div class="col-md-8 col-sm-12">
											<input type="text" name="sectionnamegroupsets" id="sectionnamegroupsets" value="#local.sectionnamegroupsets#" class="form-control form-control-sm">
										</div>
									</div>
								</div>
							</div>
						</div>

						#showStepRollingDates(event=arguments.event)#
						#showButtonBar(event=arguments.event, validateFunction='forceFilter')#
					</cfform>
				</cfif>
			</div>
			</cfoutput>
		</cfsavecontent>
	
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="screenReport" access="private" output="false" returntype="struct">
		<cfargument name="reportAction" type="string" required="true">
		<cfargument name="qryReportInfo" type="query" required="true">
		<cfargument name="siteCode" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = { data="", success=true, errMsg="", isReportEmpty=false }>
		<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(arguments.siteCode)>

		<cftry>
			<cfset local.memberLink = buildLinkToTool(toolType='MemberAdmin',mca_ta='edit')>
			<cfset local.strSQLPrep = prepSQL(orgID=local.mc_siteInfo.orgID, siteID=local.mc_siteInfo.siteID,
				reportRuleID=arguments.qryReportInfo.ruleID, reportOtherXML=arguments.qryReportInfo.otherXML,
				existingFields="m_lastname,m_firstname,m_membernumber,m_company", existingAliases="m")>
			<cfif local.strSQLPrep.ruleErr>
				<cfthrow message="There was an error in the report criteria.">
			</cfif>

			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryReport" result="local.qryReportResult">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					<cfif len(local.strSQLPrep.ruleSQL)>#PreserveSingleQuotes(local.strSQLPrep.ruleSQL)#</cfif>

					select m.memberID, m.company, m.lastname + ', ' +  m.firstname + ' (' + m.membernumber + ')' as mc_combinedName
					from dbo.ams_members as m
					inner join ##tmpVGRMembers as tblM on tblM.memberID = m.memberID 
					where m.orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.mc_siteInfo.orgID#">
					and m.memberID = m.activeMemberID
					and m.isProtected = 0
					and m.status <> 'D'
					order by m.lastname, m.firstname, m.company, m.membernumber;

					<cfif len(local.strSQLPrep.ruleSQL)>
						IF OBJECT_ID('tempdb..##tmpVGRMembers') IS NOT NULL
							DROP TABLE ##tmpVGRMembers;
					</cfif>

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;		
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfsavecontent variable="local.strReturn.data">
				<cfoutput>
				<div id="screenreport">
					#showReportHeader(siteID=local.mc_siteInfo.siteID, reportAction=arguments.reportAction, reportName=arguments.qryReportInfo.reportName)#
				</cfoutput>

				<cfif local.qryReport.recordcount is 0>
					<cfset local.strReturn.isReportEmpty = true>
					<cfoutput><div>No results to report.</div></cfoutput>
				<cfelse>
					<cfoutput>
						<p>Below is the list of records that match the Member Filter criteria that you specified. When you run the Full Report View, you will receive a detailed report on each of these members.</p>
						<cfif arguments.reportAction eq "screen">
							<p>To proceed, change the Report View above and then select the additional criteria that you want to include in each report</p>
						</cfif>
						<table class="table table-sm table-borderless">
						<thead>
							<tr>
								<th class="text-left">Name</th>
							</tr>
						</thead>
						</table>
					</cfoutput>
	
					<cfoutput query="local.qryReport">
						<div class="row my-2">
							<div class="col">
								<cfif arguments.reportAction eq "screen">
									<a href="#local.memberLink#&memberid=#local.qryReport.memberid#" target="_blank">#local.qryReport.mc_combinedName#</a><br/>
								<cfelse>
									<b>#local.qryReport.mc_combinedName#</b><br/>
								</cfif>
								<cfif len(local.qryReport.company)>#local.qryReport.company#<br/></cfif>
							</div>
						</div>
					</cfoutput>					
				</cfif>

				<cfoutput>
				#showReportFooter(reportAction=arguments.reportAction, defaultTimeZoneID=local.mc_siteInfo.defaultTimeZoneID)#
				#showRawSQL(reportAction=arguments.reportAction, qryName="local.qryReport", strQryResult=local.qryReportResult)#
				</div>
				</cfoutput>
			</cfsavecontent>
		
		<cfcatch type="any">
			<cfset local.strReturn.success = false>
			<cfset local.strReturn.data = "">
		</cfcatch>
		</cftry>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="pdfReport" access="private" output="false" returntype="struct">
		<cfargument name="reportAction" type="string" required="true">
		<cfargument name="qryReportInfo" type="query" required="true">
		<cfargument name="siteCode" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = { data="", success=true, errMsg="", format="json", isReportEmpty=false }>
		<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(arguments.siteCode)>

		<cfset local.frmView = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmview/text())")>

		<cfif local.frmView eq "2">
			<cfreturn super.pdfReport(reportAction=arguments.reportAction, qryReportInfo=arguments.qryReportInfo, siteCode=arguments.siteCode)>
		<cfelse>
			<cftry>
				<cfsetting requesttimeout="600">

				<!--- --------------- --->
				<!--- RUN MEMBER RULE --->
				<!--- --------------- --->
				<cfset local.prepMemberRule = prepSQL_memberrule(ruleID=arguments.qryReportInfo.ruleID, orgID=local.mc_siteInfo.orgID)>
				<cfset local.ruleSQL = local.prepMemberRule.ruleSQL>
				<cfset local.joinSQLNoMemberData = local.prepMemberRule.arrJoinsNoMemberData>
				<cfif local.prepMemberRule.ruleErr>
					<cfthrow message="There was an error in the report criteria.">
				</cfif>
				
				<!--- main member query --->
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryData" result="local.qryDataResult">
					SET XACT_ABORT, NOCOUNT ON;
					BEGIN TRY
						SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

						declare @queueTypeID int, @insertingQueueStatusID int, @readyQueueStatusID int,
							@jobUID uniqueIdentifier = newid(),
							@recordedByMemberID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#session.cfcuser.memberdata.memberID#">,
							@orgID int = #local.mc_siteInfo.orgID#,
							@siteID int = #local.mc_siteInfo.siteID#,
							@reportID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.qryReportInfo.reportID#">,
							@queryCount int = 0;

						<cfif len(local.ruleSQL)>#PreserveSingleQuotes(local.ruleSQL)#</cfif>

						IF OBJECT_ID('tempdb..##tmpMembers') IS NOT NULL
							DROP TABLE ##tmpMembers;
						CREATE TABLE ##tmpMembers (memberid int);
						
						insert into ##tmpMembers (memberid)
						select m.memberid
						from dbo.ams_members as m
						<cfif len(local.joinSQLNoMemberData)>#PreserveSingleQuotes(local.joinSQLNoMemberData)#</cfif>
						where m.orgID = @orgID
						and m.isProtected = 0
						and m.status in ('A','I');
									
						select @queryCount = count(*) from ##tmpMembers;

						if @queryCount > 0 begin
							select @queueTypeID = queueTypeID from platformQueue.dbo.tblQueueTypes where queueType='callSheets';

							select @insertingQueueStatusID = queueStatusID
							from platformQueue.dbo.tblQueueStatuses
							where queueTypeID=@queueTypeID and queueStatus = 'insertingItems';

							select @readyQueueStatusID = queueStatusID
							from platformQueue.dbo.tblQueueStatuses
							where queueTypeID=@queueTypeID and queueStatus = 'readyToProcess';

							insert into platformQueue.dbo.queue_callSheets (itemUID, recordedByMemberID, orgID, siteID, reportID, dateAdded)
							values(@jobUID, @recordedByMemberID, @orgID, @siteID, @reportID, getdate());

							insert into platformQueue.dbo.queue_callSheetsDetail (itemUID, memberID, statusID, dateUpdated)
							select @jobUID, memberid, @insertingQueueStatusID, getdate()
							from ##tmpMembers;

							-- resume task
							EXEC dbo.sched_resumeTask @name='Member CallSheet Report Queue', @engine='BERLinux';

							update cd 
							set cd.statusID = @readyQueueStatusID,
								cd.dateUpdated = getdate()
							from platformQueue.dbo.queue_callSheetsDetail as cd 
							where
								cd.itemUID = @jobUID 
								and cd.statusID = @insertingQueueStatusID;
						end

						select @queryCount as queryCount, @jobUID as itemUID;

						IF OBJECT_ID('tempdb..##tmpMembers') IS NOT NULL
							DROP TABLE ##tmpMembers;

						SET TRANSACTION ISOLATION LEVEL READ COMMITTED;		
					END TRY
					BEGIN CATCH
						IF @@trancount > 0 ROLLBACK TRANSACTION;
						SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
						EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
					END CATCH
				</cfquery>

				<cfif not local.qryData.queryCount>
					<cfset local.strReturn.isReportEmpty = true>
					<cfthrow message="here are no members to report based on the report criteria." type="RptError">
				<cfelse>
					<cftry>
						<cfif not directoryExists("#application.paths.SharedTempNoWeb.path#callsheet_#local.qryData.itemUID#")>
							<cfdirectory action="CREATE" directory="#application.paths.SharedTempNoWeb.path#callsheet_#local.qryData.itemUID#">
						</cfif>	
						<cfcatch type="Any">
							<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
						</cfcatch>
					</cftry>
					<cfsavecontent variable="local.strReturn.data">
						<cfoutput>
						<div id="screenreport">
							<h4>Call Sheet Has Been Scheduled</h4>
							<br/>
							<div>
								The Call Sheet report containing #local.qryData.queryCount# members(s) has been scheduled and will begin shortly.<br/>
								You will be sent an e-mail with the results with the report.<br/><br/>
								Please wait until you receive the emailed report before contacting MemberCentral with any questions.
							</div>
						</div>
						</cfoutput>
					</cfsavecontent>
				</cfif>

			<cfcatch type="RptError">
				<cfset local.strReturn.success = false>
				<cfset local.strReturn.data = "">
				<cfset local.strReturn.errMsg = cfcatch.message>
			</cfcatch>
			<cfcatch type="any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
				<cfset local.strReturn.success = false>
				<cfset local.strReturn.data = "">
			</cfcatch>
			</cftry>

			<cfreturn local.strReturn>
		</cfif>
	</cffunction>

	<cffunction name="saveReportExtra" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.otherXML = XMLParse(arguments.event.getValue('qryReportInfo').otherXML);
			
			local.strFields = structNew();
			local.strFields.fieldsetattop = {
				label="Member Field Set at Top",
				value=CreateObject("component","model.admin.MemberFieldSets.MemberFieldSets").getFieldSetUIDByID(fieldSetID=arguments.event.getValue('fieldSetToShowAtTop',0))
			};
			local.strFields.groupsetattop = { label="Member Group Set at Top", value=arguments.event.getValue('groupsetattop','') };
			local.strFields.frmgivinghistoryfrom = { label="Giving History Start Date", value=arguments.event.getValue('frmgivinghistoryfrom','') };
			local.strFields.frmgivinghistoryto = { label="Giving History End Date", value=arguments.event.getValue('frmgivinghistoryto','') };
			local.strFields.frmpaymentsummaryfrom = { label="Payment Summary Batch Deposit Start Date", value=arguments.event.getValue('frmpaymentsummaryfrom','') };
			local.strFields.frmpaymentsummaryto = { label="Payment Summary Batch Deposit End Date", value=arguments.event.getValue('frmpaymentsummaryto','') };
			local.strFields.frmlinkalloctype = { label="Link Allocation Type", value=arguments.event.getValue('frmlinkalloctype','') };
			if (this.showEventsTab)
				local.strFields.sectionnameevents = { label="Heading at top of Section (Event)", value=arguments.event.getValue('sectionnameevents','') };
			if (arguments.event.getValue('mc_siteinfo.sf_subscriptions')) {
				local.strFields.sectionnamesubscriptions = { label="Heading at top of Section (Subscription History)", value=arguments.event.getValue('sectionnamesubscriptions','') };
				local.strFields.frmincludepastdue = { label="Only include subscriptions with an Amount Past Due", value=arguments.event.getValue('frmIncludePastDue','') };
				local.strFields.frmincludeduenow = { label="Only include subscriptions with an Amount Due Now", value=arguments.event.getValue('frmIncludeDueNow','') };
				local.strFields.frmincludefuturedue = { label="Only include subscriptions with an Amount Due In Future", value=arguments.event.getValue('frmIncludeFutureDue','') };
				local.strFields.frmdrfrom = { label="Subscription History Invoice Due Start Date", value=arguments.event.getValue('frmDRFrom','') };
				local.strFields.frmdrto = { label="Subscription History Invoice Due End Date", value=arguments.event.getValue('frmDRTo','') };
				local.strFields.frmasofbd = { label="Subscription History As Of Batch Deposit Date", value=arguments.event.getValue('frmAsOfBD','') };
			}
			local.strFields.sectionnamegivinghistory = { label="Heading at top of Section (Giving History)", value=arguments.event.getValue('sectionnamegivinghistory','') };
			local.strFields.sectionnamepaymentsummary = { label="Name of Payment Summary Section", value=arguments.event.getValue('sectionnamepaymentsummary','') };
			local.strFields.sectionnamefieldsets = { label="Heading at top of Section (Additional Member Field Sets)", value=arguments.event.getValue('sectionnamefieldsets','') };
			local.strFields.sectionnamegroupsets = { label="Heading at top of Section (Additional Member Group Sets)", value=arguments.event.getValue('sectionnamegroupsets','') };
			local.strFields.frmview = { label="Report View", value=arguments.event.getValue('frmView','') };
			local.strFields.frmshowphotos = { label="Include Member Photos", value=arguments.event.getValue('frmShowPhotos','') };
			if (this.showEventsTab)
				local.strFields.eidlist = { label="Events List", value=XMLSearch(local.otherXML,'string(/report/extra/eidlist/text())') };
			local.strFields.givegllist = { label="Giving GL List", value=XMLSearch(local.otherXML,'string(/report/extra/givegllist/text())') };
			local.strFields.paygllist = { label="Payment GL List", value=XMLSearch(local.otherXML,'string(/report/extra/paygllist/text())') };

			if (isDefined("local.otherXML.report.extra.memhist"))
				local.strFields.memhist = { label="Member History", value=local.otherXML.report.extra.memhist };
			if (arguments.event.getValue('mc_siteInfo.sf_relationships') and isDefined("local.otherXML.report.extra.relationship"))
				local.strFields.relationship = { label="Relationships", value=local.otherXML.report.extra.relationship };
			if (isDefined("local.otherXML.report.extra.notes"))
				local.strFields.notes = { label="Notes", value=local.otherXML.report.extra.notes };

			reportSaveReportExtra(qryReportInfo=arguments.event.getValue("qryReportInfo"), strFields=local.strFields, event=arguments.event);
			return returnAppStruct('','echo');
		</cfscript>
	</cffunction>
</cfcomponent>