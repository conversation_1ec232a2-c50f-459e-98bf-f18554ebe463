CREATE PROC dbo.ams_runConditionCacheChecks
@orgID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
	
	DECLARE @queueTypeID int, @queueStatusID int, @errorSubject varchar(300), @errmsg varchar(max), @tmpCount int;
	SELECT @queueTypeID = queueTypeID FROM platformQueue.dbo.tblQueueTypes WHERE queueType = 'conditionCacheCheck';
	SELECT @queueStatusID = queueStatusID FROM platformQueue.dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID AND queueStatus = 'processingItem';

	-- mark queue entry as processing
	UPDATE platformQueue.dbo.queue_conditionCacheCheck
	SET statusID = @queueStatusID
	WHERE orgID = @orgID;

	/* Merged Members in cache_members_conditions */
	SELECT @tmpCount = count(cmc.conditionID)
	FROM dbo.cache_members_conditions as cmc
	INNER JOIN dbo.ams_members as m on m.orgID = @orgID and m.memberID = cmc.memberID and m.memberID <> m.activeMemberID
	WHERE cmc.orgID = @orgID;

	IF @tmpCount > 0
		delete cmc
		from dbo.cache_members_conditions as cmc 
		inner join dbo.ams_members as m on m.orgID = @orgID and m.memberID = cmc.memberID and m.memberID <> m.activeMemberID 
		where cmc.orgID = @orgID;

	SET @tmpCount = null;


	/* Merged Members in cache_members_groups */	
	SELECT @tmpCount = count(cmg.autoID)
	FROM dbo.cache_members_groups as cmg
	INNER JOIN dbo.ams_members as m on m.orgID = @orgID and m.memberID = cmg.memberID and m.memberID <> m.activeMemberID
	WHERE cmg.orgID = @orgID;

	IF @tmpCount > 0
		delete cmg 
		from dbo.cache_members_groups as cmg 
		inner join dbo.ams_members as m on m.orgID = @orgID and m.memberID = cmg.memberID and m.memberID <> m.activeMemberID 
		where cmg.orgID = @orgID;
	
	SET @tmpCount = null;


	/* Mismatch between orgID of members and entries in cache_members_groups */	
	SELECT @tmpCount = COUNT(*)
	FROM (
		SELECT distinct memberID
		FROM dbo.cache_members_groups
		WHERE orgID = @orgID
		AND memberID <> 0
			EXCEPT 
		SELECT memberID
		FROM dbo.ams_members 
		WHERE orgID = @orgID
	) as tmp;

	IF @tmpCount > 0 BEGIN
		SET @errorSubject = CONCAT('Mismatch between orgID ',@orgID,' of members and entries in cache_members_groups');
		SET @errmsg = CONCAT('There are entries in cache_members_groups where the orgID ',@orgID,' in the cache doesnt match the members in the cache. Run <pre>SELECT distinct memberID FROM dbo.cache_members_groups WHERE orgID = @orgID AND memberID <> 0 EXCEPT SELECT memberID FROM dbo.ams_members WHERE orgID = @orgID</pre>');
		EXEC platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;
	END
	SET @tmpCount = null;


	/* Mismatch between orgID of groups and entries in cache_members_groups */
	SELECT @tmpCount = COUNT(*)
	FROM (
		SELECT distinct groupID
		FROM dbo.cache_members_groups
		WHERE orgID = @orgID
			EXCEPT 
		SELECT groupID
		FROM dbo.ams_groups 
		WHERE orgID = @orgID
	) as tmp;

	IF @tmpCount > 0 BEGIN
		SET @errorSubject = CONCAT('Mismatch between orgID ',@orgID,' of groups and entries in cache_members_groups');
		SET @errmsg = CONCAT('There are entries in cache_members_groups where the orgID ',@orgID,' in the cache doesnt match the groups in the cache. Run <pre>SELECT distinct groupID FROM dbo.cache_members_groups WHERE orgID = @orgID EXCEPT SELECT groupID FROM dbo.ams_groups WHERE orgID = @orgID</pre>');
		EXEC platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;
	END
	SET @tmpCount = null;


	/* Mismatch between orgID of the members and conditions for entries in cache_members_conditions */
	SELECT @tmpCount = COUNT(*)
	FROM (
		SELECT distinct memberID
		FROM dbo.cache_members_conditions
		WHERE orgID = @orgID
			EXCEPT 
		SELECT memberID
		FROM dbo.ams_members 
		WHERE orgID = @orgID
	) as tmp;

	IF @tmpCount > 0 BEGIN
		SET @errorSubject = CONCAT('Mismatch between orgID ',@orgID,' of the members and conditions for entries in cache_members_conditions');
		SET @errmsg = CONCAT('There are entries in cache_members_conditions where the orgID ',@orgID,' of the member doesnt match the orgID of the condition. Run <pre>SELECT distinct memberID FROM dbo.cache_members_conditions WHERE orgID = @orgID EXCEPT SELECT memberID FROM dbo.ams_members WHERE orgID = @orgID</pre>');
		EXEC platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@errmsg, @forDev=1;
	END

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

	DELETE FROM platformQueue.dbo.queue_conditionCacheCheck
	WHERE orgID = @orgID;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
