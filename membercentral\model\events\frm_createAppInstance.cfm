<cfsavecontent variable="local.appInstanceFormJS">
<cfoutput>
<script language="javascript">
	function doDeployToComm() {
		var thisForm = document.forms["frmCreateApp"];
		var deployValue = 0;
		for (var i=0; i < thisForm.deployToComm.length; i++){
			if (thisForm.deployToComm[i].checked) deployValue = thisForm.deployToComm[i].value;
		}
		if( deployValue == 1){
			$('##showCommunities').removeClass('d-none');
			$('##showSections').addClass('d-none');
		} else {
			$('##showCommunities').addClass('d-none');
			$('##showSections').removeClass('d-none');
		}					
	}	
	
	function closeBox() { $.colorbox.close(); }

	<!--- gl account selector functions --->
	function selectGLAccount() {
		$.colorbox( {onCleanup:closeBox, innerWidth:660, innerHeight:470, href:'#CreateObject("component","model.admin.admin").buildLinkToTool(toolType="GLAccountSelector",mca_ta="showSelector")#&mode=direct&selectFN=parent.selectGLAccountResult&glatid=3', iframe:true, overlayClose:false} );
	}
	function selectGLAccountResult(objGL) {
		if (objGL.thepathexpanded.length > 0) {
			$('##DefaultGLAccountPath').html('<span class="mr-2">' + objGL.thepathexpanded + '</span> (<span class="text-danger font-weight-bold">Remember to save!</span>)');
			$('##DefaultGLAccountID').val(objGL.glaccountid);
			closeBox();
		} else { 
			var msg = '<div style="m-2">
				<h4>Error selecting GL Account</h4>
				<div>There was a problem selecting the GL Account used for new events on this calendar.<br/>
				Try again; if the issue persists, contact MemberCentral for assistance.</div>
				</div>';
			$.colorbox( {onCleanup:closeBox, html:msg, overlayClose:false} );
		}
	}
	function setDuplicateMessage(boxEl, messageEl, iconEl, success, message){
		iconEl.toggleClass('fa-circle-check', success).toggleClass('fa-circle-exclamation', !success);
		messageEl.html(message);
		boxEl.toggleClass('text-green', success).toggleClass('text-danger', !success).removeClass('d-none');
	}
	function doesPageExist(pageName) {
		var boxEl = $('##pageBox');
		var messageEl = $('##pageText');
		var iconEl = $('##pageImg');
		var re = /[^a-zA-Z0-9\-_]/;
		mca_hideAlert('err_createapp');

		var existsResult = function(r) {
			if (r.success && r.success.toLowerCase() == 'true'){
				setDuplicateMessage(boxEl, messageEl, iconEl, !r.pageexists, r.pageexists ? 'Page Name already used!' : 'Passed!');
			} else {
				boxEl.addClass('d-none');
				mca_showAlert('err_createapp', 'We were unable to check whether this page name exists.');
			}
		};

		if (pageName.length > 0) {
			if(re.test(pageName)){
				setDuplicateMessage(boxEl, messageEl, iconEl, false, 'Only letters, numbers, underscores, and dashses are allowed');
			}
			else {
				checkPageExists(pageName,existsResult);
			}
		}
		else {
			boxEl.addClass('d-none');
			return false;
		}
	}
	function checkPageExists(pageName,callback) {
		var objParams = { pageID:#val(arguments.event.getValue('pageID',0))#, pageName:pageName };
		TS_AJX('PAGE','pageExists',objParams,callback,callback,10000,callback);
	}
	function validateCreateAppInstanceForm(){
		toggleFinishButton(false);
		mca_hideAlert('err_createapp');
		var thisForm = document.forms["frmCreateApp"];	
		var arrPromises = [];
		var arrReq = new Array();
		

		if($('##pageName').length && $.trim($('##pageName').val()).length == 0 && $('##pageName').is(':visible')) {
			arrReq[arrReq.length] = 'Enter a valid name for this page.';
		}
		if($('##pageTitle').length && $.trim($('##pageTitle').val()).length == 0) {
			arrReq[arrReq.length] = 'Enter a page title.';
		}
		if($('##pageDesc').length && $.trim($('##pageDesc').val()).length == 0) {
			arrReq[arrReq.length] = 'Enter a page description.';
		}
		if($('##appInstanceName').length && $.trim($('##appInstanceName').val()).length == 0) {
			arrReq[arrReq.length] = 'Enter a name for this calendar.';
		}
		if($('##appInstanceDesc').length && $.trim($('##appInstanceDesc').val()).length == 0) {
			arrReq[arrReq.length] = 'Enter a description for this calendar.';
		}
		<cfif variables.isCommunityReady AND local.isFromCommunity EQ 0>
			if($('input[name=deployToComm]').length && $.trim($('input[name=deployToComm]:checked').val()) == 1 && $('##commSRID').length  && $.trim($('##commSRID').val()) == 0) {
				arrReq[arrReq.length] = 'You must select an e-Community.';
			}
		</cfif>

		var GLAccountRegEx = new RegExp("[0-9]*\.?[0-9]*[1-9]", "gi");
		var defaultGLAccountID = $('##DefaultGLAccountID').val();

		if(($('##DefaultGLAccountID').length && $.trim(defaultGLAccountID).length == 0) || !(GLAccountRegEx.test($.trim(defaultGLAccountID)))) {
			arrReq[arrReq.length]	= 'Select a GL Account used for new events on this calendar';
		}

		if($('##categoryName').length && $.trim($('##categoryName').val()).length == 0) arrReq[arrReq.length] = "Enter the category name.";
		if($('##categoryShortName').length && $.trim($('##categoryShortName').val()).length == 0) arrReq[arrReq.length] = "Enter the category short name.";
		if($('##categoryColor').length && $.trim($('##categoryColor').val()).length == 0) arrReq[arrReq.length] = "Select the category color.";
		
		if (arrReq.length > 0) {							
			mca_showAlert('err_createapp', arrReq.join('<br/>'), true);
			toggleFinishButton(true);
			return false;
		}
		<cfif variables.allowPageNameChange>
			arrPromises.push(
				new Promise(function(resolve, reject) {
					var checkPageNameResult = function(r) {
						if (r.success && r.success.toLowerCase() == 'true'){
							if(r.pageexists == true) {
								setDuplicateMessage($('##pageBox'), $('##pageText'), $('##pageImg'), false, 'Page Name already used!');
								mca_showAlert('err_createapp', 'Page Name already used.', true);
								toggleFinishButton(true);
								reject();
							}
							else resolve();
						} else {
							mca_showAlert('err_createapp', 'We were unable to check whether this page name exists.');
							toggleFinishButton(true);
							reject();
						}
					};
					if($.trim($('##pageName').val()).length) {
						checkPageExists($.trim($('##pageName').val()),checkPageNameResult);
					}
					else resolve();
				})
			);	
		</cfif>					
		Promise.all(arrPromises).then(function(){
			thisForm.submit();
		}).catch((error) => {
			return false;
		});	
		return false;
	}
	
	<cfif variables.isCommunityReady AND val(local.isFromCommunity) EQ 0>
		$(function() { doDeployToComm(); });
	</cfif>
</script>
</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.appInstanceFormJS)#">

<cfoutput>
<div id="err_createapp" class="alert alert-danger mb-4 mt-2 d-none"></div>
<form name="frmCreateApp" id="frmCreateApp" action="/?#cgi.QUERY_STRING#" method="POST" onsubmit="return validateCreateAppInstanceForm();">
	<input type="hidden" name="lid"  id="lid" value="#arguments.event.getValue('lid')#">
	<input type="hidden" name="pageTemplateID"  id="pageTemplateID" value="#arguments.event.getValue('pageTemplateID')#">
	<input type="hidden" name="allowReturnAfterLogin"  id="allowReturnAfterLogin" value="#arguments.event.getValue('allowReturnAfterLogin')#">

	<cfif len(trim(arguments.event.getValue('error.errorMessage')))>
		<div class="alert alert-danger mb-2">
			Correct the following errors:<br/>
			<cfloop list="#arguments.event.getValue('error.errorMessage')#" delimiters="|" index="local.currentMessage">
				- #local.currentMessage#<br />
			</cfloop>
		</div>
	</cfif>

	<cfif variables.allowPageNameChange>
		<div class="form-row">
			<div class="col">
				<div class="form-label-group">
					<input type="text" name="pageName" id="pageName" class="form-control" autocomplete="off" value="#arguments.event.getValue('pageName')#"  onblur="doesPageExist(this.value);" maxlength="50">                      
					<label for="pageName">Page Name <span class="text-danger">*</span></label>
				</div>
				<div id="pageBox" class="form-text small mb-2 d-none">
					<i class="fa-solid" id="pageImg"></i> <span id="pageText"></span>
				</div>
			</div>
		</div>
	<cfelse>
		<div class="form-group row">
			<label for="pageName" class="col-sm-4 col-form-label-sm font-size-md">Page Name <span class="text-danger">*</span>:</label>
			<div class="col-sm-7">
				#local.appInfo.suggestedPageName# <cfinput type="hidden" name="pageName"  id="pageName" value="#local.appInfo.suggestedPageName#">
			</div>
		</div>
	</cfif>
	<div id="showSections">
		<cfif val(local.isFromCommunity) EQ 0>
			<div class="form-group" >
				<div class="form-label-group">
					<select name="sectionID" id="sectionID" class="form-control">
						<cfloop query="local.getSections">
							<option value="#local.getSections.sectionID#"<cfif arguments.event.getValue('sectionID') EQ local.getSections.sectionID> selected</cfif>>#local.getSections.thePathExpanded#</option>
						</cfloop>
					</select>
					<label for="sectionID">Section</label>
				</div>
			</div>
		</cfif>	
	</div>
	<div class="form-group" >
		<div class="form-label-group">
			<select name="pageModeID" id="pageModeID" class="form-control">
				<option value="0">No Override</option>
				<cfloop query="local.qryModes">
					<option value="#local.qryModes.modeID#"<cfif arguments.event.getValue('pageModeID') EQ local.qryModes.modeID> selected</cfif>>#local.qryModes.modeName#</option>
				</cfloop>
			</select>
			<label for="pageModeID">Mode Override</label>
		</div>
	</div>
	<div class="form-row">
		<div class="col">
			<div class="form-label-group">
				<input type="text" name="pageTitle" id="pageTitle" class="form-control" autocomplete="off" value="#arguments.event.getValue('pageTitle')#">                      
				<label for="pageTitle">Page Title <span class="text-danger">*</span></label>
			</div>
		</div>
	</div>
	<div class="form-group">
		<div class="form-label-group">
			<textarea name="pageDesc" id="pageDesc" class="form-control" cols="60" rows="4" maxlength="400">#arguments.event.getValue('pageDesc')#</textarea>
			<label for="pageDesc">Page Description <span class="text-danger">*</span></label>
		</div>
	</div>
	<div class="form-row">
		<div class="col">
			<div class="form-label-group">
				<input type="text" name="appInstanceName" id="appInstanceName" class="form-control" autocomplete="off" value="#arguments.event.getValue('appInstanceName')#">                      
				<label for="appInstanceName">Calendar Name <span class="text-danger">*</span></label>
			</div>
		</div>
	</div>
	<div class="form-row">
		<div class="col">
			<div class="form-label-group">
				<textarea name="appInstanceDesc" id="appInstanceDesc" class="form-control" cols="60" rows="4" maxlength="400">#arguments.event.getValue('appInstanceDesc')#</textarea>
				<label for="appInstanceDesc">Calendar Description <span class="text-danger">*</span></label>
			</div>
		</div>
	</div>
	
	<cfif val(local.isFromCommunity)>
		<input type="hidden" name="commSRID" id="commSRID" value="#arguments.event.getValue('commSRID')#">
		<input type="hidden" name="deployToComm" id="deployToComm" value="1">
	<cfelse>
		<cfif variables.isCommunityReady>
			<div class="form-group row p-1">
				<div class="col-sm-5">
					<span>Will this application be used within an e-community?</span>		
				</div>
				<div class="col-sm-7">
					<div class="form-check-inline">
						<input type="radio" name="deployToComm" id="deployToCommYes" value="1" class="form-check-input" onClick="doDeployToComm();" <cfif arguments.event.getValue('deployToComm', 0) EQ 1>checked</cfif>>
						<label class="form-check-label" for="deployToCommYes">Yes</label>
					</div>
					<div class="form-check-inline">
						<input type="radio" name="deployToComm" id="deployToCommNo" value="0" class="form-check-input" onClick="doDeployToComm();" <cfif arguments.event.getValue('deployToComm', 0) NEQ 1>checked</cfif>>
						<label class="form-check-label" for="deployToCommNo">No</label>
					</div>		
				</div>
			</div>
			<div class="form-group" id="showCommunities">
				<div class="form-label-group">
					<select name="commSRID" id="commSRID" class="form-control">
						<option value="0">Choose an e-Community</option>
						<cfloop query="local.qryCommunities">
							<option value="#local.qryCommunities.siteResourceID#"<cfif arguments.event.getValue('commSRID') EQ local.qryCommunities.siteResourceID> SELECTED</cfif>>#local.qryCommunities.communityName#</option>
						</cfloop>
					</select>
					<label for="commSRID">E-Community <span class="text-danger">*</span></label>
				</div>
			</div>
		</cfif>
	</cfif>	

	<div class="form-group row p-1">
		<input type="hidden" name="DefaultGLAccountID" id="DefaultGLAccountID" value="#val(arguments.event.getValue('defaultGLAccountID'))#" />
		<label for="DefaultGLAccountPath" class="col-sm-5 col-form-label-sm font-size-md">GL Account for New Events <span class="text-danger">*</span>:</label>
		<div class="col-sm-7">
			<span id="DefaultGLAccountPath"><cfif len(arguments.event.getValue('DefaultGLAccountPath',''))>#arguments.event.getValue('GLAccountPath')#<cfelse>(No account selected)</cfif></span>
			<br/><a href="javascript:selectGLAccount('p');">Choose GL Account</a>
		</div>
	</div>
	<div id="defaultCategory" class="card card-box">
		<div class="card-header bg-light">
			<div class="card-header--title">
				<b>Default Category</b>
			</div>
		</div>
		<div class="card-body">
			<div class="form-row">
				<div class="col">
					<div class="form-label-group">
						<input type="text" name="categoryName" id="categoryName" class="form-control" autocomplete="off" maxlength="50" value="">                      
						<label for="categoryName">Category Name <span class="text-danger">*</span></label>
					</div>
				</div>
			</div>
			<div class="form-row">
				<div class="col">
					<div class="form-label-group">
						<input type="text" name="categoryShortName" id="categoryShortName" class="form-control" autocomplete="off" maxlength="20" value="">                      
						<label for="categoryShortName">Category Short Name <span class="text-danger">*</span></label>
					</div>
				</div>
			</div>
			<div class="form-group row">
				<label for="categoryColor" class="col-sm-4 col-form-label-sm font-size-md">Category Color <span class="text-danger">*</span></label>
				<div class="col-sm-8">
					<input type="color" name="categoryColor" id="categoryColor" value="" class="form-control form-control-sm" autocomplete="off" maxlength="7" style="width:100px;">
				</div>
			</div>
		</div>
	</div>
	<div class="form-group text-right mt-3">
		<button type="submit" name="btnSaveEventDetails" id="btnSaveEventDetails" class="btn btn-sm btn-primary d-none">Save Information</button>
	</div>
</form>
</cfoutput>