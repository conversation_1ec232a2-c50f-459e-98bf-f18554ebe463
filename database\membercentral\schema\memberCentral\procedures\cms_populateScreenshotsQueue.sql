ALTER PROC dbo.cms_populateScreenshotsQueue
@siteID int,
@contentVersionID int,
@featureImageConfigID int,
@referenceType varchar(30),
@referenceID int,
@viewportWidth int,
@viewportHeight int,
@deviceScaleFactor smallint,
@fullPage bit,
@enteredByMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @itemID int, @queueTypeID int, @statusReady int, @insertdate datetime = getdate(), @nextAttemptDate datetime;
	SELECT @queueTypeID = queueTypeID FROM platformQueue.dbo.tblQueueTypes WHERE queueType = 'screenshots';
	SELECT @statusReady = queueStatusID FROM platformQueue.dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID AND queueStatus = 'readyToProcess';

	SELECT @itemID = itemID
	FROM platformQueue.dbo.queue_screenshots
	WHERE siteID = @siteID
	AND contentVersionID = @contentVersionID
	AND viewportWidth = @viewportWidth
	AND viewportHeight = @viewportHeight
	AND deviceScaleFactor = @deviceScaleFactor
	AND fullPage = @fullPage
	AND ISNULL(featureImageConfigID,0) = ISNULL(@featureImageConfigID,0)
	AND ISNULL(referenceType,'') = ISNULL(@referenceType,'')
	AND ISNULL(referenceID,0) = ISNULL(@referenceID,0);

	IF @itemID IS NULL BEGIN
		-- delay if same referenceID/referenceType exists
		SELECT @nextAttemptDate = MAX(nextAttemptDate)
		FROM platformQueue.dbo.queue_screenshots
		WHERE referenceType = @referenceType
		AND referenceID = @referenceID;

		IF @nextAttemptDate IS NOT NULL
			SET @nextAttemptDate = DATEADD(MINUTE,5,@nextAttemptDate);
		ELSE
			SET @nextAttemptDate = @insertdate;
		
		INSERT INTO platformQueue.dbo.queue_screenshots (siteID, contentVersionID, viewportWidth, viewportHeight, deviceScaleFactor, fullPage, featureImageConfigID,
			referenceType, referenceID, enteredByMemberID, statusID, dateAdded, dateUpdated, nextAttemptDate)
		VALUES (@siteID, @contentVersionID, @viewportWidth, @viewportHeight, @deviceScaleFactor, @fullPage, @featureImageConfigID,
			@referenceType, @referenceID, @enteredByMemberID, @statusReady, @insertdate, @insertdate, @nextAttemptDate);

		EXEC dbo.sched_resumeTask @name='Process Screenshots Queue', @engine='MCLuceeLinux';
	END
	
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
