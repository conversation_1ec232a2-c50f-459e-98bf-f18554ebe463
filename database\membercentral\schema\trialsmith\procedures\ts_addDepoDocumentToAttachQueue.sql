ALTER PROC dbo.ts_addDepoDocumentToAttachQueue
@depoDocumentID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @statusReady int, @nowDate datetime = GETDATE();

	SELECT @queueTypeID = queueTypeID FROM platformQueue.dbo.tblQueueTypes WHERE queueType = 'depoDocumentsAttach';
	SELECT @statusReady = queueStatusID FROM platformQueue.dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID AND queueStatus = 'readyToProcess';

	IF NOT EXISTS (SELECT 1 FROM platformQueue.dbo.queue_depoDocumentsAttach WHERE depoDocumentID = @depoDocumentID) BEGIN
		INSERT INTO platformQueue.dbo.queue_depoDocumentsAttach (depoDocumentID, dateAdded, dateUpdated, statusID)
		VALUES (@depoDocumentID, @nowDate, @nowDate, @statusReady);

		EXEC membercentral.dbo.sched_resumeTask @name='Process Depo Document Attachments', @engine='BERLinux';
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
