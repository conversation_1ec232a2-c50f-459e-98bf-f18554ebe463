ALTER PROC dbo.cp_cancelContributionFromQueue
@itemID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @readyToProcessStatusID int, @doneStatusID int, @siteID int, @contributionID int, @memberID int, 
		@AROption char(1), @adjustCount int, @offsetCount int, @nowDateTime datetime, @nowDate date, @cancelledStatusID int, 
		@oldStatusID int, @cancellationDate date, @runByMemberID int, @deletedStatusID int, @minTID int, @invoiceProfileID int, 
		@invoiceID int, @adjAmount decimal(18,2), @GLAccountID int, @invoiceNumber varchar(18), @dueDate date, @trashID int, 
		@programID int, @scheduleID int, @detail varchar(500), @orgID int, @transactionID int, @processingStatusID int;
	DECLARE @tblTIDs TABLE (transactionID int PRIMARY KEY, typeID int, creditGLAccountID int, statusID int, amount decimal(18,2), detail varchar(500));
	DECLARE @tblInvoices TABLE (invoiceID int PRIMARY KEY, invoiceProfileID int);
	DECLARE @tblAdjust TABLE (transactionID int PRIMARY KEY, amountToAdjust decimal(18,2), creditGLAccountID int);
	DECLARE @tblOffsets TABLE (transactionID int PRIMARY KEY, amount decimal(18,2), dueDate date, GLAccountID int, scheduleID int, detail varchar(500));

	select @queueTypeID = queueTypeID from platformQueue.dbo.tblQueueTypes where queueType = 'contributionsCancel';
	select @readyToProcessStatusID = queueStatusID from platformQueue.dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'readyToProcess';
	select @processingStatusID = queueStatusID from platformQueue.dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'processing';
	select @doneStatusID = queueStatusID from platformQueue.dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'done';
	select @cancelledStatusID = statusID from dbo.cp_statuses where statusName = 'Cancelled';
	select @deletedStatusID = statusID from dbo.cp_statuses where statusName = 'Deleted';
	select @runByMemberID = dbo.fn_ams_getMCSystemMemberID();
	set @nowDateTime = GETDATE();
	set @nowDate = @nowDateTime;

	-- get contribution info from queue item
	select @siteID=siteID, @contributionID=contributionID, @memberID=memberID, @AROption=AROption
	from platformQueue.dbo.queue_contributionsCancel
	where itemID = @itemID
	and statusID = @readyToProcessStatusID;

	select @programID = c.programID, @oldStatusID = c.statusID, @cancellationDate = c.cancellationDate
	from dbo.cp_contributions as c
	inner join dbo.cp_statuses as cps on cps.statusID = c.statusID
	where c.contributionID = @contributionID
	and cps.statusCode in ('A','C','P','E','Q');

	IF @contributionID IS NULL OR @oldStatusID IS NULL
		goto on_end;

	update platformQueue.dbo.queue_contributionsCancel
	set statusID = @processingStatusID, dateUpdated = getdate()
	where itemID = @itemID;

	select @orgID = dbo.fn_getOrgIDFromSiteID(@siteID);

	-- get all transactions ties to this contribution
	INSERT INTO @tblTIDs (transactionID, typeID, creditGLAccountID, statusID, amount, detail)
	select transactionID, typeID, creditGLAccountID, statusID, amount, detail
	from dbo.fn_cp_contributionTransactions(@contributionID);

	-- put all open invoices used for contributor into table since they were already created and can be used for adjustments
	insert into @tblInvoices (invoiceID, invoiceProfileID)
	select distinct i.invoiceID, i.invoiceProfileID
	from @tblTIDs as ct
	inner join dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.transactionID = ct.transactionID
	inner join dbo.tr_invoices as i on i.orgID = @orgID and i.invoiceID = it.invoiceID
	inner join dbo.tr_invoiceStatuses as invs on invs.statusID = i.statusID
	where invs.status = 'Open';

	-- If the AROption is A OR D
	IF @AROption IN ('A','D') BEGIN

		-- get all contribution-related sales transactions we need to adjust
		INSERT INTO @tblAdjust (transactionID, amountToAdjust, creditGLAccountID)
		select ct.transactionID, tsFull.cache_amountAfterAdjustment, ct.creditGLAccountID
		from @tblTIDs as ct
		cross apply dbo.fn_tr_transactionSalesWithDIT(@orgID,ct.transactionID) as tsFull
		where ct.typeID = 1
		and ct.statusID = 1
		and tsFull.cache_amountAfterAdjustment > 0
		OPTION(RECOMPILE);
			set @adjustCount = @@ROWCOUNT;

		-- get all installments we need to offset
		INSERT INTO @tblOffsets (transactionID, amount, dueDate, GLAccountID, scheduleID, detail)
		select ct.transactionID, ct.amount, ti.dueDate, ct.creditGLAccountID, ti.scheduleID, ct.detail
		from @tblTIDs as ct
		inner join dbo.tr_transactionInstallments as ti on ti.orgID = @orgID and ti.transactionID = ct.transactionID
		where ct.statusID = 1
		and ti.isActive = 1
		and ti.isConverted = 0
		and ti.isPaidOnCreate = 0;
			set @offsetCount = @@ROWCOUNT;

		BEGIN TRAN;
			IF @AROption = 'A' BEGIN
				-- change the status of the contribution to Cancelled. 
				INSERT INTO dbo.cp_statusHistory (contributionID, updateDate, statusID, oldStatusID, enteredByMemberID)
				VALUES (@contributionID, getdate(), @cancelledStatusID, @oldStatusID, @runByMemberID);

				update dbo.cp_contributions 
				set cancellationDate = null,
					statusID = @cancelledStatusID
				where contributionID = @contributionID;
			END
			ELSE BEGIN
				-- if not already cancelled, change the status of the contribution to Cancelled. 
				IF @oldStatusID <> @cancelledStatusID
					INSERT INTO dbo.cp_statusHistory (contributionID, updateDate, statusID, oldStatusID, enteredByMemberID)
					VALUES (@contributionID, getdate(), @cancelledStatusID, @oldStatusID, @runByMemberID);

				-- change the status of the contribution to Deleted.
				INSERT INTO dbo.cp_statusHistory (contributionID, updateDate, statusID, oldStatusID, enteredByMemberID)
				VALUES (@contributionID, getdate(), @deletedStatusID, @cancelledStatusID, @runByMemberID);

				update dbo.cp_contributions 
				set cancellationDate = null,
					statusID = @deletedStatusID
				where contributionID = @contributionID;
			END
			
			-- all active sale transactions connected to this contribution should be adjusted to 0.
			IF @adjustCount > 0 BEGIN
				SELECT @minTID = min(transactionID) from @tblAdjust;
				WHILE @minTID IS NOT NULL BEGIN
					select @invoiceProfileID = null, @invoiceID = null, @adjAmount = null, @GLAccountID = null;

					select @adjAmount = amountToAdjust*-1, @GLAccountID = creditGLAccountID from @tblAdjust where transactionID = @minTID;
					select @invoiceProfileID = invoiceProfileID from dbo.tr_glAccounts where orgID = @orgID and glAccountID = @GLAccountID;
					select top 1 @invoiceID = invoiceID from @tblInvoices where invoiceProfileID = @invoiceProfileID;
				
					-- if necessary, create invoice assigned to contributor based on invoice profile
					IF @invoiceID is null BEGIN
						EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@runByMemberID, 
							@assignedToMemberID=@memberID, @dateBilled=@nowDate, @dateDue=@nowdate, 
							@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT;

						insert into @tblInvoices (invoiceID, invoiceProfileID)
						values (@invoiceID, @invoiceProfileID);
					END	

					EXEC dbo.tr_createTransaction_adjustment @recordedOnSiteID=@siteID, @recordedByMemberID=@runByMemberID, 
						@statsSessionID=null, @amount=@adjAmount, @taxAmount=null, @transactionDate=@nowDateTime,
						@autoAdjustTransactionDate=1, @saleTransactionID=@minTID, @invoiceID=@invoiceID, @byPassTax=0, 
						@byPassAccrual=0, @xmlSchedule=null, @transactionID=@trashID OUTPUT;
				
					SELECT @minTID = min(transactionID) from @tblAdjust where transactionID > @minTID;
				END

				delete from @tblAdjust;
			END

			-- all active installment transactions not yet converted to sales should have an installment offset
			IF @offsetCount > 0 BEGIN
				set @minTID = null;
				select @minTID = min(transactionID) from @tblOffsets;
				WHILE @minTID is not null BEGIN
					select @dueDate = null, @adjAmount = null, @GLAccountID = null, @scheduleID = null, @detail = null, @transactionID = null;

					select @dueDate=dueDate, @GLAccountID=GLAccountID, @adjAmount=amount*-1, @scheduleID=scheduleID, @detail=detail
					from @tblOffsets
					where transactionID = @minTID;

					EXEC dbo.tr_createTransaction_installment @ownedByOrgID=@orgID, @recordedOnSiteID=@siteID, 
						@assignedToMemberID=@memberID, @recordedByMemberID=@runByMemberID, @statsSessionID=null, 
						@detail=@detail, @amount=@adjAmount, @transactionDate=@dueDate, @revenueGLAccountID=@GLAccountID, 
						@scheduleID=@scheduleID, @offsetInstallmentTID=@minTID, @transactionID=@transactionID OUTPUT;

					select @minTID = min(transactionID) from @tblOffsets where transactionID > @minTID;
				END

				delete from @tblOffsets;
			END

			-- all tr_transactionInstallments should be set to not active
			UPDATE ti
			set ti.isActive = 0
			from @tblTIDs as ct
			inner join dbo.tr_transactionInstallments as ti on ti.orgID = @orgID and ti.transactionID = ct.transactionID
			where ti.isActive = 1;
		COMMIT TRAN;

		GOTO on_done;
	END

	-- If the AROption is B 
	IF @AROption = 'B' BEGIN
		-- get all unpaid sales transactions we need to adjust
		INSERT INTO @tblAdjust (transactionID, amountToAdjust, creditGLAccountID)
		select ct.transactionID, tsFull.cache_amountAfterAdjustment - tsFull.cache_activePaymentAllocatedAmount - tsFull.cache_pendingPaymentAllocatedAmount, ct.creditGLAccountID
		from @tblTIDs as ct
		cross apply dbo.fn_tr_transactionSalesWithDIT(@orgID,ct.transactionID) as tsFull
		where ct.typeID = 1
		and ct.statusID = 1
		and tsFull.cache_amountAfterAdjustment - tsFull.cache_activePaymentAllocatedAmount - tsFull.cache_pendingPaymentAllocatedAmount > 0
		OPTION(RECOMPILE);
			set @adjustCount = @@ROWCOUNT;

		-- get all installments we need to offset
		INSERT INTO @tblOffsets (transactionID, amount, dueDate, GLAccountID, scheduleID, detail)
		select ct.transactionID, ct.amount, ti.dueDate, ct.creditGLAccountID, ti.scheduleID, ct.detail
		from @tblTIDs as ct
		inner join dbo.tr_transactionInstallments as ti on ti.orgID = @orgID and ti.transactionID = ct.transactionID
		where ct.statusID = 1
		and ti.isActive = 1
		and ti.isConverted = 0
		and ti.isPaidOnCreate = 0
		and ti.dueDate >= @cancellationDate;
			set @offsetCount = @@ROWCOUNT;

		BEGIN TRAN;
			-- If CancellationDate of the contributionID is today or earlier, change the status of the contribution to Cancelled (changeDate should be the cancellationDate). 
			IF @cancellationDate <= @nowDate BEGIN
				INSERT INTO dbo.cp_statusHistory (contributionID, updateDate, statusID, oldStatusID, enteredByMemberID)
				VALUES (@contributionID, @cancellationDate, @cancelledStatusID, @oldStatusID, @runByMemberID);

				update dbo.cp_contributions 
				set cancellationDate = null,
					statusID = @cancelledStatusID
				where contributionID = @contributionID;
			END

			-- all active unpaid sale transactions connected to this contribution should be adjusted to 0.
			IF @adjustCount > 0 BEGIN
				SELECT @minTID = min(transactionID) from @tblAdjust;
				WHILE @minTID IS NOT NULL BEGIN
					select @invoiceProfileID = null, @invoiceID = null, @adjAmount = null, @GLAccountID = null;

					select @adjAmount = amountToAdjust*-1, @GLAccountID = creditGLAccountID from @tblAdjust where transactionID = @minTID;
					select @invoiceProfileID = invoiceProfileID from dbo.tr_glAccounts where orgID = @orgID and glAccountID = @GLAccountID;
					select top 1 @invoiceID = invoiceID from @tblInvoices where invoiceProfileID = @invoiceProfileID;
				
					-- if necessary, create invoice assigned to contributor based on invoice profile
					IF @invoiceID is null BEGIN
						EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@runByMemberID, 
							@assignedToMemberID=@memberID, @dateBilled=@nowDate, @dateDue=@nowdate, 
							@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT;

						insert into @tblInvoices (invoiceID, invoiceProfileID)
						values (@invoiceID, @invoiceProfileID);
					END	

					EXEC dbo.tr_createTransaction_adjustment @recordedOnSiteID=@siteID, @recordedByMemberID=@runByMemberID, 
						@statsSessionID=null, @amount=@adjAmount, @taxAmount=null, @transactionDate=@nowdateTime,
						@autoAdjustTransactionDate=1, @saleTransactionID=@minTID, @invoiceID=@invoiceID, @byPassTax=0, 
						@byPassAccrual=0, @xmlSchedule=null, @transactionID=@trashID OUTPUT;
				
					SELECT @minTID = min(transactionID) from @tblAdjust where transactionID > @minTID;
				END

				delete from @tblAdjust;
			END

			-- all active installment transactions with due date after cancellationDate not yet converted to sales should have an installment offset
			IF @offsetCount > 0 BEGIN
				select @minTID = min(transactionID) from @tblOffsets;
				WHILE @minTID is not null BEGIN
					select @dueDate = null, @adjAmount = null, @GLAccountID = null, @scheduleID = null, @detail = null, @transactionID = null;

					select @dueDate=dueDate, @GLAccountID=GLAccountID, @adjAmount=amount*-1, @scheduleID=scheduleID, @detail=detail
					from @tblOffsets
					where transactionID = @minTID;

					EXEC dbo.tr_createTransaction_installment @ownedByOrgID=@orgID, @recordedOnSiteID=@siteID, 
						@assignedToMemberID=@memberID, @recordedByMemberID=@runByMemberID, @statsSessionID=null, 
						@detail=@detail, @amount=@adjAmount, @transactionDate=@dueDate, @revenueGLAccountID=@GLAccountID, 
						@scheduleID=@scheduleID, @offsetInstallmentTID=@minTID, @transactionID=@transactionID OUTPUT;

					select @minTID = min(transactionID) from @tblOffsets where transactionID > @minTID;
				END

				delete from @tblOffsets;
			END

			-- tr_transactionInstallments after cancellationdate should be set to not active
			UPDATE ti
			set ti.isActive = 0
			from @tblTIDs as ct
			inner join dbo.tr_transactionInstallments as ti on ti.orgID = @orgID and ti.transactionID = ct.transactionID
			where ti.isActive = 1
			and ti.dueDate >= @cancellationDate;
		COMMIT TRAN;

		GOTO on_done;
	END

	on_done:
		-- process conditions
		IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL 
			DROP TABLE #tblMCQRun;
		CREATE TABLE #tblMCQRun (orgID int, memberID int INDEX IX_tblMCQRun_memberID, conditionID int);

		INSERT INTO #tblMCQRun (orgID, memberID, conditionID)
		select distinct @orgID, @memberID, c.conditionID
		from dbo.ams_virtualGroupConditions as c
		cross apply (
			select cv.conditionValue
			from dbo.ams_virtualGroupConditionValues as cv
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'programList'
			where cv.conditionID = c.conditionID
		) as programList(val)
		where c.orgID = @orgID
		and c.fieldCode in ('cp_entry','cp_valuesum')
		and programList.val = cast(@programID as varchar(10));

		EXEC platformQueue.dbo.queue_triggerMCGCache @runImmediately=0, @type='ConditionsAndGroupsChanged';

		IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL 
			DROP TABLE #tblMCQRun;


		update platformQueue.dbo.queue_contributionsCancel
		set statusID = @doneStatusID, dateUpdated = getdate()
		where itemID = @itemID;

		delete from platformQueue.dbo.queue_contributionsCancel
		where itemID = @itemID;

	on_end:
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
