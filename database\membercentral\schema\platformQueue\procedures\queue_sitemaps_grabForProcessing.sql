ALTER PROC dbo.queue_sitemaps_grabForProcessing

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @queueTypeID int, @statusReady int, @statusGrabbed int;
	select @queueTypeID = queueTypeID from dbo.tblQueueTypes where queueType = 'sitemaps';
	select @statusReady = queueStatusID from dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'readyToProcess';
	select @statusGrabbed = queueStatusID from dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'processingItem';

	IF OBJECT_ID('tempdb..#tmpSitesToRun') IS NOT NULL 
		DROP TABLE #tmpSitesToRun;
	CREATE TABLE #tmpSitesToRun (itemID int, siteID int);

	-- dequeue in order of dateAdded.
	update qi WITH (UPDLOCK, READPAST)
	set qi.statusID = @statusGrabbed,
		qi.dateUpdated = getdate()
		OUTPUT inserted.itemID, inserted.siteID
		INTO #tmpSitesToRun
	from dbo.queue_sitemaps as qi
	inner join (
		select top(1) qi2.itemID 
		from dbo.queue_sitemaps as qi2
		where qi2.statusID = @statusReady
		order by qi2.dateAdded, qi2.itemID
		) as batch on batch.itemID = qi.itemID
	where qi.statusID = @statusReady;

	select itemID, siteID
	from #tmpSitesToRun;

	IF OBJECT_ID('tempdb..#tmpSitesToRun') IS NOT NULL 
		DROP TABLE #tmpSitesToRun;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
