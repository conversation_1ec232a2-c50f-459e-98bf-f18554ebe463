ALTER PROC dbo.queue_SWVideoPreview_grabForProcessing
@batchSize int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @queueTypeID int, @statusReady int, @statusGrabbed int;

	select @queueTypeID = queueTypeID from dbo.tblQueueTypes where queueType = 'SWVideoPreview';
	select @statusReady = queueStatusID from dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'readyToProcess';
	select @statusGrabbed = queueStatusID from dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'grabbedForProcessing';

	IF OBJECT_ID('tempdb..#tmpVideoPreviews') IS NOT NULL 
		DROP TABLE #tmpVideoPreviews;
	CREATE TABLE #tmpVideoPreviews (itemID int PRIMARY KEY);

	update qi WITH (UPDLOCK, READPAST)
	set qi.statusID = @statusGrabbed
		OUTPUT inserted.itemID
		INTO #tmpVideoPreviews
	from dbo.queue_SWVideoPreview as qi
	inner join (
		select top(@BatchSize) itemID
		from dbo.queue_SWVideoPreview
		where statusID = @statusReady
		order by dateAdded, itemID
		) as batch on batch.itemID = qi.itemID
	where qi.statusID = @statusReady;

	select qidd.itemID, qidd.previewID, qidd.seminarID, qidd.titleID, qidd.baseFileID, qidd.timeCodeStart, qidd.timeCodeEnd, 
		qidd.dateAdded, isnull(sP.participantID,tP.participantID) as participantID, isnull(sP.orgcode,tP.orgcode) as participantOrgCode
	from #tmpVideoPreviews as qid
	inner join dbo.queue_SWVideoPreview as qidd on qidd.itemID = qid.itemID
	left outer join seminarWeb.dbo.tblSeminars as s 
			inner join seminarWeb.dbo.tblParticipants as sP on sP.participantID = s.participantID
		on s.seminarID = qidd.seminarID
	left outer join seminarWeb.dbo.tblTitles as t 
			inner join seminarWeb.dbo.tblParticipants as tP on tP.participantID = t.participantID
		on t.titleID = qidd.titleID
	order by qid.itemID;

	IF OBJECT_ID('tempdb..#tmpVideoPreviews') IS NOT NULL 
		DROP TABLE #tmpVideoPreviews;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
END CATCH
GO
