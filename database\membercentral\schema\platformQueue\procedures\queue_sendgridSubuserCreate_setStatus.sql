ALTER PROC dbo.queue_sendgridSubuserCreate_setStatus
@itemID int,
@statusCode varchar(30)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @subuserQueueStatusID int;

	select @queueTypeID = queueTypeID
	from dbo.tblQueueTypes
	where queueType = 'SendgridSubuserCreate';

	select @subuserQueueStatusID = queueStatusID 
	from dbo.tblQueueStatuses
	where queueTypeID = @queueTypeID
	and queueStatus = @statusCode;

	UPDATE dbo.queue_sendgridSubuserCreate
	SET statusID = @subuserQueueStatusID,
		dateUpdated = getdate()
	WHERE itemID = @itemID;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
