<cfcomponent output="false" hint="Component that wraps the Member import functionality">
	
	<!--- error codes:
	1	- problem uploading, bad file, can't move file
	101	- can't read excel file to determine sheets
	102	- no readable sheets in excel file or multiple sheets in file.

	4	- stored proc error in pre-processing
	6	- one or more required files in package is missing (or number of files isnt what it should be)
	8	- one or more files in the package has multiple sheets.
	104	- duplicate columns in file
	105	- fatal data errors
	200	- no import file passed to finalize import
	201	- flat files no longer exist
	202	- stored proc error in final import
	--->

	<cffunction name="processPartialMemberImport" access="package" output="false" returntype="struct">
		<cfargument name="orgID" type="numeric" required="yes">
		<cfargument name="importTitle" type="string" required="yes">
		<cfargument name="orgCode" type="string" required="yes">
		<cfargument name="autogenMemNum" type="boolean" required="yes">
		<cfargument name="activateIncMembers" type="boolean" required="yes">
		<cfargument name="inactivateNonIncMembers" type="boolean" required="yes">
		<cfargument name="ignoreInvalidColumns" type="boolean" required="yes">
		<cfargument name="thresholdLimit" type="numeric" required="yes">

		<cfset var local = structNew()>
		<cfset local.objImportModule = CreateObject("component","model.admin.common.modules.import.import")>
		<cfset local.returnStruct = { success=true, errorCode=999, errorInfo=StructNew() }>

		<!--- Attempt upload --->
		<cftry>
			<cfset local.strImportFile = {}>
			<cfset local.strImportFile.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.orgcode)>

			<cffile action="upload" filefield="importfilename" destination="#local.strImportFile.strFolder.folderPath#" result="local.uploadResult" nameconflict="OVERWRITE">

			<cfset local.strImportFile.uploadFilenameWithExt = local.uploadResult.ServerFile>
			<cfset local.strImportFile.uploadFilenameWithoutExt = local.uploadResult.ServerFileName>
			<cfset local.strImportFile.uploadFilenameExt = local.uploadResult.ServerFileExt>

			<cfif listFindNoCase("csv,xls",local.strImportFile.uploadFilenameExt) is 0>
				<cffile action="DELETE" file="#local.strImportFile.strFolder.folderPath#/#local.strImportFile.uploadFilenameWithExt#">
				<cfif local.strImportFile.uploadFilenameExt eq "xlsx">
					<cfset local.errMsg = "Uploaded file was not in the proper format (#local.strImportFile.uploadFilenameExt#). Be sure to save your Excel file using the ""Excel 97-2003 Workbook (.XLS)"" format and try again.">
				<cfelse>
					<cfset local.errMsg = "Uploaded file was not in the proper format (#local.strImportFile.uploadFilenameExt#).">
				</cfif>
				<cfset local.returnStruct.success = false>
				<cfset local.returnStruct.errorCode = 1>
				<cfset StructInsert(local.returnStruct.errorInfo,local.returnStruct.errorCode,local.errMsg)>
				<cfreturn local.returnStruct>
			</cfif> 
		<cfcatch type="Any">
			<cfset local.returnStruct.success = false>
			<cfset local.returnStruct.errorCode = 1>
			<cfset StructInsert(local.returnStruct.errorInfo,local.returnStruct.errorCode,"There was a problem uploading the selected file. Only comma separated value files (CSV) and Microsoft Excel (XLS) files can be uploaded. Try the upload again or contact us for assistance.")>
			<cfreturn local.returnStruct>
		</cfcatch>
		</cftry>

		<!--- if XLS, parse for sheets --->
		<cfif local.strImportFile.uploadFilenameExt eq "xls">
			<cfset local.parseResult = local.objImportModule.parseXLS(strFilePath="#local.strImportFile.strFolder.folderPath#/#local.strImportFile.uploadFilenameWithExt#")>
			<cfset local.returnStruct.success = local.parseResult.success>
			<cfif NOT local.returnStruct.success>
				<cfset local.returnStruct.errorCode = local.parseResult.errorCode>
				<cfset local.returnStruct.errorInfo = local.parseResult.errorInfo>
				<cfreturn local.returnStruct>
			</cfif>
	
			<cfif arrayLen(local.parseResult.arrSheets) neq 1>
				<cfset local.returnStruct.success = false>
				<cfset local.returnStruct.errorCode = 102>
				<cfset StructInsert(local.returnStruct.errorInfo,local.returnStruct.errorCode,'The uploaded Excel file contains #arrayLen(local.parseResult.arrSheets)# sheets. Edit the file to contain only one sheet and try again.')>
				<cfreturn local.returnStruct>
			</cfif>

			<cfset local.lstDateColumns = getPossibleDateColumns(orgID=arguments.orgID)>
			<cfset local.parseResult = local.objImportModule.parseXLSSheet(strFilePath="#local.strImportFile.strFolder.folderPath#/#local.strImportFile.uploadFilenameWithExt#",
													 strFilePathCSV="#local.strImportFile.strFolder.folderPath#/#local.strImportFile.uploadFilenameWithoutExt#.csv",
													 sheetIndex=0,
													 lstDateColumns=local.lstDateColumns)>
			<cfset local.returnStruct.success = local.parseResult.success>
			<cfif NOT local.returnStruct.success>
				<cfset local.returnStruct.errorCode = local.parseResult.errorCode>
				<cfset local.returnStruct.errorInfo = local.parseResult.errorInfo>
				<cfreturn local.returnStruct>
			</cfif>
		</cfif>

		<!--- parse CSV --->
		<cfset local.parseResult = local.objImportModule.parseCSV(stFilePath="#local.strImportFile.strFolder.folderPath#/#local.strImportFile.uploadFilenameWithoutExt#.csv", stFilePathTmp="#local.strImportFile.strFolder.folderPath#/#local.strImportFile.uploadFilenameWithoutExt#Parsed.csv")>
		<cfset local.returnStruct.success = local.parseResult.isErr is 0>
		<cfif NOT local.returnStruct.success>
			<cfset local.returnStruct.errorCode = 101>
			<cfset StructInsert(local.returnStruct.errorInfo,local.returnStruct.errorCode,local.parseResult.errMsg)>
			<cfreturn local.returnStruct>
		</cfif>

		<!--- run first part of import --->
  		<cftry>
			<!--- had to do it in a cfquery because cfstoredproc kept truncating the result --->
            <cfquery name="local.qryImport" datasource="#application.dsn.membercentral.dsn#" result="local.importResult">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
				
					declare @orgID int, @importTitle varchar(200), @runByMemberID int, @importResult xml, @autogenMemNum bit, 
						@activateIncMembers bit, @inactivateNonIncMembers bit, @thresholdLimit int, @ignoreInvalidColumns bit,
						@errCount int, @dataFilename varchar(400), @dataFileExt varchar(3), @environmentName varchar(50);

					set @orgID = <cfqueryparam value="#arguments.orgID#" cfsqltype="CF_SQL_INTEGER">;
					set @importTitle = <cfqueryparam value="#arguments.importTitle#" cfsqltype="CF_SQL_VARCHAR">;
					set @runByMemberID = <cfqueryparam value="#session.cfcuser.memberdata.memberID#" cfsqltype="CF_SQL_INTEGER">;
					set @dataFilename = <cfqueryparam value="#local.strImportFile.strFolder.folderPathUNC#\#local.strImportFile.uploadFilenameWithExt#" cfsqltype="CF_SQL_VARCHAR">;
					set @dataFileExt = <cfqueryparam value="#local.strImportFile.uploadFilenameExt#" cfsqltype="CF_SQL_VARCHAR">;
					set @autogenMemNum = <cfqueryparam value="#arguments.autogenMemNum#" cfsqltype="CF_SQL_BIT">;
					set @activateIncMembers = <cfqueryparam value="#arguments.activateIncMembers#" cfsqltype="CF_SQL_BIT">;
					set @inactivateNonIncMembers = <cfqueryparam value="#arguments.inactivateNonIncMembers#" cfsqltype="CF_SQL_BIT">;
					set @ignoreInvalidColumns = <cfqueryparam value="#arguments.ignoreInvalidColumns#" cfsqltype="CF_SQL_BIT">;
					if @inactivateNonIncMembers = 1
						set @thresholdLimit = <cfqueryparam value="#arguments.thresholdLimit#" cfsqltype="CF_SQL_INTEGER">;
					else 
						set @thresholdLimit = 0;
					set @environmentName = <cfqueryparam value="#application.MCEnvironment#" cfsqltype="CF_SQL_VARCHAR">;

					-- bulk insert from file
					BEGIN TRY
						IF OBJECT_ID('tempdb..##mc_PartialMemImport') IS NOT NULL 
							DROP TABLE ##mc_PartialMemImport;
						CREATE TABLE ##mc_PartialMemImport (
							<cfloop list="#local.parseResult.strTableColumnNames#" index="local.thisCol" delimiters="#chr(7)#">
								[#local.thisCol#] varchar(max)<cfif local.thisCol neq listLast(local.parseResult.strTableColumnNames,chr(7))>, </cfif>
							</cfloop>
						);
						BULK INSERT ##mc_PartialMemImport 
							FROM '#local.strImportFile.strFolder.folderPathUNC#\#local.strImportFile.uploadFilenameWithoutExt#Parsed.csv' 
							WITH (FIELDTERMINATOR='#chr(7)#', FIRSTROW=2);
					END TRY
					BEGIN CATCH
						set @importResult = '<import><errors><error msg="Unable to import the file for processing." /><error msg="' + error_message() + '" /></errors></import>';
						GOTO on_done;
					END CATCH

					-- import file
					BEGIN TRY
						set @importResult = null;
						EXEC dbo.ams_importPartialMemberData @orgID=@orgID, @importTitle=@importTitle, @runByMemberID=@runByMemberID, @dataFilename=@dataFilename, 
							@dataFileExt=@dataFileExt, @autogenMemNum=@autogenMemNum, @activateIncMembers=@activateIncMembers, @inactivateNonIncMembers=@inactivateNonIncMembers, 
							@ignoreInvalidColumns=@ignoreInvalidColumns, @thresholdLimit=@thresholdLimit, @bypassRO=0, @bypassListEmailUpdate=0, @environmentName=@environmentName, 
							@importResult=@importResult OUTPUT;
					END TRY
					BEGIN CATCH
						set @importResult = '<import><errors><error msg="Unable to process the import file." /><error msg="' + error_message() + '" /></errors></import>';
						GOTO on_done;
					END CATCH
				
					on_done:
					set @errCount = @importResult.value('count(/import/errors/error)','int');
					SELECT @importResult as importResult, @errCount as errCount;

					IF OBJECT_ID('tempdb..##mc_PartialMemImport') IS NOT NULL 
						DROP TABLE ##mc_PartialMemImport;
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.returnStruct.importResultXML = xmlparse(local.qryImport.importResult)>
			<cfset local.returnStruct.numFatalErrors = local.qryImport.errCount>

			<cfif local.returnStruct.numFatalErrors gt 0>
				<cfset local.returnStruct.success = false>
				<cfset local.returnStruct.errorCode = 105>
				<cfset StructInsert(local.returnStruct.errorInfo,local.returnStruct.errorCode,'')>
			</cfif>
		<cfcatch type="any">
			<cfset local.returnStruct.success = false>
			<cfset local.returnStruct.errorCode = 1>
			<cfif findNoCase("An object or column name is missing or empty.",cfcatch.detail)>
				<cfset StructInsert(local.returnStruct.errorInfo,local.returnStruct.errorCode,"There was a problem importing the files.<br/>It looks like there is a data column with a blank column heading. This can happen if there is any data in any column to the right of your last column in the uploaded file.")>
			<cfelse>
				<cfset StructInsert(local.returnStruct.errorInfo,local.returnStruct.errorCode,"There was a problem importing the files.")>
				<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			</cfif>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="generateImportResultsReport" returntype="string" output="no" access="public">
		<cfargument name="jobID" type="numeric" required="yes">
		<cfargument name="logID" type="numeric" required="no" default="0">
		
		<cfset var local = structNew()>
		
		<!--- had to do it in a cfquery because cfstoredproc kept truncating the result --->
		<cfquery name="local.qryImportReport" datasource="#application.dsn.membercentral.dsn#">
			set nocount on;
			
			-- populate the log table for logging of these subprocs.
			IF OBJECT_ID('tempdb..##tmpPMILogID') IS NOT NULL 
				DROP TABLE ##tmpPMILogID;
			CREATE TABLE ##tmpPMILogID (logID int PRIMARY KEY);
			INSERT INTO ##tmpPMILogID (logID) VALUES (<cfqueryparam value="#arguments.logID#" cfsqltype="CF_SQL_INTEGER">);

			declare @jobID int, @environmentName varchar(50), @finalMSG varchar(max);
			set @jobID = <cfqueryparam value="#arguments.jobID#" cfsqltype="CF_SQL_INTEGER">;
			set @environmentName = <cfqueryparam value="#application.MCEnvironment#" cfsqltype="CF_SQL_VARCHAR">;

			EXEC dbo.ams_importPartialMemberData_changeReport @jobID=@jobID, @environmentName=@environmentName, @finalMSG=@finalMSG OUTPUT;

			select replace(@finalMSG,char(10),'<br/>') as finalMSG;

			IF OBJECT_ID('tempdb..##tmpPMILogID') IS NOT NULL 
				DROP TABLE ##tmpPMILogID;
		</cfquery>

		<cfreturn trim(local.qryImportReport.finalMSG)>
	</cffunction>

	<cffunction name="getPossibleDateColumns" output="no" access="public" returntype="string">
		<cfargument name="orgID" type="numeric" required="yes">
		
		<cfset var qryColumns = "">
		
		<cfquery name="qryColumns" datasource="#application.dsn.membercentral.dsn#">
			set nocount on;

			declare @orgID int = <cfqueryparam value="#arguments.orgID#" cfsqltype="CF_SQL_INTEGER">;

			select mdc.columnName
			from dbo.ams_memberDataColumns as mdc
			inner join dbo.ams_memberDataColumnDataTypes as mdt on mdt.dataTypeID = mdc.dataTypeID
			where mdc.orgID = @orgID
			and mdt.dataTypeCode = 'DATE'
				union all
			select PLName + '_activeDate'
			from ams_memberProfessionalLicenseTypes
			where orgID = @orgID;
		</cfquery>
		
		<cfreturn valuelist(qryColumns.columnName)>
	</cffunction>

	<cffunction name="showImportResults" access="package" output="false" returntype="string">
		<cfargument name="strResult" type="struct" required="yes">
		<cfargument name="doAgainURL" type="string" required="yes">
		<cfargument name="continueURL" type="string" required="yes">
		<cfargument name="cancelURL" type="string" required="yes">
		<cfargument name="importMode" type="string" required="no" default="">

		<cfset var local = structNew()>
		<cfset local.jobID = 0>
		<cfset local.numMembers = 0>

		<cfif arguments.importMode eq "queryBuilder">
			<cfset local.tryAgainButtonLabel = "Try again">
			<cfset local.returnToButtonLabel = "Return to Queries">
		<cfelseif arguments.importMode eq "linkedRecordsQueryBuilder">
			<cfset local.tryAgainButtonLabel = "Try again">
			<cfset local.returnToButtonLabel = "Return to Linked Records Queries">
		<cfelse>
			<cfset local.tryAgainButtonLabel = "Try upload again">
			<cfset local.returnToButtonLabel = "Return to Uploads">
		</cfif>

		<cfif arguments.strResult.success>
			<cfset local.logID = val(XMLSearch(arguments.strResult.importResultXML, "string(/import/@logid)"))>
			<cfset local.jobID = val(XMLSearch(arguments.strResult.importResultXML, "string(/import/@jobID)"))>
			<cfset local.numMembers = val(XMLSearch(arguments.strResult.importResultXML, "string(/import/@memCount)"))>
			<cfset local.updateReport = generateImportResultsReport(jobID=local.jobID, logID=local.logID)>
			
			<cfif local.numMembers gt 0>
				<cfset local.continueJSON = "#arguments.continueURL#&jID=#local.jobID#&lid=#local.logID#">
			</cfif>
		</cfif>

		<cfsavecontent variable="local.importResultJS">
			<cfoutput>
				<script language="javascript">
					function returnToUpdates(){
						<cfif arguments.importMode eq "queryBuilder">
							top.returnToQueriesFromImport();
						<cfelseif arguments.importMode eq "linkedRecordsQueryBuilder">
							top.returnToLinkedRecordsQueriesFromImport();
						<cfelse>
							self.location.href='#arguments.doAgainURL#';
						</cfif>
					}
				<cfif local.jobID gt 0>
					function cancelMemUpdate() {
						if(confirm('Are you sure you want to cancel the update?')){
							<cfif arguments.importMode eq "queryBuilder">
								var createResult = function(r) {
									if (r.success && r.success.toLowerCase() == 'true') top.returnToQueriesFromImport();
									else {
										alert('An error occured while trying to cancel the import.');
										$('button[name="btnCancel"]').attr('disabled',false);
									}
								};

								$('button[name="btnCancel"]').attr('disabled',true);
								var objParams = { jobID:#local.jobID#, logID:#local.logID# };
								TS_AJX('ADMINIMPORT','cancelImport',objParams,createResult,createResult,20000,createResult);
							<cfelseif arguments.importMode eq "linkedRecordsQueryBuilder">
								var createResult = function(r) {
									if (r.success && r.success.toLowerCase() == 'true') top.returnToLinkedRecordsQueriesFromImport();
									else {
										alert('An error occured while trying to cancel the import.');
										$('button[name="btnCancel"]').attr('disabled',false);
									}
								};

								$('button[name="btnCancel"]').attr('disabled',true);
								var objParams = { jobID:#local.jobID#, logID:#local.logID# };
								TS_AJX('ADMINIMPORT','cancelImport',objParams,createResult,createResult,20000,createResult);
							<cfelse>
								self.location.href='#arguments.cancelURL#&jID=#local.jobID#&lid=#local.logID#';
							</cfif>
						}
						else return false;
					}

					<cfif arguments.strResult.success and local.numMembers gt 0>
						function continueMemUpdate() {
							$('##divMemUpdateBtnBar').hide(300);
							$('##divMemUpdateBtnBarLoading').show();

							var #ToScript(local.continueJSON,"MUcontURL")#
							$.getJSON(MUcontURL)
								.done(continueMemUpdateR)
								.fail(function() { var r = { success:0, data:'' }; continueMemUpdateR(r); });
						}
						function continueMemUpdateR(r) {
							$('div##uploadResultScreen').html(r.data);
							if (!r.success)
								showAlert('An error occurred during the update. Contact MemberCentral for assistance.','#arguments.importMode#');
						}
					</cfif>
				</cfif>
				</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.importResultJS#">

		<!--- If fatal errors --->
		<cfif NOT arguments.strResult.success>
			<cfsavecontent variable="local.data">
				<cfoutput>
				<h4>Mass Update Members Issue Report</h4>
				<div class="alert alert-danger">
					<div class="mb-3"><b>The update was stopped and requires your attention.</b></div>
					<cfif arguments.strResult.errorCode eq 105 and structKeyExists(arguments.strResult, "importResultXML")>
						<cfset local.arrErrors = XMLSearch(arguments.strResult.importResultXML,"/import/errors/error")>
						<div>
						<cfif arrayLen(local.arrErrors) gt 300>
							<b>Only the first 300 errors are shown.</b><br/><br/>
						</cfif>
						<cfset local.thisErrNum = 0>
						<cfloop array="#local.arrErrors#" index="local.thisErr">
							<cfset local.thisErrNum = local.thisErrNum + 1>
							#local.thisErr.xmlAttributes.msg#<br/>
							<cfif local.thisErrNum is 300>
								<cfbreak>
							</cfif>
						</cfloop>
						</div>
					<cfelseif arguments.strResult.errorCode eq 104>
						<div>There are duplicate columns in the update file.</div>
					<cfelse>
						<div>#arguments.strResult.errorInfo[arguments.strResult.errorCode]#</div>
					</cfif>
					<button class="btn btn-secondary btn-sm mt-3" name="btnDoOver" type="button" onclick="returnToUpdates();">Try upload again</button>
				</div>
				</cfoutput>
			</cfsavecontent>

		<!--- if success but no changes needed --->
		<cfelseif arguments.strResult.success and local.numMembers eq 0>
			<cfset cancelImport(jobID=local.jobID, logID=local.logID)>
			<cfsavecontent variable="local.data">
				<cfoutput>
				<h4>Mass Update Members No Action Needed</h4>
				<div>There were no changes to process.</div>
				<br/>				
				<cfif len(local.updateReport)>
					<div>#local.updateReport#</div>
					<br/>
				</cfif>
				<button class="btn btn-secondary btn-sm" name="btnDoOver" type="button" onclick="returnToUpdates();">#local.returnToButtonLabel#</button>
				</cfoutput>
			</cfsavecontent>

		<!--- if success with changes to confirm --->
		<cfelseif arguments.strResult.success and local.numMembers gt 0>
			<cfsavecontent variable="local.data">
				<cfoutput>
				<h4>Mass Update Members Confirmation Needed</h4>
				<div>The update has been prepared and requires your confirmation to continue.</div>
				<div>Note: This update will be automatically cancelled in 60 minutes unless you click continue before that time.</div>
				<br/>
				<cfif len(local.updateReport)>
					<div>#local.updateReport#</div>
					<br/>
				</cfif>
				<div id="divMemUpdateBtnBar">
					<button class="btn btn-primary btn-sm" name="btnContinue" type="button" onClick="continueMemUpdate();">Continue</button>
					<button class="btn btn-secondary btn-sm" name="btnCancel" type="button" onClick="cancelMemUpdate();">Cancel</button>
				</div>
				<div id="divMemUpdateBtnBarLoading" style="display:none;" class="c">
					<i class="fa-light fa-circle-notch fa-spin fa-2x"></i> <b>Submitting the mass update. Please wait...</b>
				</div>
				</cfoutput>
			</cfsavecontent>
		<cfelse>
			<cfsavecontent variable="local.data">
				<cfoutput>
				<h4>Mass Update Members Issue Report</h4>
				<div style="background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00;">
					<div><b>An undetermined error occurred during the update. Contact MemberCentral for assistance.</b></div>
				</div>
				</cfoutput>
			</cfsavecontent>
		</cfif>
			
		<cfreturn local.data>
	</cffunction>

	<cffunction name="continueImport" access="package" output="false" returntype="boolean">
		<cfargument name="jobID" type="numeric" required="yes">
		<cfargument name="siteCode" type="string" required="yes">

		<cfset var local = structNew()>
		<cfset local.success = true>

		<cfsetting requesttimeout="600">
		<cftry>
			<cfset local.updateReport = generateImportResultsReport(jobID=arguments.jobID)>
			
			<cfquery name="local.qryImportContinue" datasource="#application.dsn.membercentral.dsn#">
				set nocount on;
				
				-- populate the log table for logging of these subprocs. we dont know the logID so just put 0.
				IF OBJECT_ID('tempdb..##tmpPMILogID') IS NOT NULL 
					DROP TABLE ##tmpPMILogID;
				CREATE TABLE ##tmpPMILogID (logID int PRIMARY KEY);
				INSERT INTO ##tmpPMILogID (logID) VALUES (0);

				declare @jobID int, @actualMemberID int;
				select @jobID = <cfqueryparam value="#arguments.jobID#" cfsqltype="CF_SQL_INTEGER">;

				exec dbo.ams_importPartialMemberData_final @jobID=@jobID, @mode='manyMembers', @actualMemberID=@actualMemberID OUTPUT;

				select @actualMemberID as actualMemberID;

				IF OBJECT_ID('tempdb..##tmpPMILogID') IS NOT NULL 
					DROP TABLE ##tmpPMILogID;
			</cfquery>

			<cfset local.actualMemberID = local.qryImportContinue.actualMemberID>
		<cfcatch type="Any">
			<cfset local.success = false>
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfreturn local.success>
		</cfcatch>
		</cftry>

		<!--- email report --->
		<cftry>
			<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(arguments.siteCode)>
			
			<cfquery name="local.qryOrgEmail" datasource="#application.dsn.membercentral.dsn#">
				select emailImportResults
				from dbo.organizations
				where orgID = <cfqueryparam value="#local.mc_siteInfo.orgID#" cfsqltype="CF_SQL_INTEGER">
			</cfquery>

			<cfset local.emailContent = "#session.cfcuser.memberdata.firstname# #session.cfcuser.memberdata.lastname# submitted a mass member update on #dateformat(now(),'dddd, mmmm d, yyyy')# #timeformat(now(),'h:mm tt')# CT.<br/><br/>#local.updateReport#">

			<cfif len(trim(local.qryOrgEmail.emailImportResults))>
				<cfscript>
					local.importAdminSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='ImportAdmin',siteID=local.mc_siteInfo.siteID);
					
					local.arrEmailTo = [];
					local.toEmailArr = listToArray(local.qryOrgEmail.emailImportResults,';');
					for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
						local.arrEmailTo.append({ name:'', email:trim(local.toEmailArr[local.i]) });
					}

					local.strResult = application.objEmailWrapper.sendMailESQ(
						emailfrom={ name=local.mc_siteInfo.orgName, email=local.mc_siteInfo.networkEmailFrom },
						emailto=local.arrEmailTo,
						emailreplyto=local.mc_siteInfo.supportProviderEmail,
						emailsubject="#local.mc_siteInfo.orgShortName# Mass Member Update Report",
						emailtitle="#local.mc_siteInfo.sitename# Mass Member Update",
						emailhtmlcontent=local.emailContent,
						emailAttachments=[],
						siteID=local.mc_siteInfo.siteID,
						memberID=local.mc_siteInfo.sysMemberID,
						messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="MASSMEMUPDRPT"),
						sendingSiteResourceID=local.importAdminSiteResourceID
					);
				</cfscript>
			</cfif>
				
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
		</cfcatch>
		</cftry>
	
		<cfreturn local.success>
	</cffunction>

	<cffunction name="cancelImport" access="public" output="false" returntype="struct">
		<cfargument name="jobID" type="numeric" required="yes">
		<cfargument name="logID" type="numeric" required="no" default="0">

		<cfset var local = structNew()>

		<cfif arguments.jobID gt 0>
			<cfquery name="local.qryImportCancel" datasource="#application.dsn.membercentral.dsn#">
				set nocount on;
				
				-- populate the log table for logging of these subprocs.
				IF OBJECT_ID('tempdb..##tmpPMILogID') IS NOT NULL 
					DROP TABLE ##tmpPMILogID;
				CREATE TABLE ##tmpPMILogID (logID int PRIMARY KEY);
				INSERT INTO ##tmpPMILogID (logID) VALUES (<cfqueryparam value="#arguments.logID#" cfsqltype="CF_SQL_INTEGER">);

				declare @jobID int = <cfqueryparam value="#arguments.jobID#" cfsqltype="CF_SQL_INTEGER">;

				exec dbo.ams_importPartialMemberData_cancel @jobID=@jobID;

				IF OBJECT_ID('tempdb..##tmpPMILogID') IS NOT NULL 
					DROP TABLE ##tmpPMILogID;
			</cfquery>
		</cfif>

		<cfreturn {"success":true}>
	</cffunction>

	<cffunction name="processMassUpdateMemberNumbers" access="package" output="false" returntype="struct">
		<cfargument name="orgID" type="numeric" required="yes">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="orgCode" type="string" required="yes">

		<cfset var local = structNew()>
		<cfset local.objImportModule = CreateObject("component","model.admin.common.modules.import.import")>
		<cfset local.returnStruct = { success=true, errorCode=999, errorInfo=StructNew() }>

		<!--- Attempt upload --->
		<cftry>
			<cfset local.strImportFile = {}>
			<cfset local.strImportFile.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.orgcode)>

			<cffile action="upload" filefield="importfilename" destination="#local.strImportFile.strFolder.folderPath#" result="local.uploadResult" nameconflict="OVERWRITE">

			<cfset local.strImportFile.uploadFilenameWithExt = local.uploadResult.ServerFile>
			<cfset local.strImportFile.uploadFilenameWithoutExt = local.uploadResult.ServerFileName>
			<cfset local.strImportFile.uploadFilenameExt = local.uploadResult.ServerFileExt>

			<cfif listFindNoCase("csv,xls",local.strImportFile.uploadFilenameExt) is 0>
				<cffile action="DELETE" file="#local.strImportFile.strFolder.folderPath#/#local.strImportFile.uploadFilenameWithExt#">
				<cfif local.strImportFile.uploadFilenameExt eq "xlsx">
					<cfset local.errMsg = "Uploaded file was not in the proper format (#local.strImportFile.uploadFilenameExt#). Be sure to save your Excel file using the ""Excel 97-2003 Workbook (.XLS)"" format and try again.">
				<cfelse>
					<cfset local.errMsg = "Uploaded file was not in the proper format (#local.strImportFile.uploadFilenameExt#).">
				</cfif>
				<cfset local.returnStruct.success = false>
				<cfset local.returnStruct.errorCode = 1>
				<cfset StructInsert(local.returnStruct.errorInfo,local.returnStruct.errorCode,local.errMsg)>
				<cfreturn local.returnStruct>
			</cfif> 
		<cfcatch type="Any">
			<cfset local.returnStruct.success = false>
			<cfset local.returnStruct.errorCode = 1>
			<cfset StructInsert(local.returnStruct.errorInfo,local.returnStruct.errorCode,"There was a problem uploading the selected file. Only comma separated value files (CSV) and Microsoft Excel (XLS) files can be uploaded. Try the upload again or contact us for assistance.")>
			<cfreturn local.returnStruct>
		</cfcatch>
		</cftry>

		<!--- if XLS, parse for sheets --->
		<cfif local.strImportFile.uploadFilenameExt eq "xls">
			<cfset local.parseResult = local.objImportModule.parseXLS(strFilePath="#local.strImportFile.strFolder.folderPath#/#local.strImportFile.uploadFilenameWithExt#")>
			<cfset local.returnStruct.success = local.parseResult.success>
			<cfif NOT local.returnStruct.success>
				<cfset local.returnStruct.errorCode = local.parseResult.errorCode>
				<cfset local.returnStruct.errorInfo = local.parseResult.errorInfo>
				<cfreturn local.returnStruct>
			</cfif>
	
			<cfif arrayLen(local.parseResult.arrSheets) neq 1>
				<cfset local.returnStruct.success = false>
				<cfset local.returnStruct.errorCode = 102>
				<cfset StructInsert(local.returnStruct.errorInfo,local.returnStruct.errorCode,'The uploaded Excel file contains #arrayLen(local.parseResult.arrSheets)# sheets. Edit the file to contain only one sheet and try again.')>
				<cfreturn local.returnStruct>
			</cfif>

			<cfset local.parseResult = local.objImportModule.parseXLSSheet(strFilePath="#local.strImportFile.strFolder.folderPath#/#local.strImportFile.uploadFilenameWithExt#",
													 strFilePathCSV="#local.strImportFile.strFolder.folderPath#/#local.strImportFile.uploadFilenameWithoutExt#.csv",
													 sheetIndex=0,
													 lstDateColumns="")>
			<cfset local.returnStruct.success = local.parseResult.success>
			<cfif NOT local.returnStruct.success>
				<cfset local.returnStruct.errorCode = local.parseResult.errorCode>
				<cfset local.returnStruct.errorInfo = local.parseResult.errorInfo>
				<cfreturn local.returnStruct>
			</cfif>
		</cfif>

		<!--- parse CSV --->
		<cfset local.parseResult = local.objImportModule.parseCSV(stFilePath="#local.strImportFile.strFolder.folderPath#/#local.strImportFile.uploadFilenameWithoutExt#.csv", stFilePathTmp="#local.strImportFile.strFolder.folderPath#/#local.strImportFile.uploadFilenameWithoutExt#Parsed.csv")>
		<cfset local.returnStruct.success = local.parseResult.isErr is 0>
		<cfif NOT local.returnStruct.success>
			<cfset local.returnStruct.errorCode = 101>
			<cfset StructInsert(local.returnStruct.errorInfo,local.returnStruct.errorCode,local.parseResult.errMsg)>
			<cfreturn local.returnStruct>
		<cfelseif not listFindNoCase(local.parseResult.strTableColumnNames,"MemberNumber",chr(7)) or not listFindNoCase(local.parseResult.strTableColumnNames,"NewMemberNumber",chr(7))>
			<cfset local.returnStruct.success = false>
			<cfset local.returnStruct.errorCode = 105>
			<cfset StructInsert(local.returnStruct.errorInfo,local.returnStruct.errorCode,'Required Columns "MemberNumber" or "NewMemberNumber" is missing.')>
			<cfreturn local.returnStruct>
		</cfif>

		<!--- run mass update membernumbers --->
		<cftry>
			 <cfquery name="local.qryImport" datasource="#application.dsn.membercentral.dsn#" result="local.qryImportResult">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
				
					declare @orgID int, @importTitle varchar(100), @runByMemberID int, @dataFilename varchar(400), 
						@dataFileExt varchar(3), @importResult xml, @errCount int, @environmentName varchar(50);
					set @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">;
					set @importTitle = 'Manual MemberNumber Change';
					set @runByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">;
					set @dataFilename = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.strImportFile.strFolder.folderPathUNC#\#local.strImportFile.uploadFilenameWithExt#">;
					set @dataFileExt = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.strImportFile.uploadFilenameExt#">;
					set @environmentName = <cfqueryparam value="#application.MCEnvironment#" cfsqltype="CF_SQL_VARCHAR">;

					-- bulk insert from file
					BEGIN TRY
						IF OBJECT_ID('tempdb..##mc_PartialMemImport') IS NOT NULL 
							DROP TABLE ##mc_PartialMemImport;
						CREATE TABLE ##mc_PartialMemImport (
							<cfloop list="#local.parseResult.strTableColumnNames#" index="local.thisCol" delimiters="#chr(7)#">
								[#local.thisCol#] varchar(max)<cfif local.thisCol neq listLast(local.parseResult.strTableColumnNames,chr(7))>, </cfif>
							</cfloop>
						);
						BULK INSERT ##mc_PartialMemImport 
							FROM '#local.strImportFile.strFolder.folderPathUNC#\#local.strImportFile.uploadFilenameWithoutExt#Parsed.csv' 
							WITH (FIELDTERMINATOR='#chr(7)#', FIRSTROW=2);
					END TRY
					BEGIN CATCH
						select @importResult = '<import><errors><error msg="Unable to import the file for processing." /><error msg="' + error_message() + '" /></errors></import>';
						GOTO on_done;
					END CATCH

					-- import file
					BEGIN TRY
						set @importResult = null;
						EXEC dbo.ams_importPartialMemberData @orgID=@orgID, @importTitle=@importTitle, @runByMemberID=@runByMemberID, 
							@dataFilename=@dataFilename, @dataFileExt=@dataFileExt, @autogenMemNum=0, @activateIncMembers=0, 
							@ignoreInvalidColumns=1, @inactivateNonIncMembers=0, @thresholdLimit=0, @bypassRO=0, @bypassListEmailUpdate=0,
							@environmentName=@environmentName, @importResult=@importResult OUTPUT;
					END TRY
					BEGIN CATCH
						select @importResult = '<import><errors><error msg="Unable to process the import file." /><error msg="' + error_message() + '" /></errors></import>';
						GOTO on_done;
					END CATCH
				
					on_done:
					select @errCount = @importResult.value('count(/import/errors/error)','int');
					SELECT @importResult as importResult, @errCount as errCount;

					IF OBJECT_ID('tempdb..##mc_PartialMemImport') IS NOT NULL 
						DROP TABLE ##mc_PartialMemImport;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
					select @importResult = '<import><errors><error msg="Unable to process the import file." /><error msg="' + error_message() + '" /></errors></import>';
					SELECT @importResult as importResult, 1 as errCount;
				END CATCH
			</cfquery>
			<cfset local.returnStruct.importResultXML = xmlparse(local.qryImport.importResult)>
			<cfset local.returnStruct.numFatalErrors = local.qryImport.errCount>

			<cfif local.returnStruct.numFatalErrors gt 0>
				<cfset local.returnStruct.success = false>
				<cfset local.returnStruct.errorCode = 105>
				<cfset structInsert(local.returnStruct.errorInfo,local.returnStruct.errorCode,local.returnStruct.importResultXML)>
			</cfif>
		<cfcatch type="Any">
			<cfset local.returnStruct.success = false>
			<cfset local.returnStruct.errorCode = 1>
			<cfset local.returnStruct.errMsg = "There was a problem importing the files. Try the upload again or contact us for assistance.">
			<cfset structInsert(local.returnStruct.errorInfo,local.returnStruct.errorCode,local.returnStruct.errMsg)>
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="processMassDeleteMembers" access="package" output="false" returntype="struct">
		<cfargument name="orgID" type="numeric" required="yes">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="orgCode" type="string" required="yes">

		<cfset var local = structNew()>
		<cfset local.objImportModule = CreateObject("component","model.admin.common.modules.import.import")>
		<cfset local.returnStruct = { success=true, errorCode=999, errorInfo=StructNew() }>

		<!--- Attempt upload --->
		<cftry>
			<cfset local.strImportFile = {}>
			<cfset local.strImportFile.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.orgcode)>

			<cffile action="upload" filefield="importfilename" destination="#local.strImportFile.strFolder.folderPath#" result="local.uploadResult" nameconflict="OVERWRITE">

			<cfset local.strImportFile.uploadFilenameWithExt = local.uploadResult.ServerFile>
			<cfset local.strImportFile.uploadFilenameWithoutExt = local.uploadResult.ServerFileName>
			<cfset local.strImportFile.uploadFilenameExt = local.uploadResult.ServerFileExt>

			<cfif listFindNoCase("csv,xls",local.strImportFile.uploadFilenameExt) is 0>
				<cffile action="DELETE" file="#local.strImportFile.strFolder.folderPath#/#local.strImportFile.uploadFilenameWithExt#">
				<cfif local.strImportFile.uploadFilenameExt eq "xlsx">
					<cfset local.errMsg = "Uploaded file was not in the proper format (#local.strImportFile.uploadFilenameExt#). Be sure to save your Excel file using the ""Excel 97-2003 Workbook (.XLS)"" format and try again.">
				<cfelse>
					<cfset local.errMsg = "Uploaded file was not in the proper format (#local.strImportFile.uploadFilenameExt#).">
				</cfif>
				<cfset local.returnStruct.success = false>
				<cfset local.returnStruct.errorCode = 1>
				<cfset StructInsert(local.returnStruct.errorInfo,local.returnStruct.errorCode,local.errMsg)>
				<cfreturn local.returnStruct>
			</cfif> 
		<cfcatch type="Any">
			<cfset local.returnStruct.success = false>
			<cfset local.returnStruct.errorCode = 1>
			<cfset StructInsert(local.returnStruct.errorInfo,local.returnStruct.errorCode,"There was a problem uploading the selected file. Only comma separated value files (CSV) and Microsoft Excel (XLS) files can be uploaded. Try the upload again or contact us for assistance.")>
			<cfreturn local.returnStruct>
		</cfcatch>
		</cftry>

		<!--- if XLS, parse for sheets --->
		<cfif local.strImportFile.uploadFilenameExt eq "xls">
			<cfset local.parseResult = local.objImportModule.parseXLS(strFilePath="#local.strImportFile.strFolder.folderPath#/#local.strImportFile.uploadFilenameWithExt#")>
			<cfset local.returnStruct.success = local.parseResult.success>
			<cfif NOT local.returnStruct.success>
				<cfset local.returnStruct.errorCode = local.parseResult.errorCode>
				<cfset local.returnStruct.errorInfo = local.parseResult.errorInfo>
				<cfreturn local.returnStruct>
			</cfif>
	
			<cfif arrayLen(local.parseResult.arrSheets) neq 1>
				<cfset local.returnStruct.success = false>
				<cfset local.returnStruct.errorCode = 102>
				<cfset StructInsert(local.returnStruct.errorInfo,local.returnStruct.errorCode,'The uploaded Excel file contains #arrayLen(local.parseResult.arrSheets)# sheets. Edit the file to contain only one sheet and try again.')>
				<cfreturn local.returnStruct>
			</cfif>

			<cfset local.parseResult = local.objImportModule.parseXLSSheet(strFilePath="#local.strImportFile.strFolder.folderPath#/#local.strImportFile.uploadFilenameWithExt#",
					strFilePathCSV="#local.strImportFile.strFolder.folderPath#/#local.strImportFile.uploadFilenameWithoutExt#.csv", sheetIndex=0, lstDateColumns="")>
			<cfset local.returnStruct.success = local.parseResult.success>
			<cfif NOT local.returnStruct.success>
				<cfset local.returnStruct.errorCode = local.parseResult.errorCode>
				<cfset local.returnStruct.errorInfo = local.parseResult.errorInfo>
				<cfreturn local.returnStruct>
			</cfif>
		</cfif>

		<!--- parse CSV --->
		<cfset local.parseResult = local.objImportModule.parseCSV(stFilePath="#local.strImportFile.strFolder.folderPath#/#local.strImportFile.uploadFilenameWithoutExt#.csv", stFilePathTmp="#local.strImportFile.strFolder.folderPath#/#local.strImportFile.uploadFilenameWithoutExt#Parsed.csv")>
		<cfset local.returnStruct.success = local.parseResult.isErr is 0>
		<cfif NOT local.returnStruct.success>
			<cfset local.returnStruct.errorCode = 101>
			<cfset StructInsert(local.returnStruct.errorInfo,local.returnStruct.errorCode,local.parseResult.errMsg)>
			<cfreturn local.returnStruct>
		<cfelseif not listFindNoCase(local.parseResult.strTableColumnNames,"MemberNumber",chr(7))>
			<cfset local.returnStruct.success = false>
			<cfset local.returnStruct.errorCode = 105>
			<cfset StructInsert(local.returnStruct.errorInfo,local.returnStruct.errorCode,'Required Column "MemberNumber" is missing.')>
			<cfreturn local.returnStruct>
		</cfif>

		<!--- run mass delete members --->
		<cftry>
			 <cfquery name="local.qryImport" datasource="#application.dsn.membercentral.dsn#" result="local.qryImportResult">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
				
					declare @orgID int, @runByMemberID int, @runByMember VARCHAR(200), @importResult xml, @errCount int, @environmentName varchar(50);
					set @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">;
					set @runByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">;
					SET @runByMember = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#session.cfcuser.memberdata.firstName# #session.cfcuser.memberdata.lastName# (#session.cfcuser.memberdata.membernumber#)">;
					SET @environmentName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#application.MCEnvironment#">;

					-- bulk insert from file
					BEGIN TRY
						IF OBJECT_ID('tempdb..##mc_PartialMemImport') IS NOT NULL
							DROP TABLE ##mc_PartialMemImport;
						IF OBJECT_ID('tempdb..##tblErrorMessages') IS NOT NULL
							DROP TABLE ##tblErrorMessages;

						CREATE TABLE ##tblErrorMessages (rowid int IDENTITY(1,1), msg varchar(400));

						CREATE TABLE ##mc_PartialMemImport (
							<cfloop list="#local.parseResult.strTableColumnNames#" index="local.thisCol" delimiters="#chr(7)#">
								[#local.thisCol#] varchar(max)<cfif local.thisCol neq listLast(local.parseResult.strTableColumnNames,chr(7))>, </cfif>
							</cfloop>
						);
						
						BULK INSERT ##mc_PartialMemImport 
							FROM '#local.strImportFile.strFolder.folderPathUNC#\#local.strImportFile.uploadFilenameWithoutExt#Parsed.csv' 
							WITH (FIELDTERMINATOR='#chr(7)#', FIRSTROW=2);

						-- invalid membernumber
						INSERT INTO ##tblErrorMessages (msg)
						SELECT 'MemberNumber ' + tmp.MemberNumber + ' does not match a valid member account.'
						FROM ##mc_PartialMemImport AS tmp
						LEFT OUTER JOIN dbo.ams_members AS m ON m.orgID = @orgID AND m.memberNumber = tmp.MemberNumber
							AND m.memberID = m.activeMemberID
							AND m.[status] <> 'D'
						WHERE tmp.MemberNumber <> ''
						AND m.memberID IS NULL;

						IF EXISTS(SELECT 1 FROM ##tblErrorMessages) BEGIN
							SELECT @importResult = (
								SELECT isnull((SELECT TOP 301 isnull(msg,'') as "@msg"
								FROM ##tblErrorMessages
								ORDER BY rowid
								FOR XML PATH('error'), root('errors'), type),'<errors/>')
							FOR XML PATH('import'), TYPE);

							GOTO on_done;
						END
						
						ALTER TABLE ##mc_PartialMemImport ADD MCAccountStatus VARCHAR(MAX);
						UPDATE ##mc_PartialMemImport SET MCAccountStatus = 'Inactive';
					END TRY
					BEGIN CATCH
						select @importResult = '<import><errors><error msg="Unable to import the file for processing." /><error msg="' + error_message() + '" /></errors></import>';
						GOTO on_done;
					END CATCH

					-- import file
					BEGIN TRY
						DECLARE @statusReady INT, @queueTypeID INT, @itemGroupUID UNIQUEIDENTIFIER, @xmlMessage XML, @nowDate DATETIME;

						SET @importResult = '<import><errors/></import>';
						SELECT @queueTypeID = queueTypeID FROM platformQueue.dbo.tblQueueTypes WHERE queueType = 'memberDelete';
						SELECT @statusReady = queueStatusID FROM platformQueue.dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID AND queueStatus = 'readyToProcess';
						SET @itemGroupUID = NEWID();
						SET @nowDate = getdate();
						
						EXEC dbo.ams_importPartialMemberData_autoconfirm @orgID=@orgID, @importTitle='Mass Member Delete', @runByMemberID=@runByMemberID,
							@activateIncMembers=0, @inactivateNonIncMembers=0, @thresholdLimit=0, @bypassRO=0, @finalMSGHeader=@runByMember,
							@emailSubject='Mass Member Delete', @environmentName=@environmentName, @importResult=@importResult OUTPUT, @errCount=@errCount OUTPUT;

						IF @errCount > 0
							GOTO on_done2;

						-- queue items
						INSERT INTO platformQueue.dbo.queue_memberDelete (itemGroupUID, orgID, memberID, addedByMemberID, statusID, dateAdded, dateUpdated)
						SELECT @itemGroupUID, @orgID, m.memberID, @runByMemberID, @statusReady, @nowDate, @nowDate
						FROM ##mc_PartialMemImport AS tmp
						INNER JOIN dbo.ams_members AS m ON m.orgID = @orgID AND m.memberNumber = tmp.MemberNumber
							AND m.memberID = m.activeMemberID
							AND m.[status] <> 'D'
							EXCEPT
						SELECT @itemGroupUID, @orgID, memberID, @runByMemberID, @statusReady, @nowDate, @nowDate
						FROM platformQueue.dbo.queue_memberDelete
						WHERE orgID = @orgID;

						-- send message to service broker to create all the individual messages
						SELECT @xmlMessage = ISNULL((
							SELECT 'memberDeleteLoad' AS t, cast(@itemGroupUID as varchar(60)) AS u
							FOR XML RAW('mc'), TYPE
						),'<mc/>');

						EXEC platformQueue.dbo.queue_DataImport_sendMessage @xmlMessage=@xmlMessage;

						on_done2:

					END TRY
					BEGIN CATCH
						IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
						select @importResult = '<import><errors><error msg="Unable to complete the import." /><error msg="' + error_message() + '" /></errors></import>';
						GOTO on_done;
					END CATCH
				
					on_done:
					select @errCount = @importResult.value('count(/import/errors/error)','int');
					SELECT @importResult as importResult, @errCount as errCount;

					IF OBJECT_ID('tempdb..##mc_PartialMemImport') IS NOT NULL
						DROP TABLE ##mc_PartialMemImport;
					IF OBJECT_ID('tempdb..##tblErrorMessages') IS NOT NULL
						DROP TABLE ##tblErrorMessages;
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
					select @importResult = '<import><errors><error msg="Unable to process the import file." /><error msg="' + error_message() + '" /></errors></import>';
					SELECT @importResult as importResult, 1 as errCount;
				END CATCH
			</cfquery>
			<cfset local.returnStruct.importResultXML = xmlparse(local.qryImport.importResult)>
			<cfset local.returnStruct.numFatalErrors = local.qryImport.errCount>

			<cfif local.returnStruct.numFatalErrors gt 0>
				<cfset local.returnStruct.success = false>
				<cfset local.returnStruct.errorCode = 105>
				<cfset structInsert(local.returnStruct.errorInfo,local.returnStruct.errorCode,local.returnStruct.importResultXML)>
			</cfif>
		<cfcatch type="Any">
			<cfset local.returnStruct.success = false>
			<cfset local.returnStruct.errorCode = 1>
			<cfset local.returnStruct.errMsg = "There was a problem importing the files. Try the upload again or contact us for assistance.">
			<cfset structInsert(local.returnStruct.errorInfo,local.returnStruct.errorCode,local.returnStruct.errMsg)>
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="processMassUpdateMembersByQuery" access="package" output="false" returntype="struct">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="queryID" type="numeric" required="true">
		<cfargument name="queryName" type="string" required="true">
		<cfargument name="isRecurringUpdate" type="boolean" required="true">
		<cfargument name="schedInterval" type="string" required="true">
		<cfargument name="schedIntervalTypeID" type="string" required="true">
		<cfargument name="schedNextRunDate" type="string" required="true">
		<cfargument name="schedEndRunDate" type="string" required="true">
		<cfargument name="arrQueryFields" type="array" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { success=true, errorCode=999, errorInfo=StructNew() }>
		<cfset local.tmpSuffix = replace(createUUID(),'-','','ALL')>
		
		<!--- run first part of import --->
  		<cftry>
            <cfquery name="local.qryImport" datasource="#application.dsn.membercentral.dsn#" result="local.importResult">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					IF OBJECT_ID('tempdb..##tmpQueryFieldsData') IS NOT NULL 
						DROP TABLE ##tmpQueryFieldsData;
					CREATE TABLE ##tmpQueryFieldsData (columnName sysname, newValue varchar(max));
				
					DECLARE @orgID int, @queryID int, @runByMemberID int, @importResult xml, @errCount int, @environmentName varchar(50);
					DECLARE @tmpOrgMemberColumns TABLE (columnName sysname PRIMARY KEY, fieldCode varchar(40));

					SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">;
					SET @queryID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.queryID#">;
					SET @runByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">;
					SET @environmentName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#application.MCEnvironment#">;

					INSERT INTO @tmpOrgMemberColumns (columnName, fieldCode)
					SELECT columnName, fieldCode
					FROM dbo.fn_ams_getOrgMemberColumns(@orgID);

					<cfloop array="#arguments.arrQueryFields#" index="local.thisField">
						INSERT INTO ##tmpQueryFieldsData (columnName, newValue)
						VALUES (<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.thisField.columnName#">,
								<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#local.thisField.columnValue#">);
					</cfloop>

					UPDATE dbo.ams_savedQueries
					SET queryName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.queryName#">,
						<cfif arguments.isRecurringUpdate and arguments.schedInterval gt 0 and arguments.schedIntervalTypeID gt 0 and len(arguments.schedNextRunDate) gt 0>
							interval = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.schedInterval#">,
							intervalTypeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.schedIntervalTypeID#">,
							nextRunDate = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#ParseDateTime(replace(arguments.schedNextRunDate,' - ',' '))#">,
							<cfif len(arguments.schedEndRunDate) eq 0>
								endRunDate = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" null="yes">
							<cfelse>
								endRunDate = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#ParseDateTime(replace(arguments.schedEndRunDate,' - ',' '))#">
							</cfif>
						<cfelse>
							interval = NULL,
							intervalTypeID = NULL,
							nextRunDate = NULL,
							endRunDate = NULL
						</cfif>
					WHERE queryID = @queryID;

					DELETE
					FROM dbo.ams_savedQueriesFields
					WHERE queryID = @queryID;

					INSERT INTO dbo.ams_savedQueriesFields (queryID, columnName, fieldCode, newValue)
					SELECT @queryID, tmp.columnName, omc.fieldCode, tmp.newValue
					FROM ##tmpQueryFieldsData AS tmp
					INNER JOIN @tmpOrgMemberColumns AS omc ON omc.columnName = tmp.columnName;

					EXEC dbo.ams_scheduledQueryImportMemberData @queryID=@queryID, @orgID=@orgID, 
						@environmentName=@environmentName, @runByMemberID=@runByMemberID, @importResult=@importResult OUTPUT;

					SET @errCount = @importResult.value('count(/import/errors/error)','int');
					SELECT @importResult as importResult, @errCount as errCount;

					IF OBJECT_ID('tempdb..##tmpQueryFieldsData') IS NOT NULL 
						DROP TABLE ##tmpQueryFieldsData;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.returnStruct.importResultXML = xmlparse(local.qryImport.importResult)>
			<cfset local.returnStruct.numFatalErrors = local.qryImport.errCount>

			<cfif local.returnStruct.numFatalErrors gt 0>
				<cfset local.returnStruct.success = false>
				<cfset local.returnStruct.errorCode = 105>
				<cfset StructInsert(local.returnStruct.errorInfo,local.returnStruct.errorCode,'')>
			</cfif>
		<cfcatch type="any">
			<cfset local.returnStruct.success = false>
			<cfset local.returnStruct.errorCode = 1>
			<cfset StructInsert(local.returnStruct.errorInfo,local.returnStruct.errorCode,"There was a problem importing the data.")>
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="importJobExists" access="public" output="false" returntype="boolean">
		<cfargument name="jobID" type="numeric" required="true">

		<cfset var qryImportJob = "">

		<cfquery name="qryImportJob" datasource="#application.dsn.platformQueue.dsn#">
			SELECT jobID
			FROM dbo.memimport_jobs
			WHERE jobID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.jobID#">
		</cfquery>

		<cfreturn qryImportJob.recordCount is 1>
	</cffunction>

	<cffunction name="getSavedQuery" access="public" output="false" returntype="query">
		<cfargument name="queryID" type="numeric" required="true">

		<cfset var qrySavedQuery = "">

		<cfquery name="local.qrySavedQuery" datasource="#application.dsn.memberCentral.dsn#">
			SELECT sq.queryID, sq.queryName, sq.ruleID, sq.interval, sq.intervalTypeID, stt.[name] as intervalTypeName, sq.nextRunDate , sq.endRunDate
			FROM dbo.ams_savedQueries AS sq
			LEFT JOIN dbo.scheduledTaskIntervalTypes AS stt ON stt.intervalTypeID = sq.intervalTypeID
			WHERE sq.queryID = <cfqueryparam value="#arguments.queryID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>
		
		<cfreturn qrySavedQuery>
	</cffunction>

	<cffunction name="deleteSavedQuery" access="public" output="false" returntype="struct">
		<cfargument name="queryID" type="numeric" required="yes">
		
		<cfset var local = structNew()>
		<cfset local.returnStruct = { "success":false }>
		
		<cftry>
			<cfstoredproc procedure="ams_deleteSavedQuery" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam cfsqltype="CF_SQL_INTEGER" type="in" value="#arguments.queryID#">
				<cfprocparam cfsqltype="CF_SQL_INTEGER" type="in" value="#session.cfcuser.memberdata.memberID#">
			</cfstoredproc>

			<cfset local.returnStruct["success"] = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.returnStruct["success"] = false>
		</cfcatch>
		</cftry>
		
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="copySavedQuery" access="public" output="false" returntype="struct">
		<cfargument name="queryID" type="numeric" required="yes">
		
		<cfset var local = structNew()>
		<cfset local.returnStruct = { "success":false, "newqueryid":0 }>
		
		<cftry>
			<cfstoredproc procedure="ams_copySavedQuery" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam cfsqltype="CF_SQL_INTEGER" type="in" value="#arguments.queryID#">
				<cfprocparam cfsqltype="CF_SQL_INTEGER" type="in" value="#session.cfcuser.memberdata.memberID#">
				<cfprocparam cfsqltype="CF_SQL_INTEGER" type="out" variable="local.newQueryID">
			</cfstoredproc>

			<cfset local.returnStruct["success"] = true>
			<cfset local.returnStruct["newqueryid"] = local.newQueryID>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.returnStruct["success"] = false>
		</cfcatch>
		</cftry>
		
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="insertQueryRunLog" access="public" output="false" returntype="void">
		<cfargument name="queryID" type="numeric" required="true">

		<cfquery datasource="#application.dsn.platformstatsMC.dsn#" name="local.qryLogReport">
			set nocount on;

			declare @memberID int;
			<cfif structKeyExists(session,"cfcuser") and val(session.cfcuser.memberdata.memberid) gt 0>
				set @memberID = #val(session.cfcuser.memberdata.memberid)#;
			<cfelse>
				select @memberID = memberCentral.dbo.fn_ams_getMCSystemMemberID();
			</cfif>

			INSERT INTO dbo.ams_savedQueriesRunLog (queryID, memberID, dateRun)
			VALUES (<cfqueryparam value="#arguments.queryID#" cfsqltype="CF_SQL_INTEGER">, @memberID, getdate())
		</cfquery>
	</cffunction>

	<cffunction name="showContentEditorForSavedFieldvalue" access="public" output="false" returntype="struct">
		<cfargument name="objName" type="string" required="true">
		<cfargument name="objValue" type="string" required="true">

		<cfreturn { "success":true, "html":application.objWebEditor.embed(argumentcollection=arguments) }>
	</cffunction>

	<cffunction name="getSavedQueriesFields" access="public" output="false" returntype="struct">
		<cfargument name="queryID" type="numeric" required="yes">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { 'success': true, 'arrfields':arrayNew(1) }>

		<cfquery name="local.qryFields" datasource="#application.dsn.memberCentral.dsn#">
			SELECT fieldID, columnName, newValue
			FROM dbo.ams_savedQueriesFields
			WHERE queryID = <cfqueryparam value="#arguments.queryID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>

		<cfloop query="local.qryFields">
			<cfset local.tmp = structNew()>
			<cfset local.tmp['fieldid'] = local.qryFields.fieldID>
			<cfset local.tmp['columnname'] = local.qryFields.columnName>
			<cfset local.tmp['newvalue'] = local.qryFields.newValue>
			<cfset arrayAppend(local.returnStruct['arrfields'],local.tmp)>
		</cfloop>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="createSavedQuery" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="queryName" type="string" required="true">

		<cfset var returnStruct = { "success":false, "queryID":0, "ruleID":0 }>

		<cfstoredproc procedure="ams_createSavedQuery" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.queryName#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcUser.memberData.memberID#">
			<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="returnStruct.queryid">
			<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="returnStruct.ruleid">
		</cfstoredproc>

		<cfset returnStruct.success = true>
		
		<cfreturn returnStruct>
	</cffunction>

	<cffunction name="getQueryCreators" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">
	
		<cfset var local = structNew()>

		<cfquery name="local.qryQueryCreators" datasource="#application.dsn.memberCentral.dsn#">
			select distinct m2.memberID, m2.firstName, m2.middleName, m2.lastName, m2.lastname + ', ' + m2.firstName as createdByName
			from dbo.ams_savedQueries sq
			inner join membercentral.dbo.ams_members as m on m.memberID = sq.createdByMemberID
			inner join membercentral.dbo.ams_members as m2 on m2.memberID = m.activeMemberID
			where sq.siteID = <cfqueryparam value="#arguments.siteID#" cfsqltype="CF_SQL_INTEGER">
			order by createdByName
		</cfquery>

		<cfreturn local.qryQueryCreators>
	</cffunction>

	<cffunction name="getImportFields" access="public" output="false" returntype="struct">
		<cfargument name="orgID" type="numeric" required="true">

		<cfset var strImportFields = { "qryFields":queryNew(''), "qryFieldValues":queryNew('') }>

		<cfstoredproc procedure="ams_getImportPartialMemberDataFields" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="1">
			<cfprocresult name="strImportFields.qryFields" resultset="1">
			<cfprocresult name="strImportFields.qryFieldValues" resultset="2">
		</cfstoredproc>

		<cfreturn strImportFields>
	</cffunction>

	<cffunction name="getLinkedRecordsQueryCreators" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">
	
		<cfset var qryLinkedRecordsQueryCreators = "">

		<cfquery name="qryLinkedRecordsQueryCreators" datasource="#application.dsn.memberCentral.dsn#">
			select distinct m2.memberID, m2.firstName, m2.middleName, m2.lastName, m2.lastname + ', ' + m2.firstName as createdByName
			from dbo.ams_savedLinkedRecordsQueries sfb
			inner join membercentral.dbo.ams_members as m on m.memberID = sfb.createdByMemberID
			inner join membercentral.dbo.ams_members as m2 on m2.memberID = m.activeMemberID
			where sfb.siteID = <cfqueryparam value="#arguments.siteID#" cfsqltype="CF_SQL_INTEGER">
			order by createdByName
		</cfquery>

		<cfreturn qryLinkedRecordsQueryCreators>
	</cffunction>

	<cffunction name="getSavedLinkedRecordsQuery" access="public" output="false" returntype="query">
		<cfargument name="queryID" type="numeric" required="true">

		<cfset var qrySavedLinkedRecordsQuery = "">

		<cfquery name="qrySavedLinkedRecordsQuery" datasource="#application.dsn.memberCentral.dsn#">
			SELECT sfb.queryID, sfb.queryName, sfb.orgRuleID, sfb.indRuleID, sfb.fieldSetID, sfb.interval, sfb.intervalTypeID, stt.[name] as intervalTypeName, sfb.nextRunDate, sfb.endRunDate
			FROM dbo.ams_savedLinkedRecordsQueries AS sfb
			LEFT JOIN dbo.scheduledTaskIntervalTypes AS stt ON stt.intervalTypeID = sfb.intervalTypeID
			WHERE sfb.queryID = <cfqueryparam value="#arguments.queryID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>
		
		<cfreturn qrySavedLinkedRecordsQuery>
	</cffunction>

	<cffunction name="deleteSavedLinkedRecordQuery" access="public" output="false" returntype="struct">
		<cfargument name="queryID" type="numeric" required="yes">
		
		<cfset var local = structNew()>
		<cfset local.returnStruct = { "success":false }>
		<cftry>
			<cfstoredproc procedure="ams_deleteSavedLinkedRecordsQuery" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam cfsqltype="CF_SQL_INTEGER" type="in" value="#arguments.queryID#">
				<cfprocparam cfsqltype="CF_SQL_INTEGER" type="in" value="#session.cfcuser.memberdata.memberID#">
			</cfstoredproc>

			<cfset local.returnStruct["success"] = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.returnStruct["success"] = false>
		</cfcatch>
		</cftry>
		
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="copySavedLinkedRecordQuery" access="public" output="false" returntype="struct">
		<cfargument name="queryID" type="numeric" required="yes">
		
		<cfset var local = structNew()>
		<cfset local.returnStruct = { "success":false, "newqueryid":0 }>
		
		<cftry>
			<cfstoredproc procedure="ams_copySavedLinkedRecordsQuery" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam cfsqltype="CF_SQL_INTEGER" type="in" value="#arguments.queryID#">
				<cfprocparam cfsqltype="CF_SQL_INTEGER" type="in" value="#session.cfcuser.memberdata.memberID#">
				<cfprocparam cfsqltype="CF_SQL_INTEGER" type="out" variable="local.newqueryid">
			</cfstoredproc>

			<cfset local.returnStruct["success"] = true>
			<cfset local.returnStruct["newqueryid"] = local.newqueryid>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.returnStruct["success"] = false>
		</cfcatch>
		</cftry>
		
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="createSavedLinkedRecordsQuery" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="queryName" type="string" required="true">

		<cfset var returnStruct = { "success":false, "queryid":0, "orgRuleID":0, "indRuleID":0 }>

		<cfstoredproc procedure="ams_createSavedLinkedRecordsQuery" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.queryName#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcUser.memberData.memberID#">
			<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="returnStruct.queryID">
			<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="returnStruct.orgRuleID">
			<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="returnStruct.indRuleID">
		</cfstoredproc>

		<cfset returnStruct.success = true>
		
		<cfreturn returnStruct>
	</cffunction>

	<cffunction name="processMassUpdateMembersByLinkedRecordsQuery" access="package" output="false" returntype="struct">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="queryID" type="numeric" required="true">
		<cfargument name="queryName" type="string" required="true">
		<cfargument name="fieldSetID" type="numeric" required="true">
		<cfargument name="isRecurringUpdate" type="boolean" required="true">
		<cfargument name="schedInterval" type="string" required="true">
		<cfargument name="schedIntervalTypeID" type="string" required="true">
		<cfargument name="schedNextRunDate" type="string" required="true">
		<cfargument name="schedEndRunDate" type="string" required="true">
		<cfargument name="arrQueryFields" type="array" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { success=true, errorCode=999, errorInfo=StructNew() }>
		<cfset local.tmpSuffix = replace(createUUID(),'-','','ALL')>
		
		<!--- run first part of import --->
  		<cftry>
            <cfquery name="local.qryImport" datasource="#application.dsn.membercentral.dsn#" result="local.importResult">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					IF OBJECT_ID('tempdb..##tmpQueryFieldsData') IS NOT NULL 
						DROP TABLE ##tmpQueryFieldsData;
					CREATE TABLE ##tmpQueryFieldsData (columnName sysname, newValue varchar(max));

					DECLARE @orgID int, @queryID int, @runByMemberID int, @importResult xml, @errCount int, @environmentName varchar(50);
					DECLARE @tmpOrgMemberColumns TABLE (columnName sysname PRIMARY KEY, fieldCode varchar(40));

					SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">;
					SET @queryID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.queryID#">;
					SET @runByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">;
					SET @environmentName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#application.MCEnvironment#">;

					INSERT INTO @tmpOrgMemberColumns (columnName, fieldCode)
					SELECT columnName, fieldCode
					FROM dbo.fn_ams_getOrgMemberColumns(@orgID);

					<cfloop array="#arguments.arrQueryFields#" index="local.thisField">
						INSERT INTO ##tmpQueryFieldsData (columnName, newValue)
						VALUES (<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.thisField.columnName#">,
								<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#local.thisField.columnValue#">);
					</cfloop>

					UPDATE dbo.ams_savedLinkedRecordsQueries
					SET queryName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.queryName#">,
						fieldSetID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.fieldSetID#">,
						<cfif arguments.isRecurringUpdate and arguments.schedInterval gt 0 and arguments.schedIntervalTypeID gt 0 and len(arguments.schedNextRunDate) gt 0>
							interval = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.schedInterval#">,
							intervalTypeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.schedIntervalTypeID#">,
							nextRunDate = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#ParseDateTime(replace(arguments.schedNextRunDate,' - ',' '))#">,
							<cfif len(arguments.schedEndRunDate) eq 0>
								endRunDate = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" null="yes">
							<cfelse>
								endRunDate = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#ParseDateTime(replace(arguments.schedEndRunDate,' - ',' '))#">
							</cfif>
						<cfelse>
							interval = NULL,
							intervalTypeID = NULL,
							nextRunDate = NULL,
							endRunDate = NULL
						</cfif>
					WHERE queryID = @queryID;

					DELETE FROM dbo.ams_savedLinkedRecordsQueriesFields
					WHERE queryID = @queryID;

					INSERT INTO dbo.ams_savedLinkedRecordsQueriesFields (queryID, columnName, fieldCode, newValue)
					SELECT @queryID, tmp.columnName, omc.fieldCode, tmp.newValue
					FROM ##tmpQueryFieldsData AS tmp
					INNER JOIN @tmpOrgMemberColumns AS omc ON omc.columnName = tmp.columnName;

					EXEC dbo.ams_scheduledLinkedRecordsQueryImportMemberData @queryID=@queryID, @orgID=@orgID, 
						@environmentName=@environmentName, @runByMemberID=@runByMemberID, @importResult=@importResult OUTPUT;

					SET @errCount = @importResult.value('count(/import/errors/error)','int');
					SELECT @importResult as importResult, @errCount as errCount;

					IF OBJECT_ID('tempdb..##tmpQueryFieldsData') IS NOT NULL 
						DROP TABLE ##tmpQueryFieldsData;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
					SELECT @importResult = '<import><errors><error msg="Unable to run linked records query." /><error msg="' + error_message() + '" /></errors></import>';
					SELECT @importResult as importResult, 1 as errCount;
				END CATCH
			</cfquery>

			<cfset local.returnStruct.importResultXML = xmlparse(local.qryImport.importResult)>
			<cfset local.returnStruct.numFatalErrors = local.qryImport.errCount>

			<cfif local.returnStruct.numFatalErrors gt 0>
				<cfset local.returnStruct.success = false>
				<cfset local.returnStruct.errorCode = 105>
				<cfset StructInsert(local.returnStruct.errorInfo,local.returnStruct.errorCode,'')>
			</cfif>
		<cfcatch type="any">
			<cfset local.returnStruct.success = false>
			<cfset local.returnStruct.errorCode = 1>
			<cfset StructInsert(local.returnStruct.errorInfo,local.returnStruct.errorCode,"There was a problem importing the data.")>
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="insertLinkedRecordsQueryRunLog" access="public" output="false" returntype="void">
		<cfargument name="queryID" type="numeric" required="true">

		<cfquery datasource="#application.dsn.platformstatsMC.dsn#" name="local.qryLogReport">
			set nocount on;

			declare @memberID int;
			<cfif structKeyExists(session,"cfcuser") and val(session.cfcuser.memberdata.memberid) gt 0>
				set @memberID = #val(session.cfcuser.memberdata.memberid)#;
			<cfelse>
				select @memberID = memberCentral.dbo.fn_ams_getMCSystemMemberID();
			</cfif>

			INSERT INTO dbo.ams_savedLinkedRecordsQueriesRunLog (queryID, memberID, dateRun)
			VALUES (<cfqueryparam value="#arguments.queryID#" cfsqltype="CF_SQL_INTEGER">, @memberID, getdate())
		</cfquery>
	</cffunction>

	<cffunction name="getFieldsByFieldSetID" access="public" output="false" returntype="query">
		<cfargument name="fieldsetID" type="numeric" required="true">
	
		<cfset var local = structNew()>

		<cfquery name="local.qryFields" datasource="#application.dsn.memberCentral.dsn#">
			select mf.fieldID, mf.dbField
			from dbo.ams_memberFields as mf 
			inner join dbo.ams_memberDataColumnDisplayTypes as dt on dt.displayTypeID = mf.displayTypeID
			inner join dbo.ams_memberDataColumnDataTypes as ddt on ddt.dataTypeID = mf.dataTypeID
			where mf.fieldsetID = <cfqueryparam value="#arguments.fieldsetID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>

		<cfreturn local.qryFields>
	</cffunction>

	<cffunction name="getLinkedRecordsQueryFieldsDetails" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="true">
		<cfargument name="queryID" type="numeric" required="yes">
		<cfargument name="fieldsetID" type="numeric" required="true">
		
		<cfset local.returnStruct = { 'success': true, 'arrSavedFields':arrayNew(1), 'arrImportFields':arrayNew(1), 'arrImportFieldValues':arrayNew(1) }>

		<cfset local.objImport = CreateObject("component","import")>
		<cfset local.strImportFields = local.objImport.getImportFields(orgID=arguments.mcproxy_orgID)>
		<cfset local.qryFields = local.objImport.getFieldsByFieldSetID(fieldSetID=val(arguments.fieldSetID))>

		<cfquery name="local.qrySavedFields" datasource="#application.dsn.memberCentral.dsn#">
			SELECT fbf.fieldID, fbf.columnName, fbf.newValue
			FROM dbo.ams_savedLinkedRecordsQueriesFields fbf
			WHERE fbf.queryID = <cfqueryparam value="#arguments.queryID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>

		<cfquery name="local.qryImportFields" dbtype="query">
			SELECT *
			FROM [local].strImportFields.qryFields
			WHERE columnName IN (<cfqueryparam value="#valueList(local.qryFields.dbField)#" cfsqltype="cf_sql_varchar" list="true">)
		</cfquery>

		<cfquery name="local.arrImportFields" dbtype="query" returntype="array">
			SELECT columnName, columnDataType, max_length, allowMultiple, area, areaID, viewSuffix, viewAlias, 
				defaultValueID, MDdataTypeCode, MDdisplayTypeCode, allowNewValuesOnImport, isRequired, allowNull
			FROM [local].qryImportFields
		</cfquery>

		<cfquery name="local.arrImportFieldValues" dbtype="query" returntype="array">
			SELECT columnName, columnValue
			FROM [local].strImportFields.qryFieldValues
			WHERE columnName IN (<cfqueryparam value="#valueList(local.qryFields.dbField)#" cfsqltype="cf_sql_varchar" list="true">)
			ORDER BY columnName, columnValue
		</cfquery>

		<cfset local.returnStruct['arrImportFields'] = local.arrImportFields>
		<cfset local.returnStruct['arrImportFieldValues'] = local.arrImportFieldValues>

		<cfloop query="local.qrySavedFields">
			<cfset local.tmp = structNew()>
			<cfset local.tmp['fieldID'] = local.qrySavedFields.fieldID>
			<cfset local.tmp['columnName'] = local.qrySavedFields.columnName>
			<cfset local.tmp['newValue'] = local.qrySavedFields.newValue>
			<cfset arrayAppend(local.returnStruct['arrSavedFields'],local.tmp)>
		</cfloop>

		<cfreturn local.returnStruct>
	</cffunction>

</cfcomponent>