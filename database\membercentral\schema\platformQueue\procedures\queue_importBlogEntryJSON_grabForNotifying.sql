ALTER PROC dbo.queue_importBlogEntryJSON_grabForNotifying

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @statusReady int, @statusGrabbed int, @communityResourceTypeID int;
	SELECT @queueTypeID = queueTypeID FROM dbo.tblQueueTypes WHERE queueType = 'importBlogEntryJSON';
	SELECT @statusReady = queuestatusID FROM dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID and queueStatus = 'ReadyToNotify';
	SELECT @statusGrabbed = queuestatusID FROM dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID and queueStatus = 'GrabbedForNotifying';

	IF OBJECT_ID('tempdb..#tmpImportBlogEntryJSON') IS NOT NULL
		DROP TABLE #tmpImportBlogEntryJSON;
	CREATE TABLE #tmpImportBlogEntryJSON (itemID int);

	WITH processedJobs AS (
		SELECT qi.itemID 
		FROM dbo.queue_importBlogEntryJSON AS qi
		WHERE qi.statusID = @statusReady
		AND NOT EXISTS (
			SELECT 1
			FROM dbo.queue_importBlogEntryJSON AS tmp
			WHERE tmp.itemGroupUID = qi.itemGroupUID
			AND tmp.statusID <> @statusReady
		)
	)
	UPDATE qid WITH (UPDLOCK, READPAST)
	SET qid.statusID = @statusGrabbed,
		qid.dateUpdated = getdate()
		OUTPUT inserted.itemID
		INTO #tmpImportBlogEntryJSON
	FROM dbo.queue_importBlogEntryJSON AS qid
	INNER JOIN processedJobs AS batch ON batch.itemID = qid.itemID;

	SELECT @communityResourceTypeID = membercentral.dbo.fn_getResourceTypeID('Community');

	SELECT DISTINCT qid.itemGroupUID, qid.itemID, qid.siteID, qid.blogID, qid.submittedMemberID, qid.blogEntryJSON, qid.errorMessage,
		mActive.firstName + ' ' + mActive.lastName as memberName, me.email AS memberEmail,
		blogName = ai.applicationInstanceName + CASE WHEN communityInstances.applicationInstanceName IS NOT NULL THEN ' (' + communityInstances.applicationInstanceName + ')' ELSE '' END
	FROM #tmpImportBlogEntryJSON AS tmp
	INNER JOIN dbo.queue_importBlogEntryJSON AS qid ON qid.itemID = tmp.itemID
	INNER JOIN membercentral.dbo.bl_blog AS b ON b.blogID = qid.blogID
	INNER JOIN membercentral.dbo.cms_applicationInstances AS ai ON ai.applicationInstanceID = b.applicationInstanceID
		AND ai.siteID = qid.siteID
	INNER JOIN membercentral.dbo.cms_siteResources AS sr ON sr.siteResourceID = ai.siteResourceID
	INNER JOIN membercentral.dbo.cms_siteResources AS parentResource ON parentResource.siteID = qid.siteID
		AND parentResource.siteResourceID = sr.parentSiteResourceID
	LEFT OUTER JOIN membercentral.dbo.cms_siteResources AS grandparentResource
		INNER JOIN membercentral.dbo.cms_applicationInstances AS communityInstances ON communityInstances.siteResourceID = grandParentResource.siteResourceID
		ON grandparentResource.siteID = qid.siteID
			AND grandparentResource.siteResourceID = parentResource.parentSiteResourceID
			AND grandparentResource.resourceTypeID = @communityResourceTypeID
	INNER JOIN membercentral.dbo.ams_members AS m ON m.memberID = qid.submittedMemberID
	INNER JOIN membercentral.dbo.ams_members AS mActive on mActive.memberID = m.activeMemberID
	INNER JOIN membercentral.dbo.ams_memberEmails AS me ON me.orgID = m.orgID and me.memberID = mActive.memberID
	INNER JOIN membercentral.dbo.ams_memberEmailTags AS metag ON metag.orgID = me.orgID and metag.memberID = me.memberID AND metag.emailTypeID = me.emailTypeID
	INNER JOIN membercentral.dbo.ams_memberEmailTagTypes AS metagt ON metagt.orgID = metag.orgID and metagt.emailTagTypeID = metag.emailTagTypeID AND metagt.emailTagType = 'Primary'
	ORDER BY qid.itemGroupUID, qid.itemID;

	IF OBJECT_ID('tempdb..#tmpImportBlogEntryJSON') IS NOT NULL
		DROP TABLE #tmpImportBlogEntryJSON;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
