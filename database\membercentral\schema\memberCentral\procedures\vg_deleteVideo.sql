ALTER PROC dbo.vg_deleteVideo
@albumID int,
@videoID int,
@contributorMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @siteID int, @siteCode varchar(10), @orgCode varchar(10), @activeMemberID int, 
		@applicationTypeID int, @siteResourceID int, @applicationInstanceID int,
		@userAssetsPath varchar(60), @thumbFilePath varchar(200), @queueTypeID int, @queueStatusID int;

	select @applicationTypeID = dbo.fn_getApplicationTypeIDFromName('VideoGallery');

	SELECT @activeMemberID = activeMemberID
	FROM dbo.ams_members
	WHERE memberID = @contributorMemberID;

	select @siteID = ai.siteID, @siteCode = s.siteCode, @orgCode = o.orgCode, 
		@applicationInstanceID = g.applicationInstanceID, @siteResourceID = v.siteResourceID
	from dbo.vg_videos as v
	inner join dbo.vg_albums as a on a.albumID = v.albumID
	inner join dbo.vg_gallery as g on g.galleryID = a.galleryID
	inner join dbo.cms_applicationInstances as ai on ai.applicationInstanceID = g.applicationInstanceID
	inner join dbo.sites as s on s.siteID = ai.siteID
	inner join dbo.organizations as o on o.orgID = s.orgID
	where v.videoID = @videoID
	and v.albumID = @albumID;

	select @userAssetsPath = userAssetsPath
	from dbo.fn_getServerSettings();

	set @thumbFilePath = @userAssetsPath + lower(@orgCode + '\' + @siteCode + '\vgalleries\thumbs\' + cast(@videoID as varchar(10)) + '.jpg');

	select @queueTypeID = queueTypeID from platformQueue.dbo.tblQueueTypes where queueType = 'MCFileDelete';
	select @queueStatusID = queueStatusID from platformQueue.dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'readyToProcess';

	BEGIN TRAN;
		-- Update the status 
		UPDATE dbo.cms_siteResources
		SET siteResourceStatusID = 3
		WHERE siteResourceID = @siteResourceID;

		UPDATE dbo.vg_albums 
		SET featuredVideoID = NULL
		WHERE featuredVideoID = @videoID
		AND albumID = @albumID;

		DELETE FROM dbo.vg_videos
		WHERE videoID = @videoID
		AND albumID = @albumID;
		
		EXEC dbo.vg_reorderVideos @albumID=@albumID, @siteID=@siteID;

		-- create activity log entry
		EXEC platformstatsMC.dbo.act_recordLog @memberID=@activeMemberID, @activityType='remove', 
			@applicationTypeID=@applicationTypeID, @applicationInstanceID=@applicationInstanceID,
			@supportSiteResourceID=@siteResourceID, @supportMemberID=NULL, @supportMessage=NULL;
	COMMIT TRAN;

	IF dbo.fn_fileExists(@thumbFilePath) = 1
		INSERT INTO platformQueue.dbo.queue_MCFileDelete (filePath, dateAdded, dateUpdated, statusID)
		VALUES (@thumbFilePath, GETDATE(), GETDATE(), @queueStatusID);

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
