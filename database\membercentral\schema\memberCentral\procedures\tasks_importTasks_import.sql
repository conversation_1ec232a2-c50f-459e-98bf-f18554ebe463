ALTER PROC dbo.tasks_importTasks_import
@siteID int,
@runByMemberID int,
@runImmediately bit,
@importResult xml OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpTaskCustomDetails') IS NOT NULL 
		DROP TABLE #tmpTaskCustomDetails;
	IF OBJECT_ID('tempdb..#tmpTaskTagCategories') IS NOT NULL 
		DROP TABLE #tmpTaskTagCategories;
	CREATE TABLE #tmpTaskCustomDetails (rowID int, fieldID int, fieldValue varchar(max), valueID int);
	CREATE TABLE #tmpTaskTagCategories (rowID int, categoryID int);

	DECLARE @colList varchar(max), @minCol varchar(255), @dynSQL nvarchar(max), @selColList varchar(max), @taskTagColList varchar(max), 
		@itemUID uniqueidentifier, @itemGroupUID uniqueidentifier, @queueTypeID int, @statusInserting int, @statusReady int, @xmlMessage xml;

	select @queueTypeID = queueTypeID from platformQueue.dbo.tblQueueTypes where queueType = 'TaskImport';
	select @statusInserting = queueStatusID from platformQueue.dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'Inserting';
	select @statusReady = queueStatusID from platformQueue.dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'ReadyToProcess';

	-- all imported tasks get the same ItemGroupUID
	select @itemGroupUID = NEWID();

	BEGIN TRY

		IF EXISTS (select 1 from #tblTaskTags)
			select @taskTagColList = COALESCE(@taskTagColList + ', ', '') + quotename(columnName) from #tblTaskTags;
		
		IF EXISTS (select 1 from #tblCustomColumns) BEGIN
			-- string columns
			set @colList = null;
			select @colList = COALESCE(@colList + ',', '') + quotename(columnName) 
			from #tblCustomColumns
			where dataTypeCode = 'STRING'
			and displayTypeCode not in ('SELECT','RADIO','CHECKBOX');

			if @colList is not null BEGIN
				set @selColList = null;
				select @selColList = COALESCE(@selColList + '; ', '') + 'ALTER TABLE #tasks_tasksImport ALTER COLUMN ' + tbl.listitem + ' varchar(max) null'
					from dbo.fn_varcharListToTable(@colList,',') as tbl;
				exec(@selColList);

				set @selColList = null;
				select @selColList = COALESCE(@selColList + ', ', '') + tbl.listitem + ' = isnull(' + tbl.listitem + ','''')'
				from dbo.fn_varcharListToTable(@colList,',') as tbl;

				set @dynSQL = 'update #tasks_tasksImport set ' +  @selColList + ';';
				exec(@dynSQL);

				set @dynSQL=null;
				set @dynSQL = 'select tmp.rowID, f.fieldID, nullif(tmp.valueString,''''), fv.valueID
								from (
									select rowID, columnName, valueString
									from #tasks_tasksImport
									unpivot (valueString for columnName in (' + @colList + ')) u
								) tmp
								inner join #tblCustomColumns as impCols on impCols.columnName = tmp.columnName
								inner join dbo.cf_fields as f on f.fieldID = impCols.fieldID
								left outer join dbo.cf_fieldValues as fv on fv.fieldID = f.fieldID and fv.valueString = tmp.valueString COLLATE Latin1_General_CS_AS';
				
				INSERT INTO #tmpTaskCustomDetails (rowID, fieldID, fieldValue, valueID)
				EXEC(@dynSQL);
			end

			-- integer columns
			set @colList = null;
			select @colList = COALESCE(@colList + ',', '') + quotename(columnName) 
			from #tblCustomColumns
			where dataTypeCode = 'INTEGER'
			and displayTypeCode not in ('SELECT','RADIO','CHECKBOX');

			if @colList is not null BEGIN
				set @selColList = null;
				select @selColList = COALESCE(@selColList + '; ', '') + 'ALTER TABLE #tasks_tasksImport ALTER COLUMN ' + tbl.listitem + ' varchar(10) null'
					from dbo.fn_varcharListToTable(@colList,',') as tbl;
				exec(@selColList);

				set @selColList = null;
				select @selColList = COALESCE(@selColList + ', ', '') + tbl.listitem + ' = isnull(' + tbl.listitem + ','''')'
				from dbo.fn_varcharListToTable(@colList,',') as tbl;

				set @dynSQL = 'update #tasks_tasksImport set ' +  @selColList + ';';
				exec(@dynSQL);

				set @dynSQL=null;
				set @dynSQL = 'select tmp.rowID, f.fieldID, nullif(tmp.valueInteger,''''), fv.valueID
								from (
									select rowID, columnName, valueInteger
									from #tasks_tasksImport
									unpivot (valueInteger for columnName in (' + @colList + ')) u
								) tmp
								inner join #tblCustomColumns as impCols on impCols.columnName = tmp.columnName
								inner join dbo.cf_fields as f on f.fieldID = impCols.fieldID
								inner join dbo.cf_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID
								left outer join dbo.cf_fieldValues as fv on fv.fieldID = f.fieldID and cast(fv.valueInteger as varchar(10)) = tmp.valueInteger';
				
				INSERT INTO #tmpTaskCustomDetails (rowID, fieldID, fieldValue, valueID)
				EXEC(@dynSQL);
			end

			-- decimal columns
			set @colList = null;
			select @colList = COALESCE(@colList + ',', '') + quotename(columnName) 
			from #tblCustomColumns
			where dataTypeCode = 'DECIMAL2'
			and displayTypeCode not in ('SELECT','RADIO','CHECKBOX');

			if @colList is not null BEGIN
				set @selColList = null;
				select @selColList = COALESCE(@selColList + '; ', '') + 'ALTER TABLE #tasks_tasksImport ALTER COLUMN ' + tbl.listitem + ' varchar(15) null'
					from dbo.fn_varcharListToTable(@colList,',') as tbl;
				exec(@selColList);

				set @selColList = null;
				select @selColList = COALESCE(@selColList + ', ', '') + tbl.listitem + ' = isnull(' + tbl.listitem + ','''')'
				from dbo.fn_varcharListToTable(@colList,',') as tbl;

				set @dynSQL = 'update #tasks_tasksImport set ' +  @selColList + ';';
				exec(@dynSQL);

				set @dynSQL=null;
				set @dynSQL = 'select tmp.rowID, f.fieldID, nullif(tmp.valueDecimal2,''''), fv.valueID
								from (
									select rowID, columnName, valueDecimal2
									from #tasks_tasksImport
									unpivot (valueDecimal2 for columnName in (' + @colList + ')) u
								) tmp
								inner join #tblCustomColumns as impCols on impCols.columnName = tmp.columnName
								inner join dbo.cf_fields as f on f.fieldID = impCols.fieldID
								left outer join dbo.cf_fieldValues as fv on fv.fieldID = f.fieldID and cast(fv.valueDecimal2 as varchar(15)) = tmp.valueDecimal2';
				
				INSERT INTO #tmpTaskCustomDetails (rowID, fieldID, fieldValue, valueID)
				EXEC(@dynSQL);
			end

			-- date columns
			set @colList = null;
			select @colList = COALESCE(@colList + ',', '') + quotename(columnName) 
			from #tblCustomColumns
			where dataTypeCode = 'DATE'
			and displayTypeCode not in ('SELECT','RADIO','CHECKBOX');

			if @colList is not null BEGIN
				set @selColList = null;
				select @selColList = COALESCE(@selColList + '; ', '') + 'ALTER TABLE #tasks_tasksImport ALTER COLUMN ' + tbl.listitem + ' varchar(30) null'
					from dbo.fn_varcharListToTable(@colList,',') as tbl;
				exec(@selColList);

				set @selColList = null;
				select @selColList = COALESCE(@selColList + ', ', '') + tbl.listitem + ' = isnull(' + tbl.listitem + ','''')'
				from dbo.fn_varcharListToTable(@colList,',') as tbl;

				set @dynSQL = 'update #tasks_tasksImport set ' +  @selColList + ';';
				exec(@dynSQL);

				set @dynSQL=null;
				set @dynSQL = 'select tmp.rowID, f.fieldID, nullif(tmp.valueDate,''''), fv.valueID
								from (
									select rowID, columnName, valueDate
									from #tasks_tasksImport
									unpivot (valueDate for columnName in (' + @colList + ')) u
								) tmp
								inner join #tblCustomColumns as impCols on impCols.columnName = tmp.columnName
								inner join dbo.cf_fields as f on f.fieldID = impCols.fieldID
								left outer join dbo.cf_fieldValues as fv on fv.fieldID = f.fieldID and cast(fv.valueDate as varchar(30)) = tmp.valueDate';
				
				INSERT INTO #tmpTaskCustomDetails (rowID, fieldID, fieldValue, valueID)
				EXEC(@dynSQL);
			end

			-- select, radio, and checkbox custom field columns
			set @colList = null;
			select @colList = COALESCE(@colList + ',', '') + quotename(columnName) 
			from #tblCustomColumns
			where displayTypeCode in ('SELECT','RADIO','CHECKBOX');

			if @colList is not null BEGIN
				set @selColList = null;
				select @selColList = COALESCE(@selColList + '; ', '') + 'ALTER TABLE #tasks_tasksImport ALTER COLUMN ' + tbl.listitem + ' varchar(max) null'
					from dbo.fn_varcharListToTable(@colList,',') as tbl;
				exec(@selColList);

				set @selColList = null;
				select @selColList = COALESCE(@selColList + ', ', '') + tbl.listitem + ' = isnull(' + tbl.listitem + ','''')'
				from dbo.fn_varcharListToTable(@colList,',') as tbl;

				set @dynSQL = 'update #tasks_tasksImport set ' +  @selColList + ';';
				exec(@dynSQL);

				set @dynSQL=null;
				set @dynSQL = 'select tmp.rowID, f.fieldID, tbl.listitem, fv.valueID
								from (
									select rowID, columnName, columnValue
									from #tasks_tasksImport
									unpivot (columnValue for columnName in (' + @colList + ')) u
								)tmp
								cross apply dbo.fn_varcharListToTable(tmp.columnValue,''|'') as tbl
								inner join #tblCustomColumns as impCols on impCols.columnName = tmp.columnName
								inner join dbo.cf_fields as f on f.fieldID = impCols.fieldID
								inner join dbo.cf_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID
								inner join dbo.cf_fieldValues as fv on fv.fieldID = f.fieldID
									and case when ft.dataTypeCode = ''DATE'' then cast(cast(tbl.listitem as date) as varchar(15)) else tbl.listitem end
										 = case when ft.dataTypeCode = ''STRING'' then cast(fv.valueString as varchar(max))
												 when ft.dataTypeCode = ''DECIMAL2'' then cast(fv.valueDecimal2 as varchar(15))
												 when ft.dataTypeCode = ''INTEGER'' then cast(fv.valueInteger as varchar(10))
												 when ft.dataTypeCode = ''BIT'' then cast(fv.valueBit as varchar(1))
												 when ft.dataTypeCode = ''DATE'' then cast(fv.valueDate as varchar(15))
											else '''' end';
				
				INSERT INTO #tmpTaskCustomDetails (rowID, fieldID, fieldValue, valueID)
				EXEC(@dynSQL);
			end
		END

		IF @taskTagColList is not null BEGIN
			select @selColList = null, @dynSQL=null;
			select @selColList = COALESCE(@selColList + '; ', '') + 'ALTER TABLE #tasks_tasksImport ALTER COLUMN ' + tbl.listitem + ' varchar(max) null'
				from dbo.fn_varcharListToTable(@taskTagColList,',') as tbl;
			exec(@selColList);

			set @selColList = null;
			select @selColList = COALESCE(@selColList + ', ', '') + tbl.listitem + ' = isnull(' + tbl.listitem + ','''')'
			from dbo.fn_varcharListToTable(@taskTagColList,',') as tbl;
			
			set @dynSQL = 'update #tasks_tasksImport set ' +  @selColList + ';';
			exec(@dynSQL);

			set @dynSQL=null;
			set @dynSQL = 'select tmp.rowID, tbl.categoryID
						from (
							select rowID, taskTag
							from #tasks_tasksImport
							unpivot (taskTag for columnName in (' + @taskTagColList + ')) u
						) tmp
						inner join dbo.#tblTaskTags as tbl on tbl.columnName = tmp.taskTag';
		
			INSERT INTO #tmpTaskTagCategories (rowID, categoryID)
			EXEC(@dynSQL);
		END

		-- delete task tag field details where task tags not selected
		DELETE tmpCustom
		FROM #tmpTaskCustomDetails as tmpCustom
		INNER JOIN #tblCustomColumns as tmpCols on tmpCols.fieldID = tmpCustom.fieldID
		LEFT OUTER JOIN #tmpTaskTagCategories as tmpCat on tmpCat.categoryID = tmpCols.categoryID
		WHERE tmpCols.itemType = 'TaskTagCustom'
		AND tmpCat.categoryID IS NULL;
		
		BEGIN TRAN;
			INSERT INTO platformQueue.dbo.tblQueueItems (itemUID, queueStatusID)
			select itemUID, @statusInserting
			from #tasks_tasksImport;

			-- task project, prospect, and status
			INSERT INTO platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueInteger)
			select @itemGroupUID, tmp.itemUID, @runByMemberID, @siteID, dc.columnID, tmp.rowID, unPvtInt.columnValueInt
			from #tasks_tasksImport as tmp
			inner join (
				select rowID, columnname, columnValueInt
				from #tasks_tasksImport
				unpivot (columnValueInt for columnname in (MCProjectID,MCProspectMemberID,MCTaskStatusID)) u
			) as unPvtInt on unPvtInt.rowID = tmp.rowID
			inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID and dc.columnname = unPvtInt.columnname;

			-- task assignees
			INSERT INTO platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueInteger)
			select @itemGroupUID, tmp.itemUID, @runByMemberID, @siteID, dc.columnID, it.rowID, tmp.memberID
			from #tblSolicitorMemberNumbers as tmp
			inner join #tasks_tasksImport as it on it.itemUID = tmp.itemUID
			inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID and dc.columnname = 'MCSolicitorMemberID'
			where tmp.memberID is not null;

			-- task categories
			INSERT INTO platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueInteger)
			select @itemGroupUID, tmp.itemUID, @runByMemberID, @siteID, dc.columnID, tmp.rowID, tmpTag.categoryID
			from #tasks_tasksImport as tmp
			inner join #tmpTaskTagCategories as tmpTag on tmpTag.rowID = tmp.rowID
			inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID and dc.columnname = 'MCTaskCategoryID'
			where tmpTag.categoryID is not null;

			-- task due date and reminder date
			INSERT INTO platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueDate)
			select @itemGroupUID, tmp.itemUID, @runByMemberID, @siteID, dc.columnID, tmp.rowID, unPvtDate.columnValueDate
			from #tasks_tasksImport as tmp
			inner join (
				select rowID, columnname, columnValueDate
				from #tasks_tasksImport
				unpivot (columnValueDate for columnname in (TaskDueDate,TaskReminderDate)) u
			) as unPvtDate on unPvtDate.rowID = tmp.rowID
			inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID and dc.columnname = unPvtDate.columnname;

			-- task objective
			INSERT INTO platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueText)
			select @itemGroupUID, tmp.itemUID, @runByMemberID, @siteID, dc.columnID, tmp.rowID, unPvtText.columnValueText
			from #tasks_tasksImport as tmp
			inner join (
				select rowID, columnname, columnValueText
				from #tasks_tasksImport
				unpivot (columnValueText for columnname in (TaskObjective)) u
			) as unPvtText on unPvtText.rowID = tmp.rowID
			inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID and dc.columnname = unPvtText.columnname;


			INSERT INTO platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueString)
			select @itemGroupUID, tmp.itemUID, @runByMemberID, @siteID, dc.columnID, tmp.rowID, fd.fieldValue
			from #tasks_tasksImport as tmp
			inner join #tmpTaskCustomDetails as fd on fd.rowID = tmp.rowID
			inner join #tblCustomColumns as tmpCustom on tmpCustom.fieldID = fd.fieldID
			inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID and dc.columnname = tmpCustom.columnname;

			-- update queue item groups to show ready to process
			update qi WITH (UPDLOCK, HOLDLOCK)
			set qi.queueStatusID = @statusReady,
				qi.dateUpdated = getdate()
			from platformQueue.dbo.tblQueueItems as qi
			inner join #tasks_tasksImport as tmp on tmp.itemUID = qi.itemUID;

			-- resume task
			EXEC dbo.sched_resumeTask @name='Tasks Import Queue', @engine='MCLuceeLinux';
		COMMIT TRAN;

		IF @runImmediately = 1 BEGIN
			select @itemUID = min(itemUID) from #tasks_tasksImport;
			while @itemUID is not null BEGIN
				EXEC dbo.tasks_importTaskFromQueue @itemUID=@itemUID;
				select @itemUID = min(itemUID) from #tasks_tasksImport where itemUID > @itemUID;
			END
		END ELSE BEGIN
			-- send message to service broker to create all the individual messages
			select @xmlMessage = isnull((
				select 'taskImportLoad' as t, cast(@itemGroupUID as varchar(60)) as u
				FOR XML RAW('mc'), TYPE
			),'<mc/>');
			EXEC platformQueue.dbo.queue_DataImport_sendMessage @xmlMessage=@xmlMessage;
		END

	END TRY
	BEGIN CATCH
		IF @@trancount > 0 ROLLBACK TRANSACTION;
		INSERT INTO #tblImportErrors (msg) VALUES ('Unable to import tasks.');
		INSERT INTO #tblImportErrors (msg) VALUES (left(error_message(),600));
	END CATCH

	-- return the xml results
	select @importResult = (
		select getdate() as "@date", 
			isnull((select top 301 dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg"
			from #tblImportErrors
			order by msg
			FOR XML path('error'), root('errors'), type),'<errors/>')
		for xml path('import'), TYPE);

	IF OBJECT_ID('tempdb..#tmpTaskCustomDetails') IS NOT NULL 
		DROP TABLE #tmpTaskCustomDetails;
	IF OBJECT_ID('tempdb..#tmpTaskTagCategories') IS NOT NULL 
		DROP TABLE #tmpTaskTagCategories;
	
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
