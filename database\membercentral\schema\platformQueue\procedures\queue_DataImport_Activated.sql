ALTER PROC dbo.queue_DataImport_Activated
AS

SET XACT_ABORT, NOCOUNT ON;
SET DEADLOCK_PRIORITY -5;

DECLARE @DialogHandle uniqueidentifier, @MessageType sysname, @MessageBody varbinary(max),
	@xmldata xml, @itemUID uniqueidentifier, @itemID int, @ErrorMessage nvarchar(2048), 
	@queueType varchar(30), @queueTypeFound bit;

WHILE 1 = 1
BEGIN TRY

	SELECT @DialogHandle = NULL, @MessageType = NULL, @MessageBody = NULL, @xmldata = NULL, @queueType = NULL,
		@itemUID = NULL, @itemID = NULL, @ErrorMessage = NULL, @queueTypeFound = 0;

	-- initially set to snapshot. this allows us to go in and out throughout this request
	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
	BEGIN TRANSACTION;
		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

		WAITFOR (
			RECEIVE TOP (1) @DialogHandle = conversation_handle,
							@MessageType = message_type_name,
							@MessageBody = message_body
			FROM dbo.DataImportQueue
		), TIMEOUT 1000;

		IF @DialogHandle IS NULL BEGIN
			COMMIT TRANSACTION;
			BREAK;
		END

		IF @MessageType = N'PlatformQueue/GeneralXMLRequest' BEGIN
			BEGIN TRY
				SET @xmldata = cast(@MessageBody as xml);

				SELECT @queueType = @xmldata.value('(/mc/@t)[1]','varchar(30)');
			
				IF @queueTypeFound = 0 AND @queueType = 'memberConditions' BEGIN
					EXEC dbo.queue_MemberConditions_load @xmlMessage=@xmldata;
					SET @queueTypeFound = 1;
				END
				IF @queueTypeFound = 0 AND @queueType = 'memberImportLoad' BEGIN
					SELECT @itemID = @xmldata.value('(/mc/@u)[1]','int');
					EXEC dbo.queue_MemberUpdate_load @jobID=@itemID;
					SET @queueTypeFound = 1;
				END
				IF @queueTypeFound = 0 BEGIN
					SELECT @itemUID = @xmldata.value('(/mc/@u)[1]','uniqueidentifier');
					IF @queueTypeFound = 0 AND @queueType = 'batchPostLoad' BEGIN
						EXEC dbo.queue_BatchPost_load @itemGroupUID=@itemUID;
						SET @queueTypeFound = 1;
					END
					IF @queueTypeFound = 0 AND @queueType = 'memberDeleteLoad' BEGIN
						EXEC dbo.queue_memberDelete_load @itemGroupUID=@itemUID;
						SET @queueTypeFound = 1;
					END
					IF @queueTypeFound = 0 AND @queueType = 'memberMergeMatchLoad' BEGIN
						EXEC dbo.queue_MemberMergeMatch_load @itemGroupUID=@itemUID;
						SET @queueTypeFound = 1;
					END
					IF @queueTypeFound = 0 AND @queueType = 'importAcctOpt1Load' BEGIN
						EXEC dbo.queue_AcctOption1Import_load @itemGroupUID=@itemUID;
						SET @queueTypeFound = 1;
					END
					IF @queueTypeFound = 0 AND @queueType = 'importAcctOpt2Load' BEGIN
						EXEC dbo.queue_AcctOption2Import_load @itemGroupUID=@itemUID;
						SET @queueTypeFound = 1;
					END
					IF @queueTypeFound = 0 AND @queueType = 'importEventsLoad' BEGIN
						EXEC dbo.queue_EventsImport_load @itemGroupUID=@itemUID;
						SET @queueTypeFound = 1;
					END
					IF @queueTypeFound = 0 AND @queueType = 'importPagesLoad' BEGIN
						EXEC dbo.queue_importPages_load @itemGroupUID=@itemUID;
						SET @queueTypeFound = 1;
					END
					IF @queueTypeFound = 0 AND @queueType = 'importSWODProgramsLoad' BEGIN
						EXEC dbo.queue_importSWODPrograms_load @itemGroupUID=@itemUID;
						SET @queueTypeFound = 1;
					END
					IF @queueTypeFound = 0 AND @queueType = 'addSubscribersLoad' BEGIN
						EXEC dbo.queue_SubscriptionAdd_load @itemGroupUID=@itemUID;
						SET @queueTypeFound = 1;
					END
					IF @queueTypeFound = 0 AND @queueType = 'ARByDayInvProfLoad' BEGIN
						EXEC dbo.queue_ARByDayInvProf_load;
						SET @queueTypeFound = 1;
					END
					IF @queueTypeFound = 0 AND @queueType = 'expireSubsLoad' BEGIN
						EXEC dbo.queue_SubscriptionExpire_load @itemGroupUID=@itemUID;
						SET @queueTypeFound = 1;
					END
					IF @queueTypeFound = 0 AND @queueType = 'subscriptionBilledLoad' BEGIN
						EXEC dbo.queue_SubscriptionBilled_load @itemGroupUID=@itemUID;
						SET @queueTypeFound = 1;
					END
					IF @queueTypeFound = 0 AND @queueType = 'subscriptionForceAddLoad' BEGIN
						EXEC dbo.queue_subscriptionForceAdd_load @itemGroupUID=@itemUID;
						SET @queueTypeFound = 1;
					END
					IF @queueTypeFound = 0 AND @queueType = 'subscriptionActivateLoad' BEGIN
						EXEC dbo.queue_SubscriptionActivate_load @itemGroupUID=@itemUID;
						SET @queueTypeFound = 1;
					END
					IF @queueTypeFound = 0 AND @queueType = 'subscriptionInactivateLoad' BEGIN
						EXEC dbo.queue_SubscriptionInactivate_load @itemGroupUID=@itemUID;
						SET @queueTypeFound = 1;
					END
					IF @queueTypeFound = 0 AND @queueType = 'setSubRecogRangeLoad' BEGIN
						EXEC dbo.queue_SetSubRecogRange_load @itemGroupUID=@itemUID;
						SET @queueTypeFound = 1;
					END
					IF @queueTypeFound = 0 AND @queueType = 'subscriptionDeleteLoad' BEGIN
						EXEC dbo.queue_SubscriptionDelete_load @itemGroupUID=@itemUID;
						SET @queueTypeFound = 1;
					END
					IF @queueTypeFound = 0 AND @queueType = 'contributionsInstallmentsLoad' BEGIN
						EXEC dbo.queue_ContributionsInstallments_load @itemGroupUID=@itemUID;
						SET @queueTypeFound = 1;
					END
					IF @queueTypeFound = 0 AND @queueType = 'contributionsSalesLoad' BEGIN
						EXEC dbo.queue_ContributionsSales_load @itemGroupUID=@itemUID;
						SET @queueTypeFound = 1;
					END
					IF @queueTypeFound = 0 AND @queueType = 'contributionsImportLoad' BEGIN
						EXEC dbo.queue_ContributionsImport_load @itemGroupUID=@itemUID;
						SET @queueTypeFound = 1;
					END
					IF @queueTypeFound = 0 AND @queueType = 'deferredRecognitionLoad' BEGIN
						EXEC dbo.queue_DeferredRecognition_load @itemGroupUID=@itemUID;
						SET @queueTypeFound = 1;
					END
					IF @queueTypeFound = 0 AND @queueType = 'depoDocsToFileShare2Load' BEGIN
						EXEC dbo.queue_DepoDocsToFileShare2_load @itemGroupUID=@itemUID;
						SET @queueTypeFound = 1;
					END
					IF @queueTypeFound = 0 AND @queueType = 'fileShare2toDepoDocsLoad' BEGIN
						EXEC dbo.queue_fileShare2toDepoDocs_load @itemGroupUID=@itemUID;
						SET @queueTypeFound = 1;
					END
					IF @queueTypeFound = 0 AND @queueType = 'eventSearchTextLoad' BEGIN
						EXEC dbo.queue_eventSearchText_load @itemGroupUID=@itemUID;
						SET @queueTypeFound = 1;
					END
					IF @queueTypeFound = 0 AND @queueType = 'lyrisListSyncLoad' BEGIN
						EXEC dbo.queue_lyrisListSync_load @itemGroupUID=@itemUID;
						SET @queueTypeFound = 1;
					END
					IF @queueTypeFound = 0 AND @queueType = 'taskDeleteLoad' BEGIN
						EXEC dbo.queue_taskDelete_load @itemGroupUID=@itemUID;
						SET @queueTypeFound = 1;
					END
					IF @queueTypeFound = 0 AND @queueType = 'taskImportLoad' BEGIN
						EXEC dbo.queue_TaskImport_load @itemGroupUID=@itemUID;
						SET @queueTypeFound = 1;
					END
					IF @queueTypeFound = 0 AND @queueType = 'trackGrpMembershipHistoryLoad' BEGIN
						EXEC dbo.queue_trackGrpMembershipHistory_load @itemGroupUID=@itemUID;
						SET @queueTypeFound = 1;
					END
					IF @queueTypeFound = 0 AND @queueType = 'tsSubscriberDeposLoad' BEGIN
						EXEC dbo.queue_tsSubscriberDepos_load @itemGroupUID=@itemUID;
						SET @queueTypeFound = 1;
					END
				END

				END CONVERSATION @DialogHandle;
			END TRY		
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET @ErrorMessage = N'queue_DataImport_Activated - ' + ERROR_MESSAGE();
				INSERT INTO dbo.sb_ServiceBrokerErrorLogs (ErrorMessage, MessageBody) VALUES(@ErrorMessage, @MessageBody);
				END CONVERSATION @DialogHandle;
			END CATCH
		END
		ELSE BEGIN
			IF @MessageType = N'http://schemas.microsoft.com/SQL/ServiceBroker/EndDialog' BEGIN
				END CONVERSATION @DialogHandle;
			END 
			ELSE BEGIN
				IF @MessageType = N'http://schemas.microsoft.com/SQL/ServiceBroker/Error' BEGIN
					SET @xmldata = cast(@MessageBody as xml);						
					with xmlnamespaces (DEFAULT N'http://schemas.microsoft.com/SQL/ServiceBroker/Error')
					select @ErrorMessage = N'queue_DataImport_Activated - ' + @xmldata.value ('(/Error/Description)[1]', 'NVARCHAR(2048)');
					INSERT INTO dbo.sb_ServiceBrokerErrorLogs (ErrorMessage, MessageBody) VALUES(@ErrorMessage, @MessageBody);
					END CONVERSATION @DialogHandle;
				END
				ELSE BEGIN
					SET @ErrorMessage = N'queue_DataImport_Activated - Unexpected message type received: ' + @MessageType; 
					INSERT INTO dbo.sb_ServiceBrokerErrorLogs (ErrorMessage, MessageBody) VALUES(@ErrorMessage, @MessageBody);
					END CONVERSATION @DialogHandle;
				END
			END
		END

	IF @@trancount > 0	
		COMMIT TRANSACTION;

	IF membercentral.dbo.fn_getCurrentTransactionIsolationLevel() <> 'ReadCommitted'
		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	IF membercentral.dbo.fn_getCurrentTransactionIsolationLevel() <> 'ReadCommitted'
		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=1;
	RETURN -1;
END CATCH

IF membercentral.dbo.fn_getCurrentTransactionIsolationLevel() <> 'ReadCommitted'
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
GO
