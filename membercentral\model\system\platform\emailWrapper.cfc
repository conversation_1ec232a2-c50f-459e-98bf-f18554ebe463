<cfcomponent output="false">
	<cffunction name="sendMailESQ" access="public" output="false" returntype="struct">
		<cfargument name="emailfrom" type="struct" required="yes">
		<cfargument name="emailto" type="array" required="yes">
		<cfargument name="emailreplyto" type="string" required="yes">
		<cfargument name="emailsubject" type="string" required="yes">
		<cfargument name="emailtitle" type="string" required="yes">
		<cfargument name="emailhtmlcontent" type="string" required="yes">
		<cfargument name="emailAttachments" type="array" required="no" default="#arrayNew(1)#">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="memberID" type="numeric" required="yes">
		<cfargument name="messageTypeID" type="numeric" required="yes">
		<cfargument name="sendingSiteResourceID" type="numeric" required="yes">
		<cfargument name="isTestMessage" type="boolean" default="0" required="no">
		<cfargument name="referenceType" type="string" default="" required="no">
		<cfargument name="referenceID" type="numeric" default="0" required="no">
		<cfargument name="doWrapEmail" type="boolean" default="true" required="no">
		<cfargument name="consentListID" type="string" default="" required="no">

		<cfset var local = structNew()>
		<cfset local.strReturn = { success=false, err="" }>

		<cftry>
			<!--- validate the to emails and kick out if no valid ones left. trim the email first. --->
			<cfloop from="#arrayLen(arguments.emailto)#" to="1" index="local.thisEl" step="-1">
				<cfset arguments.emailto[local.thisEl].email = trim(arguments.emailto[local.thisEl].email)>
				<cfif len(arguments.emailto[local.thisEl].email) gt 200 OR trim(arguments.emailto[local.thisEl].email) eq "" OR NOT isValid("regex",arguments.emailto[local.thisEl].email,application.regEx.email)>
					<cfset arguments.emailto.deleteAt(local.thisEl)>
				</cfif>
			</cfloop>
			<cfif NOT arrayLen(arguments.emailto)>
				<cfthrow message="No valid To Email Provided">
			</cfif>

			<!--- sendgrid API requires only one reply-to address. so pick first one in case there is a list --->
			<cfset arguments.emailreplyto = ListFirst(replace(replace(arguments.emailreplyto,";",",","ALL")," ","","ALL"))>

			<cfif arguments.doWrapEmail>
				<cfset local.emailHTMLContent = wrapMessage(emailTitle=arguments.emailtitle, emailContent=arguments.emailhtmlcontent, sitecode=application.objSiteInfo.getSiteCodeFromSiteID(arguments.siteid))>
			<cfelse>
				<cfset local.emailHTMLContent = arguments.emailhtmlcontent>
			</cfif>

			<!--- email sending queue will always override emails when not in production, but this allows us to inject the note at the top of the message --->
			<cfif application.MCEnvironment neq "production">
				<cfset local.sendToAddress = "<EMAIL>">

				<cfset local.tmpPreviousParams = structNew()>
				<cfif arguments.emailto.len() gt 1 OR arguments.emailto[1].email neq local.sendToAddress>
					<cfset local.origEmailTo = arguments.emailto.reduce(
						function(prev,item){ 
							return listAppend(prev,arguments.item.email);
						}, ''
					)>
					<cfset structInsert(local.tmpPreviousParams, "to", local.origEmailTo)>
				</cfif>
				<cfif len(arguments.emailreplyto)>
					<cfset structInsert(local.tmpPreviousParams, "replyto", arguments.emailreplyto)>
				</cfif>

				<cfif structCount(local.tmpPreviousParams)>
					<cfsavecontent variable="local.injectedNote">
						<cfoutput>
						<table width="100%" border="0" cellspacing="0" cellpadding="4" style="border:1px solid ##999;border-collapse:collapse;">
						<tr bgcolor="##DEDEDE">
							<td style="background-color:##fff;font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:10px;">
								<b><span style="color:red;">NOTE:</span></b> The recipients of this email have been modified.<br/>
								<cfif structKeyExists(local.tmpPreviousParams,"to")>
									<b><span style="color:blue;">TO</span></b> changed from <span style="color:blue;">#local.tmpPreviousParams["to"]#</span> to <span style="color:blue;">#local.sendToAddress#</span><br/>
								</cfif>
								<cfif structKeyExists(local.tmpPreviousParams,"replyto")>
									<b><span style="color:blue;">REPLYTO</span></b> changed from <span style="color:blue;">#local.tmpPreviousParams["replyto"]#</span> to <span style="color:blue;">-blank-</span><br/>
								</cfif>
							</td>
						</tr>
						</table>
						<br/>
						</cfoutput>
					</cfsavecontent>
					<cfset local.emailHTMLContent = injectHeader(htmlcontent=local.emailHTMLContent,headercontent=local.injectedNote)>
				</cfif>
				
				<cfset arguments.emailto = [{ name:"AppTesting", email:local.sendToAddress }]>
				<cfset arguments.emailreplyto = "">
			</cfif>

			<cfquery name="local.qryQueueEmail" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @resourceTypeID int, @contentID int, @siteResourceID int, @contentVersionID int, 
						@sendOnDate datetime = getdate(), @messageID int, @orgID int, @emailTagTypeID int, @emailTypeID int, @recipientID int,
						@messageStatusIDQueued int, @attachmentID int, @s3keyMod varchar(4), @objectKey varchar(400), 
						@attachmentFileName varchar(400), @filePathForS3Upload varchar(400), @s3bucketName varchar(100) = 'platformmail-membercentral-com',
						@siteID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">,
						@sendingSiteResourceID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.sendingSiteResourceID#">,
						@contentTitle varchar(100) = <cfqueryparam cfsqltype="cf_sql_varchar" value="#left(arguments.emailtitle,100)#">,
						@messageContent varchar(max) = <cfqueryparam cfsqltype="cf_sql_longvarchar" value="#local.emailHTMLContent#">,
						@memberID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.memberID#">,
						@messageTypeID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.messageTypeID#">,
						@fromName varchar(200) = <cfqueryparam cfsqltype="cf_sql_varchar" value="#left(trim(arguments.emailfrom.name),200)#">,
						@fromEmail varchar(200) = <cfqueryparam cfsqltype="cf_sql_varchar" value="#left(trim(arguments.emailfrom.email),200)#">,
						@replyToEmail varchar(200) = <cfqueryparam cfsqltype="cf_sql_varchar" value="#left(arguments.emailreplyto,200)#">,
						@subject varchar(400) = <cfqueryparam cfsqltype="cf_sql_varchar" value="#left(arguments.emailsubject,400)#">,
						@referenceType varchar(20) = NULLIF(<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.referenceType#">,''),
						@referenceID int = NULLIF(<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.referenceID#">,0),
						@isTestMessage bit = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.isTestMessage#">,
						@s3UploadReadyStatusID int, @nowDate datetime = getdate(),
						@consentListIDs varchar(200) = NULLIF(<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.consentListID#">,'');
					DECLARE @tblRecipients TABLE (recipientID int PRIMARY KEY);

					SET @resourceTypeID = dbo.fn_getResourceTypeID('ApplicationCreatedContent');
					SELECT @orgID = orgID FROM dbo.sites WHERE siteID = @siteID;
					SELECT @emailTagTypeID = emailTagTypeID FROM dbo.ams_memberEmailTagTypes WHERE orgID = @orgID AND emailTagType = 'Primary';
					SELECT @emailTypeID = emailTypeID FROM dbo.ams_memberEmailTags WHERE orgID = @orgID and memberID = @memberID AND emailTagTypeID = @emailTagTypeID;
					SELECT @messageStatusIDQueued = statusID FROM platformMail.dbo.email_statuses WHERE statusCode = 'Q';

					SELECT @s3UploadReadyStatusID = qs.queueStatusID
					FROM platformQueue.dbo.tblQueueTypes as qt
					INNER JOIN platformQueue.dbo.tblQueueStatuses as qs on qs.queueTypeID = qt.queueTypeID
					WHERE qt.queueType = 's3Upload'
					AND qs.queueStatus = 'readyToProcess';

					BEGIN TRAN;
						EXEC dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@resourceTypeID, 
							@parentSiteResourceID=@sendingSiteResourceID, @siteResourceStatusID=1, @isHTML=1, 
							@languageID=1, @isActive=1, @contentTitle=@contentTitle, @contentDesc='', @rawContent=@messageContent, 
							@memberID=@memberID, @contentID=@contentID OUTPUT, @siteResourceID=@siteResourceID OUTPUT;

						SELECT TOP 1 @contentVersionID = cv.contentVersionID
						FROM dbo.cms_content as c 
						INNER JOIN dbo.cms_contentLanguages as cl on c.contentID = cl.contentID and cl.languageID = 1
						INNER JOIN dbo.cms_contentVersions as cv on cv.contentLanguageID = cl.contentLanguageID and cv.isActive = 1
						WHERE c.contentID = @contentID;

						EXEC platformMail.dbo.email_insertMessage @messageTypeID=@messageTypeID, @siteID=@siteID, 
							@sendingSiteResourceID=@sendingSiteResourceID, @isTestMessage=@isTestMessage,
							@sendOnDate=@sendOnDate, @recordedByMemberID=@memberID, @fromName=@fromName, 
							@fromEmail=@fromEmail, @replyToEmail=@replyToEmail, @senderEmail='', 
							@subject=@subject, @contentVersionID=@contentVersionID, @messageWrapper='', 
							@referenceType=@referenceType, @referenceID=@referenceID, @consentListIDs=@consentListIDs, @messageID=@messageID OUTPUT;
						
						-- add recipients as I (not ready to be queued yet)
						<cfloop array="#arguments.emailto#" index="local.thisTo">
							EXEC platformMail.dbo.email_insertMessageRecipientHistory @messageID=@messageID, @memberID=@memberID, 
								@toName=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#left(trim(local.thisTo.name),200)#">,
								@toEmail=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.thisTo.email#">, 
								@emailTypeID=@emailTypeID, @statusCode='I', @siteID=@siteID, @recipientID=@recipientID OUTPUT;
							
							INSERT INTO @tblRecipients (recipientID)
							VALUES (@recipientID);
						</cfloop>

						<cfloop array="#arguments.emailAttachments#" index="local.thisAttachment">
							<cfif FileExists("#local.thisAttachment.folderpath#/#local.thisAttachment.file#")>
								<cfset local.pathFromS3Uploader = replace(replacenocase("#local.thisAttachment.folderpath#/#local.thisAttachment.file#",application.paths.SharedTempNoWeb.path,application.paths.SharedTempNoWeb.pathS3Uploader),'/','\','all')>

								SET @attachmentFileName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.thisAttachment.file#">;
								SET @filePathForS3Upload = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.pathFromS3Uploader#">;

								EXEC platformMail.dbo.email_insertAttachment @fileName=@attachmentFileName, 
									@localDirectory=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.thisAttachment.folderpath#">, 
									@attachmentID=@attachmentID OUTPUT;

								INSERT INTO platformMail.dbo.email_messageRecipientAttachments (recipientID, attachmentID)
								SELECT recipientID, @attachmentID
								FROM @tblRecipients;

								-- insert to s3 upload queue
								SET @s3keyMod = FORMAT(@attachmentID % 1000, '0000');
								SET @objectKey = LOWER('#application.MCEnvironment#/outgoing/' + @s3keyMod + '/' + cast(@attachmentID as varchar(10)) + '/' + @attachmentFileName);

								-- set deleteOnSuccess=0 because we may need to send the same actual attachment in multiple messages. It can be cleaned up by the directory cleanup scripts.
								IF NOT EXISTS (select 1 from platformQueue.dbo.queue_S3Upload where s3bucketName = @s3bucketName and objectKey = @objectKey)
									INSERT INTO platformQueue.dbo.queue_S3Upload (statusID, s3bucketName, objectKey, filePath, deleteOnSuccess, dateAdded, dateUpdated)
									VALUES (@s3UploadReadyStatusID, @s3bucketName, @objectKey, @filePathForS3Upload, 0, @nowDate, @nowDate);
							</cfif>
						</cfloop>

						-- mark recipients as queued
						UPDATE mrh
						SET mrh.emailStatusID = @messageStatusIDQueued
						FROM @tblRecipients as tmp
						INNER JOIN platformMail.dbo.email_messageRecipientHistory as mrh on mrh.recipientID = tmp.recipientID;
					COMMIT TRAN;

					-- return the messageID and recipientIDs
					SELECT recipientID, @messageID as messageID
					FROM @tblRecipients;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.strReturn.success = true>
			<cfset local.strReturn.messageID = local.qryQueueEmail.messageID>
			<cfset local.strReturn.arrRecipientID = QueryColumnData(query=local.qryQueueEmail, columnName="recipientID")>
			
		<cfcatch type="Any">
			<cfset local.strReturn.success = false>
			<cfset local.strReturn.err = cfcatch.message & " " & cfcatch.detail>
		</cfcatch>
		</cftry>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="getBaseTemplateHTML" access="public" output="false" returntype="string">
		<cfargument name="sitecode" type="string" required="yes">
		<cfargument name="emailTitle" type="string" required="no" default="">

		<cfsavecontent variable="local.template">
			<cfoutput>
			<!DOCTYPE html>
			<html>
			<head>
				<cfif len(arguments.emailTitle)>
					<title>#arguments.emailTitle#</title>
				</cfif>
				<meta http-equiv="Content-Type" content="text/html; charset=utf8" />
				#application.objSiteInfo.getSiteInfo(arguments.siteCode).customHeadContent#
			</head>
			<body>
				<!--BodyContent-->
			</body>
			</html>
			</cfoutput>		
		</cfsavecontent>

		<cfreturn local.template>
	</cffunction>

	<cffunction name="wrapMessage" access="public" output="false" returntype="string">
		<cfargument name="emailTitle" type="string" required="yes">
		<cfargument name="emailContent" type="string" required="yes">
		<cfargument name="sitecode" type="string" required="yes">
		<cfargument name="emailFooter" type="string" required="no" default="">

		<cfset var local = structNew()>

		<cfset local.basetemplate = getBaseTemplateHTML(sitecode=arguments.sitecode, emailTitle=arguments.emailTitle)>

		<cfsavecontent variable="local.emailMessageContent">
			<cfoutput>
			<table cellspacing="0" cellpadding="0" width="100%" border="0">
			<tr>
				<td style="background-color:##eee;padding:10px 20px 20px 20px;">
					<table style="background-color:##eee;" cellspacing="0" cellpadding="0" width="100%" border="0">
					<tr>
						<td style="font:bold 18px Verdana,Helvetica,Arial,sans-serif;color:##069;padding-left:20px;padding-bottom:8px;">
							#arguments.emailTitle#
						</td>
					</tr>
					<tr>
						<td style="background-color:##fff;font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:20px;">
							#arguments.emailContent#
						</td>
					</tr>
					<cfif len(arguments.emailFooter)>
						<tr>
							<td style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;padding-top: 20px;text-align:center">
								#arguments.emailFooter#
							</td>
						</tr>
					</cfif>
					</table>
				</td>
			</tr>
			</table>
			</cfoutput>		
		</cfsavecontent>
		<cfset local.emailMessage = replacenocase(local.basetemplate,"<!--BodyContent-->",local.emailMessageContent,"all")>
		<cfreturn local.emailMessage>
	</cffunction>

	<cffunction name="isCompleteHTMLDocument" access="public" returntype="boolean" output="false">
		<cfargument name="htmlcontent" type="string" required="true">
		<cfscript>
			var local = {};
			var contentToTest = arguments.htmlcontent;
			local.isCompleteHTMLDocument = true;
			local.regexesToTest = {
				doctype: "<!DOCTYPE[^>]*>",
				htmltag: "<\s*html\b[^>]*>",
				headtag: "<\s*head\b[^>]*>",
				bodytag: "<\s*body\b[^>]*>",
				htmlclosetag: "</html\b[^>]*>",
				headclosetag: "(</head\b[^>]*>)",
				bodyclosetag: "</body\b[^>]*>",
				bodyContent : "(<body\b[^>]*>)([\w\W]*)(<\/body>)"
			};

			local.textBetweenCloseHeadAndOpenBody = "(?:</head\b[^>]*>)([\W\w]+)(?:<\s*body\b[^>]*>)";
			local.scripttag = "<script\b[^>]*>[\w\W]*?<\/script>";


			local.passedTests = local.regexesToTest.filter(function(key, value) {
				// return true if the regex  matches
				return refindnocase(value,contentToTest,0);
			});

			// did all regexes match?
			local.isCompleteHTMLDocument = (structCount(local.regexesToTest) eq structCount(local.passedTests));

			// if regexes all passed, do other checks

			// make sure no script tags are found
			local.isCompleteHTMLDocument = (local.isCompleteHTMLDocument and not refindnocase(local.scripttag,contentToTest));

			if (local.isCompleteHTMLDocument and refindnocase(local.textBetweenCloseHeadAndOpenBody,contentToTest,0)) {
				// text found between close head and open body tag

				local.textBetweenCloseHeadAndOpenBodyMatches = refindnocase(local.textBetweenCloseHeadAndOpenBody,contentToTest,0,true);
				local.textBetweenCloseHeadAndOpenBodyMatchText = trim(mid(contentToTest, local.textBetweenCloseHeadAndOpenBodyMatches.pos[2], local.textBetweenCloseHeadAndOpenBodyMatches.len[2]));

				// pass if text between close head and open body tag was just whitespace
				local.isCompleteHTMLDocument = (len(local.textBetweenCloseHeadAndOpenBodyMatchText) eq 0);
			}


			if (local.isCompleteHTMLDocument and refindnocase(local.textBetweenCloseHeadAndOpenBody,contentToTest,0)) {
				// text found between close head and open body tag

				local.textBetweenCloseHeadAndOpenBodyMatches = refindnocase(local.textBetweenCloseHeadAndOpenBody,contentToTest,0,true);
				local.textBetweenCloseHeadAndOpenBodyMatchText = trim(mid(contentToTest, local.textBetweenCloseHeadAndOpenBodyMatches.pos[2], local.textBetweenCloseHeadAndOpenBodyMatches.len[2]));

				// pass if text between close head and open body tag was just whitespace
				local.isCompleteHTMLDocument = (len(local.textBetweenCloseHeadAndOpenBodyMatchText) eq 0);
			}
			
			return local.isCompleteHTMLDocument;
		</cfscript>
	</cffunction>

	<cffunction name="cleanupHTML" access="public" returntype="string" output="false">
		<cfargument name="htmlcontent" type="string" required="true">
		<cfargument name="htmlheadtext" type="string" required="false" default="">

		<cfscript>
			var local = {};
			local.regexes = {
				doctype: "<!DOCTYPE[^>]*>",
				htmltag: "<\s*html\b[^>]*>",
				headtag: "<\s*head\b[^>]*>",
				bodytag: "<\s*body\b[^>]*>",
				htmlclosetag: "</html\b[^>]*>",
				headclosetag: "(</head\b[^>]*>)",
				bodyclosetag: "</body\b[^>]*>",
				bodyContent : "(<body\b[^>]*>)([\w\W]*)(<\/body>)",
				titleTag: "<title\b[^>]*>[\w\W]*<\/title>",
				metatag: "<meta\b[^>]*>",
				styleBlock: "<style\s+[^>]*>[\w\W]*?<\/style>",
				textBetweenCloseHeadAndOpenBody: "(?:</head\b[^>]*>)([\W\w]+)(?:<\s*body\b[^>]*>)",
				consecutiveHeadCloseTags: "(</head\b[^>]*>\s*){2,}",
				scripttag: "<script\b[^>]*>[\w\W]*?<\/script>"
			};
			try {
				local.messageContent = arguments.htmlcontent;

				//remove any script tags
				local.messageContent = local.messageContent.rereplacenocase(local.regexes.scripttag,"","all");

				local.hasDocType = refindnocase(local.regexes.doctype,local.messageContent,0);
				local.hasHTMLTag = refindnocase(local.regexes.htmltag,local.messageContent,0);
				local.hasHeadTag = refindnocase(local.regexes.headtag,local.messageContent,0);
				local.hasBodyTag = refindnocase(local.regexes.bodytag,local.messageContent,0);
				local.hasHTMLCloseTag = refindnocase(local.regexes.htmlclosetag,local.messageContent,0);
				local.hasHeadCloseTag = refindnocase(local.regexes.headclosetag,local.messageContent,0);
				local.hasBodyCloseTag = refindnocase(local.regexes.bodyclosetag,local.messageContent,0);


				if (not local.hasDocType) {
					local.messageContent = "<!DOCTYPE html>#local.messageContent#";
					local.hasDocType=1;
				}

				if (not local.hasHTMLtag) {
					//put open html after doctype
					local.doctypeTag = refindnocase(local.regexes.doctype,local.messageContent,0,"true")
					if (local.doctypeTag.pos[1])
						local.messageContent = insert("<html>",local.messageContent,local.doctypeTag.pos[1]+local.doctypeTag.len[1]-1);
					local.hasHTMLtag = 1;
					//put close html at end
					local.messageContent = local.messageContent & "</html>";
					local.hasHTMLtag = 1;
				}

				if (not local.hasHTMLCloseTag) {
					//put close html at end
					local.messageContent = local.messageContent & "</html>";
					local.hasHTMLCloseTag = 1;
				}

				if (not local.hasHeadTag) {
					//put site default head after html tag
					local.htmlTag = refindnocase(local.regexes.htmltag,local.messageContent,0,"true")
					if (local.htmlTag.pos[1]) {
						local.headHTML = '<head></head>';
						local.messageContent = insert(local.headHTML,local.messageContent,local.htmlTag.pos[1]+local.htmlTag.len[1]-1);
						local.hasHeadTag = 1;
					}
				}

				if (refindnocase(local.regexes.textBetweenCloseHeadAndOpenBody,local.messageContent,0)) {
					local.textBetweenCloseHeadAndOpenBodyMatches = refindnocase(local.regexes.textBetweenCloseHeadAndOpenBody,local.messageContent,0,true);
					local.textBetweenCloseHeadAndOpenBodyMatchText = mid(local.messageContent, local.textBetweenCloseHeadAndOpenBodyMatches.pos[2], local.textBetweenCloseHeadAndOpenBodyMatches.len[2]);
					local.messageContent = local.messageContent.replace(local.textBetweenCloseHeadAndOpenBodyMatchText,'',"one");
					local.messageContent=local.messageContent.rereplacenocase(local.regexes.headclosetag,"#local.textBetweenCloseHeadAndOpenBodyMatchText#\1");
					local.messageContent=local.messageContent.rereplacenocase(local.regexes.consecutiveHeadCloseTags,"</head>");
				}

				if (not local.hasHeadCloseTag and local.hasBodyTag) {
					//put head close tag right before body
					local.bodytag = refindnocase(local.regexes.bodytag,local.messageContent,0,"true")
					if (local.bodytag.pos[1]) {
						local.headHTML = '</head>';
						local.messageContent = insert(local.headHTML,local.messageContent,local.bodytag.pos[1]-1);
						local.hasHeadCloseTag = 1;
					}
				}

				if (not local.hasBodyTag) {
					// put body tag after closing head tag
					local.htmlHeadEndTag = refindnocase(local.regexes.headclosetag,local.messageContent,0,"true")
					if (local.htmlHeadEndTag.pos[1]) {
						local.messageContent = insert("<body>",local.messageContent,local.htmlHeadEndTag.pos[1]+local.htmlHeadEndTag.len[1]-1);
						local.hasBodyTag=1;
					}
				}
				if (not local.hasBodyCloseTag) {
					local.htmlEndTag = refindnocase(local.regexes.htmlclosetag,local.messageContent,0,"true")
					if (local.htmlEndTag.pos[1]) {
						local.messageContent = insert("</body>",local.messageContent,local.htmlEndTag.pos[1]-1);
						local.hasBodyCloseTag = 1
					}

				}

				// move things from body into head

				local.bodyContentMatches = refindnocase(local.regexes.bodyContent,local.messageContent,0,true);
				var bodyContent=mid(local.messageContent,local.bodyContentMatches.pos[3],local.bodyContentMatches.len[3]) ;
				var addToHead = "";

				//move any title tags in the body to the head
				local.hasTitleTag = refindnocase(local.regexes.titleTag,bodyContent,0);
				if (local.hasTitleTag) {
					local.titleMatches = reMatchNoCase(local.regexes.titleTag,bodyContent);
					local.titleMatches.each(function(thisMatch, index) {
						addToHead = addToHead & thisMatch;
						bodyContent = bodyContent.replace(thisMatch,'',"one");
					});
				}
				//move any meta tags in the body to head
				local.hasMetaTag = refindnocase(local.regexes.metatag,bodyContent,0);
				if (local.hasMetaTag) {
					local.metaMatches = reMatchNoCase(local.regexes.metatag,bodyContent);
					local.metaMatches.each(function(thisMatch, index) {
						addToHead = addToHead & thisMatch;
						bodyContent = bodyContent.replace(thisMatch,'',"one");
					});
				}
				//move any style tags in the body to head in the same order they are defined
				local.hasStyleTag = refindnocase(local.regexes.styleBlock,bodyContent,0);
				if (local.hasStyleTag) {
					local.styleMatches = reMatchNoCase(local.regexes.styleBlock,bodyContent);
					local.styleMatches.each(function(thisMatch, index) {
						addToHead = addToHead & thisMatch;
						bodyContent = bodyContent.replace(thisMatch,'',"one");
					});
				}


				//inject anything found into the end of the head
				if (len(addToHead)) {
					local.messageContent=local.messageContent.rereplacenocase(local.regexes.headclosetag,"#local.addToHead#\1");

					//replace body with modified body
					local.messageContent = local.messageContent.rereplacenocase(local.regexes.bodyContent,"\1#bodyContent#\3");
				}
			} catch(e) {
				application.objError.sendError(cfcatch=cfcatch, objectToDump=local);
			}
			return local.messageContent;
		</cfscript>
	</cffunction>

	<cffunction name="disableClickTrackingForSpecificLinks" access="public" returntype="string" output="false">
		<cfargument name="htmlcontent" type="string" required="true">

		<cfscript>
			var local = {};
			local.regexes = { 
				linkToAnchorTags: "(<a)(?:\s+)(?!\btracking=""off"")([^>]*\b(href\s*=\s*[""']##))"
			};
			try {
				local.messageContent = arguments.htmlcontent;
				local.messageContent = local.messageContent.rereplacenocase(local.regexes.linkToAnchorTags,"\1 tracking=""off"" \2","all");

			} catch(e) {
				application.objError.sendError(cfcatch=cfcatch, objectToDump=local);
			}
			return local.messageContent;
		</cfscript>
	</cffunction>

	<cffunction name="removeMCControlledContent" access="public" returntype="string" output="false">
		<cfargument name="htmlcontent" type="string" required="true">
		<cfscript>
			var local = {};
			local.controlledContentRegEx = "<!--MCControlledDoNotDeleteStart-->[\w\W]*?<!--MCControlledDoNotDeleteEnd-->";
			return arguments.htmlcontent.rereplacenocase(local.controlledContentRegEx,"");
		</cfscript>
	</cffunction>
	<cffunction name="injectPreviewText" access="public" returntype="string" output="false">
		<cfargument name="htmlcontent" type="string" required="true">
		<cfargument name="preheadertext" type="string" required="true">

		<cfset var local = {}>
		<cfset local.messageContent = arguments.htmlcontent>
		<cfif len(trim(arguments.preheadertext))>
			<cfsavecontent variable="local.preHeaderHTML">
				<cfoutput>
<!--- Preheader Template from Email On Acid --->
<!--MCControlledDoNotDeleteStart-->
<span style="display:none !important;visibility:hidden;mso-hide:all;font-size:1px;line-height:1px;max-height:0px;max-width:0px;opacity:0;overflow:hidden;">
#arguments.preheadertext#
</span>
<span style="display:none !important;visibility:hidden;mso-hide:all;font-size:1px;line-height:1px;max-height:0px;max-width:0px;opacity:0;overflow:hidden;">
<!--EOA COMMENT: This snippet of white space has been added to ensure short preview text does not run into the following text of your email.-->
&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;&nbsp;&zwnj;
</span>
<!--MCControlledDoNotDeleteEnd-->
				</cfoutput>
			</cfsavecontent>
			<cfset local.messageContent = injectHeader(htmlcontent=arguments.htmlcontent,headercontent=local.preHeaderHTML)>
		</cfif>
		<cfreturn local.messageContent>
	</cffunction>
	<cffunction name="injectHeader" access="public" returntype="string" output="false">
		<cfargument name="htmlcontent" type="string" required="true">
		<cfargument name="headercontent" type="string" required="true">
		<cfscript>
			var local = {}
			local.messageContent = arguments.htmlcontent;
			if (len(trim(arguments.headercontent))) {
				// inject header immediately after opening body tag
				local.messageContent = local.messageContent.rereplacenocase("(<body\b[^>]*>)","\1#arguments.headercontent#");
			}
			return local.messageContent;
		</cfscript>
	</cffunction>
	<cffunction name="injectFooter" access="public" returntype="string" output="false">
		<cfargument name="htmlcontent" type="string" required="true">
		<cfargument name="footercontent" type="string" required="true">
		<cfscript>
			var local = {}
			local.messageContent = arguments.htmlcontent;
			if (len(trim(arguments.footercontent))) {
				// inject preheader text immediately before closing body tag
				local.messageContent = local.messageContent.rereplacenocase("(</body\b[^>]*>)","#arguments.footercontent#\1");
			}
			return local.messageContent;
		</cfscript>
	</cffunction>
	<cffunction name="appendParamsToLinks" access="public" returntype="string" output="false">
		<cfargument name="htmlcontent" type="string" required="true">
		<cfargument name="stringToAppendToLinks" type="string" required="true">
		<cfscript>
			var local = {}
			local.messageContent = arguments.htmlcontent;

			local.regexLinkNoQueryString = "(<a[^>]*\bhref\s*=\s*[""']https?://)([^""'##?]*)((?:##[^""']*)?[""'])";
			local.regexLinkWithQueryString = "(<a[^>]*\bhref\s*=\s*[""']https?://)([^""']*\?[^""'##]*)((?:##[^""']*)?[""'])";

			//update links WITH existing querystrings first
			local.messageContent = local.messageContent.rereplacenocase(local.regexLinkWithQueryString,"\1\2&#arguments.stringToAppendToLinks#\3","all");

			//now, update links that have no querystring
			local.messageContent = local.messageContent.rereplacenocase(local.regexLinkNoQueryString,"\1\2?#arguments.stringToAppendToLinks#\3","all");

			return local.messageContent;
		</cfscript>
	</cffunction>
	<cffunction name="appendUTMCodesToLinks" access="public" returntype="string" output="false" hint="Adds passed in UTM Codes to links that don't already have a utm_campaign tag">
		<cfargument name="htmlcontent" type="string" required="true">
		<cfargument name="utm_campaign" type="string" required="true">
		<cfargument name="utm_source" type="string" required="true">
		<cfargument name="utm_medium" type="string" required="false" default="">
		<cfargument name="utm_term" type="string" required="false" default="">
		<cfargument name="utm_content" type="string" required="false" default="">
		<cfscript>
			var local = {}
			local.messageContent = arguments.htmlcontent;

			local.regexLinkNoQueryString = "(<a[^>]*\bhref\s*=\s*[""']https?://(?:[^""'/?]*))/?([^""'##?]*)((?:##[^""']*)?[""'])";
			local.regexLinkWithQueryStringWithoutCampaignSourceOrMedium = "(<a[^>]*\bhref\s*=\s*[""']https?://(?:[^""'/?]*))/?([^""']*\?(?:(?!\butm_(?:campaign|source|medium)=)(?:[^\""']|##(?=(?:\d+;|x[\da-f]+;))))*?)((?:##(?!(?:\d+;|x[\da-f]+;))[^""']*)?[""'])";

			if (len(arguments.utm_campaign)) {
				//build UTM String
				local.utmstring = "utm_campaign=#application.objCommon.slugify(arguments.utm_campaign)#";
				if (len(arguments.utm_source)) local.utmstring = local.utmstring & "&utm_source=#application.objCommon.slugify(arguments.utm_source)#";
				if (len(arguments.utm_medium)) local.utmstring = local.utmstring & "&utm_medium=#application.objCommon.slugify(arguments.utm_medium)#";
				if (len(arguments.utm_term)) local.utmstring = local.utmstring & "&utm_term=#application.objCommon.slugify(arguments.utm_term)#";
				if (len(arguments.utm_content)) local.utmstring = local.utmstring & "&utm_content=#application.objCommon.slugify(arguments.utm_content)#";
				
				//update links WITH existing querystrings first (excludes links that already have a utm_campaign param)
				local.messageContent = local.messageContent.rereplacenocase(local.regexLinkWithQueryStringWithoutCampaignSourceOrMedium,"\1/\2&#local.utmstring#\3","all")

				//now, update links that have no querystring
				local.messageContent = local.messageContent.rereplacenocase(local.regexLinkNoQueryString,"\1/\2?#local.utmstring#\3","all");
			}

			return local.messageContent;
		</cfscript>
	</cffunction>
	<cffunction name="getAttachmentsListAsAttachment" access="public" output="false" returntype="struct">
		<cfargument name="sitecode" type="string" required="yes">
		<cfargument name="arrAttachments" type="array" required="true">
		<cfargument name="fileNameWithNoExt" type="string" required="true">
		
		<cfset var local = structNew()>
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.sitecode)>
		<cfset local.strAttachment = {
			"folderPath": local.strFolder.folderPath,
			"fileName": "#arguments.fileNameWithNoExt#.html",
			"filePath": "",
			"displayName": ""
		}>

		<cfif arrayLen(arguments.arrAttachments)>
			<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(arguments.siteCode)>
			<cfsavecontent variable="local.messageContent">
				<cfoutput>
				<!DOCTYPE HTML>
				<html>
				<head>
					<style>
						body {font-family: Calibri,Candara,Segoe,Segoe UI,Optima,Arial,sans-serif;}
						.attachmentList {
							margin-top:20px;
							border-radius: 15px;
							border: 2px solid ##73AD21;
						}
						.attachment {
							margin:0px;
							padding: 10px;
							text-align: center;
							font-size: 4vw;
							line-height: 1.6;
						}
						.docTitle {
							text-align:center;
							margin-bottom:5px;
							font-size:6vw;
							line-height:1.6;
							font-weight:bold;
						}
						@media (min-width: 700px) {
							.attachment {font-size:3vw;}
							.docTitle {font-size:4vw;}
						}
						@media (min-width: 1000px) {
							.attachment {font-size:2vw;}
							.docTitle {font-size:3vw;}
						}
					</style>
					<title>Attachments Included In Message</title>
				</head>
				<body>
					<div class="docTitle">Attachments Included In Message</div>
					<div class="attachmentList">
						<cfloop array="#arguments.arrAttachments#" index="local.thisAttachment">
							<div class="attachment">
								<a href="#local.mc_siteinfo.scheme#://#local.mc_siteinfo.mainhostname#/docDownload/#local.thisAttachment.documentID#">#local.thisAttachment.fileName# &nbsp;(Click to Download)</a>
							</div>
						</cfloop>
					</div>
				</body>
				</html>
				</cfoutput>
			</cfsavecontent>

			<cffile action="write" file="#local.strAttachment.folderPath#/#local.strAttachment.fileName#" output="#application.objcommon.minText(local.messageContent)#">
			<cfset local.strAttachment.filePath = "#local.strAttachment.folderPath#/#local.strAttachment.fileName#">
			<cfset local.strAttachment.displayName = local.strAttachment.fileName>
		</cfif>

		<cfreturn local.strAttachment>
	</cffunction>
	<cffunction name="OnMissingMethod" access="public" returntype="any" output="false" hint="Handles missing method exceptions.">
		<cfargument name="MissingMethodName" type="string" required="true" hint="The name of the missing method." />
		<cfargument name="MissingMethodArguments" type="struct" required="true" hint="The arguments that were passed to the missing method. This might be a named argument set or a numerically indexed set."/>

		<cfset var freshObj = "">
		<cfset var freshFunction = "">
		<cflock name="emailWrapperOnMissingMethod" timeout="3">
			<cfscript>
			//race condition: make sure another request didn't already inject function
			if (structKeyExists(this, arguments.MissingMethodName)) {
				freshFunction = this[arguments.MissingMethodName];
				return freshFunction(argumentCollection=arguments.MissingMethodArguments);
			} else {
				//Test if new function exists in cfc, but application scope hasn't been refreshed yet
				freshObj = createObject("component","emailWrapper");
				if (structKeyExists(freshObj, arguments.MissingMethodName)) {
					freshFunction = freshObj[arguments.MissingMethodName];
					structInsert(this,arguments.MissingMethodName,freshFunction);
					return freshFunction(argumentCollection=arguments.MissingMethodArguments);
				} else {
					throw(type="Missing Method", message="Missing method '#ARGUMENTS.missingMethodName#()'. Also checked disk to see if function exists in updated source file");	
				}
			}
			</cfscript>
		</cflock>
	</cffunction>
</cfcomponent>