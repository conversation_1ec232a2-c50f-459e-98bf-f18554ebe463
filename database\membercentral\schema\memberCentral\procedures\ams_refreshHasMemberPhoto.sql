ALTER PROC dbo.ams_refreshHasMemberPhoto
@orgID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpPhotos') IS NOT NULL 
		DROP TABLE #tmpPhotos;
	IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL 
		DROP TABLE #tblMCQRun;
	CREATE TABLE #tblMCQRun (orgID int, memberID int INDEX IX_tblMCQRun_memberID, conditionID int);
	CREATE TABLE #tmpPhotos (memberphoto varchar(200) PRIMARY KEY, depth tinyint, isFile bit INDEX IX_tmpPhotos_isFile, 
		memberID int null, membernumber varchar(50) null, hasMemberPhoto bit null);

	declare @userAssetsPath varchar(200), @PhotoPath varchar(240), @deletePhotos varchar(max),
		@errorSubject varchar(300), @errmsg varchar(max), @memberList varchar(max), @orgCode varchar(10), 
		@finalMsg varchar(max) = '', @queueTypeID int, @queueStatusIDReady int;
	declare @tblShouldBeYes TABLE (memberID int PRIMARY KEY, membernumber varchar(50));
	declare @tblShouldBeNo TABLE (memberID int PRIMARY KEY, membernumber varchar(50));

	select @userAssetsPath = userAssetsPath 
	from dbo.fn_getServerSettings();

	select @orgCode = orgcode, @PhotoPath = @userAssetsPath + LOWER(orgcode) + '\memberphotos\'
	from dbo.organizations
	where orgID = @orgID;

	-- get all photos on disk
	INSERT INTO #tmpPhotos (memberphoto, depth, isFile)
	EXEC xp_dirtree @PhotoPath, 1, 1;

	-- delete any folders (merged)
	DELETE FROM #tmpPhotos where isFile = 0;

	-- try to get membernumber from filename
	UPDATE #tmpPhotos
	set membernumber = left(left(memberphoto,len(memberphoto)-4),50)
	where right(memberphoto,4) = '.jpg';

	-- join against members
	UPDATE tmp
	SET tmp.memberID = m.memberID, 
		tmp.hasMemberPhoto = m.hasMemberPhoto
	FROM #tmpPhotos as tmp
	left outer join dbo.ams_members as m on m.membernumber = tmp.membernumber
		and m.orgID = @orgID
		and m.[status] in ('A','I')
	where tmp.membernumber is not null;

	-- these memberphotos are invalid and need to be deleted from disk
	-- send an email to developer support to delete them
	select @deletePhotos = COALESCE(@deletePhotos + '<br/>','') + @PhotoPath + memberphoto 
	from #tmpPhotos
	where memberID is null;

	IF @deletePhotos is not null
		SET @finalMsg = @finalMsg + 'Orphaned memberphotos to delete from disk.<br/>These memberphotos are orphaned or otherwise invalid and need to be deleted from disk. Also delete the thumbnails.<br/><br/>' + @deletePhotos + '<br/><br/>';

	-- these are the memberphotos that exist but member is set to 0
	-- set the flag to yes and reprocess conditions
	INSERT INTO @tblShouldBeYes (memberID, membernumber)
	select memberID, membernumber 
	from #tmpPhotos 
	where hasMemberPhoto = 0;

	IF @@ROWCOUNT > 0 begin
		set @memberList = null;
		select @memberList = COALESCE(@memberList + '<br/>','') + membernumber from @tblShouldBeYes;
		SET @finalMsg = @finalMsg + 'HasMemberPhoto was set to 0 but has photo on disk.<br/>These members were set to hasMemberPhoto = 0 even though photos were on disk. Need to investigate why these were not 1 or why the photo was not deleted. They were changed to 1:<br/><br/>' + isnull(@memberList,'');
			
		UPDATE m
		SET m.hasMemberPhoto = 1,
			m.dateLastUpdated = getdate()
		FROM dbo.ams_members as m
		INNER JOIN @tblShouldBeYes as tmp on tmp.memberID = m.memberID;

		update tmp
		set tmp.hasMemberPhoto = 1
		from #tmpPhotos as tmp
		INNER JOIN @tblShouldBeYes as tmpY on tmpY.memberID = tmp.memberID;

		-- add to thumb queue
		select @queueTypeID = queueTypeID from platformQueue.dbo.tblQueueTypes where queueType = 'memberPhotoThumb';
		select @queueStatusIDReady = queueStatusID from platformQueue.dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'readyToProcess';
		
		INSERT INTO platformQueue.dbo.queue_memberPhotoThumb (orgcode, memberid, membernumber, width, height, dateAdded, dateUpdated, statusID)
		select o.orgcode, m.memberID, lower(m.memberNumber), o.memberPhotoWidth, o.memberPhotoHeight, getdate(), getdate(), @queueStatusIDReady
		from dbo.ams_members as m 
		inner join dbo.organizations as o on o.orgID = m.orgID 
		INNER JOIN @tblShouldBeYes as tmp on tmp.memberID = m.memberID;

		-- resume task
		EXEC dbo.sched_resumeTask @name='Member Photo Thumbnails Queue', @engine='BERLinux';

		-- process any groups
		INSERT INTO #tblMCQRun (orgID, memberID, conditionID)
		SELECT distinct @orgID, tmp.memberID, c.conditionID
		from dbo.ams_virtualGroupConditions as c
		cross join @tblShouldBeYes as tmp
		where c.orgID = @orgID
		and c.fieldCode = 'm_hasMemberPhoto';

		EXEC platformQueue.dbo.queue_triggerMCGCache @runImmediately=0, @type='ConditionsAndGroupsChanged';

		TRUNCATE TABLE #tblMCQRun;
	end

	-- these are the members set to 1 with no photo
	-- set the flag to no and reprocess conditions
	INSERT INTO @tblShouldBeNo (memberID, membernumber)
	select m.memberID, m.membernumber
	from dbo.ams_members as m
	where m.orgID = @orgID
	and m.hasMemberPhoto = 1
	and m.[status] in ('A','I')
	and NOT EXISTS (select membernumber from #tmpPhotos where membernumber = m.membernumber);

	IF @@ROWCOUNT > 0 begin
		set @memberList = null;
		select @memberList = COALESCE(@memberList + '<br/>','') + membernumber from @tblShouldBeNo;
		SET @finalMsg = @finalMsg + 'HasMemberPhoto was set to 1 but has no photo on disk.<br/>These members were set to hasMemberPhoto = 1 even though photos were not on disk. Need to investigate why these were not 0. They were changed to 0:<br/><br/>' + isnull(@memberList,'');

		UPDATE m
		SET m.hasMemberPhoto = 0,
			m.hasMemberPhotoThumb = 0,
			m.dateLastUpdated = getdate()
		FROM dbo.ams_members as m
		INNER JOIN @tblShouldBeNo as tmp on tmp.memberID = m.memberID;

		-- process any groups
		INSERT INTO #tblMCQRun (orgID, memberID, conditionID)
		SELECT distinct @orgID, tmp.memberID, c.conditionID
		from dbo.ams_virtualGroupConditions as c
		cross join @tblShouldBeNo as tmp
		where c.orgID = @orgID
		and c.fieldCode = 'm_hasMemberPhoto';

		EXEC platformQueue.dbo.queue_triggerMCGCache @runImmediately=0, @type='ConditionsAndGroupsChanged';

		TRUNCATE TABLE #tblMCQRun;
	end

	-- email support about it
	if len(@finalMsg) > 0 begin
		SET @errorSubject = 'Issues with HasMemberPhoto on ' + @orgCode;
		EXEC platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorSubject, @messageContent=@finalMSG, @forDev=1;
	end
	
	IF OBJECT_ID('tempdb..#tmpPhotos') IS NOT NULL 
		DROP TABLE #tmpPhotos;
	IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL 
		DROP TABLE #tblMCQRun;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
