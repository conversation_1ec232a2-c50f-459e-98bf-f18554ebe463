ALTER PROC dbo.queue_TaskImport_grabForNotification
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @statusReady int, @statusGrabbed int, @queueTypeID int;

	select @queueTypeID = queueTypeID from dbo.tblQueueTypes where queueType = 'TaskImport';
	select @statusReady = queueStatusID from dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'readyToNotify';
	select @statusGrabbed = queueStatusID from dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'grabbedForNotifying';

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	IF OBJECT_ID('tempdb..#tmpNotifyTasks') IS NOT NULL
		DROP TABLE #tmpNotifyTasks;
	CREATE TABLE #tmpNotify (itemGroupUID uniqueidentifier);
	CREATE TABLE #tmpNotifyTasks (itemGroupUID uniqueidentifier, itemUID uniqueidentifier, siteID int, recordedByMemberID int, project varchar(200), prospectName varchar(250));

	-- dequeue. 
	; WITH itemGroupUIDs AS (
		select distinct qid.itemGroupUID
		from dbo.tblQueueItems as qi
		inner join dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
		where qi.queueStatusID = @statusReady
			except
		select distinct qid.itemGroupUID
		from dbo.tblQueueItems as qi
		inner join dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
		where qi.queueStatusID <> @statusReady
	)
	UPDATE dbo.tblQueueItems WITH (UPDLOCK, READPAST)
	SET queueStatusID = @statusGrabbed,
		dateUpdated = getdate()
		OUTPUT qid.itemGroupUID
		INTO #tmpNotify
	FROM dbo.tblQueueItems as qi
	inner join dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
	INNER JOIN itemGroupUIDs on itemGroupUIDs.itemGroupUID = qid.itemGroupUID
	where qi.queueStatusID = @statusReady;

	-- if none found, no need to continue. return empty query.
	IF @@ROWCOUNT = 0 BEGIN
		select * from #tmpNotifyTasks;
		GOTO on_done;
	END

	-- events in groupUID
	insert into #tmpNotifyTasks (itemGroupUID, itemUID, siteID, recordedByMemberID, project, prospectName)
	select itemGroupUID, itemUID, siteID, recordedByMemberID, projectContent.contentTitle, 
		mActive.firstName + ' ' + mActive.lastName + ' (' + mActive.memberNumber + ')'
	from (
		select qid.itemGroupUID, qid.itemUID, qid.siteID, qid.recordedByMemberID, dc.columnname, qid.columnValueInteger
		from (select distinct itemGroupUID from #tmpNotify) as tmpN
		inner join dbo.tblQueueItemData as qid on qid.itemGroupUID = tmpN.itemGroupUID
		inner join dbo.tblQueueTypeDataColumns as dc on dc.columnID = qid.columnID and dc.columnname in ('MCProjectID','MCProspectMemberID')
	) as tmp
	PIVOT (min(columnValueInteger) for columnname in (MCProjectID,MCProspectMemberID)) as pvt
	inner join memberCentral.dbo.tasks_projects as p on p.projectID = pvt.MCProjectID
	inner join memberCentral.dbo.ams_members as m on m.memberID = pvt.MCProspectMemberID
	inner join memberCentral.dbo.ams_members as mActive on mActive.memberID = m.activeMemberID
	cross apply memberCentral.dbo.fn_getContent(p.projectContentID,1) as projectContent;	
	
	-- return notifying details
	select distinct tmpN.itemGroupUID, mActive.memberID as recordedByMemberID, me.email as reportEmail, 
		s.siteName, s.siteCode, mActive.firstname, mActive.lastname, mActive.memberNumber, 
		tmpN.project, tmpN.prospectName
	from #tmpNotifyTasks as tmpN
	INNER JOIN membercentral.dbo.sites as s on s.siteID = tmpN.siteID
	INNER JOIN membercentral.dbo.ams_members as m on m.memberID = tmpN.recordedByMemberID
	INNER JOIN membercentral.dbo.ams_members as mActive on mActive.memberID = m.activeMemberID
	INNER JOIN membercentral.dbo.ams_memberEmailTags as metag on metag.orgID = mActive.orgID and metag.memberID = mActive.memberID
	INNER JOIN membercentral.dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = mActive.orgID 
		and metagt.emailTagTypeID = metag.emailTagTypeID
		and metagt.emailTagType = 'Primary'
	INNER JOIN membercentral.dbo.ams_memberEmails as me on me.orgID = mActive.orgID 
		and me.memberID = metag.memberID
		and me.emailTypeID = metag.emailTypeID
	order by tmpN.itemGroupUID, tmpN.prospectName, tmpN.project;
	
	on_done:
	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	IF OBJECT_ID('tempdb..#tmpNotifyTasks') IS NOT NULL
		DROP TABLE #tmpNotifyTasks;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
