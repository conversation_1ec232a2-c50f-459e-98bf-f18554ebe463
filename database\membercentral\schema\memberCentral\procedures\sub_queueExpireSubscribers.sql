ALTER PROC dbo.sub_queueExpireSubscribers
@recordedByMemberID int,
@subscriberIDList varchar(max)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @statusReady int, @queueTypeID int, @itemGroupUID uniqueidentifier, @xmlMessage xml;

	IF OBJECT_ID('tempdb..#tmpSubscribers') IS NOT NULL 
		DROP TABLE #tmpSubscribers;
	CREATE TABLE #tmpSubscribers (subscriberID int, itemUID uniqueidentifier DEFAULT NEWID());

	select @queueTypeID = queueTypeID from platformQueue.dbo.tblQueueTypes where queueType = 'expireSubs';
	select @statusReady = queueStatusID from platformQueue.dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'readyToProcess';

	-- there should only be one itemGroupUID for these emails
	set @itemGroupUID = NEWID();

	-- get subscribers to expire
	insert into #tmpSubscribers (subscriberID)
	select s.subscriberID
	from dbo.fn_intListToTable(@subscriberIDList,',') as tmp
	inner join dbo.sub_subscribers as s on s.subscriberID = tmp.listitem
		except
	select qi.subscriberID
	from platformQueue.dbo.queue_subscriptionExpire as qi
	inner join platformQueue.dbo.tblQueueStatuses as qs on qs.queueStatusID = qi.statusID
	where qs.queueStatus not in ('readyToNotify','grabbedForNotifying','done');

	insert into platformQueue.dbo.queue_subscriptionExpire (itemUID, itemGroupUID, subscriberID, dateAdded, recordedByMemberID, statusID, dateUpdated)
	select itemUID, @itemGroupUID, subscriberID, getdate(), @recordedByMemberID, @statusReady, getdate()
	from #tmpSubscribers;

	-- resume task
	EXEC dbo.sched_resumeTask @name='Subscriber Expired Queue', @engine='BERLinux';

	-- send message to service broker to create all the individual messages
	select @xmlMessage = isnull((
		select 'expireSubsLoad' as t, cast(@itemGroupUID as varchar(60)) as u
		FOR XML RAW('mc'), TYPE
	),'<mc/>');
	EXEC platformQueue.dbo.queue_DataImport_sendMessage @xmlMessage=@xmlMessage;

	IF OBJECT_ID('tempdb..#tmpSubscribers') IS NOT NULL 
		DROP TABLE #tmpSubscribers;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
