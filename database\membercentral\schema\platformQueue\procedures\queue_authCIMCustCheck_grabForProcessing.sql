ALTER PROC dbo.queue_authCIMCustCheck_grabForProcessing
@batchSize int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @statusReady int, @statusGrabbed int;
	SELECT @queueTypeID = queueTypeID FROM dbo.tblQueueTypes WHERE queueType = 'authCIMCustCheck';
	SELECT @statusReady = queuestatusID FROM dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID and queueStatus = 'ReadyToProcess';
	SELECT @statusGrabbed = queuestatusID FROM dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID and queueStatus = 'GrabbedForProcessing';

	IF OBJECT_ID('tempdb..#tmpAuthCIMCustomers') IS NOT NULL
		DROP TABLE #tmpAuthCIMCustomers;
	CREATE TABLE #tmpAuthCIMCustomers (itemID int);

	-- dequeue in order of dateAdded. get @batchsize entries
	UPDATE qid WITH (UPDLOCK, READPAST)
	SET qid.statusID = @statusGrabbed,
		qid.dateUpdated = getdate()
		OUTPUT inserted.itemID
		INTO #tmpAuthCIMCustomers
	FROM dbo.queue_authCIMCustCheck AS qid
	INNER JOIN (
		SELECT TOP (@batchSize) itemID 
		FROM dbo.queue_authCIMCustCheck
		WHERE statusID = @statusReady
		ORDER BY dateAdded, itemID
	) AS batch ON batch.itemID = qid.itemID
	WHERE qid.statusID = @statusReady;

	SELECT qid.itemID, qid.profileID, qid.gatewayUsername, qid.gatewayPassword, qid.customerProfileID
	FROM #tmpAuthCIMCustomers AS tmp
	INNER JOIN dbo.queue_authCIMCustCheck AS qid ON qid.itemID = tmp.itemID
	ORDER BY qid.itemID;

	IF OBJECT_ID('tempdb..#tmpAuthCIMCustomers') IS NOT NULL
		DROP TABLE #tmpAuthCIMCustomers;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
