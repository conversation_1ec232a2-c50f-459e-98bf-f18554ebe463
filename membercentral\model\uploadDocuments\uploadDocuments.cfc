<cfcomponent extends="model.AppLoader" output="no">
	<cfset variables.defaultEvent = "controller">
	<cfset variables.applicationReservedURLParams = "ul,upDocAction">

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
	
		<cfset var local = structNew()>
		<cfset local.objSearch = CreateObject("component","model.search.search")>

		<cfset arguments.event.setValue('mainurl','/?#getBaseQueryString(false)#')>
		<cfset arguments.event.setValue('resourceurl','#arguments.event.getValue('mainurl')#&event=cms.showResource&resid=#this.siteResourceID#')>
		
		<cfset local.useMemberID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=arguments.event.getValue('mc_siteInfo.orgID'))>

		<cfif (application.objCMS.getTemplateSetting(arguments.event,"supportsBootstrap") eq "true") or (isdefined("session.enableMobile") and session.enableMobile)>
			<cfset arguments.event.setValue('viewDirectory', 'responsive')>
		<cfelse>
			<cfset arguments.event.setValue('viewDirectory', 'default')>
		</cfif>
		
		<!--- determine step --->
		<cfif arguments.event.getValue('upDocAction','') EQ 'uploadDocs'>
			<cfset arguments.event.setValue('step',3)>
		<cfelseif arguments.event.getValue('upDocAction','') EQ 'uploadDocsConf' AND val(arguments.event.getValue('docsCount',0))>
			<cfset arguments.event.setValue('step',4)>
		<cfelseif arguments.event.getValue('ul',0) IS 1 OR local.useMemberID GT 0>
			<cfset arguments.event.setValue('step',2)>
		<cfelse>
			<cfset arguments.event.setValue('step',1)>
		</cfif>

		<!--- force login --->
		<cfif arguments.event.getValue('step') NEQ 1 AND NOT local.useMemberID>
			<cfset application.objWebsite.forceLogin(event=arguments.event)>
		<cfelse>
			<cfset local.getIdentifiedMemberData = getIdentifiedMemberData(siteID=arguments.event.getValue('mc_siteinfo.siteID'), orgID=arguments.event.getValue('mc_siteInfo.orgID'))>
			<cfset structAppend(local, local.getIdentifiedMemberData , true)>
		</cfif>

		<cfset local.viewToUse = "echo">
		<cfset local.data = "">

		<!--- step action --->
		<cfswitch expression="#arguments.event.getValue('step')#">
			<cfcase value="1">
				<cfset local.qryContentUploadDocumentsStep1 = local.objSearch.getOptions(contentTitle='Trialsmith_Options_UploadDocuments_Step1')>

				<!--- support these special "merge codes" --->
				<cfset local.depoBucketID = local.objSearch.getDepoSearchBucketID(siteID=arguments.event.getValue('mc_siteinfo.siteID'))>
				<cfset local.searchURL = "/?pg=search">
				<cfif val(local.depoBucketID)>
					<cfset local.searchURL = "/?pg=search&bid=#val(local.depoBucketID)#">
				</cfif>
				<cfset local.qryContentUploadDocumentsStep1RawContent = local.qryContentUploadDocumentsStep1.rawContent>
				<cfset local.qryContentUploadDocumentsStep1RawContent = replaceNoCase(local.qryContentUploadDocumentsStep1RawContent,'{{siteName}}',arguments.event.getValue('mc_siteinfo.siteName'),'all')>
				<cfset local.qryContentUploadDocumentsStep1RawContent = replaceNoCase(local.qryContentUploadDocumentsStep1RawContent,'{{totalNumberOfDepos}}',application.objCommon.getDocumentCount(),'all')>
				<cfset local.qryContentUploadDocumentsStep1RawContent = replaceNoCase(local.qryContentUploadDocumentsStep1RawContent,'{{searchURL}}',local.searchURL,'all')>

				<cfset local.data = local.qryContentUploadDocumentsStep1RawContent>
			</cfcase>
			<cfcase value="2">
				<cfset local.qryContentUploadDocumentsStep2 = local.objSearch.getOptions(contentTitle='Trialsmith_Options_UploadDocuments_Step2')>
				<cfset local.qryContentUploadDocumentsStep2Info = local.objSearch.getOptions(contentTitle='Trialsmith_Options_UploadDocuments_Step2Info')>
				<cfset local.qryContentUploadDocumentsStep2Terms = local.objSearch.getOptions(contentTitle='Trialsmith_Options_UploadDocuments_Step2Terms')>
				<cfset local.qryContentUploadDocumentsStep2FAQ = local.objSearch.getOptions(contentTitle='Trialsmith_Options_UploadDocuments_Step2FAQ')>
				<cfset local.frmUploadLink = "#arguments.event.getValue('resourceurl')#&upDocAction=uploadDocs&mode=stream">
				<cfset local.showDocUploadConfirmLink = "#arguments.event.getValue('resourceurl')#&upDocAction=uploadDocsConf&mode=stream">

				<cfif not structKeyExists(local,"cfcuser_TrialSmithPending") or local.cfcuser_TrialSmithPending>
					<cfset local.notAllowedMessage = CreateObject("component","model.system.platform.tsAccountStatusMessages").getPendingHTML()>
				<cfelseif not structKeyExists(local,"cfcuser_TSAllowed") or not local.cfcuser_TSAllowed>
					<cfset local.notAllowedMessage = CreateObject("component","model.system.platform.tsAccountStatusMessages").getNotAllowedHTML()>
				</cfif>

				<cfquery name="local.qryDepoStates" datasource="#application.dsn.tlasites_trialsmith.dsn#">
					SELECT State, Description
					FROM dbo.depoTLA
					WHERE display = 1 OR State = 'TS'
					ORDER BY Description
				</cfquery>

				<cfquery name="local.qryDepoDocumentSettings" datasource="#application.dsn.tlasites_trialsmith.dsn#">
					SELECT DepoAmazonBucks, DepoAmazonBucksCredit
					FROM dbo.depoDocumentSettings
				</cfquery>

				<cfset local.qryTotalCreditsAndDepositions = getTotalCreditsAndDepositions(depomemberdataID=local.cfcuser_depomemberdataid)>

				<cfset local.defaultSelectedAssociation = (arguments.event.getValue("mc_siteInfo.sitecode") neq "TS") ? arguments.event.getValue("mc_siteInfo.sitecode") : local.cfcuser_TLAMemberState>
				<cfsavecontent variable="local.data">
					<cfinclude template="/views/uploadDocuments/#arguments.event.getValue('viewDirectory')#/frm_uploadDocuments_step2.cfm">
				</cfsavecontent>
			</cfcase>
			<cfcase value="3">
				<cfset local.arrUploadedDocs = []>
				<cfset local.uploadDocsResult = { "success":true, "uploadeddocscount":0, "errmsg":"" }>
				
				<!--- Attempt upload of document --->
				<cftry>
					<cffile action="upload" filefield="depoDoc" destination="#application.paths.docs.uploads.path#" result="local.strUploadResult" nameconflict="makeunique">
					<cfif local.strUploadResult.fileWasSaved>
						<cfset local.serverUploadedFilePath = "#application.paths.docs.uploads.path##local.strUploadResult.serverfile#">
						
						<!--- add compressed files to unzip queue --->
						<cfif listFindNoCase("rar,zip",local.strUploadResult.serverFileExt)>
							<cfset addToDepoDocumentsUnzipQueue(pathToZip=local.serverUploadedFilePath, depomemberdataID=local.cfcuser_depomemberdataid, 
								docState=arguments.event.getValue('docState',''), creditType=arguments.event.getValue('creditType',''), 
								DepoAmazonBucksFullName=arguments.event.getValue('depoDocAmazonBucksFullName',''), 
								DepoAmazonBucksEmail=arguments.event.getValue('depoDocAmazonBucksEmail',''))>
							<cfset arrayAppend(local.arrUploadedDocs, { "fileName":local.strUploadResult.serverFileName, "fileExt":local.strUploadResult.serverFileExt })>
							<cfset local.uploadDocsResult.uploadeddocscount++>
						<cfelse>
							<cfset local.documentID = doSaveDocument(depomemberdataID=local.cfcuser_depomemberdataid, docState=arguments.event.getValue('docState',''), 
								originalExt=local.strUploadResult.serverFileExt, creditType=arguments.event.getValue('creditType',''), 
								DepoAmazonBucksFullName=arguments.event.getValue('depoDocAmazonBucksFullName',''), 
								DepoAmazonBucksEmail=arguments.event.getValue('depoDocAmazonBucksEmail',''), serverTemporaryUploadedFilePath=local.serverUploadedFilePath)>
							<cfif local.documentID>
								<cfset arrayAppend(local.arrUploadedDocs, { "fileName":local.documentID, "fileExt":lcase(local.strUploadResult.serverFileExt) })>
								<cfset local.uploadDocsResult.uploadeddocscount++>
							</cfif>
						</cfif>
					</cfif>
				<cfcatch type="Any">
					<cfset application.objError.sendError(cfcatch=cfcatch)>
					<!--- if any errors, delete the uploaded file --->
					<cfif StructKeyExists(local.strUploadResult, "serverfile") AND FileExists("#application.paths.docs.uploads.path##local.strUploadResult.serverfile#")>
						<cffile action="DELETE" file="#application.paths.docs.uploads.path##local.strUploadResult.serverfile#">	
					</cfif>
					<cfif StructKeyExists(local.strUploadResult, "serverfile") AND arrayLen(local.arrUploadedDocs)>
						<cfloop array="#local.arrUploadedDocs#" index="local.thisDoc">
							<cfif FileExists("#application.paths.docs.uploads.path##local.thisDoc.fileName#.#local.thisDoc.fileExt#")>
								<cffile action="DELETE" file="#application.paths.docs.uploads.path##local.thisDoc.fileName#.#local.thisDoc.fileExt#">
							</cfif>
						</cfloop>
					</cfif>
					<cfset local.uploadDocsResult = { "success":false, "uploadeddocscount":0, "errmsg":"Some error occured while uploading documents. Try again." }>
				</cfcatch>
				</cftry>

				<cfset local.data = serializeJSON(local.uploadDocsResult)>
			</cfcase>
			<cfcase value="4">
				<cfset local.strEmailContent = generateUploadDocsConfirmation(event=arguments.event)>
				<cfset doEmailUploadDocsConfirmation(strEmailContent=local.strEmailContent)>
				<cfset local.data = local.strEmailContent.dispConfirmation>
			</cfcase>
		</cfswitch>

		<!--- return the app struct --->
		<cfreturn returnAppStruct(local.data,local.viewToUse)>
	</cffunction>

	<cffunction name="doSaveDocument" access="private" output="false" returntype="numeric">
		<cfargument name="depomemberdataID" type="numeric" required="true">
		<cfargument name="docState" type="string" required="true">
		<cfargument name="originalExt" type="string" required="true">
		<cfargument name="creditType" type="string" required="true">
		<cfargument name="DepoAmazonBucksFullName" type="string" required="true">
		<cfargument name="DepoAmazonBucksEmail" type="string" required="true">
		<cfargument name="serverTemporaryUploadedFilePath" type="string" required="true">

		<cfset var local = structNew()>

		<cfquery name="local.qryAddDepoDocument" datasource="#application.dsn.tlasites_trialsmith.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				DECLARE @documentID int, @uploadSourceID int;

				SELECT @uploadSourceID = uploadSourceID
				FROM dbo.depoDocumentUploadSources
				WHERE sourceName = 'User Uploaded';

				EXEC dbo.ts_addDepoDocument
					@depomemberdataID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.depomemberdataID#">,
					@docOrgCode=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.docState#">,
					@originalExt=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.originalExt#">,
					@contributeDate=<cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#now()#">,
					<cfif arguments.creditType EQ "amazon">
						@DepoAmazonBucks=1,
						@DepoAmazonBucksFullName=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.DepoAmazonBucksFullName#">,
						@DepoAmazonBucksEmail=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.DepoAmazonBucksEmail#">,
					<cfelse>
						@DepoAmazonBucks=0,
						@DepoAmazonBucksFullName=NULL,
						@DepoAmazonBucksEmail=NULL,
					</cfif>
					@uploadSourceID=@uploadSourceID,
					@enteredByDepomemberdataID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.depomemberdataID#">,
					@documentID=@documentID OUTPUT;

				SELECT @documentID AS documentID;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfset local.documentID = val(local.qryAddDepoDocument.documentID)>

		<cfset local.finalFileName = "#local.documentID#.#lcase(arguments.originalExt)#">

		<cfset local.uploadedFilePath = "#application.paths.docs.uploads.path##local.finalFileName#">
		<cfset local.originalDocFilePath = "#application.paths.docs.originals.path##local.finalFileName#">

		<!--- rename file to documentID (ensuring extension is lower case) --->
		<cffile action="RENAME" source="#arguments.serverTemporaryUploadedFilePath#" destination="#local.uploadedFilePath#">

		<!--- copy file to original docs --->
		<cffile action="COPY" source="#local.uploadedFilePath#" destination="#local.originalDocFilePath#">
		
		<cfif arguments.originalExt EQ 'pdf'>
			<cfstoredproc procedure="ts_addDepoDocumentToAttachQueue" datasource="#application.dsn.tlasites_trialsmith.dsn#">
				<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#local.documentID#">
			</cfstoredproc>
		<cfelse>
			<cfset local.originalDocS3UploadFilePath = "#application.paths.docs.originalsS3Upload.path##local.finalFileName#">
			<cfset local.s3keyMod = numberFormat(local.documentID mod 1000,"0000")>
			<cfset local.s3objectKey = lcase("depos/original/#local.s3keyMod#/#local.finalFileName#")>
			<cfset addToS3UploadQueue(filepath=local.originalDocS3UploadFilePath, objectKey=local.s3objectKey)>
		</cfif>

		<cfreturn local.documentID>
	</cffunction>

	<cffunction name="addToDepoDocumentsUnzipQueue" access="private" output="false" returntype="void">
		<cfargument name="pathToZip" type="string" required="true">
		<cfargument name="depomemberdataID" type="numeric" required="true">
		<cfargument name="docState" type="string" required="true">
		<cfargument name="creditType" type="string" required="true">
		<cfargument name="DepoAmazonBucksFullName" type="string" required="true">
		<cfargument name="DepoAmazonBucksEmail" type="string" required="true">
		
		<cfset var qryAddToDepoDocumentsUnzipQueue = "">

		<cfquery name="qryAddToDepoDocumentsUnzipQueue" datasource="#application.dsn.tlasites_trialsmith.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				DECLARE @uploadSourceID int;

				SELECT @uploadSourceID = uploadSourceID
				FROM dbo.depoDocumentUploadSources
				WHERE sourceName = 'User Uploaded';

				EXEC dbo.ts_addDepoDocumentToUnzipQueue
					@pathToZip=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.pathToZip#">,
					@depomemberdataID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.depomemberdataID#">,
					@docState=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.docState#">,
					<cfif arguments.creditType EQ "amazon">
						@DepoAmazonBucks=1,
						@DepoAmazonBucksFullName=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.DepoAmazonBucksFullName#">,
						@DepoAmazonBucksEmail=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.DepoAmazonBucksEmail#">,
					<cfelse>
						@DepoAmazonBucks=0,
						@DepoAmazonBucksFullName=NULL,
						@DepoAmazonBucksEmail=NULL,
					</cfif>
					@uploadSourceID=@uploadSourceID;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
	</cffunction>

	<cffunction name="addToS3UploadQueue" access="private" output="false" returntype="void">
		<cfargument name="filepath" type="string" required="true">
		<cfargument name="objectKey" type="string" required="true">

		<cfset var qryAddDocToS3UploadQueue = "">

		<cfquery name="qryAddDocToS3UploadQueue" datasource="#application.dsn.platformQueue.dsn#">
			SET NOCOUNT ON;

			DECLARE @objectKey varchar(400), @s3bucketName varchar(100), @filePath varchar(400),
				@s3UploadReadyStatusID int, @nowDate datetime = getdate();

			SET @s3bucketName = 'trialsmith-depos';
			set @filePath = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.filepath#">
			set @objectKey = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.objectKey#">

			SELECT @s3UploadReadyStatusID = qs.queueStatusID
			FROM dbo.tblQueueTypes as qt
			INNER JOIN dbo.tblQueueStatuses as qs on qs.queueTypeID = qt.queueTypeID
			WHERE qt.queueType = 's3Upload'
			AND qs.queueStatus = 'readyToProcess';

			IF NOT EXISTS (select 1 from dbo.queue_S3Upload where s3bucketName = @s3bucketName and objectKey = @objectKey)
				INSERT INTO dbo.queue_S3Upload (statusID, s3bucketName, objectKey, filePath, deleteOnSuccess, dateAdded, dateUpdated)
				VALUES (@s3UploadReadyStatusID, @s3bucketName, @objectKey, @filePath, 1, @nowDate, @nowDate);
		</cfquery>
	</cffunction>

	<cffunction name="generateUploadDocsConfirmation" access="private" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.mc_siteInfo = arguments.event.getValue('mc_siteinfo')>
		<cfset local.strMember = getIdentifiedMemberData(siteID=local.mc_siteInfo.siteID, orgID=local.mc_siteInfo.orgID)>

		<cfif NOT val(local.strMember.cfcuser_memberID)>
			<cfthrow message="Invalid MemberID.">
		<cfelseif NOT val(arguments.event.getValue('docsCount',0))>
			<cfthrow message="No Uploaded Documents Found.">
		</cfif>

		<cfset local.memberKey = application.objMergeCodes.generateMemberKey(orgcode=local.mc_siteInfo.orgcode, membernumber=local.strMember.cfcuser_memberNumber)>

		<cfset local.strEmailContent = { 
			"memberID":local.strMember.cfcuser_memberID, 
			"emailFrom":{ name="TrialSmith", email="<EMAIL>" },
			"arrEmailTo":[],
			"emailreplyto":"<EMAIL>",
			"emailsubject":"Upload Documents Confirmation",
			"emailContent":"", 
			"dispConfirmation":"",
			"siteID":local.mc_siteInfo.siteID,
			"sendingSiteResourceID":this.siteResourceID
		}>

		<cfif len(local.strMember.cfcuser_email)>
			<cfset arrayAppend(local.strEmailContent.arrEmailTo, { name:'#local.strMember.cfcuser_firstname# #local.strMember.cfcuser_lastname#', email:local.strMember.cfcuser_email })>
		</cfif>

		<cfset local.docLabel = arguments.event.getValue('docsCount') GT 1 ? 'documents' : 'document'>

		<cfsavecontent variable="local.strEmailContent.emailContent">
			<cfinclude template="/views/uploadDocuments/frm_emailConfirmation.cfm">
		</cfsavecontent>

		<cfsavecontent variable="local.strEmailContent.dispConfirmation">
			<cfinclude template="/views/uploadDocuments/frm_dispConfirmation.cfm">
		</cfsavecontent>

		<cfreturn local.strEmailContent>
	</cffunction>

	<cffunction name="doEmailUploadDocsConfirmation" access="private" output="false" returntype="boolean">
		<cfargument name="strEmailContent" type="struct" required="true">

		<cfset var emailSent = false>

		<cfif arrayLen(arguments.strEmailContent.arrEmailTo)>
			<cfset local.strEmailResult = application.objEmailWrapper.sendMailESQ(
				emailfrom=arguments.strEmailContent.emailfrom,
				emailto=arguments.strEmailContent.arrEmailTo,
				emailreplyto=arguments.strEmailContent.emailreplyto,
				emailsubject=arguments.strEmailContent.emailSubject,
				emailtitle=arguments.strEmailContent.emailSubject,
				emailhtmlcontent=arguments.strEmailContent.emailContent,
				siteID=arguments.strEmailContent.siteID,
				memberID=arguments.strEmailContent.memberID,
				messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="DOCUPLOADCONF"),
				sendingSiteResourceID=arguments.strEmailContent.sendingSiteResourceID,
				doWrapEmail=false
			)>
			<cfset emailSent = local.strEmailResult.success>
		</cfif>

		<cfreturn emailSent>
	</cffunction>

	<cffunction name="getIdentifiedMemberData" access="private" output="false" returntype="struct">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="orgID" type="numeric" required="yes">

		<cfset var local = structNew()>

		<cfif application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
			<cfset local.strMember = {
				"cfcuser_memberID": session.cfcuser.memberData.memberID,
				"cfcuser_depomemberdataid":session.cfcuser.memberData.depomemberdataID,
				"cfcuser_membertypeid":session.cfcuser.memberData.membertypeid,
				"cfcuser_prefix":session.cfcuser.memberData.prefix,
				"cfcuser_firstname":session.cfcuser.memberData.firstname,
				"cfcuser_middlename":session.cfcuser.memberData.middlename,
				"cfcuser_lastname":session.cfcuser.memberData.lastname,
				"cfcuser_suffix":session.cfcuser.memberData.suffix,
				"cfcuser_professionalSuffix":session.cfcuser.memberData.professionalSuffix,
				"cfcuser_company":session.cfcuser.memberData.company,
				"cfcuser_email":session.cfcuser.memberData.email,
				"cfcuser_memberNumber":session.cfcuser.memberData.memberNumber,
				"cfcuser_TSAllowed":application.objUser.getSubscriptionData(cfcuser=session.cfcuser, orgID=4, key='TSAllowed'),
				"cfcuser_TrialSmithPending":application.objUser.getSubscriptionData(cfcuser=session.cfcuser, orgID=4, key='TrialSmithPending'),
				"cfcuser_TLAMemberState":application.objUser.getSubscriptionData(cfcuser=session.cfcuser, orgID=4, key='TLAMemberState'),
				"cfcuser_billingState":application.objUser.getSubscriptionData(cfcuser=session.cfcuser, orgID=4, key='billingState')
			}>
		<cfelse>
			<cfset local.useMemberID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=arguments.orgID)>
			<cfset local.memberData = application.objUser.getMemberDetail(memberID=local.useMemberID, siteID=arguments.siteID)>
			<cfset local.strMember = {
				"cfcuser_memberID": local.useMemberID,
				"cfcuser_depomemberdataid":val(local.memberData.depomemberdataID),
				"cfcuser_membertypeid":local.memberData.membertypeid,
				"cfcuser_prefix":local.memberData.prefix,
				"cfcuser_firstname":local.memberData.firstname,
				"cfcuser_middlename":local.memberData.middlename,
				"cfcuser_lastname":local.memberData.lastname,
				"cfcuser_suffix":local.memberData.suffix,
				"cfcuser_professionalSuffix":local.memberData.professionalSuffix,
				"cfcuser_company":local.memberData.company,
				"cfcuser_email":local.memberData.email,
				"cfcuser_memberNumber":local.memberData.memberNumber,
				"cfcuser_TSAllowed":"",
				"cfcuser_Pending":"",
				"cfcuser_TLAMemberState":"",
				"cfcuser_billingState":""
			}>

			<cfset local.isBot = (findnocase("Bot", (session.mcStruct?.deviceProfile?.type ?: 'Bot')))>
			<!--- This memberID is not affiliated with a networkProfile/depomemberdataid --->
			<cfif not local.isBot and not local.strMember["cfcuser_depomemberdataid"]>
				<!--- See if they are eligible for us to create an account in waiting --->
				<cfset local.groupPrintID = application.objMember.getMemberGroupPrintID(memberID=local.useMemberID)>
				<cfset local.hasPermission = application.objSiteResource.checkResourceRightsForGroupPrint(
					resourceID=application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).siteSiteResourceID,
					functionName="TrialSmithAllowed",
					groupPrintID=local.groupPrintID,
					siteID=arguments.siteID
				)>
				<cfif local.hasPermission>
					<!--- create an account in waiting and use that depomemberdataid --->
					<!--- get depomemberdataid if there is one. otherwise create depoaccount but link only via mcmemberidtemp --->
					<cfset local.TrialSmithAllowedRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="Site", functionName="TrialSmithAllowed")>
					<cfquery name="local.createAccountInWaiting" datasource="#application.dsn.membercentral.dsn#">
						SET XACT_ABORT, NOCOUNT ON;
						BEGIN TRY
							DECLARE @memberID int = <cfqueryparam value="#local.useMemberID#" cfsqltype="CF_SQL_INTEGER">,
								@siteID int = <cfqueryparam value="#arguments.siteID#" cfsqltype="CF_SQL_INTEGER">,
								@statsSessionID int = <cfqueryparam value="#session.cfcuser.statsSessionID#" cfsqltype="CF_SQL_INTEGER">,
								@depomemberdataid int, @TrialSmithMemberID int, @TrialSmithAllowedRFID int;
							SET @TrialSmithAllowedRFID = <cfqueryparam value="#local.TrialSmithAllowedRFID#" cfsqltype="CF_SQL_INTEGER">;

							EXEC dbo.ams_createDepoTLASITESAccount @siteID=@siteID, @memberID=@memberID, @statsSessionID=@statsSessionID, 
								@TrialSmithAllowedFID=@TrialSmithAllowedRFID, @depomemberdataid=@depomemberdataid OUTPUT, 
								@TrialSmithMemberID=@TrialSmithMemberID OUTPUT;
							IF @depomemberdataid > 0
								UPDATE trialsmith.dbo.depomemberdata
								SET MCmemberIDtemp = @memberID
								WHERE depomemberdataid = @depomemberdataid;

							SELECT depomemberdataid
							FROM trialsmith.dbo.depomemberdata
							WHERE depomemberdataid = @depomemberdataid;

						END TRY
						BEGIN CATCH
							IF @@trancount > 0 ROLLBACK TRANSACTION;
							EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
						END CATCH
					</cfquery>
					<cfif local.createAccountInWaiting.recordCount>
						<cfset local.strMember["cfcuser_depomemberdataid"] = local.createAccountInWaiting.depomemberdataID>
					</cfif>
				</cfif>
			</cfif>

			<cfif local.strMember["cfcuser_depomemberdataid"]>
				<cfset local.cfcuser = session.cfcuser>
				<cfset local.cfcuser.subscriptionData = application.objUser.getSubscriptionInfo(subscriptionData=local.cfcuser.subscriptionData, depoMemberDataID=local.strMember["cfcuser_depomemberdataid"], orgCode=session.mcStruct.sitecode, isloggedIn=true)>

				<cfset local.strMember.cfcuser_TSAllowed = application.objUser.getSubscriptionData(cfcuser=local.cfcuser, orgID=4, key='TSAllowed')>
				<cfset local.strMember.cfcuser_TrialSmithPending = application.objUser.getSubscriptionData(cfcuser=local.cfcuser, orgID=4, key='TrialSmithPending')>
				<cfset local.strMember.cfcuser_billingState = application.objUser.getSubscriptionData(cfcuser=local.cfcuser, orgID=4, key='TLAMemberState')>
				<cfset local.strMember.cfcuser_TLAMemberState = application.objUser.getSubscriptionData(cfcuser=local.cfcuser, orgID=4, key='billingState')>
			</cfif>
		</cfif>

		<cfreturn local.strMember>
	</cffunction>

	<cffunction name="getTotalCreditsAndDepositions" access="private" output="false" returntype="query">
		<cfargument name="depoMemberDataID" type="numeric" required="true">

		<cfset var qryTotalCreditsAndDepositions = "">

		<cfquery name="qryTotalCreditsAndDepositions" datasource="#application.dsn.tlasites_trialsmith.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @depomemberdataID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.depomemberdataID#">,
				@firmPlanID int, @PCTotal float, @totalDepositions int;
			DECLARE @tmpDepoMembers TABLE (depomemberdataID int);

			SELECT @firmPlanID = firmPlanID 
			FROM dbo.tlaFirmPlanLink 
			WHERE depoMemberDataID = @depomemberdataID;

			IF @firmPlanID IS NOT NULL
				INSERT INTO @tmpDepoMembers (depomemberdataID)
				select depoMemberDataID
				from dbo.tlaFirmPlanLink
				where firmPlanID = @firmPlanID;
			ELSE
				INSERT INTO @tmpDepoMembers (depomemberdataID)
				VALUES (@depomemberdataID);

			SELECT @PCTotal = SUM(pc.PurchaseCreditAmount)
			FROM dbo.PurchaseCredits as pc
			INNER JOIN @tmpDepoMembers as m ON m.depoMemberDataID = pc.depoMemberDataID;

			SELECT @totalDepositions = COUNT(d.DocumentID)
			FROM dbo.depoDocuments AS d 
			INNER JOIN dbo.depodocumenttypes AS dt on d.documenttypeid = dt.typeid AND dt.acctcode BETWEEN 3000 AND 3999
			INNER JOIN @tmpDepoMembers AS m ON m.depoMemberDataID = d.depoMemberDataID
			WHERE d.disabled = 'N';

			SELECT ISNULL(@PCTotal,0) AS PCTotal, @totalDepositions AS totalDepositions;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryTotalCreditsAndDepositions>
	</cffunction>

</cfcomponent>		