ALTER PROC dbo.ts_queuePurchaseCreditNotifications
@itemCount int OUTPUT
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpCreditUsedDepoMembers') IS NOT NULL 
		DROP TABLE #tmpCreditUsedDepoMembers;
	IF OBJECT_ID('tempdb..#tmpDepoMembers') IS NOT NULL 
		DROP TABLE #tmpDepoMembers;
	CREATE TABLE #tmpCreditUsedDepoMembers (depomemberdataID int);
	CREATE TABLE #tmpDepoMembers (depomemberdataID int PRIMARY KEY);

	DECLARE @queueTypeID int, @statusReady int, @nowDate datetime = GETDATE();
	SELECT @queueTypeID = queueTypeID FROM platformQueue.dbo.tblQueueTypes WHERE queueType = 'TSPurchaseCreditsNotify';
	SELECT @statusReady = queuestatusID FROM platformQueue.dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID AND queueStatus = 'readyToProcess';
	SET @itemCount = 0;

	-- get depoMembers whose credits were used in the previous week
	INSERT INTO #tmpCreditUsedDepoMembers
	SELECT DISTINCT depomemberdataID
	FROM dbo.PurchaseCredits
	WHERE CreditDate BETWEEN DATEADD(DAY,-7,@nowDate) AND @nowDate
	AND (PurchaseCreditAmount <= 0 OR AmazonBucksCreditAmount <= 0);

	IF @@ROWCOUNT = 0
		GOTO on_done;

	-- DepoCredits
	INSERT INTO #tmpDepoMembers
	SELECT DISTINCT pc.depomemberdataID
	FROM dbo.PurchaseCredits AS pc
	INNER JOIN #tmpCreditUsedDepoMembers AS tmp ON tmp.depomemberdataID = pc.depomemberdataID
	GROUP BY pc.depomemberdataID
	HAVING SUM(pc.purchaseCreditAmount) <= 0
		INTERSECT
	SELECT DISTINCT pc.depomemberdataID
	FROM dbo.PurchaseCredits AS pc
	INNER JOIN #tmpCreditUsedDepoMembers AS tmp ON tmp.depomemberdataID = pc.depomemberdataID
	WHERE pc.purchaseCreditAmount > 0;

	-- AmazonCredits
	INSERT INTO #tmpDepoMembers
	SELECT depomemberdataID
	FROM (
		SELECT DISTINCT pc.depomemberdataID
		FROM dbo.PurchaseCredits AS pc
		INNER JOIN #tmpCreditUsedDepoMembers AS tmp ON tmp.depomemberdataID = pc.depomemberdataID
		GROUP BY pc.depomemberdataID
		HAVING SUM(pc.AmazonBucksCreditAmount) <= 0
			INTERSECT
		SELECT DISTINCT pc.depomemberdataID
		FROM dbo.PurchaseCredits AS pc
		INNER JOIN #tmpCreditUsedDepoMembers AS tmp ON tmp.depomemberdataID = pc.depomemberdataID
		WHERE pc.AmazonBucksCreditAmount > 0
	) tmp
		EXCEPT
	SELECT depomemberdataID FROM #tmpDepoMembers;

	-- populate queue
	INSERT INTO platformQueue.dbo.queue_TSPurchaseCreditsNotify (depomemberdataID, FirstName, LastName, Email, statusID, dateAdded, dateUpdated)
	SELECT d.depomemberdataID, d.FirstName, d.LastName, d.Email, @statusReady, GETDATE(), GETDATE()
	FROM #tmpDepoMembers AS tmp
	INNER JOIN dbo.depoMemberData AS d ON d.depomemberdataID = tmp.depomemberdataID
	LEFT OUTER JOIN platformQueue.dbo.queue_TSPurchaseCreditsNotify AS qi ON qi.depomemberdataID = tmp.depomemberdataID
	WHERE d.Email <> ''
	AND d.optOutTSMarketing = 0
	AND qi.itemID IS NULL;
	SET @itemCount = @@ROWCOUNT;

	IF @itemCount > 0
		EXEC membercentral.dbo.sched_resumeTask @name='TrialSmith Purchase Credits Notification', @engine='MCLuceeLinux';

	on_done:
	IF OBJECT_ID('tempdb..#tmpCreditUsedDepoMembers') IS NOT NULL 
		DROP TABLE #tmpCreditUsedDepoMembers;
	IF OBJECT_ID('tempdb..#tmpDepoMembers') IS NOT NULL 
		DROP TABLE #tmpDepoMembers;
	
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
