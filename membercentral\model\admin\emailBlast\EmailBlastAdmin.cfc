<cfcomponent extends="model.admin.admin" output="no">
	<cfscript>
		defaultEvent = 'controller';
	</cfscript>

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();
			// load objects ----------------------------------------------------------------------------- ::
			this.objEmailBlast = CreateObject('component', 'model.admin.emailBlast.emailBlast');
			this.objCategories = CreateObject('component', 'model.system.platform.category');
			
			// set rights into event -------------------------------------------------------------------- ::
			local.tmpRights = buildRightAssignments(siteResourceID=this.siteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.event.getValue('mc_siteInfo.siteid'));
			arguments.event.getCollection()['mc_admintoolInfo']['myRights'] = local.tmpRights;

			// build quick links ------------------------------------------------------------------------ ::
			this.link.listMyEmailBlasts	= buildCurrentLink(arguments.event,"listMyEmailBlasts");
			this.link.listAllEmailBlasts = buildCurrentLink(arguments.event,"listAllEmailBlasts");
			this.link.listCategories = buildCurrentLink(arguments.event,"listCategories");
			this.link.editCategory = buildCurrentLink(arguments.event,"editCategory");
			this.link.saveCategory = buildCurrentLink(arguments.event,"saveCategory") & "&mode=stream";
			this.link.message = buildCurrentLink(arguments.event,"message");
			this.link.showBlast = buildCurrentLink(arguments.event,"showBlast");
			this.link.copyBlast = buildCurrentLink(arguments.event,"copyBlast") & "&mode=stream";
			this.link.saveNewBlast = buildCurrentLink(arguments.event,"saveNewBlast") & "&mode=stream";
			this.link.saveBlast = buildCurrentLink(arguments.event,"saveBlast") & "&mode=stream";
			this.link.showFooter = buildCurrentLink(arguments.event,"showFooter");
			this.link.editFooter = buildCurrentLink(arguments.event,"editFooter") & "&mode=direct";
			this.link.saveFooter = buildCurrentLink(arguments.event,"saveFooter") & "&mode=direct";
			this.link.editCustomFont = buildCurrentLink(arguments.event,"editCustomFont") & "&mode=direct";
			this.link.saveCustomFont = buildCurrentLink(arguments.event,"saveCustomFont") & "&mode=direct";
			this.link.downloadMessageAttachment = "/?pg=admin&mca_ajaxlib=emailBlast&mca_ajaxfunc=downloadMessageAttachment&mode=stream";

			this.link.editMember = buildLinkToTool(toolType='MemberAdmin',mca_ta='edit');
	
			// Run Assigned Method ---------------------------------------------------------------------- ::
			local.methodToRun = this[arguments.event.getValue('mca_ta')];

			// pass the argument collection to the current method and execute it. ----------------------- ::
			return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>

	<cffunction name="listMyEmailBlasts" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();
			local.data = listBlasts(arguments.event,false);
		</cfscript>
		
		<cfreturn local.data>
	</cffunction>

	<cffunction name="listAllEmailBlasts" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();
			local.data = listBlasts(arguments.event,true);
		</cfscript>
		
		<cfreturn local.data>
	</cffunction>

	<cffunction name="listBlasts" access="private" output="false" returntype="struct">
		<cfargument name="Event" type="any" required="yes">
		<cfargument name="showAllBlasts" type="boolean" required="yes">

		<cfscript>
			var local = structNew();
			
			if (arguments.showAllBlasts) 
				local.dsp = "all";
			else
				local.dsp = "my";

			local.listEmailBlastsLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=emailBlastJSON&meth=getEmailBlasts&mode=stream&dsp=#local.dsp#&srid=#this.siteResourceID#";
			local.showBlastMessagePreviewLink = buildCurrentLink(arguments.event,"showBlastMessagePreviewByBlastID") & "&mode=direct";
			local.qryCategories = this.objCategories.getCategories(this.siteResourceID);
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_emailBlasts.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="showCommonTop" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfif arguments.event.valueExists('blastID')>
			<cfset local.qryBlastInfo = this.objEmailBlast.getBlastInfo(arguments.event.getValue('blastId',0))>
			<cfif local.qryBlastInfo.recordcount is 0>
				<cflocation url="#this.link.showBlast#" addtoken="no">
			</cfif>

			<cfset local.isBlastOwner = local.qryBlastInfo.createdByMemberID eq session.cfcuser.memberdata.memberid>
			<cfset local.canEditBlast = arguments.event.getValue('mc_adminToolInfo.myRights.editAnyBlast') or (local.isBlastOwner and arguments.event.getValue('mc_adminToolInfo.myRights.editOwnBlast'))>
			
			<cfif arguments.event.getValue('mca_ta') eq "previewBlast">
				<cfset appendBreadCrumbs(arguments.event,{ link='#this.link.showBlast#&blastID=#local.qryBlastInfo.blastID#', text=encodeForHTML(local.qryBlastInfo.blastName) })>
				<cfset appendBreadCrumbs(arguments.event,{ link='', text="Preview Blast" })>
			<cfelse>
				<cfset appendBreadCrumbs(arguments.event,{ link='', text=encodeForHTML(local.qryBlastInfo.blastName) })>
			</cfif>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_commonTop.cfm">
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="showBlast" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfset local.qryBlastInfo = this.objEmailBlast.getBlastInfo(arguments.event.getValue('blastId', 0))>
		<cfset local.qryBlastEmailTypes = this.objEmailBlast.getBlastEmailTypes(arguments.event.getValue('blastId', 0))>
		<cfset local.qryBlastEmailTagTypes = this.objEmailBlast.getBlastEmailTagTypes(arguments.event.getValue('blastId', 0))>
		<cfset local.qryFooters = this.objEmailBlast.getFooters(arguments.event.getValue('mc_siteinfo.siteid'))>
		<cfset local.qryOrgEmailTypes = application.objOrgInfo.getOrgEmailTypes(orgID=arguments.event.getValue('mc_siteinfo.orgID'))>
		<cfset local.qryOrgEmailTags = application.objOrgInfo.getOrgEmailTagTypes(arguments.event.getValue('mc_siteinfo.orgid'))>
		<cfset local.qryOrgEmailPrimaryTag = local.qryOrgEmailTags.filter(function(thisEmailTag) {
			return (thisEmailTag.emailTagType is "primary" and thisEmailTag.isSystemType is 1);
		})>
		<!--- Default to Primary Email Tag if nothing defined --->
		<cfif not local.qryBlastEmailTagTypes.recordcount and not local.qryBlastEmailTypes.recordcount>
			<cfset local.qryBlastEmailTagTypes = local.qryOrgEmailPrimaryTag>
			<cfset local.hasEmailTypesOrTags = false>
		<cfelse>
			<cfset local.hasEmailTypesOrTags = true>
		</cfif>

		<cfset local.attachmentsLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=emailBlastJSON&meth=getAttachments&mode=stream&blastID=#val(local.qryBlastInfo.blastid)#">
		<cfset local.mcEBDocUploadLink = buildCurrentLink(arguments.event,"uploadAttachment") & "&blastID=#val(local.qryBlastInfo.blastid)#&mode=stream">

		<cfif val(local.qryBlastInfo.blastID) eq 0>
			<cfset local.objResourceTemplates = createObject("component","model.admin.common.modules.resourceTemplates.resourceTemplate")>
			<cfset local.qryCategories = this.objCategories.getCategories(this.siteResourceID)>
			
			<cfset local.qryTemplateEditors = local.objResourceTemplates.getTemplateEditors(excludeEditorCode='AceEditor')>
			<cfset local.BeePluginEditorID = QueryFilter(local.qryTemplateEditors, function(thisRow) { return arguments.thisRow.editorCode EQ 'BeePlugin'; }).editorID>
			<cfset local.CKEditorID = QueryFilter(local.qryTemplateEditors, function(thisRow) { return arguments.thisRow.editorCode EQ 'CKEditor'; }).editorID>
			
			<cfset local.hasEmailBlastTemplates = local.objResourceTemplates.hasResourceTypeTemplates(siteID=arguments.event.getValue('mc_siteinfo.siteID'), resourceType='EmailBlast')>
			<cfif local.hasEmailBlastTemplates>
				<cfset local.strTemplateData = { 
					siteID=arguments.event.getValue('mc_siteinfo.siteid'),
					siteCode=arguments.event.getValue('mc_siteinfo.siteCode'),
					resourceType='EmailBlast',
					title="", 
					gridext="#this.siteResourceID#_1",
					initGridOnLoad=true,
					displayMode="tabs",
					readOnly=true
				}>
				<cfset local.strResourceTemplatesGrid = local.objResourceTemplates.manageResourceTemplates(strData=local.strTemplateData)>
			</cfif>
		</cfif>

		<cfif local.qryBlastInfo.blastID gt 0>
			<cfset local.cleanedUpBlastContent = local.qryBlastInfo.rawcontent>
			<cfset local.isCompleteHTMLDocument = application.objEmailWrapper.isCompleteHTMLDocument(htmlcontent=local.qryBlastInfo.rawcontent)>
			<cfif not local.isCompleteHTMLDocument>
				<cfset local.cleanedUpBlastContent = application.objEmailWrapper.cleanupHTML(htmlcontent=local.qryBlastInfo.rawcontent)>
			</cfif>
			<cfif local.cleanedUpBlastContent neq local.qryBlastInfo.rawcontent>
				<cfstoredproc procedure="cms_updateContent" datasource="#application.dsn.membercentral.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryBlastInfo.contentID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="1">
					<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="1">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.qryBlastInfo.contentTitle#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="">
					<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="#local.cleanedUpBlastContent#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.sysMemberID')#">
				</cfstoredproc>
				<cfset local.qryBlastInfo = this.objEmailBlast.getBlastInfo(arguments.event.getValue('blastId', 0))>
			</cfif>
			<cfset local.qrySendingHistorySummary = this.objEmailBlast.getBlastSendingHistorySummary(blastID=local.qryBlastInfo.blastID, siteID=arguments.event.getValue('mc_siteInfo.siteID'))>
			<cfset local.qryOrgIdentities = application.objOrgInfo.getOrgIdentities(orgID=arguments.event.getValue('mc_siteinfo.orgID'))>
			<cfset local.showOrgIdentitySelector = local.qryOrgIdentities.recordCount gt 1>
			<cfset local.qryBlastConsentLists = this.objEmailBlast.getBlastConsentLists(orgID=arguments.event.getValue('mc_siteinfo.orgID'), blastID=val(local.qryBlastInfo.blastID))>
			<cfset local.qryOrgOptOutLists = createObject("component","model.admin.emailPreferences.emailPreferences").getConsentLists(orgID=arguments.event.getValue('mc_siteInfo.orgID'), mode="Opt-Out")>
			<cfset local.showOptOutsPerm = local.qryOrgOptOutLists.recordCount>
			<cfset local.qryOrgAvailableOptOutLists = createObject("component","model.admin.emailPreferences.emailPreferences").getConsentLists(orgID=arguments.event.getValue('mc_siteInfo.orgID'), mode="Opt-Out", excludeGlobalLists =1)>
			<cfset local.qryCategories = this.objCategories.getCategories(this.siteResourceID)>

			<cfset local.showBlastMessagePreviewLink = buildCurrentLink(arguments.event,"showBlastMessagePreview") & "&mode=stream">
			<cfset local.previewRecipientSummaryLink = buildCurrentLink(arguments.event,"previewRecipientSummary") & "&blastID=#val(local.qryBlastInfo.blastID)#&mode=direct">
			<cfset local.showBlastSendingHistoryLink = buildCurrentLink(arguments.event,"showBlastSendingHistory") & "&blastID=#val(local.qryBlastInfo.blastID)#&mode=direct">
			<cfset local.showSendBlastOptionsLink = buildCurrentLink(arguments.event,"showSendBlastOptions") & "&blastID=#val(local.qryBlastInfo.blastID)#&mode=direct">

			<cfset local.showSendingHistorySummary = (local.qrySendingHistorySummary.recordCount or len(local.qryBlastInfo.emailDateScheduled))> 

			<cfif local.showOrgIdentitySelector>
				<cfset local.strOrgIdentitySelector = createObject("component","model.admin.common.modules.orgIdentitySelector.orgIdentitySelector").getOrgIdentitySelector(
						orgID=arguments.event.getValue('mc_siteinfo.orgID'), selectorID="blastOrgIdentityID", selectedValueID=local.qryBlastInfo.orgIdentityID, 
						allowBlankOption=false, onchangeJSFuncName="onchangeBlastOrgIdentity")>
			</cfif>

			<cfset local.canEditBlast = arguments.event.getValue('mc_adminToolInfo.myRights.editAnyBlast') or (local.qryBlastInfo.createdByMemberID eq session.cfcuser.memberdata.memberid and arguments.event.getValue('mc_adminToolInfo.myRights.editOwnBlast'))>

			<cfset local.hasBlastSchedPerms = arguments.event.getValue('mc_adminToolInfo.myRights.scheduleAnyBlast') or (local.qryBlastInfo.createdByMemberID eq session.cfcuser.memberdata.memberid and arguments.event.getValue('mc_adminToolInfo.myRights.scheduleOwnBlast'))>
			<cfset local.hasCancelSchedPerms = len(local.qryBlastInfo.emailDateScheduled) and local.canEditBlast>
			<cfset local.manageListLink =  buildLinkToTool(toolType='EmailPreferencesAdmin',mca_ta='listConsentLists')>
			
			<cfsavecontent variable="local.data">
				<cfinclude template="dsp_showBlast.cfm">
			</cfsavecontent>

		<cfelse>
			<cfsavecontent variable="local.data">
				<cfif NOT arguments.event.getValue('mc_adminToolInfo.myRights.editAnyBlast') and NOT arguments.event.getValue('mc_adminToolInfo.myRights.editOwnBlast')>
					<cfoutput>
						<h2>Not Allowed</h2>
						<p>You do not have appropriate permissions to perform this action.</p>
					</cfoutput>
				<cfelse>
					<cfif arguments.event.getValue('templateID',0) gt 0>
						<cfset local.qryTemplateDetails = local.objResourceTemplates.getResourceTemplateDetails(siteID=arguments.event.getValue('mc_siteinfo.siteID'), 
							templateID=arguments.event.getValue('templateID'), resourceType='EmailBlast')>
					</cfif>
					<cfinclude template="dsp_createBlast.cfm">
				</cfif>
			</cfsavecontent>
		</cfif>
	
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="showSendBlastOptions" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.qryBlastInfo = this.objEmailBlast.getBlastInfo(arguments.event.getValue('blastID',0))>
		<cfset local.qryAdvanceFormulas = this.objEmailBlast.getAdvanceFormulas(arguments.event.getValue('mc_siteinfo.siteid'))>

		<cfstoredproc procedure="email_previewEmailBlastSummary" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam cfsqltype="CF_SQL_INTEGER" type="in" value="#local.qryBlastInfo.blastID#">
			<cfprocparam cfsqltype="CF_SQL_TINYINT" type="in" value="0">
			<cfprocparam cfsqltype="CF_SQL_INTEGER" type="in" null="true">
			<cfprocresult name="local.qrySummaryCounts" resultset="1">
		</cfstoredproc>

		<cfset local.totalMessagesToBeSent = local.qrySummaryCounts.summaryItemCount[local.qrySummaryCounts.recordCount]>
		<cfset local.previewRecipientSummaryLink = buildCurrentLink(arguments.event,"previewRecipientSummary") & "&blastID=#val(local.qryBlastInfo.blastID)#&mode=direct">

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_showBlast_sendOptions.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="showBlastSendingHistory" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.messagesLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=emailBlastJSON&mode=stream&meth=getBlastMessages&blastID=#arguments.event.getValue('blastID',0)#">
		<cfset local.showEmailActivity = buildLinkToTool(toolType='MemberSettingsAdmin',mca_ta='showEmailActivity', navMethod='searchEmailActivity')>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_showBlast_sendingHistory.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="previewRecipientSummary" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.objVGRAdmin = CreateObject("component","model.admin.virtualGroups.virtualGroupsAdmin")>
		<cfset local.qryBlastInfo = this.objEmailBlast.getBlastInfo(arguments.event.getValue('blastID',0))>
		<cfset local.previewRecipientListLink = buildCurrentLink(arguments.event,"previewRecipientList") & "&blastID=#val(local.qryBlastInfo.blastID)#&mode=stream">
		
		<!--- if rule has no conditions, dont run rule. --->
		<cfset local.numConditions = local.objVGRAdmin.countRuleConditions(ruleID=local.qryBlastInfo.ruleID)>
		<cfif local.numConditions is 0>
			<cfset local.ruleErr = true>
		<cfelse>
			<!--- ensure rule is active and cache refreshed. show error if necessary --->
			<cfset local.msg = local.objVGRAdmin.activateRule(ruleID=local.qryBlastInfo.ruleID, forceCache=true)>
			<cfif local.msg is not 0>
				<cfset local.ruleErr = true>
			<cfelse>
				<cfset local.ruleErr = false>

				<cfstoredproc procedure="ams_getVirtualGroupRuleVerbose" datasource="#application.dsn.membercentral.dsn#">
					<cfprocparam cfsqltype="CF_SQL_INTEGER" type="in" value="#local.qryBlastInfo.ruleID#">
					<cfprocparam cfsqltype="CF_SQL_LONGVARCHAR" type="out" variable="local.ruleVerbose">
				</cfstoredproc>

				<cfif arguments.event.getValue('updateMailTypesAndTags',0) and listLen(arguments.event.getValue('blastMailTypesAndTags',''))>
					<cfset local.strEmailTypes = getEmailTypesAndTagsFromList(blastMailTypesAndTags=arguments.event.getValue('blastMailTypesAndTags',''))>
					<cfset updateBlastEmailTypesAndTags(blastID=local.qryBlastInfo.blastID, emailTypeIDList=local.strEmailTypes.emailTypeIDList, emailTagTypeIDList=local.strEmailTypes.emailTagTypeIDList)>
				</cfif>

				<cfstoredproc procedure="email_previewEmailBlastSummary" datasource="#application.dsn.membercentral.dsn#">
					<cfprocparam cfsqltype="CF_SQL_INTEGER" type="in" value="#local.qryBlastInfo.blastID#">
					<cfprocparam cfsqltype="CF_SQL_TINYINT" type="in" value="0">
					<cfprocparam cfsqltype="CF_SQL_INTEGER" type="in" null="true">
					<cfprocresult name="local.qrySummaryCounts" resultset="1">
				</cfstoredproc>
			</cfif>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfif local.ruleErr>
				<cfoutput>
				<div class="alert alert-danger">
					We were unable to identify members based on the current member filter. If you continue to see this message, contact us for assistance.
				</div>
				</cfoutput>
			<cfelse>
				<cfinclude template="dsp_showBlast_previewRecipientSummary.cfm">
			</cfif>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="previewRecipientList" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset arguments.event.paramValue('row',1)>
		<cfset local.blastID = arguments.event.getValue('blastId',0)>
		<cfset local.rowNum = arguments.event.getValue('row')>
		<cfset local.emailTypeID = arguments.event.getValue('etid',0)>

		<cfset local.qryBlastInfo = this.objEmailBlast.getBlastInfo(blastID=local.blastID)>
		<cfif local.emailTypeID gt 0>
			<cfset local.qryOrgEmailTypes = application.objOrgInfo.getOrgEmailTypes(orgID=arguments.event.getValue('mc_siteinfo.orgID'))>
			<cfquery name="local.qryBlastEmailType" dbtype="query">
				select emailType
				from [local].qryOrgEmailTypes
				where emailTypeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.emailTypeID#">
			</cfquery>
		</cfif>

		<cfstoredproc procedure="email_previewEmailBlastSummary" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam cfsqltype="CF_SQL_INTEGER" type="in" value="#local.blastID#">
			<cfprocparam cfsqltype="CF_SQL_TINYINT" type="in" value="#local.rowNum#">
			<cfif local.emailTypeID gt 0>
				<cfprocparam cfsqltype="CF_SQL_INTEGER" type="in" value="#local.emailTypeID#">
			<cfelse>
				<cfprocparam cfsqltype="CF_SQL_INTEGER" type="in" null="true">
			</cfif>
			<cfprocresult name="local.qrySummaryDetailAll" resultset="1">
		</cfstoredproc>

		<!--- limit display to max 100 --->
		<cfquery name="local.qrySummaryDetail" dbtype="query">
			SELECT TOP 100 * FROM [local].qrySummaryDetailAll
		</cfquery>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_showBlast_previewRecipientList.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="uploadAttachment" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.objDocument = CreateObject("component","model.system.platform.document")>

		<cfif len(arguments.event.getTrimValue('file',''))>
			<cftry>
				<cfset local.newFile = local.objDocument.uploadFile("form.file")>
				<cfif local.newFile.uploadComplete>
					<cfset local.objDocument.forceFileExtentionIfBlank(local.newFile)>
					<cfset local.insertResults = local.objDocument.insertDocument(siteID=arguments.event.getValue('mc_siteinfo.siteid'), 
						resourceType='ApplicationCreatedDocument', parentSiteResourceID=this.siteResourceID, sectionID=0, 
						docTitle=local.newFile.clientFile, docDesc='', author='', fileData=local.newFile, isActive=1, isVisible=true, 
						contributorMemberID=session.cfcuser.memberdata.memberid, recordedByMemberID=session.cfcuser.memberdata.memberid,
						oldFileExt=local.newFile.serverFileExt)>

					<cfquery name="local.qrySaveStoreDoc" datasource="#application.dsn.membercentral.dsn#">
						SET XACT_ABORT, NOCOUNT ON;
						BEGIN TRY

							DECLARE @rfid int, @siteID int, @orgID int, @docSRID int, @publicgroupID int;
							SET @rfid = 4;
							SET @siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteinfo.siteid')#">;
							SET @orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteinfo.orgID')#">;
							SET @docSRID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.insertResults.documentSiteResourceID#">;

							SELECT @publicgroupID = groupID
							FROM dbo.ams_groups
							WHERE orgID = @orgID
							AND groupcode = 'Public'
							AND issystemgroup = 1;

							BEGIN TRAN;
								INSERT INTO dbo.email_emailBlastDocuments (blastID, documentID)
								VALUES (
									<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('blastID')#">,
									<cfqueryparam cfsqltype="cf_sql_integer" value="#local.insertResults.documentID#">
								);

								EXEC dbo.cms_createSiteResourceRight @siteID=@siteid, @siteResourceID=@docSRID, @include=1, 
									@functionIDList=@rfid, @roleID=null, @groupID=@publicgroupID, @inheritedRightsResourceID=null, 
									@inheritedRightsFunctionID=null;
							COMMIT TRAN;

						END TRY
						BEGIN CATCH
							IF @@trancount > 0 ROLLBACK TRANSACTION;
							EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
						END CATCH
					</cfquery>
				</cfif>
			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch)>
				<cffile action="delete" file="#local.newFile.ServerDirectory#/#local.newFile.ServerFile#">
			</cfcatch>
			</cftry>
		</cfif>

		<cfset local.data = "">
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="deleteAllBlastAttachments" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="blastID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.objDocument = CreateObject("component","model.system.platform.document")>

		<cftry>
			<cfif not hasEditBlastRights(siteID=arguments.mcproxy_siteID, blastID=arguments.blastID)>
				<cfthrow message="invalid request">
			</cfif>

			<cfset local.qryBlastAttachments = CreateObject('component','emailBlast').getEmailBlastAttachments(blastID=arguments.blastID)>

			<cfif local.qryBlastAttachments.recordCount>
				<cfloop query="local.qryBlastAttachments">
					<cfset local.objDocument.deleteDocument(siteID=arguments.mcproxy_siteID, documentID=local.qryBlastAttachments.documentID)>
				</cfloop>

				<cfquery name="local.qryDeleteBlastAttachments" datasource="#application.dsn.membercentral.dsn#">
					DELETE FROM dbo.email_emailBlastDocuments
					WHERE blastID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.blastID#">
					AND documentID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="true" value="#valueList(local.qryBlastAttachments.documentID)#">)
				</cfquery>
			</cfif>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="deleteAttachment" access="public" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="blastID" type="numeric" required="true">
		<cfargument name="documentID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfif not hasEditBlastRights(siteID=arguments.mcproxy_siteID, blastID=arguments.blastID)>
				<cfthrow message="invalid request">
			</cfif>

			<cfset CreateObject("component","model.system.platform.document").deleteDocument(siteID=arguments.mcproxy_siteID, documentID=arguments.documentID)>

			<cfquery datasource="#application.dsn.membercentral.dsn#">
				DELETE FROM dbo.email_emailBlastDocuments
				WHERE blastID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.blastID#">
				AND documentID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.documentID#">
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="showBlastMessagePreview" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.blastID = val(arguments.event.getValue('blastID',0))>

		<cfset local.blastContentRawHTML = "">
		<cfif len(arguments.event.getValue('blastContent',''))>
			<cfset local.blastContentRawHTML = arguments.event.getValue('blastContent','')>
		<cfelseif arguments.event.getValue('editorCode','CKEditor') eq "BeePlugin" and len(arguments.event.getValue('jsonTemplateStringified',''))>
			<cfset local.contentHTMLStruct = createObject("component","sitecomponents.common.javascript.beeplugin.beeplugin").callBeePluginContentAPI(apiAction="html", jsonTemplateStringified=arguments.event.getValue('jsonTemplateStringified',''))>
			<cfset local.blastContentRawHTML = (DeserializeJSON(local.contentHTMLStruct)?.html ?:'')>
		</cfif>

		<cfif val(arguments.event.getValue('incAttachListAsAttachment',0))>
			<cfset local.downloadEmailBlastListAsAttachmentLink = "#buildCurrentLink(arguments.event,"downloadEmailBlastListAsAttachment")#&blastID=#local.blastID#&mode=stream">
		</cfif>

		<cfif arguments.event.getValue('updateMailTypesAndTags',0) and listLen(arguments.event.getValue('blastMailTypesAndTags',''))>
			<cfset local.strEmailTypes = getEmailTypesAndTagsFromList(blastMailTypesAndTags=arguments.event.getValue('blastMailTypesAndTags',''))>
			<cfset updateBlastEmailTypesAndTags(blastID=local.blastID, emailTypeIDList=local.strEmailTypes.emailTypeIDList, emailTagTypeIDList=local.strEmailTypes.emailTagTypeIDList)>
		</cfif>

		<cfset local.memberStr = getRandomRecipientForMessagePreview(blastID=local.blastID)>
		<cfset local.arrAttachments = getBlastAttachmentsArray(attachmentList=arguments.event.getValue('attachmentsList',''))>

		<cfset local.qryBlastInfo = this.objEmailBlast.getBlastInfo(blastID=local.blastID)>
		<cfset arguments.event.setValue('fromEmail', this.objEmailBlast.getValidFromEmailByMessageType(
			siteID=arguments.event.getValue('mc_siteinfo.siteid'),
			messageTypeCode='EMAILBLAST',
			fromEmail=local.qryBlastInfo.fromEmail))>

		<cfset local.previewMessageResult = getPreviewedMessageByData(
			siteCode=arguments.event.getValue('mc_siteinfo.sitecode'),
			memberID=local.memberStr.memberID,
			emailTypeID=local.memberStr.emailTypeID,
			rawContent=local.blastContentRawHTML,
			footerContentID=val(arguments.event.getValue('footerContentID',0)),
			contentTitle=arguments.event.getValue('subject',''),
			utmAutoAppend=arguments.event.getValue('utmAutoAppend',0),
			utmCampaign=arguments.event.getValue('utmCampaign',''),
			includeAttachmentListAsAttachment=val(arguments.event.getValue('incAttachListAsAttachment',0)),
			orgIdentityID=arguments.event.getValue('blastOrgIdentityID',0),
			arrAttachments=local.arrAttachments,
			recipientEmail=local.memberStr.email
		)>
		<cfset local.messageStr = local.previewMessageResult.msg>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_showBlastMessagePreview.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="showBlastMessagePreviewByBlastID" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.blastID = val(arguments.event.getValue('blastID',0))>
		<cfset local.qryBlastInfo = this.objEmailBlast.getBlastInfo(blastID=local.blastID)>

		<cfif local.qryBlastInfo.includeAttachmentListAsAttachment>
			<cfset local.downloadEmailBlastListAsAttachmentLink = "#buildCurrentLink(arguments.event,"downloadEmailBlastListAsAttachment")#&blastID=#local.blastID#&mode=stream">
		</cfif>
		
		<cfset local.memberStr = getRandomRecipientForMessagePreview(blastID=local.blastID)>
		<cfset local.arrAttachments = getBlastAttachmentsArray(blastID=local.blastID)>

		<cfset local.previewMessageResult = getPreviewedMessageByData(
			siteCode=arguments.event.getValue('mc_siteinfo.sitecode'),
			memberID=local.memberStr.memberID,
			emailTypeID=local.memberStr.emailTypeID,
			rawContent=local.qryBlastInfo.rawContent,
			footerContentID=local.qryBlastInfo.footerContentID,
			contentTitle=local.qryBlastInfo.contentTitle,
			utmAutoAppend=local.qryBlastInfo.utm_autoappend,
			utmCampaign=local.qryBlastInfo.utm_campaign,
			includeAttachmentListAsAttachment=local.qryBlastInfo.includeAttachmentListAsAttachment,
			orgIdentityID=val(local.qryBlastInfo.orgIdentityID),
			arrAttachments=local.arrAttachments,
			recipientEmail=local.memberStr.email
		)>
		<cfset local.messageStr = local.previewMessageResult.msg>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_listBlasts_previewMessage.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="getRandomRecipientForMessagePreview" access="public" output="false" returntype="struct">
		<cfargument name="blastID" type="numeric" required="true">

		<cfset local.qryBlastInfo = this.objEmailBlast.getBlastInfo(blastID=arguments.blastID)>

		<!--- if rule has no conditions, dont run rule. --->
		<cfset local.objVGRAdmin = CreateObject("component","model.admin.virtualGroups.virtualGroupsAdmin")>
		<cfset local.numConditions = local.objVGRAdmin.countRuleConditions(ruleID=local.qryBlastInfo.ruleID)>
		<cfif local.numConditions GT 0>
			<!--- ensure rule is active and cache refreshed. --->
			<cfset local.msg = local.objVGRAdmin.activateRule(ruleID=local.qryBlastInfo.ruleID, forceCache=true)>
		</cfif>

		<cfstoredproc procedure="email_previewEmailBlastSummary" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam cfsqltype="CF_SQL_INTEGER" type="in" value="#arguments.blastID#">
			<cfprocparam cfsqltype="CF_SQL_TINYINT" type="in" value="6">
			<cfprocparam cfsqltype="CF_SQL_INTEGER" type="in" null="true">
			<cfprocresult name="local.qryRecipients" resultset="1">
		</cfstoredproc>

		<cfif local.qryRecipients.recordCount>
			<cfset local.randomRecipientStr = QueryGetRow(local.qryRecipients, randRange(1,local.qryRecipients.recordCount))>
			<cfset local.memberStr = {
				memberID = local.randomRecipientStr.memberID,
				memberDisplayName = "#local.randomRecipientStr.firstName# #local.randomRecipientStr.lastName#",
				emailTypeID = local.randomRecipientStr.emailTypeID,
				email = local.randomRecipientStr.memberEmail }>
		<cfelse>
			<cfset local.memberStr = {
				memberID = session.cfcuser.memberdata.memberID,
				memberDisplayName = "#session.cfcuser.memberdata.firstName# #session.cfcuser.memberdata.lastName#",
				emailTypeID = 0,
				email = session.cfcuser.memberdata.email }>
		</cfif>

		<cfset local.memberStr.totalRecipientsCount = local.qryRecipients.recordCount>

		<cfreturn local.memberStr>
	</cffunction>

	<cffunction name="getBlastAttachmentsArray" access="public" output="false" returntype="array">
		<cfargument name="blastID" type="numeric" required="false" default="0">
		<cfargument name="attachmentList" type="string" required="false" default="" hint="parsed list to override attachment titles">
		
		<cfset var local = structNew()>
		<cfset local.arrAttachments = []>

		<cfif len(arguments.attachmentList)>
			<cfloop list="#arguments.attachmentList#" item="local.thisAttachment">
				<cfset local.documentID = getToken(local.thisAttachment,1,"|")>
				<cfset local.docTitle = getToken(local.thisAttachment,2,"|")>
				<cfif val(local.documentID) AND len(local.docTitle)>
					<cfset arrayAppend(local.arrAttachments, { documentID = local.documentID, fileName = local.docTitle })>
				</cfif>
			</cfloop>
		<cfelseif arguments.blastID gt 0>
			<cfset local.qryBlastAttachments = CreateObject('component','emailBlast').getEmailBlastAttachments(blastID=arguments.blastID)>
			<cfif local.qryBlastAttachments.recordCount>
				<cfquery name="local.arrAttachments" dbtype="query" returntype="array">
					SELECT documentID, docTitle AS fileName
					FROM [local].qryBlastAttachments
				</cfquery>
			</cfif>
		</cfif>

		<cfreturn local.arrAttachments>
	</cffunction>

	<cffunction name="getPreviewedMessage" access="public" output="false" returntype="struct">
		<cfargument name="blastID" type="numeric" required="true">
		<cfargument name="mid" type="numeric" required="true">
		<cfargument name="etid" type="numeric" required="true">
		<cfargument name="overrideRawContent" type="string" required="false" default="">
		
		<cfset var local = structNew()>
		<cfset local.objEmailBlast = CreateObject('component','emailBlast')>

		<cfset local.subject = getSubject()>
		<cfset local.qryBlastInfo = local.objEmailBlast.getBlastInfo(arguments.blastId)>

		<cfset local.arrAttachments = []>
		<cfif local.qryBlastInfo.includeAttachmentListAsAttachment>
			<cfset local.arrAttachments = getBlastAttachmentsArray(blastID=arguments.blastID)>
		</cfif>

		<cfset local.rawContent = len(arguments.overrideRawContent) GT 0 ? arguments.overrideRawContent : local.qryBlastInfo.rawContent>

		<cfreturn getPreviewedMessageByData(
			siteCode=local.qryBlastInfo.siteCode,
			memberID=arguments.mid,
			emailTypeID=arguments.etid,
			rawContent=local.rawContent,
			footerContentID=local.qryBlastInfo.footerContentID,
			contentTitle=local.qryBlastInfo.contentTitle,
			utmAutoAppend=local.qryBlastInfo.utm_autoappend,
			utmCampaign=local.qryBlastInfo.utm_campaign,
			includeAttachmentListAsAttachment=local.qryBlastInfo.includeAttachmentListAsAttachment,
			orgIdentityID=local.qryBlastInfo.orgIdentityID,
			arrAttachments=local.arrAttachments
		)>
	</cffunction>

	<cffunction name="getPreviewedMessageByData" access="private" output="false" returntype="struct">
		<cfargument name="siteCode" type="string" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="emailTypeID" type="numeric" required="true">
		<cfargument name="rawContent" type="string" required="true">
		<cfargument name="footerContentID" type="numeric" required="true">
		<cfargument name="contentTitle" type="string" required="true">
		<cfargument name="utmAutoAppend" type="string" required="true">
		<cfargument name="utmCampaign" type="string" required="true">
		<cfargument name="includeAttachmentListAsAttachment" type="boolean" required="true">
		<cfargument name="orgIdentityID" type="numeric" required="true">
		<cfargument name="arrAttachments" type="array" required="false" default="[]">
		<cfargument name="recipientEmail" type="string" required="true" default="">
		
		<cfset var local = structNew()>
		<cfset local.mc_siteInfo = duplicate(application.objSiteInfo.getSiteInfo(arguments.siteCode))>

		<cfset local.subject = getSubject()>
		<cfset local.fullHtmlMessage = arguments.rawContent>
		<cfif not application.objEmailWrapper.isCompleteHTMLDocument(htmlcontent=local.fullHtmlMessage)>
			<cfset local.fullHtmlMessage = application.objEmailWrapper.cleanupHTML(htmlcontent=local.fullHtmlMessage)>
		</cfif>

		<cfif arguments.includeAttachmentListAsAttachment AND arrayLen(arguments.arrAttachments)>
			<cfset local.attachmentFileName = "Attachments - Click to Open.html">
		</cfif>
		<cfif arguments.footerContentID>
			<cfset local.qryFooter = CreateObject('component','emailBlast').getFooter(siteID=local.mc_siteInfo.siteID, contentID=arguments.footerContentID)>
			<cfif len(trim(local.qryFooter.rawContent))>
				<cfset local.fullHtmlMessage = application.objEmailWrapper.injectFooter(htmlcontent=local.fullHtmlMessage,footercontent="<hr /><div>#local.qryFooter.rawContent#</div>")>
			</cfif>
		</cfif>
		<cfset local.fullHtmlMessage = appendOptOutFooterIfNoMergeTags(fullHtmlMessage=local.fullHtmlMessage)>
		<cfset local.messageSubject = replacenocase(local.subject,"@@contentTitle@@",arguments.contentTitle,"all")>

		<cfif arguments.utmAutoAppend>
			<cfset local.utmCampaign = len(arguments.utmCampaign) ? arguments.utmCampaign : local.messageSubject>
			<cfset local.fullHtmlMessage = application.objEmailWrapper.appendUTMCodesToLinks(
				htmlcontent=local.fullHtmlMessage,
				utm_campaign=local.utmCampaign,
				utm_source="MemberCentral EmailBlast",
				utm_medium="email",
				utm_content="#dateformat(now(),"yyyy-mm-dd")# #local.messageSubject#"
			)>
		</cfif>
		<cfset local.fullHtmlMessage = application.objEmailWrapper.disableClickTrackingForSpecificLinks(htmlcontent=local.fullHtmlMessage)>
		
		<cfif application.MCEnvironment eq "production">
			<cfset local.thisHostname = local.mc_siteInfo.mainhostname>
		<cfelse>
			<cfset local.thisHostname = application.objPlatform.getCurrentHostname()>
		</cfif>

		<cfset local.memberInfo = application.objMember.getMemberInfo(memberID=arguments.memberID)>
		<cfset local.qryMemberFields = application.objMergeCodes.getMergeViewFields(orgID=local.mc_siteInfo.orgID, memberID=local.memberInfo.memberID, content="#local.fullHtmlMessage##local.messageSubject#")>
		<cfif arguments.emailTypeID GT 0>
			<cfset local.qryMemberEmails = application.objMember.getMemberEmails(memberid=local.memberInfo.memberID, emailTypeID=arguments.emailTypeID, orgID=local.mc_siteInfo.orgID)>
			<cfset local.recipientEmail = local.qryMemberEmails.email>
		<cfelse>
			<cfset local.recipientEmail = arguments.recipientEmail>
		</cfif>
		
		<cfset local.tempMemberData = { memberID=local.memberInfo.memberID, firstName=local.memberInfo.FirstName, middleName=local.memberInfo.MiddleName,
			lastName=local.memberInfo.LastName, company=local.memberInfo.Company, suffix=local.memberInfo.Suffix, prefix=local.memberInfo.Prefix,
			professionalSuffix=local.memberInfo.professionalSuffix, membernumber=local.memberInfo.membernumber, orgcode=local.memberInfo.orgcode,
			siteID=local.mc_siteInfo.siteID, hostname=local.thisHostname, useRemoteLogin=local.mc_siteInfo.useRemoteLogin, recipientEmail=local.recipientEmail }>

		<cfloop array="#getMetaData(local.qryMemberFields)#" index="local.thisColumn">
			<cfif NOT StructKeyExists(local.tempMemberData,local.thisColumn.Name)>
				<cfset local.thisTempVal = local.qryMemberFields[local.thisColumn.Name][1]>
				<cfset structInsert(local.tempMemberData,local.thisColumn.Name,local.thisTempVal,true)>
			</cfif>
		</cfloop>
		
		<cfset local.strContentArgs = { content=local.fullHtmlMessage, memberdata=local.tempMemberData, orgcode=local.mc_siteInfo.orgcode, 
										sitecode=local.mc_siteInfo.siteCode, orgIdentityID=arguments.orgIdentityID, outputShortCodeDataToBrowser=false }>
		<cfset local.strSubjectArgs = { content=local.messageSubject, memberdata=local.tempMemberData, orgcode=local.mc_siteInfo.orgcode, 
										sitecode=local.mc_siteInfo.siteCode, orgIdentityID=arguments.orgIdentityID, outputShortCodeDataToBrowser=false }>

		<cfset local.strMergedContent = application.objMergeCodes.processMergeCodes(argumentcollection=local.strContentArgs)>
		<cfset local.strMergedSubjectContent = application.objMergeCodes.processMergeCodes(argumentcollection=local.strSubjectArgs)>

		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.msg = structNew()>
		<cfset local.returnStruct.msg.toname = "#local.memberInfo.firstName# #local.memberInfo.lastName#">
		<cfset local.returnStruct.msg.toemail = local.recipientEmail>
		<cfset local.returnStruct.msg.subject = local.strMergedSubjectContent.content> 
		<cfset local.returnStruct.msg.body = local.strMergedContent.content>
		<cfif arguments.includeAttachmentListAsAttachment AND arrayLen(arguments.arrAttachments)>
			<cfset local.returnStruct.msg.attachmentfilename = local.attachmentFileName>
		</cfif>
		
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="appendOptOutFooterIfNoMergeTags" access="public" output="false" returntype="string">
		<cfargument name="fullHtmlMessage" type="string" required="true">

		<cfif NOT reFindNoCase("\[\[(emailOptOutStatement|emailOptOutURL|consentListManagementURL)\]\]", arguments.fullHtmlMessage)>
			<cfset local.optOutFooter = '<div style="padding:5px;background-color:##fff;text-align:center;"><a href="[[emailOptOutURL]]">Manage Preferences</a></div>'>
			<cfreturn application.objEmailWrapper.injectFooter(htmlcontent=arguments.fullHtmlMessage, footercontent=local.optOutFooter)>
		<cfelse>
			<cfreturn arguments.fullHtmlMessage>
		</cfif>
	</cffunction>

	<cffunction name="checkMessageHasNoOptOutMergeTags" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="blastContent" type="string" required="true">
		<cfargument name="footerContentID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { "success" = false }>

		<cfset local.msgHTML = arguments.blastContent>
		<cfif arguments.footerContentID gt 0>
			<cfset local.qryFooter = CreateObject('component','emailBlast').getFooter(siteID=arguments.mcproxy_siteID, contentID=arguments.footerContentID)>
			<cfset local.msgHTML &= trim(local.qryFooter.rawContent)>
		</cfif>
		<cfif NOT reFindNoCase("\[\[(emailOptOutStatement|emailOptOutURL|consentListManagementURL)\]\]", local.msgHTML)>
			<cfset local.returnStruct["success"] = true>
		</cfif>
	
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="downloadEmailBlastListAsAttachment" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.objEmailBlast = CreateObject('component','emailBlast')>
		<cfset local.qryBlastInfo = local.objEmailBlast.getBlastInfo(blastID=arguments.event.getValue('blastID',0))>
		<cfset local.data = "">

		<cfif local.qryBlastInfo.includeAttachmentListAsAttachment or val(arguments.event.getValue('incAttachListAsAttachment',0))>

			<cfset local.arrAttachments = []>
			<cfif len(arguments.event.getValue('attachmentsList',''))>
				<cfset local.arrAttachments = getBlastAttachmentsArray(attachmentList=arguments.event.getValue('attachmentsList'))>
			<cfelse>
				<cfset local.arrAttachments = getBlastAttachmentsArray(blastID=local.qryBlastInfo.blastID)>
			</cfif>

			<cfset local.strAttachment = application.objEmailWrapper.getAttachmentsListAsAttachment(siteCode=local.qryBlastInfo.sitecode, arrAttachments=local.arrAttachments,
											fileNameWithNoExt="Attachments - Click to Open")>

			<cfset application.objDocDownload.waitForFileExists(sourceFilePath=local.strAttachment.filePath)>
			<cfset local.docResult = application.objDocDownload.doDownloadDocument(sourceFilePath=local.strAttachment.filePath, displayName=local.strAttachment.displayName, deleteSourceFile=1)>
			<cfif NOT local.docResult>
				<cflocation url="/?pg=404" addtoken="false">
			</cfif>	
		<cfelse>
			<cfsavecontent variable="local.data">
				<cfoutput>
					<br/><div><b>The attachment is not available. Try again.</b></div>
				</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="getSubject" access="public" output="false" returntype="string">
		<cfreturn "@@contentTitle@@">
	</cffunction>

	<cffunction name="archiveMessage" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="messageID" type="numeric" required="true">
		
		<cfset var local = structNew()>
		
		<cfquery name="local.qryUpdate" datasource="#application.dsn.platformMail.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				DECLARE @siteID int, @messageID int, @queueStatusID int, @scheduledStatusID int, @cancelledStatusID int, @nowDate datetime;
				SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">;
				SET @messageID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.messageID#">;
				SET @nowDate = GETDATE();

				SELECT @queueStatusID = statusID FROM dbo.email_statuses WHERE statusCode = 'Q';
				SELECT @scheduledStatusID = statusID FROM dbo.email_statuses WHERE statusCode = 'scheduled';
				SELECT @cancelledStatusID = statusID FROM dbo.email_statuses WHERE statusCode = 'C';

				BEGIN TRAN;
					UPDATE dbo.email_messages
					SET status = 'D'
					WHERE messageID = @messageID
					AND siteID = @siteID;

					UPDATE dbo.email_messageRecipientHistory
					SET emailStatusID = @cancelledStatusID, 
						dateLastUpdated = @nowDate
					WHERE siteID = @siteID 
					and messageID = @messageID
					AND emailStatusID in (@queueStatusID,@scheduledStatusID);
				COMMIT TRAN;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>

		<cfreturn local.returnStruct>
	</cffunction>
	
	<cffunction name="cancelMessage" access="public" output="false" returntype="struct">
		<cfargument name="messageID" type="numeric" required="true">
		
		<cfset var local = structNew()>
		
		<cfquery name="local.qryUpdate" datasource="#application.dsn.platformMail.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				DECLARE @messageID int, @queueStatusID int, @scheduledStatusID int, @loadingStatusID int, @cancelledStatusID int, @nowDate datetime;
				SET @messageID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.messageID#">;
				SET @nowDate = GETDATE();


				SELECT @queueStatusID = statusID FROM dbo.email_statuses WHERE statusCode = 'Q';
				SELECT @loadingStatusID = statusID FROM dbo.email_statuses WHERE statusCode = 'I';
				SELECT @scheduledStatusID = statusID FROM dbo.email_statuses WHERE statusCode = 'scheduled';
				SELECT @cancelledStatusID = statusID FROM dbo.email_statuses WHERE statusCode = 'C';

				BEGIN TRAN;
					UPDATE dbo.email_messageRecipientHistory
					SET emailStatusID = @cancelledStatusID, 
						dateLastUpdated = @nowDate
					WHERE messageID = @messageID
					AND emailStatusID in (@queueStatusID,@scheduledStatusID,@loadingStatusID);
				COMMIT TRAN;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>

		<cfreturn local.returnStruct>
	</cffunction>
	
	<cffunction name="copyBlast" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any" required="yes">
		
		<cfset var local = structNew()>

		<cfstoredproc procedure="email_copyEmailBlast" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam cfsqltype="CF_SQL_INTEGER" type="in" value="#arguments.event.getValue('blastId',0)#">
			<cfprocparam cfsqltype="CF_SQL_INTEGER" type="in" value="#session.cfcuser.memberdata.memberID#">
			<cfprocparam cfsqltype="CF_SQL_INTEGER" type="out" variable="local.newBlastID">
		</cfstoredproc>

		<cflocation url="#this.link.showBlast#&blastID=#local.newBlastID#" addtoken="no">
	</cffunction>
		
	<cffunction name="deleteBlast" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="blastID" type="numeric" required="yes">
		
		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>

		<cfset local.emailBlastSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='EmailBlast',siteID=arguments.mcproxy_siteID)>
		<cfset local.tmpRights = buildRightAssignments(siteResourceID=local.emailBlastSRID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.mcproxy_siteID)>
		<cfset local.qryBlastInfo = CreateObject('component','emailBlast').getBlastInfo(blastID=arguments.blastID)>
		
		<cftry>
			<cfif NOT (local.tmpRights.deleteAnyBlast OR (local.tmpRights.deleteOwnBlast AND local.qryBlastInfo.createdByMemberID EQ session.cfcuser.memberdata.memberID))>
				<cfthrow message="invalid request">
			</cfif>

			<cfstoredproc procedure="email_deleteEmailBlast" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam cfsqltype="CF_SQL_INTEGER" type="in" value="#arguments.blastID#">
				<cfprocparam cfsqltype="CF_SQL_INTEGER" type="in" value="#session.cfcuser.memberdata.memberID#">
			</cfstoredproc>

			<cfset local.returnStruct.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>
		
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="hasEditBlastRights" access="private" output="false" returntype="boolean">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="blastID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.siteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='EmailBlast', siteID=arguments.siteID)>
		<cfset local.tmpRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.siteResourceID, memberID=session.cfcuser.memberData.memberID, siteID=arguments.siteID)>
		<cfset local.qryBlastInfo = CreateObject('component','emailBlast').getBlastInfo(blastID=arguments.blastID)>

		<cfreturn local.tmpRights.editAnyBlast or (local.qryBlastInfo.createdByMemberID eq session.cfcuser.memberdata.memberid and local.tmpRights.editOwnBlast)>
	</cffunction>

	<cffunction name="saveNewBlast" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.objResourceTemplates = createObject("component","model.admin.common.modules.resourceTemplates.resourceTemplate")>

		<cfset local.qryTemplateEditors = local.objResourceTemplates.getTemplateEditors(excludeEditorCode='AceEditor')>
		<cfset local.BeePluginEditorID = QueryFilter(local.qryTemplateEditors, function(thisRow) { return arguments.thisRow.editorCode EQ 'BeePlugin'; }).editorID>

		<cftry>
			<cfset local.baseHTML = application.objEmailWrapper.getBaseTemplateHTML(sitecode=arguments.event.getValue('mc_siteinfo.sitecode'), emailTitle='')>
			<cfset local.baseHTML = local.baseHTML.replaceNoCase("<!--BodyContent-->","","all")>
			<cfset local.blastSupportingContent = "">
			<cfset local.saveContentScreenShot = false>

			<cfif arguments.event.getValue('blastTemplateID',0) GT 0>
				<cfset local.qryTemplateDetails = local.objResourceTemplates.getResourceTemplateDetails(siteID=arguments.event.getValue('mc_siteinfo.siteID'), 
						templateID=arguments.event.getValue('blastTemplateID'), resourceType='EmailBlast')>
				<cfset local.baseHTML = local.qryTemplateDetails.templateContent>
				<cfset local.saveContentScreenShot = len(local.baseHTML) GT 0>
				
				<cfif local.qryTemplateDetails.editorID EQ local.BeePluginEditorID>
					<cfset local.blastSupportingContent = local.objResourceTemplates.getResourceTemplateSupportingContent(templateID=arguments.event.getValue('blastTemplateID'), 
						resourceType='EmailBlast').templateSupportingContent>
				</cfif>
				<cfset arguments.event.setValue('blastEditorID',local.qryTemplateDetails.editorID)>
			</cfif>

			<cfquery name="local.qryCreateBlast" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @blastID int, @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteID')#">, 
						@enteredByMemberID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">,
						@deliveryReportEmail varchar(200) = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#session.cfcuser.memberdata.email#">,
						@appCreatedContentResourceTypeID int, @supportingContentID int, @supportingContentSiteResourceID int;

					SELECT @appCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedContent');

					BEGIN TRAN;
						EXEC dbo.email_createEmailBlast @siteID=@siteID, @memberid=@enteredByMemberID, @deliveryReportEmail=@deliveryReportEmail,
							@blastName=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('frmBN')#">,
							@categoryID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('sCategoryID')#">, 
							@subCategoryID=
								<cfif NOT val(arguments.event.getValue('sSubCategoryID',0))>
									NULL
								<cfelse>
									<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('sSubCategoryID')#">
								</cfif>, 
							@editorID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('blastEditorID')#">,
							@baseHTML=<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#local.baseHTML#">,
							@blastID=@blastID OUTPUT;
						
						<!--- Blast Supporting Content (BeePlugin JSON Template) --->
						<cfif len(local.blastSupportingContent)>
							DECLARE @blastSupportingContent varchar(max) = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#local.blastSupportingContent#">;
							
							EXEC dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID, @siteResourceStatusID=1, 
								@isHTML=0, @languageID=1, @isActive=1, @contentTitle=NULL, @contentDesc=NULL, @rawContent=@blastSupportingContent, @memberID=@enteredByMemberID,
								@contentID=@supportingContentID OUTPUT, @siteResourceID=@supportingContentSiteResourceID OUTPUT;

							UPDATE dbo.email_emailBlasts 
							SET supportingContentID = @supportingContentID
							WHERE blastID = @blastID;
						</cfif>

						<cfif local.saveContentScreenShot>
							<cfset local.MCSiteID = application.objSiteInfo.getSiteIDFromSiteCode(siteCode='MC')>
							<cfset local.featureImageConfigID = createObject("component","model.admin.common.modules.featuredImages.featuredImages").getFeaturedImageConfigIDFromCode(
									siteID=local.MCSiteID, featuredImageConfigCode='EmailScreenshot', isPlatformWide=1)>

							DECLARE @contentVersionID int, @featureImageConfigID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.featureImageConfigID#">;

							select top 1 @contentVersionID = cv.contentVersionID
							from dbo.email_emailBlasts as eb
							inner join dbo.cms_content as c on c.contentID = eb.contentID
							inner join dbo.cms_contentLanguages as cl on c.contentID = cl.contentID and cl.languageID = 1
							inner join dbo.cms_contentVersions as cv on cv.contentLanguageID = cl.contentLanguageID and cv.isActive = 1
							where eb.blastID = @blastID;

							EXEC dbo.cms_populateScreenshotsQueue @siteID=@siteID, @contentVersionID=@contentVersionID, @featureImageConfigID=@featureImageConfigID,
								@referenceType='emailBlastScreenshot', @referenceID=@blastID, @viewportWidth=414, @viewportHeight=828, @deviceScaleFactor=2, @fullPage=1,
								@enteredByMemberID=@enteredByMemberID;
						</cfif>
					COMMIT TRAN;

					SELECT @blastID AS blastID;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
			
			<cfset local.blastID = local.qryCreateBlast.blastID>

		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.blastID = 0>
		</cfcatch>
		</cftry>
		
		<cfif local.blastID gt 0>
			<cflocation url="#this.link.showBlast#&blastID=#local.blastID#" addtoken="no">
		<cfelse>
			<cflocation url="#this.link.showBlast#&err=1" addtoken="no">
		</cfif>
	</cffunction>

	<cffunction name="saveBlast" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.returnStruct = { 'success':true, 'msg':'' }>
		<cfset local.qryBlastInfo = this.objEmailBlast.getBlastInfo(arguments.event.getValue('blastId',0))>
		
		<cfset local.rc = arguments.event.getCollection()>

		<cfif len(arguments.event.getTrimValue('replyTo','')) AND NOT isValid("regex",arguments.event.getTrimValue('replyTo'),application.regEx.email)>
			<cfset arguments.event.setValue('replyTo','')>
		</cfif>
		<cfif len(arguments.event.getTrimValue('deliveryRcpt','')) AND NOT isValid("regex",arguments.event.getTrimValue('deliveryRcpt'),application.regEx.email)>
			<cfset arguments.event.setValue('deliveryRcpt','')>
		</cfif>

		<cfif NOT len(arguments.event.getTrimValue('fromName','')) 
				or NOT len(arguments.event.getTrimValue('replyTo',''))
				or NOT len(arguments.event.getTrimValue('subject',''))>

			<cfset local.returnStruct["success"] = false>
			<cfset local.returnStruct["msg"] = 'Empty Name / Reply-To Email / Email Subject.'>
			<cfreturn returnAppStruct(serializeJSON(local.returnStruct),'echo')>
		</cfif>

		<cftry>
			<cfset local.qryOrgIdentities = application.objOrgInfo.getOrgIdentities(orgID=arguments.event.getValue('mc_siteinfo.orgID'))>
			<cfset local.showOrgIdentitySelector = local.qryOrgIdentities.recordCount>

			<cfset local.qryOrgOptOutLists = createObject("component","model.admin.emailPreferences.emailPreferences").getConsentLists(orgID=arguments.event.getValue('mc_siteInfo.orgID'), mode="Opt-Out")>
			<cfset local.showOptOutsPerm = local.qryOrgOptOutLists.recordCount>

			<cfset local.blastContent = application.objResourceRenderer.qualifyAllLinks(content=arguments.event.getTrimValue('blastContent'), siteid=arguments.event.getValue('mc_siteinfo.siteid'))>
			<cfset local.previewText = arguments.event.getTrimValue('previewText','')>
			<cfif len(local.previewText)>
				<cfset local.blastContent = application.objEmailWrapper.injectPreviewText(htmlcontent=local.blastContent, preheadertext=local.previewText)>
			</cfif>
			<cfset local.includeAttachmentListAsAttachment = val(arguments.event.getValue('incAttachListAsAttachment',0))>
			<cfset local.utmAutoAppend = arguments.event.getValue('utmAutoAppend',0)>
			<cfset local.utmCampaign = arguments.event.getTrimValue('utmCampaign','')>
			<cfset local.consentListIDs = "">
			<cfset local.chkIsPrimary = "">
			<cfif local.showOptOutsPerm>
				<cfset local.consentListIDs = arguments.event.getValue('consentListID','')>
				<cfset local.chkIsPrimary = arguments.event.getValue('chkIsPrimary','')>
				<cfif not len(local.chkIsPrimary) and listLen(local.consentListIDs)>
					<cfset local.chkIsPrimary = listFirst(local.consentListIDs)>
				</cfif>
			</cfif>

			<cfset local.MCSiteID = application.objSiteInfo.getSiteIDFromSiteCode(siteCode='MC')>
			<cfset local.featureImageConfigID = createObject("component","model.admin.common.modules.featuredImages.featuredImages").getFeaturedImageConfigIDFromCode(
					siteID=local.MCSiteID, featuredImageConfigCode='EmailScreenshot', isPlatformWide=1)>

			<!--- activate rule version --->
			<cfset local.blastRuleID = local.qryBlastInfo.ruleID>
			<cfif val(arguments.event.getValue('useRuleVersionID_#local.blastRuleID#',0))>
				<cfset CreateObject("component","model.admin.virtualGroups.virtualGroupsAdmin").acceptRuleVersion(ruleID=local.blastRuleID, ruleVersionID=arguments.event.getValue('useRuleVersionID_#local.blastRuleID#'))>
			</cfif>

			<cfquery name="local.qryUpdateBlast" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					IF OBJECT_ID('tempdb..##tmpdocTitles') IS NOT NULL
						DROP TABLE ##tmpdocTitles;
					CREATE TABLE ##tmpdocTitles (documentID int PRIMARY KEY, docTitle varchar(500));

					DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryBlastInfo.siteID#">,
						@blastID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryBlastInfo.blastID#">,
						@enteredByMemberID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">,
						@appCreatedContentResourceTypeID int, @supportingContentID int,
						@supportingContentSiteResourceID int, @contentVersionID int, @featureImageConfigID int;

					SET @featureImageConfigID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.featureImageConfigID#">;

					SELECT @appCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedContent');

					<cfloop collection="#local.rc#" item="local.thisKey">
						<cfif FindNoCase("docTitle_",local.thisKey)>
							<cfset local.documentID = trim(local.thisKey.split("_")[2])>
							<cfset local.docTitle = trim(local.rc[local.thisKey])>
							
							<cfif val(local.documentID) AND len(local.docTitle)>
								INSERT INTO ##tmpdocTitles (documentID, docTitle)
								VALUES (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.documentID#">,
										<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.docTitle#">);
							</cfif>
						</cfif>
					</cfloop>

					BEGIN TRAN;
						EXEC dbo.cms_updateContent @contentID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryBlastInfo.contentID#">,
							@languageID=1, @isHTML=1, @contentTitle=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getTrimValue('subject')#">,
							@contentDesc=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.previewText#">,
							@rawcontent=<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#local.blastContent#">,
							@memberID=@enteredByMemberID;

						UPDATE dbo.email_emailBlasts 
						SET 
							blastName = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('frmBN','')#">,
							categoryID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('sCategoryID')#">,
							<cfif arguments.event.getValue('sSubCategoryID') gt 0>
								subcategoryID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('sSubCategoryID')#">,
							<cfelse>
								subcategoryID = null,
							</cfif>
							fromName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getTrimValue('fromName')#">,
							fromEmail = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('mc_siteinfo.networkEmailFrom')#">,
							replyTo = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getTrimValue('replyTo')#">,
							footerContentID = NULLIF(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(arguments.event.getValue('footerContentID'))#">,0),
							deliveryReportEmail = nullif(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getTrimValue('deliveryRcpt')#">,''),
							includeAttachmentListAsAttachment = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#local.includeAttachmentListAsAttachment#">,
							utm_autoappend = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#local.utmAutoAppend#">,
							utm_campaign = nullif(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.utmCampaign#">,'')
							<cfif local.showOrgIdentitySelector>
								, orgIdentityID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('blastOrgIdentityID')#">
							</cfif>
						WHERE blastID = @blastID;

						<!--- Blast Supporting Content (BeePlugin JSON Template) --->
						<cfif local.qryBlastInfo.editorCode EQ 'BeePlugin'>
							DECLARE @blastSupportingContent varchar(max) = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.event.getTrimValue('blastSupportingContent','')#">;

							<cfif val(local.qryBlastInfo.supportingContentID)>
								EXEC dbo.cms_updateContent @contentID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryBlastInfo.supportingContentID#">,
									@languageID=1, @isHTML=0, @contentTitle=NULL, @contentDesc=NULL, @rawcontent=@blastSupportingContent, @memberID=@enteredByMemberID;
							<cfelse>
								EXEC dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@appCreatedContentResourceTypeID, @siteResourceStatusID=1, @isHTML=0, 
									@languageID=1, @isActive=1, @contentTitle=NULL, @contentDesc=NULL, @rawContent=@blastSupportingContent, @memberID=@enteredByMemberID,
									@contentID=@supportingContentID OUTPUT, @siteResourceID=@supportingContentSiteResourceID OUTPUT;

								UPDATE dbo.email_emailBlasts 
								SET supportingContentID = @supportingContentID
								WHERE blastID = @blastID;
							</cfif>
						</cfif>


						<cfset local.strEmailTypes = getEmailTypesAndTagsFromList(blastMailTypesAndTags=arguments.event.getValue('blastMailTypesAndTags',''))>

						DECLARE @tblEmailTypes table (emailTypeID int);
						DECLARE @tblEmailTagTypes table (emailTagTypeID int);

						INSERT INTO @tblEmailTypes (emailTypeID)
						SELECT listItem
						FROM dbo.fn_intListToTableInline(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.strEmailTypes.emailTypeIDList#">,',');

						INSERT INTO @tblEmailTagTypes (emailTagTypeID)
						SELECT listItem
						FROM dbo.fn_intListToTableInline(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.strEmailTypes.emailTagTypeIDList#">,',');

						DELETE bet
						FROM dbo.email_emailBlastEmailTypes AS bet
						LEFT OUTER JOIN @tblEmailTypes AS tmp ON tmp.emailTypeID = bet.emailTypeID
						WHERE bet.blastID = @blastID
						AND tmp.emailTypeID IS NULL;

						INSERT INTO dbo.email_emailBlastEmailTypes(blastID, emailTypeID)
						SELECT @blastID, tmp.emailTypeID
						FROM @tblEmailTypes as tmp
						LEFT OUTER JOIN dbo.email_emailBlastEmailTypes AS bet ON bet.emailTypeID = tmp.emailTypeID
							AND bet.blastID = @blastID
						WHERE bet.emailTypeID IS NULL;

						DELETE bet
						FROM dbo.email_emailBlastEmailTagTypes AS bet
						LEFT OUTER JOIN @tblEmailTagTypes AS tmp ON tmp.emailTagTypeID = bet.emailTagTypeID
						WHERE bet.blastID = @blastID
						AND tmp.emailTagTypeID IS NULL;

						INSERT INTO dbo.email_emailBlastEmailTagTypes(blastID, emailTagTypeID)
						SELECT @blastID, tmp.emailTagTypeID
						FROM @tblEmailTagTypes as tmp
						LEFT OUTER JOIN dbo.email_emailBlastEmailTagTypes AS bet ON bet.emailTagTypeID = tmp.emailTagTypeID
							AND bet.blastID = @blastID
						WHERE bet.emailTagTypeID IS NULL;

						<cfif local.showOptOutsPerm>
							DECLARE @tblConsentLists table (consentListID int, isPrimary bit);

							INSERT INTO @tblConsentLists (consentListID, isPrimary)
							SELECT listItem, 0
							FROM dbo.fn_intListToTableInline(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.consentListIDs#">,',');

							<cfif len(local.chkIsPrimary)>
								update @tblConsentLists set 
									isPrimary=1
								where consentListID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.chkIsPrimary#">
							</cfif>

							DELETE bcl
							FROM dbo.email_emailBlastConsentLists AS bcl
							WHERE bcl.blastID = @blastID;

							INSERT INTO dbo.email_emailBlastConsentLists(blastID, consentListID,isPrimary)
							SELECT @blastID, tmp.consentListID, tmp.isPrimary
							FROM @tblConsentLists as tmp;
						</cfif>

						UPDATE cdl
						SET cdl.docTitle = tmpDoc.docTitle,
							cdl.dateModified = GETDATE()
						FROM dbo.cms_documentLanguages AS cdl
						INNER JOIN dbo.email_emailBlastDocuments AS ebd ON ebd.documentID = cdl.documentID
						INNER JOIN ##tmpdocTitles AS tmpDoc ON tmpDoc.documentID = ebd.documentID
						WHERE ebd.blastID = @blastID;

						-- populate screenshot queue
						select top 1 @contentVersionID = cv.contentVersionID
						from dbo.email_emailBlasts as eb
						inner join dbo.cms_content as c on c.contentID = eb.contentID
						inner join dbo.cms_contentLanguages as cl on c.contentID = cl.contentID and cl.languageID = 1
						inner join dbo.cms_contentVersions as cv on cv.contentLanguageID = cl.contentLanguageID and cv.isActive = 1
						where eb.blastID = @blastID;

						EXEC dbo.cms_populateScreenshotsQueue @siteID=@siteID, @contentVersionID=@contentVersionID, @featureImageConfigID=@featureImageConfigID,
							@referenceType='emailBlastScreenshot', @referenceID=@blastID, @viewportWidth=414, @viewportHeight=828, @deviceScaleFactor=2, @fullPage=1,
							@enteredByMemberID=@enteredByMemberID;
					COMMIT TRAN;

					IF OBJECT_ID('tempdb..##tmpdocTitles') IS NOT NULL
						DROP TABLE ##tmpdocTitles;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfif len(arguments.event.getValue('postSaveAction',''))>
				<cfset local.afID = arguments.event.getValue('afID',0)>
				<cfset local.endRunDate = arguments.event.getValue('endRunDate','')>
				<cfif arguments.event.getValue('postSaveAction') eq "scheduleBlast">
					<cfset this.objEmailBlast.scheduleBlast(blastID=local.qryBlastInfo.blastID, emailDateScheduled=arguments.event.getValue('emailDateScheduled',''),
						afID=local.afID, endRunDate=local.endRunDate)>
				<cfelseif arguments.event.getValue('postSaveAction') eq "sendBlast">
					<cfif local.afID and local.qryBlastInfo.siteID eq arguments.event.getValue('mc_siteinfo.siteid')>
						<cfquery name="local.qryUpdateBlast" datasource="#application.dsn.membercentral.dsn#">
							update dbo.email_emailBlasts 
							set afID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.afID#">,
								emailDateScheduled = getdate(),
								<cfif len(local.endRunDate)>
									endRunDate = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#ParseDateTime(replace(local.endRunDate,' - ',' '))#">
								<cfelse>
									endRunDate = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" null="yes">					
								</cfif>
							where blastID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qryBlastInfo.blastID#">
						</cfquery>
					</cfif>

					<cfset local.sendResult = doSendBlast(siteID=arguments.event.getValue('mc_siteinfo.siteid'), blastID=local.qryBlastInfo.blastID)>
					<cfset local.returnStruct["success"] = local.sendResult.success>
					<cfset local.returnStruct["msg"] = local.sendResult.data>
				</cfif>
			</cfif>

			<cfcatch type="Any">
				<cfset local.returnStruct["success"] = false>
				<cfset application.objError.sendError(cfcatch=cfcatch)>
			</cfcatch>
		</cftry>

		<cfreturn returnAppStruct(serializeJSON(local.returnStruct),'echo')>
	</cffunction>

	<cffunction name="sendTestMessage" access="public" output="false" returntype="struct">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="blastID" type="numeric" required="true">
		<cfargument name="mid" type="numeric" required="true">
		<cfargument name="email" type="string" required="true">
		<cfargument name="memberemail" type="string" required="true">
		
		<cfset var local = structNew()>
		<cfset local.returnStruct = { success=true }>
		
		<cftry>
			<cfset local.resultStruct = doSendBlast(siteID=arguments.siteid, blastID=arguments.blastID, isTestMessage=true, testMemberID=arguments.mid, testMemberEmail=arguments.memberemail, testDestinationEmail=arguments.email)/>
			<cfset local.returnStruct.success = local.resultStruct.success>
			<cfset local.returnStruct.data = local.resultStruct.data>
			<cfcatch type="Any">
				<cfset local.returnStruct.success = false>
				<cfset application.objError.sendError(cfcatch=cfcatch)>
			</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="sendTestMessageFromPreview" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="blastID" type="numeric" required="true">
		<cfargument name="blastContent" type="string" required="true">
		<cfargument name="fromName" type="string" required="true">
		<cfargument name="replyTo" type="string" required="true">
		<cfargument name="footerContentID" type="numeric" required="true">
		<cfargument name="subject" type="string" required="true">
		<cfargument name="previewText" type="string" required="true">
		<cfargument name="utmAutoAppend" type="string" required="true">
		<cfargument name="utmCampaign" type="string" required="true">
		<cfargument name="incAttachListAsAttachment" type="boolean" required="true">
		<cfargument name="attachmentsList" type="string" required="false" default="">
		<cfargument name="testMemberID" type="numeric" required="true">
		<cfargument name="testDestinationEmail" type="string" required="true">
		<cfargument name="testMemberEmail" type="string" required="true">
		
		<cfset var local = structNew()>
		<cfset local.returnStruct = { success = false, errmsg = "" }>

		<cfset local.qryBlastInfo = CreateObject('component','emailBlast').getBlastInfo(blastID=arguments.blastID)>
		
		<cftry>
			<cfif local.qryBlastInfo.recordcount and local.qryBlastInfo.siteCode eq arguments.mcproxy_siteCode>
				<cfset local.blastContent = application.objResourceRenderer.qualifyAllLinks(content=arguments.blastContent, siteid=local.qryBlastInfo.siteid)>
				<cfif len(arguments.previewText)>
					<cfset local.blastContent = application.objEmailWrapper.injectPreviewText(htmlcontent=local.blastContent, preheadertext=arguments.previewText)>
				</cfif>

				<cfset local.sendResult = sendBlastByData(
					blastID=arguments.blastID,
					siteCode=arguments.mcproxy_siteCode,
					rawContent=local.blastContent,
					footerContentID=arguments.footerContentID,
					contentTitle=arguments.subject,
					utmAutoAppend=arguments.utmAutoAppend,
					utmCampaign=arguments.utmCampaign,
					includeAttachmentListAsAttachment=arguments.incAttachListAsAttachment,
					arrAttachments=getBlastAttachmentsArray(attachmentList=arguments.attachmentsList),
					useRemoteLogin=local.qryBlastInfo.useRemoteLogin,
					ruleID=local.qryBlastInfo.ruleID,
					orgIdentityID=local.qryBlastInfo.orgIdentityID,
					isTestMessage=true,
					testMemberID=arguments.testMemberID,
					testMemberEmail=arguments.testMemberEmail,
					testDestinationEmail=arguments.testDestinationEmail,
					fromNameOverride=arguments.fromName,
					replyToEmailOverride=arguments.replyTo
				)>
				<cfset local.returnStruct.success = local.sendResult.success>
				<cfset local.returnStruct.data = local.sendResult.data>
			</cfif>
			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch)>
			</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="sendTestMessageFromPreviewByBlastID" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteCode" type="string" required="true">
		<cfargument name="blastID" type="numeric" required="true">
		<cfargument name="testMemberID" type="numeric" required="true">
		<cfargument name="testDestinationEmail" type="string" required="true">
		<cfargument name="testMemberEmail" type="string" required="true">
		
		<cfset var local = structNew()>
		<cfset local.returnStruct = { success = false, errmsg = "" }>

		<cfset local.qryBlastInfo = CreateObject('component','emailBlast').getBlastInfo(blastID=arguments.blastID)>
		
		<cftry>
			<cfif local.qryBlastInfo.recordcount and local.qryBlastInfo.siteCode eq arguments.mcproxy_siteCode>
				<cfset local.blastContent = application.objResourceRenderer.qualifyAllLinks(content=local.qryBlastInfo.rawContent, siteid=local.qryBlastInfo.siteid)>
				<cfif len(local.qryBlastInfo.contentDesc)>
					<cfset local.blastContent = application.objEmailWrapper.injectPreviewText(htmlcontent=local.blastContent, preheadertext=local.qryBlastInfo.contentDesc)>
				</cfif>

				<cfset local.sendResult = sendBlastByData(
					blastID=arguments.blastID,
					siteCode=local.qryBlastInfo.siteCode,
					rawContent=local.blastContent,
					footerContentID=local.qryBlastInfo.footerContentID,
					contentTitle=local.qryBlastInfo.contentTitle,
					utmAutoAppend=local.qryBlastInfo.utm_autoappend,
					utmCampaign=local.qryBlastInfo.utm_campaign,
					includeAttachmentListAsAttachment=local.qryBlastInfo.includeAttachmentListAsAttachment,
					arrAttachments=getBlastAttachmentsArray(blastID=local.qryBlastInfo.blastID),
					useRemoteLogin=local.qryBlastInfo.useRemoteLogin,
					ruleID=local.qryBlastInfo.ruleID,
					orgIdentityID=local.qryBlastInfo.orgIdentityID,
					isTestMessage=true,
					testMemberID=arguments.testMemberID,
					testMemberEmail=arguments.testMemberEmail,
					testDestinationEmail=arguments.testDestinationEmail,
					fromNameOverride=local.qryBlastInfo.fromName,
					replyToEmailOverride=local.qryBlastInfo.replyTo
				)>
				<cfset local.returnStruct.success = local.sendResult.success>
				<cfset local.returnStruct.data = local.sendResult.data>
			</cfif>
			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch)>
			</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>
	
	<cffunction name="doSendBlast" access="public" output="false" returntype="struct">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="blastID" type="numeric" required="true">
		<cfargument name="isTestMessage" type="boolean" required="false" default="false">
		<cfargument name="testMemberID" type="numeric" required="false" default="0">
		<cfargument name="testMemberEmail" type="string" required="false" default="">
		<cfargument name="testDestinationEmail" type="string" required="false" default="">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { success=false, data="" }>

		<cfset local.qryBlastInfo = CreateObject('component','emailBlast').getBlastInfo(blastID=arguments.blastID)>

		<cfif local.qryBlastInfo.recordcount and local.qryBlastInfo.siteID eq arguments.siteID>
			<cfset local.blastContent = local.qryBlastInfo.rawContent>
			<!--- 
				Commenting out for now to revert back to pre hotfix-403964. 
				
				Ideally, we'd run this, but scheduled task that sends email blasts qualifies links as mc.local.membercentral.com 
				due to default value of application.objResourceRenderer.qualifyAllLinks() qualURL.

				All email blast usages should probably pass the environment mainhostname of the sitecode that owns the blast, but that change needs some testing.


			<cfset local.blastContent = application.objResourceRenderer.qualifyAllLinks(content=local.qryBlastInfo.rawContent, siteid=local.qryBlastInfo.siteid)>
			<cfif len(local.qryBlastInfo.contentDesc)>
				<cfset local.blastContent = application.objEmailWrapper.injectPreviewText(htmlcontent=local.blastContent, preheadertext=local.qryBlastInfo.contentDesc)>
			</cfif> 
			--->

			<cfset local.resultStruct = sendBlastByData(
				blastID=local.qryBlastInfo.blastID,
				siteCode=local.qryBlastInfo.siteCode,
				rawContent=local.blastContent,
				footerContentID=local.qryBlastInfo.footerContentID,
				contentTitle=local.qryBlastInfo.contentTitle,
				utmAutoAppend=local.qryBlastInfo.utm_autoappend,
				utmCampaign=local.qryBlastInfo.utm_campaign,
				includeAttachmentListAsAttachment=local.qryBlastInfo.includeAttachmentListAsAttachment,
				arrAttachments=getBlastAttachmentsArray(blastID=local.qryBlastInfo.blastID),
				useRemoteLogin=local.qryBlastInfo.useRemoteLogin,
				ruleID=local.qryBlastInfo.ruleID,
				orgIdentityID=local.qryBlastInfo.orgIdentityID,
				isTestMessage=arguments.isTestMessage,
				testMemberID=arguments.testMemberID,
				testMemberEmail=arguments.testMemberEmail,
				testDestinationEmail=arguments.testDestinationEmail
			)>
			<cfset local.returnStruct.success = local.resultStruct.success>
			<cfset local.returnStruct.data = local.resultStruct.data>
		<cfelse>
			<cfset local.returnStruct.data = "The blast you attempted to send does not exist or you do not have permission to send it.">
		</cfif>
		
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="sendBlastByData" access="private" output="false" returntype="struct">
		<cfargument name="blastID" type="numeric" required="true">
		<cfargument name="siteCode" type="string" required="true">
		<cfargument name="rawContent" type="string" required="true">
		<cfargument name="footerContentID" type="numeric" required="true">
		<cfargument name="contentTitle" type="string" required="true">
		<cfargument name="utmAutoAppend" type="string" required="true">
		<cfargument name="utmCampaign" type="string" required="true">
		<cfargument name="includeAttachmentListAsAttachment" type="boolean" required="true">
		<cfargument name="arrAttachments" type="array" required="false" default="[]">
		<cfargument name="useRemoteLogin" type="boolean" required="true" default="false">
		<cfargument name="ruleID" type="numeric" required="true">
		<cfargument name="orgIdentityID" type="numeric" required="true">
		<cfargument name="isTestMessage" type="boolean" required="false" default="false">
		<cfargument name="testMemberID" type="numeric" required="false" default="0">
		<cfargument name="testMemberEmail" type="string" required="false" default="">
		<cfargument name="testDestinationEmail" type="string" required="false" default="">
		<cfargument name="fromNameOverride" type="string" required="false" default="">
		<cfargument name="replyToEmailOverride" type="string" required="false" default="">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { success=false, data="" }>
		<cfset local.mc_siteInfo = duplicate(application.objSiteInfo.getSiteInfo(arguments.siteCode))>

		<cfsetting requesttimeout="600">

		<cfset local.fullHtmlMessage = arguments.rawContent>

		<cfset local.hasAttachmentListAsAttachment = false>
		<cfif arguments.includeAttachmentListAsAttachment AND arrayLen(arguments.arrAttachments)>
			<cfset local.strAttachment = application.objEmailWrapper.getAttachmentsListAsAttachment(siteCode=arguments.siteCode, arrAttachments=arguments.arrAttachments, 
						fileNameWithNoExt="Attachments - Click to Open")>
			<cfset local.hasAttachmentListAsAttachment = FileExists(local.strAttachment.filePath)>
		</cfif>

		<cfif arguments.footerContentID>
			<cfset local.qryFooter = CreateObject('component','emailBlast').getFooter(siteID=local.mc_siteInfo.siteID, contentID=arguments.footerContentID)>
			<cfif len(trim(local.qryFooter.rawContent))>
				<cfset local.fullHtmlMessage = application.objEmailWrapper.injectFooter(htmlcontent=local.fullHtmlMessage,footercontent="<hr /><div>#local.qryFooter.rawContent#</div>")>
			</cfif>
		</cfif>
		<cfset local.fullHtmlMessage = appendOptOutFooterIfNoMergeTags(fullHtmlMessage=local.fullHtmlMessage)>
		<cfif arguments.utmAutoAppend>
			<cfset local.utmCampaign = len(arguments.utmCampaign) ? arguments.utmCampaign : arguments.contentTitle>
			<cfset local.fullHtmlMessage = application.objEmailWrapper.appendUTMCodesToLinks(
				htmlcontent=local.fullHtmlMessage,
				utm_campaign=local.utmCampaign,
				utm_source="MemberCentral EmailBlast",
				utm_medium="email",
				utm_content="#dateformat(now(),"yyyy-mm-dd")# #arguments.contentTitle#"
			)>
		</cfif>
		<cfset local.fullHtmlMessage = application.objEmailWrapper.disableClickTrackingForSpecificLinks(htmlcontent=local.fullHtmlMessage)>

		<cfset local.markRecipientAsReady = 1>
		<cfif arguments.includeAttachmentListAsAttachment AND local.hasAttachmentListAsAttachment>
			<cfset local.markRecipientAsReady = 0>
		</cfif>
		
		<cfif arguments.isTestMessage>
			<cfset local.strRecipientExtMergeTags = application.objMergeCodes.detectExtendedMergeCodes(siteID=local.mc_siteInfo.siteID, 
						rawContent="#local.fullHtmlMessage#", extraMergeTagList="")>
			<cfif local.markRecipientAsReady AND local.strRecipientExtMergeTags.contentHasMergeCodes>
				<cfset local.markRecipientAsReady = 0>
			</cfif>

			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="email_sendTestBlast">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberid#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.mc_siteInfo.siteID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.blastID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.testMemberID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.testDestinationEmail#">
				<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="#local.fullHtmlMessage#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#local.markRecipientAsReady#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.fromNameOverride#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.replyToEmailOverride#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.contentTitle#">
				<cfprocparam type="out" cfsqltype="CF_SQL_INTEGER" variable="local.messageID">
				<cfprocparam type="out" cfsqltype="CF_SQL_INTEGER" variable="local.recipientID">
				<cfprocparam type="out" cfsqltype="CF_SQL_VARCHAR" variable="local.membernumber">
				<cfprocparam type="out" cfsqltype="CF_SQL_VARCHAR" variable="local.orgcode">
				<cfprocparam type="out" cfsqltype="CF_SQL_VARCHAR" variable="local.outputMessage">
			</cfstoredproc>

			<cfif NOT local.markRecipientAsReady>
				<cfif arguments.includeAttachmentListAsAttachment AND local.hasAttachmentListAsAttachment>
					<cfset insertEmailAttachmentListAsAttachment(messageID=local.messageID, strAttachment=local.strAttachment, markRecipientAsReady=false)>
				</cfif>

				<cfif local.strRecipientExtMergeTags.contentHasMergeCodes>
					<cfset local.thisRecipMainHostname = application.objSiteInfo.getSiteInfo(arguments.siteCode).mainhostname>
					<cfset local.strMergeTagArgs = { siteID=local.mc_siteInfo.siteID, orgcode=local.orgcode, recipientID=local.recipientID, 
													messageID=local.messageID, recipientMemberID=arguments.testMemberID, memberID=arguments.testMemberID, 
													membernumber=local.membernumber, hostname=local.thisRecipMainHostname, useRemoteLogin=arguments.useRemoteLogin, 
													strRecipientExtMergeTags=local.strRecipientExtMergeTags, recipientEmail=arguments.testMemberEmail,
													orgIdentityID=arguments.orgIdentityID }>
					<cfset application.objMergeCodes.replaceExtendedMergeCodes(argumentCollection=local.strMergeTagArgs)>
				</cfif>

				<cfquery datasource="#application.dsn.platformMail.dsn#" name="local.qryChangeStatus">
					exec dbo.email_setMessageRecipientHistoryStatus
						@siteID= <cfqueryparam value="#local.mc_siteInfo.siteID#" cfsqltype="CF_SQL_INTEGER">,
						@messageID= <cfqueryparam value="#local.messageID#" cfsqltype="CF_SQL_INTEGER">,
						@recipientID= <cfqueryparam value="#local.recipientID#" cfsqltype="CF_SQL_INTEGER">,
						@statusCode= <cfqueryparam value="Q" cfsqltype="CF_SQL_VARCHAR">,
						@updateDate= <cfqueryparam value="0" cfsqltype="CF_SQL_BIT">
				</cfquery>

			</cfif>
			
			<cfhttp method="get" url="#application.paths.backendPlatform.internalUrl#?event=integrations.sendgrid.processmessage&messageID=#local.messageID#">

		<cfelse>
			<cftry>
				<!--- ensure rule is active and cache refreshed. show error if necessary --->
				<cfset local.msg = CreateObject("component","model.admin.virtualGroups.virtualGroupsAdmin").activateRule(ruleID=arguments.ruleID, forceCache=true)>
				<cfif local.msg is not 0>
					<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="email_sendBlastDateAdvance">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.mc_siteInfo.siteID#">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.blastID#">
					</cfstoredproc>
					
					<cfthrow message="We could not send this blast because we were unable to recalculate the defined filters for this blast.">
				<cfelse>
					<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="email_sendBlast">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberid#">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.mc_siteInfo.siteID#">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.blastID#">
						<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="#local.fullHtmlMessage#">
						<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#local.markRecipientAsReady#">
						<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.messageID">
						<cfprocparam type="Out" cfsqltype="CF_SQL_BIT" variable="local.hasExtendedMergeCodes">
					</cfstoredproc>

					<cfif val(local.messageID) AND NOT local.markRecipientAsReady AND arguments.includeAttachmentListAsAttachment AND local.hasAttachmentListAsAttachment>
						<cfset insertEmailAttachmentListAsAttachment(messageID=local.messageID, strAttachment=local.strAttachment, markRecipientAsReady=(NOT local.hasExtendedMergeCodes))>
					</cfif>
				</cfif>
			<cfcatch type="Any">
				<cfif structKeyExists(cfcatch,"detail") and findNoCase("No recipients for message", cfcatch.detail)>
					<cfset local.returnStruct.data = "<div class='alert alert warning'><p class='font-weight-bold'>No Recipients Found</p><p>Your blast did not send because no valid recipients were found. Please consult the Summary of Selected Members at the top of this screen.</p></div>">
				<cfelseif structKeyExists(cfcatch,"message") and findNoCase("unable to recalculate the defined filters", cfcatch.message)>
					<cfset local.returnStruct.data = cfcatch.message>
				<cfelse>
					<cfset local.returnStruct.data = "The blast you attempted to send has failed.">
					<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=arguments)>
				</cfif>
				<cfset local.returnStruct.success = false>
				<cfreturn local.returnStruct>
			</cfcatch>
			</cftry>
		</cfif>
		
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.data = "The emails have been queued for Email Blast, and they will be sent out shortly.">
		<cfif isDefined("local.outputMessage") and LEN(local.outputMessage)>
			<cfset local.returnStruct.data = local.outputMessage>
		</cfif>
		
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getEmailTypesAndTagsFromList" access="private" output="false" returntype="struct">
		<cfargument name="blastMailTypesAndTags" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.resultStruct = { emailTypeIDList:"", emailTagTypeIDList:""  }>

		<cfloop list="#arguments.blastMailTypesAndTags#" index="local.thisMailTypesAndTag">
			<cfif getToken(local.thisMailTypesAndTag,1,"_") eq "type">
				<cfset local.resultStruct.emailTypeIDList = listAppend(local.resultStruct.emailTypeIDList, getToken(local.thisMailTypesAndTag,2,"_"))>
			<cfelseif getToken(local.thisMailTypesAndTag,1,"_") eq "tag">
				<cfset local.resultStruct.emailTagTypeIDList = listAppend(local.resultStruct.emailTagTypeIDList, getToken(local.thisMailTypesAndTag,2,"_"))>
			</cfif>
		</cfloop>
	
		<cfreturn local.resultStruct>
	</cffunction>

	<cffunction name="updateBlastEmailTypesAndTags" access="private" output="false" returntype="void">
		<cfargument name="blastID" type="numeric" required="true">
		<cfargument name="emailTypeIDList" type="string" required="true">
		<cfargument name="emailTagTypeIDList" type="string" required="true">

		<cfset var qryUpdateEmailTypesAndTags = "">
		
		<cfquery name="qryUpdateEmailTypesAndTags" datasource="#application.dsn.membercentral.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				DECLARE @blastID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.blastID#">;

				DECLARE @tblEmailTypes table (emailTypeID int);
				DECLARE @tblEmailTagTypes table (emailTagTypeID int);

				INSERT INTO @tblEmailTypes (emailTypeID)
				SELECT listItem
				FROM dbo.fn_intListToTableInline(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.emailTypeIDList#">,',');

				INSERT INTO @tblEmailTagTypes (emailTagTypeID)
				SELECT listItem
				FROM dbo.fn_intListToTableInline(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.emailTagTypeIDList#">,',');

				BEGIN TRAN;
					DELETE bet
					FROM dbo.email_emailBlastEmailTypes AS bet
					LEFT OUTER JOIN @tblEmailTypes AS tmp ON tmp.emailTypeID = bet.emailTypeID
					WHERE bet.blastID = @blastID
					AND tmp.emailTypeID IS NULL;

					INSERT INTO dbo.email_emailBlastEmailTypes(blastID, emailTypeID)
					SELECT @blastID, tmp.emailTypeID
					FROM @tblEmailTypes as tmp
					LEFT OUTER JOIN dbo.email_emailBlastEmailTypes AS bet ON bet.emailTypeID = tmp.emailTypeID
						AND bet.blastID = @blastID
					WHERE bet.emailTypeID IS NULL;

					DELETE bet
					FROM dbo.email_emailBlastEmailTagTypes AS bet
					LEFT OUTER JOIN @tblEmailTagTypes AS tmp ON tmp.emailTagTypeID = bet.emailTagTypeID
					WHERE bet.blastID = @blastID
					AND tmp.emailTagTypeID IS NULL;

					INSERT INTO dbo.email_emailBlastEmailTagTypes(blastID, emailTagTypeID)
					SELECT @blastID, tmp.emailTagTypeID
					FROM @tblEmailTagTypes as tmp
					LEFT OUTER JOIN dbo.email_emailBlastEmailTagTypes AS bet ON bet.emailTagTypeID = tmp.emailTagTypeID
						AND bet.blastID = @blastID
					WHERE bet.emailTagTypeID IS NULL;
				COMMIT TRAN;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
	</cffunction>

	<cffunction name="insertEmailAttachmentListAsAttachment" access="private" output="false" returntype="boolean">
		<cfargument name="messageID" type="numeric" required="true">
		<cfargument name="strAttachment" type="struct" required="true">
		<cfargument name="markRecipientAsReady" type="boolean" required="true">

		<cfset var local = structNew()>
		<cfset local.success = false>

		<!--- insert email attachments --->
		<cfif FileExists(arguments.strAttachment.filePath)>
			<cfset local.pathFromS3Uploader = replacenocase(arguments.strAttachment.filePath,application.paths.SharedTempNoWeb.path,application.paths.SharedTempNoWeb.pathS3Uploader)>
			<cfset local.pathFromS3Uploader = replace(local.pathFromS3Uploader,'/','\','all')>

			<cfquery name="local.qryInsertEmailAttachment" datasource="#application.dsn.platformMail.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @messageID int, @fileName varchar(400), @localDirectory varchar(400), @s3keyMod varchar(4), 
						@objectKey varchar(400), @filePathForS3Upload varchar(400), @attachmentID int, 
						@messageStatusIDQueued int, @s3bucketName varchar(100), @s3UploadReadyStatusID int, 
						@nowDate datetime = getdate();

					SET @messageID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.messageID#">;
					SET @fileName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.strAttachment.displayName#">;
					SET @localDirectory = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.strAttachment.folderPath#">;
					SET @filePathForS3Upload = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.pathFromS3Uploader#">;
					SET @s3bucketName = 'platformmail-membercentral-com';

					select @s3UploadReadyStatusID = qs.queueStatusID
					from platformQueue.dbo.tblQueueTypes as qt
					inner join platformQueue.dbo.tblQueueStatuses as qs on qs.queueTypeID = qt.queueTypeID
					where qt.queueType = 's3Upload'
					and qs.queueStatus = 'readyToProcess';

					select @messageStatusIDQueued = statusID from dbo.email_statuses where statusCode = 'Q';

					BEGIN TRAN;
						EXEC dbo.email_insertAttachment @fileName=@fileName, @localDirectory=@localDirectory, @attachmentID=@attachmentID OUTPUT;

						INSERT INTO dbo.email_messageRecipientAttachments (recipientID, attachmentID)
						SELECT recipientID, @attachmentID
						FROM dbo.email_messageRecipientHistory
						WHERE messageID = @messageID;

						<!--- insert to s3 upload queue --->
						SET @s3keyMod = FORMAT(@attachmentID % 1000, '0000');
						SET @objectKey = LOWER('#application.MCEnvironment#/outgoing/' + @s3keyMod + '/' + cast(@attachmentID as varchar(10)) + '/' + @fileName);

						IF NOT EXISTS (select 1 from platformQueue.dbo.queue_S3Upload where s3bucketName = @s3bucketName and objectKey = @objectKey)
							INSERT INTO platformQueue.dbo.queue_S3Upload (statusID, s3bucketName, objectKey, filePath, deleteOnSuccess, dateAdded, dateUpdated)
							VALUES (@s3UploadReadyStatusID, @s3bucketName, @objectKey, @filePathForS3Upload, 0, @nowDate, @nowDate);

						<cfif arguments.markRecipientAsReady>
							UPDATE dbo.email_messageRecipientHistory 
							SET emailStatusID = @messageStatusIDQueued
							WHERE messageID = @messageID;
						</cfif>
					COMMIT TRAN;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.success = true>
		</cfif>

		<cfreturn local.success>
	</cffunction>
	
	<cffunction name="checkMemberFilterLength" access="public" output="false" returntype="struct">
		<cfargument name="blastID" type="numeric" required="true">
		
		<cfset var local = structNew()>

		<cfset local.qryBlastInfo = CreateObject('component', 'emailBlast').getBlastInfo(arguments.blastID)>
		<cfset local.data.filtercount = CreateObject("component","model.admin.virtualGroups.virtualGroupsAdmin").countRuleConditions(ruleID=local.qryBlastInfo.ruleID)>
		<cfset local.data.success = true>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="listCategories" access="public" output="false" returntype="struct" hint="list categories for Notes">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();
			local.objCategories = createObject("component","model.admin.categories.categories");

			//make sure categoryTree exists
			if (not this.objCategories.getCategoryTrees(this.siteResourceID).recordCount)
				this.objCategories.addCategoryTree(	siteID=arguments.event.getValue('mc_siteInfo.siteid'), controllingSiteResourceID=this.siteResourceID, categoryTreeName='Email Blast Settings' );

			// security
			// --- might need to send to message page that says they dont have right to view this page
			if (NOT arguments.event.getValue('mc_adminToolInfo.myRights.View'))
				application.objCommon.redirect("#this.link.message#&message=1");

			local.strBlastTypesGridData = { 
				adminHomeResourceLink="#arguments.event.getValue('mc_adminNav.adminHomeResource')#",
				gridID="emailBlastTypes", mode="parent", siteResourceID=this.siteResourceID,
				categoryLabel="Email Blast Type",
				deleteLinkFn="doRemoveEmailBlastType",
				addLinkUrl="#this.link.editCategory#&catType=0", editLinkUrl=this.link.editCategory
			};
			local.strBlastTypesGrid = local.objCategories.getCategoriesGrid(strGridData=local.strBlastTypesGridData);

			local.strBlastCategoriesGridData = { 
				adminHomeResourceLink="#arguments.event.getValue('mc_adminNav.adminHomeResource')#",
				gridID="emailBlastCategories", mode="sub", siteResourceID=this.siteResourceID,
				categoryHeader="Email Blast Type", subCategoryHeader="Category", categoryLabel="Email Blast Category",
				deleteLinkFn="doRemoveEmailBlastCategory",
				addLinkUrl="#this.link.editCategory#&catType=1", editLinkUrl=this.link.editCategory
			};
			local.strBlastCategoriesGrid = local.objCategories.getCategoriesGrid(strGridData=local.strBlastCategoriesGridData);
			
			if (arguments.event.getValue('mc_adminToolInfo.myRights.manageFooters',false)) {
				local.editFooterLink = "#this.link.editFooter#";
				local.qryFooters = this.objEmailBlast.getFooters(arguments.event.getValue('mc_siteInfo.siteid'));
			}
			
			local.conditionsListLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=virtualGroupsJSON&meth=getConditions&dtMode=emailBlast&mode=stream";
			local.editConditionUID = buildLinkToTool(toolType='VirtualGroup',mca_ta='editConditionUID') & "&mode=direct";
			local.conditionID = arguments.event.getValue('conditionID','0');
			local.arrConditionAreas = createObject("component","model.admin.virtualGroups.virtualGroups").getConditionAreasForDropdowns(conditionType="EmailBlast",mc_siteInfo=arguments.event.getValue('mc_siteInfo'));
 
 			var currTab = arguments.event.getValue('tab','types');
			if (currTab eq '') currTab = 'types';
			
			local.arrTabs = [
				{ title='Types', id='types', fn='dsp_emailBlast_types' },
				{ title='Categories', id='cats', fn='dsp_emailBlast_categories' },
				{ title='Conditions', id='cond', fn='dsp_emailBlast_conditions' }
			];
			
			if (arguments.event.getValue('mc_adminToolInfo.myRights.manageFooters',false)) {
				arrayAppend(local.arrTabs, { title='Footers', id='footers', fn='dsp_emailBlast_footers' });
			}
			if (application.objUser.isSuperUser(cfcuser=session.cfcuser)) {
				local.customFontListLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=emailBlastJSON&meth=getCustomFontsList&mode=stream";
				local.editCustomFontLink = "#this.link.editCustomFont#";
				arrayAppend(local.arrTabs, { title='Bee Plugin Options', id='beepluginoptions', fn='dsp_emailBlast_beeplugin_options' });
			}
			
			local.clrQueryString = REReplace(cgi.query_string,'&tab=#currTab#','');
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_listCategories.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="editCategory" access="public" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();

			if (NOT arguments.event.getValue('mc_adminToolInfo.myRights.EditCategories'))
				application.objCommon.redirect("#this.link.message#&message=1");
		</cfscript>

		<cfquery name="local.qryCategoryTree" datasource="#application.dsn.membercentral.dsn#">
			select dbo.fn_getCategoryTreeIDForSiteResourceID(<cfqueryparam value="#this.siteResourceID#" cfsqltype="CF_SQL_INTEGER">) as categoryTreeID
		</cfquery>

		<cfscript>
			arguments.event.paramValue('catType',0);
			arguments.event.setValue('parentCategoryID',0);
			arguments.event.setValue('categoryID', arguments.event.getValue('categoryID',0));
			arguments.event.setValue('categoryTreeID',local.qryCategoryTree.categoryTreeID);
			
			local.catData = this.objCategories.getCategoryData(arguments.event.getValue('categoryID'));
			local.qryCategories = this.objCategories.getCategories(this.siteResourceID);
			local.formLink	= this.link.saveCategory & "&reloadGridFn=#arguments.event.getValue('reloadGridFn','')#";
			local.baseQueryString = getBaseQueryString(false);
			
			if (val(local.catData.Info.parentCategoryID) is not 0){
				arguments.event.setValue('catType',1);
				arguments.event.setValue('parentCategoryID',local.catData.Info.parentCategoryID);
			}
			
			// Build breadCrumb Trail
			// depending on permissions, go to All or just Member
			if (val(arguments.event.getValue('catType')) is 0) {
				local.catDescription = "Email Blast Type";
				local.parentCategoryLabel = "";
				local.parentDDLabel = "";
				
				appendBreadCrumbs(arguments.event,{ link='#this.link.listCategories#', text="View Email Blast Types" });
			} else {
				local.catDescription = "Email Blast Category";
				local.parentCategoryLabel = "Email Blast Type";
				local.parentDDLabel = "email blast type";
				
				appendBreadCrumbs(arguments.event,{ link='#this.link.listCategories#&tab=cats', text="View Email Blast Categories" });
			}
			
			if(arguments.event.getValue('categoryID',0)){
				appendBreadCrumbs(arguments.event,{ link='', text=encodeForHTML('Edit #local.catDescription#') });
				local.formAction = "Save #local.catDescription#";
			} else {
				appendBreadCrumbs(arguments.event,{ link='', text=encodeForHTML('Add #local.catDescription#') });
				local.formAction = "Add #local.catDescription#";
			}
			
			local.ajaxName = "ADMINCATEGORIES";
			local.ajaxMethodName = "testCategoryNameExistsInTree";
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="/model/admin/categories/frm_category.cfm">
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="saveCategory" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();
			
			if (NOT arguments.event.getValue('mc_adminToolInfo.myRights.EditCategories'))
				application.objCommon.redirect("#this.link.message#&message=1");
			
			local.categoryID = arguments.event.getValue('categoryID',0);

			if(local.categoryID gt 0){
				local.saveCat = this.objCategories.updateCategory(categoryID=local.categoryID, categoryName=arguments.event.getTrimValue('categoryName'),
					categoryDescription=arguments.event.getTrimValue('categoryDescription'), categoryCode=arguments.event.getValue('categoryCode'),
					parentCategoryID=arguments.event.getValue('parentCategoryID',0));
			}
			else {
				local.saveCat = this.objCategories.addCategory(siteResourceID=this.siteResourceID, parentCategoryID=arguments.event.getValue('parentCategoryID',0),
					categoryName=arguments.event.getTrimValue('categoryName'), categoryDescription=arguments.event.getTrimValue('categoryDescription'),
					categoryCode=arguments.event.getTrimValue('categoryCode'));
			}
		</cfscript>

		<cfif local.saveCat.success>
			<cfsavecontent variable="local.data">
				<cfoutput>
				<script language="javascript">
					<cfif len(arguments.event.getValue('reloadGridFn',''))>
						top.#arguments.event.getValue('reloadGridFn')#();
					</cfif>
					top.MCModalUtils.hideModal();
				</script>
				</cfoutput>
			</cfsavecontent>
			<cfreturn returnAppStruct(local.data,"echo")>
		<cfelse>
			<cflocation url="#this.link.editCategory#&err=1&categoryID=#local.categoryID#&catType=#arguments.event.getValue('catType')#&mode=direct" addtoken="false">
		</cfif>
	</cffunction>
		
	<cffunction name="deleteCategory" access="public" output="false" returntype="struct" hint="remove category">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="categoryID" type="numeric" required="true">

		<cfset var local = structNew()>
			
		<cfscript>
			local.emailBlastSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='EmailBlast',siteID=arguments.mcproxy_siteID);
			local.tmpRights = buildRightAssignments(siteResourceID=local.emailBlastSRID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.mcproxy_siteID);

			// security 
			// --- might need to send to message page that says they dont have right to view this page
			if (NOT local.tmpRights.DeleteCategories)	{
				local.data.success = false;
			}
			else {
				local.data = CreateObject('component','model.system.platform.category').deleteCategory(categoryID=arguments.categoryID);
			}
		</cfscript>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getReviewedEmailActivity" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="mID" type="numeric" required="true">
		<cfargument name="rID" type="numeric" required="true">
		
		<cfset var local = structNew()>
		
		<cfstoredproc datasource="#application.dsn.platformMail.dsn#" procedure="email_reviewMessage">
			<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#arguments.mcproxy_siteID#">
			<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#arguments.mID#">
			<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#arguments.rID#">
			<cfprocresult resultset="1" name="local.qryMessageMetadata">
			<cfprocresult resultset="2" name="local.qryRecipient">
			<cfprocresult resultset="3" name="local.qryRecipientMetadata">
			<cfprocresult resultset="4" name="local.qryRecipientAttachments">
		</cfstoredproc>

		<cfif local.qryRecipient.allowAdminView>
			<cfset local.messageSubject = local.qryRecipient.subject>
			<cfset local.messageBody = local.qryRecipient.messageContent>
		<cfelse>
			<cfset local.noViewText = "#local.qryRecipient.messageType#: For security, the content of this message is not viewable.">
			<cfset local.messageSubject = local.noViewText>
			<cfset local.messageBody = local.noViewText>
		</cfif>

		<!--- ------------------- --->
		<!--- merge code handling ---> 
		<!--- ------------------- --->
		<cfif local.qryMessageMetadata.recordcount gt 0>
			<!--- additional member merge codes --->
			<cfquery name="local.qryAdditionalMemberMetadata" dbtype="query">
				select fieldValue, fieldTextToReplace
				from [local].qryRecipientMetadata
				where fieldTextToReplace is not null
			</cfquery>
			
			<cfif local.qryAdditionalMemberMetadata.recordCount>
				<cfloop query="local.qryAdditionalMemberMetadata">
					<cfset local.messageBody = replace(local.messageBody,local.qryAdditionalMemberMetadata.fieldTextToReplace,local.qryAdditionalMemberMetadata.fieldValue,"ALL")>
				</cfloop>
			</cfif>

			<!--- create a structure of all possible merge codes appearing in message. --->
			<cfset local.tempMergeData = structNew()>
			<cfloop query="local.qryMessageMetadata">
				<cfset StructInsert(local.tempMergeData,local.qryMessageMetadata.fieldname,'',true)>
			</cfloop>

			<!--- create a structure of all member metadata. --->
			<cfloop query="local.qryRecipientMetadata">
				<cfif structKeyExists(local.tempMergeData,local.qryRecipientMetadata.fieldName)>
					<cfset StructUpdate(local.tempMergeData,local.qryRecipientMetadata.fieldName,local.qryRecipientMetadata.fieldValue)>
				</cfif>
			</cfloop>

			<!--- merge data with support for default value --->
			<cfloop collection="#local.tempMergeData#" item="local.thisField">
				<cfset local.messageSubject = reReplaceNoCase(local.messageSubject,"\[\[#local.thisField#\]\]",local.tempMergeData[local.thisField],"ALL")>

				<cfif len(trim(local.tempMergeData[local.thisField]))>
					<cfset local.messageBody = reReplaceNoCase(local.messageBody,"\[\[#local.thisField#,([^\]]+)\]\]",local.tempMergeData[local.thisField],"ALL")>
				<cfelse>
					<cfset local.messageBody = reReplaceNoCase(local.messageBody,"\[\[#local.thisField#,([^\]]+)\]\]","\1","ALL")>
				</cfif>
				<cfset local.messageBody = reReplaceNoCase(local.messageBody,"\[\[#local.thisField#\]\]",local.tempMergeData[local.thisField],"ALL")>
			</cfloop>
		</cfif>

		<cfset local.attachmentsContent = "">
		<cfif local.qryRecipientAttachments.recordcount gt 0>
			<cfset local.arrAttachments = ArrayNew(1)>
			<cfloop query="local.qryRecipientAttachments">
				<cfset local.tmpStr = StructNew()>
				<cfset local.tmpStr['attachmentID'] = local.qryRecipientAttachments.attachmentID>
				<cfset local.tmpStr['fileName'] = local.qryRecipientAttachments.fileName>
				<cfset arrayAppend(local.arrAttachments,local.tmpStr)>
			</cfloop>
			<cfsavecontent variable="local.attachmentsContent">
				<cfoutput>
					<cfif arrayLen(local.arrAttachments)>
						<cfloop from="1" to="#arrayLen(local.arrAttachments)#" index="local.thisAttachmentIndx">
							<cfset local.thisAttachment = local.arrAttachments[local.thisAttachmentIndx]>
							<a href="javascript:getAttachment(#arguments.rID#,#local.thisAttachment.attachmentID#);">#local.thisAttachment.fileName#</a>&nbsp;&nbsp;
						</cfloop>
					</cfif>
				</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct["success"] = true>
		<cfset local.returnStruct["msg"] = structNew()>
		<cfset local.returnStruct["msg"]["fromname"] = local.qryRecipient.fromName>
		<cfset local.returnStruct["msg"]["fromemail"] = local.qryRecipient.fromEmail>
		<cfset local.returnStruct["msg"]["subject"] = local.messageSubject>
		<cfset local.returnStruct["msg"]["emailto"] = local.qryRecipient.emailTo>
		<cfset local.returnStruct["msg"]["body"] = local.messageBody>
		<cfset local.returnStruct["msg"]["attachments"] = local.attachmentsContent>
		<cfset local.returnStruct["msg"]["dateentered"] = local.qryRecipient.dateEntered>
		<cfset local.returnStruct["stats"] = getRecipientTrackingStats(siteID=arguments.mcproxy_siteID, recipientID=local.qryRecipient.recipientID)>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="downloadMessageAttachment" access="public" output="no" returntype="string">
		<cfargument name="recipientID" type="numeric" required="true">
		<cfargument name="attachmentID" type="numeric" required="true">
		
		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfset local.data = "">

		<cfquery name="local.attachmentDetails" datasource="#application.dsn.platformMail.dsn#">
			select ra.recipientID, ra.attachmentID, att.[fileName], att.localDirectory 
			from dbo.email_messageRecipientAttachments ra
			inner join dbo.email_attachments att on att.attachmentID = ra.attachmentID
			where ra.recipientID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.recipientID#">
			and att.attachmentID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.attachmentID#">;
		</cfquery>
		
		<cfif local.attachmentDetails.recordCount gt 0>
			<!--- look locally first. If not there, look at S3 --->
			<cfset local.theFile = "#local.attachmentDetails.localDirectory#/#local.attachmentDetails.fileName#">
			<cfif FileExists(local.theFile)>
				<cfset local.docResult = application.objDocDownload.doDownloadDocument(sourceFilePath=local.theFile, displayName=local.attachmentDetails.fileName, forceDownload=1)>
			<cfelse>
				<cfset local.s3keyMod = numberFormat(arguments.attachmentID mod 1000,"0000")>
				<cfset local.objectKey = lCase('#application.MCEnvironment#/outgoing/#local.s3keyMod#/#arguments.attachmentID#/#local.attachmentDetails.fileName#')>
				<cfset local.docResult = application.objDocDownload.doDownloadDocument(sourceFilePath='', displayName=local.attachmentDetails.fileName, forceDownload=1, s3bucket='platformmail-membercentral-com', s3objectKey=local.objectKey, s3expire=1, s3requesttype="vhost")>
			</cfif>
			<cfif not local.docResult>
				<cflocation url="/?pg=404" addtoken="false">
			</cfif>
		<cfelse>
			<cfsavecontent variable="local.data">
				<cfoutput>
					<br/><div><b>The attachment is not available. Try again.</b></div>
				</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getRecipientTrackingStats" access="private" output="false" returntype="struct">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="recipientID" type="numeric" required="true">
		
		<cfset var local = structNew()>
		
		<cfquery name="local.recipientStats" datasource="#application.dsn.platformMail.dsn#">
			select s.statusCode, s.status, rt.recipientID,rt.mcEventID, rt.ipaddress, rt.dateAdded, rt.detail
			from dbo.email_messageRecipientHistoryTracking rt
			inner join dbo.email_statuses s
				on rt.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#"/>
				and rt.recipientID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.recipientID#"/>
				and s.statusID = rt.statusID
				and s.statusCode in ('sg_drop','sg_open','sg_bounce','sg_spam','sg_click')
			order by rt.dateadded
		</cfquery>

		<cfset local.recipientStruct = structNew()>
		<cfset local.recipientStruct["sg_drop"] = arrayNew(1) />
		<cfset local.recipientStruct["sg_open"] = arrayNew(1) />
		<cfset local.recipientStruct["sg_bounce"] = arrayNew(1) />
		<cfset local.recipientStruct["sg_spam"] = arrayNew(1) />
		<cfset local.recipientStruct["sg_click"] = arrayNew(1) />

		<cfset local.foundLinksList = ""/>

		<cfloop query="local.recipientStats">
			<cfset local.thisRow = structNew() />
			<cfset local.thisRow["dateAdded"] = local.recipientStats.dateAdded />

			<cfswitch expression="#local.recipientStats.statusCode#">
				<cfcase value="sg_drop,sg_bounce">
					<cfset local.thisRow["reason"] = local.recipientStats.detail />
					<cfset arrayAppend(local.recipientStruct[local.recipientStats.statusCode], local.thisRow)/>
				</cfcase>
				<cfcase value="sg_click">
					<cfset local.linkPosition = listFindNoCase(local.foundLinksList, local.recipientStats.detail, chr(9))>
					<cfif not local.linkPosition>
						<cfset local.foundLinksList = listAppend(local.foundLinksList, local.recipientStats.detail, chr(9)) />
						<cfset local.newLinkStruct = structNew()/>
						<cfset local.newLinkStruct["url"] = local.recipientStats.detail />
						<cfset local.newLinkStruct["hits"] = arrayNew(1) />
						<cfset arrayAppend(local.recipientStruct[local.recipientStats.statusCode], local.newLinkStruct)/>
						<cfset local.linkPosition = listFindNoCase(local.foundLinksList, local.recipientStats.detail, chr(9))>
					</cfif>
					<cfset local.thisRow["ip"] = local.recipientStats.ipaddress />
					<cfset arrayAppend(local.recipientStruct[local.recipientStats.statusCode][local.linkPosition].hits, local.thisRow)/>
				</cfcase>
				<cfcase value="sg_open">
					<cfset local.thisRow["ip"] = local.recipientStats.ipaddress />
					<cfset arrayAppend(local.recipientStruct[local.recipientStats.statusCode], local.thisRow)/>
				</cfcase>
				<cfcase value="sg_spam">
					<cfset arrayAppend(local.recipientStruct[local.recipientStats.statusCode], local.thisRow)/>
				</cfcase>
			</cfswitch>
		</cfloop>

		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct["success"] = true>
		<cfset local.returnStruct["recipientStruct"] = local.recipientStruct>
		<cfreturn local.returnStruct>
	</cffunction>
	
	<cffunction name="editFooter" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();
			local.formLink = this.link.saveFooter;
			local.baseQueryString = getBaseQueryString(false);
			local.contentID = arguments.event.getValue('contentID',0);
			local.qryFooter = this.objEmailBlast.getFooter(siteID=arguments.event.getValue('mc_siteinfo.siteID'), contentID=local.contentID);
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfif arguments.event.getValue('mc_adminToolInfo.myRights.manageFooters',false)>
					<cfinclude template="frm_blastFooter.cfm">
				<cfelse>
					<h2>Not Allowed</h2>
					<p>You do not have appropriate permissions to perform this action.</p>
				</cfif>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="deleteFooter"  access="public" output="true" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="contentID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfset local.emailBlastSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='EmailBlast',siteID=arguments.mcproxy_siteID)>
			<cfset local.tmpRights = buildRightAssignments(siteResourceID=local.emailBlastSRID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.mcproxy_siteID)>

			<cfif NOT local.tmpRights.manageFooters >
				<cfthrow message="invalid request">
			</cfif>

			<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryDeletefooter">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @siteResourceID int,
					@contentID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.contentID#">,
					@siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">;

				SELECT @siteResourceID = siteResourceID
				FROM dbo.cms_content
				WHERE contentID = @contentID
				AND siteID = @siteID;
				
				EXEC dbo.cms_deleteSiteResourceAndChildren @siteID=@siteID, @siteResourceID=@siteResourceID;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
		
			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="saveFooter" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>

		<cfif arguments.event.getValue('mc_adminToolInfo.myRights.manageFooters',false)>
			<cfset local.blastFooterContent = application.objResourceRenderer.qualifyAllLinks(content=arguments.event.getTrimValue('blastFooterContent'), siteid=arguments.event.getValue('mc_siteinfo.siteid') )>

			<cfif arguments.event.getValue('contentID',0)>
				<cfset this.objEmailBlast.updateFooter(orgID=arguments.event.getValue('mc_siteInfo.orgID'), contentID=arguments.event.getValue('contentID'),
					footerName=arguments.event.getTrimValue('footerName'), footerContent=local.blastFooterContent,  footerStatus = arguments.event.getValue('footerStatus'), siteResourceID=arguments.event.getValue('siteResourceID'))>
			<cfelse>
				<cfset this.objEmailBlast.insertFooter(siteID=arguments.event.getValue('mc_siteInfo.siteID'), orgID=arguments.event.getValue('mc_siteInfo.orgID'),
					footerName=arguments.event.getTrimValue('footerName'), footerContent=local.blastFooterContent, footerStatus = arguments.event.getValue('footerStatus'))>
			</cfif>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				top.MCModalUtils.hideModal();
				parent.location.reload();
			</script>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="resendEmail" access="public" output="false" returntype="string">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="recipientID" type="numeric" required="true">
		<cfargument name="toEmail" type="string" required="true">
		
		<cfset var local = structNew()>
		<cfset local.doResendEmailLink = "/?pg=admin&mca_ajaxlib=emailBlast&mca_ajaxfunc=doResendEmail&rID=#arguments.recipientID#&mode=stream">

		<cfquery name="local.qryMessageTypeSettings" datasource="#application.dsn.platformMail.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @siteID int = <cfqueryparam value="#arguments.siteID#" cfsqltype="cf_sql_integer">;

			SELECT mt.messageType, mt.allowAdminResend, mt.allowAdminForward
			FROM dbo.email_messageRecipientHistory as mrh
			INNER JOIN dbo.email_messages AS m 
				on m.siteID = @siteID
				and m.status ='A'
				and m.messageID = mrh.messageID
			INNER JOIN dbo.email_messageTypes as mt on mt.messageTypeID = m.messageTypeID
			WHERE mrh.recipientID = <cfqueryparam value="#arguments.recipientID#" cfsqltype="cf_sql_integer">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
	
		<cfsavecontent variable="local.data">
			<cfif not local.qryMessageTypeSettings.allowAdminResend>
				<cfoutput>You have no permissions to perform this operation.</cfoutput>
			<cfelse>
				<cfinclude template="frm_resendEmail.cfm">
			</cfif>
		</cfsavecontent>
		
		<cfreturn local.data>
	</cffunction>

	<cffunction name="doResendEmail" access="public" output="false" returntype="string">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="recipientID" type="numeric" required="true">
		<cfargument name="toEmail" type="string" required="true">
		
		<cfset var local = structNew()>

		<cfquery name="local.qryMessageInfo" datasource="#application.dsn.platformMail.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @siteID int = <cfqueryparam value="#arguments.siteID#" cfsqltype="cf_sql_integer">;

			SELECT mrh.toEmail, mt.allowAdminResend, mt.allowAdminForward
			FROM dbo.email_messageRecipientHistory as mrh
			INNER JOIN dbo.email_messages AS m 
				on m.siteID = @siteID
				and m.status ='A'
				and m.messageID = mrh.messageID
			INNER JOIN dbo.email_messageTypes as mt on mt.messageTypeID = m.messageTypeID
			WHERE mrh.recipientID = <cfqueryparam value="#arguments.recipientID#" cfsqltype="cf_sql_integer">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfif NOT local.qryMessageInfo.allowAdminResend OR (NOT local.qryMessageInfo.allowAdminForward AND local.qryMessageInfo.toEmail NEQ arguments.toEmail)>
			<cfset local.data = "You have no permissions to perform this operation.">
		<cfelseif isValid("regex",arguments.toEmail,application.regEx.email)>
			<cfset CreateObject('component','emailBlast').resendRecipientEmail(siteID=arguments.siteID, recipientID=arguments.recipientID, toEmail=arguments.toEmail)>

			<cfsavecontent variable="local.data">
				<cfoutput>
				<script type="text/javascript">
					top.reloadRecipientMessagesTable(true);
					top.MCModalUtils.hideModal();
				</script>
				</cfoutput>
			</cfsavecontent>
		<cfelse>
			<cfset local.data = "Invalid E-mail.">
		</cfif>
		
		<cfreturn local.data>
	</cffunction>

	<cffunction name="listBlocks" access="private" output="false" returntype="struct">
		<cfargument name="Event" type="any" required="yes">

		<cfset var local = structNew()>

		<cfset local.qrySuppressionStatuses = CreateObject("component","suppressionListEmails").getSuppressionStatuses()>
		<cfset local.qryTypeOfMail = CreateObject("component","suppressionListEmails").getTypeOfMail(siteID=arguments.event.getValue('mc_siteInfo.siteID'))>
		<cfset local.suppressedEmailsLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=emailBlastJSON&meth=getSuppressedEmails&mode=stream">
		<cfset local.suppressionAuditLogLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=emailBlastJSON&meth=getSuppressionAuditLog&mode=stream">
		<cfset local.suppressedEmailsExportLink = buildCurrentLink(arguments.event,"exportBlocks") & "&mode=stream">
		<cfset local.memSelectLink = buildLinkToTool(toolType='MemberSelectorAdmin',mca_ta='list') & '&mode=direct'>
		<cfset local.grpSelectLink = buildLinkToTool(toolType='MemberSelectorAdmin',mca_ta='listGroups') & '&mode=direct'>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_blocks.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="exportBlocks" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = StructNew();
			local.gridmode = arguments.event.getValue('gridmode','');

			local.data = "The export file could not be generated. Contact MemberCentral for assistance.";
			local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.siteCode'));
			local.reportFileName = "SuppressedEmailsExport.csv";
			
			local.fCreatedFrom = arguments.event.getValue('fCreatedFrom','');
			local.fCreatedTo = arguments.event.getValue('fCreatedTo','');
			
			local.strContentArgs = {
				siteID = arguments.event.getValue('mc_siteInfo.siteID'),
				mode = "export",
				fCreatedFrom = replace(local.fCreatedFrom,' - ',' '),
				fCreatedTo = replace(local.fCreatedTo,' - ',' '),
				fEmailsList = arguments.event.getValue('fEmailsList',''),
				fSuppressionStatuses = arguments.event.getValue('fSuppressionStatuses',''),
				fAssociatedMemberID = val(arguments.event.getValue('fAssociatedMemberID',0)),
				fAssociatedGroupID = val(arguments.event.getValue('fAssociatedGroupID',0)),
				folderPathUNC = local.strFolder.folderPathUNC,
				reportFileName = local.reportFileName
			};
			
			CreateObject("component","suppressionListEmails").getSuppressedEmails(argumentcollection=local.strContentArgs);
			
			local.stDownloadURL = application.objDocDownload.createDownloadURL(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#", displayName=local.reportFileName, deleteSourceFile=1);
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<script type="text/javascript">doExportBlockedEmails('#local.stDownloadURL#');</script>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="removeEmailFromSuppressionList" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="email" type="string" required="true">
		<cfargument name="typesList" type="string" required="true">
		<cfargument name="subuserIDList" type="string" required="true">
		
		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>

		<cfset CreateObject("component","suppressionListEmails").removeEmailFromSuppressionList(siteID=arguments.mcproxy_siteID, email=arguments.email, typesList=arguments.typesList, subuserIDList=arguments.subuserIDList, memberID=session.cfcuser.memberdata.memberid)>

		<cfset local.returnStruct.success = true>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="embedBEEPlugin" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();

			if (NOT application.objUser.isSuperUser(cfcuser=session.cfcuser))
				return returnAppStruct("No rights.","echo");

			local.data = createObject("component","sitecomponents.common.javascript.beeplugin.beeplugin").embed(objName='testField', allowMergeCodes=true, editorMode="testEditor", siteID =arguments.event.getValue('mc_siteinfo.siteID'));
			
			return returnAppStruct(local.data,"echo");
		</cfscript>
	</cffunction>


	<cffunction name="manageEmailTemplates" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		
		local.strTemplateData = { 
			siteID=arguments.event.getValue('mc_siteinfo.siteid'),
			siteCode=arguments.event.getValue('mc_siteinfo.siteCode'),
			resourceType='EmailBlast',
			title="Manage Email Templates", 
			gridext="#this.siteResourceID#_1",
			initGridOnLoad=true,
			displayMode="tabs"
		};
		local.strResourceTemplatesGrid = createObject("component","model.admin.common.modules.resourceTemplates.resourceTemplate").manageResourceTemplates(strData=local.strTemplateData);
		</cfscript>

		<cfset local.assetCachingKey = application.objCMS.getPlatformCacheBusterKey()>

		<cfsavecontent variable="local.data">
			<cfsavecontent variable="local.customJS">
				<cfoutput>
				#local.strResourceTemplatesGrid.js#
				<link rel='stylesheet' type='text/css' href='/assets/common/css/resourceTemplate.css#local.assetCachingKey#' />
				<script type="text/javascript" src="/assets/common/javascript/jsonViewer/json-viewer.js"></script>
				<script type="text/javascript" src="/assets/admin/javascript/resourceTemplate.js#local.assetCachingKey#"></script>
				<script type="text/javascript" src="/assets/admin/javascript/resourceCustomFields.js#local.assetCachingKey#"></script>
				<script src="https://app-rsrc.getbee.io/plugin/BeePlugin.js" type="text/javascript"></script>
				</cfoutput>
			</cfsavecontent>
			<cfhtmlhead text="#application.objCommon.minText(local.customJS)#">

			<cfoutput>
			#local.strResourceTemplatesGrid.html#
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="message" access="public" output="false" returntype="struct" hint="Messages for this App">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
		</cfscript>
		<cfsavecontent variable="local.data">
			<cfoutput>
				<h4>We're Sorry...</h4>
				<cfif arguments.event.valueExists('message')>
					<p>
						<cfswitch expression="#arguments.event.getValue('message')#">
							<cfcase value="1"><b>You do not have rights to this page.</b></cfcase>
						</cfswitch>
					</p>
				</cfif>
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	<cffunction name="editCustomFont" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();
			local.formLink = this.link.saveCustomFont;
			local.baseQueryString = getBaseQueryString(false);
			local.customFontID = arguments.event.getValue('customFontID',0);
			local.qryCustomFont = this.objEmailBlast.getCustomFontDetails(siteID=arguments.event.getValue('mc_siteinfo.siteID'), customFontID=local.customFontID, mode='item');
			
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser)>
					<cfinclude template="frm_blast_customFont.cfm">
				<cfelse>
					<h2>Not Allowed</h2>
					<p>You do not have appropriate permissions to perform this action.</p>
				</cfif>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	
	<cffunction name="saveCustomFont" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>

		
			<cfif arguments.event.getValue('customFontID',0)>
				<cfset this.objEmailBlast.updateCustomFont(siteID=arguments.event.getValue('mc_siteInfo.siteID'), customFontID=arguments.event.getValue('customFontID'),
					name=arguments.event.getTrimValue('name'), fontCssUrl=arguments.event.getTrimValue('fontCssUrl'),  fontFamily = arguments.event.getValue('fontFamily'))>
			<cfelse>
				<cfset this.objEmailBlast.insertCustomFont(siteID=arguments.event.getValue('mc_siteInfo.siteID'), 
					name=arguments.event.getTrimValue('name'), fontCssUrl=arguments.event.getTrimValue('fontCssUrl'), fontFamily = arguments.event.getValue('fontFamily'))>
			</cfif>
		

		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
			top.reloadCustomFontTable();
				top.MCModalUtils.hideModal();
			</script>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
</cfcomponent>