ALTER PROC dbo.tr_populatecache_tr_ARByDayInvoiceProfile
@orgID int,
@reportDate date

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	IF OBJECT_ID('tempdb..#tmpARDayInvProf') IS NOT NULL 
		DROP TABLE #tmpARDayInvProf;
	CREATE TABLE #tmpARDayInvProf (invoiceProfileID int, ARAmount decimal(18,2));


	-- mark queue entry as processing
	DECLARE @queueTypeID int, @queueStatusID int;
	SELECT @queueTypeID = queueTypeID FROM platformQueue.dbo.tblQueueTypes WHERE queueType = 'ARByDayInvProf';
	SELECT @queueStatusID = queueStatusID FROM platformQueue.dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID AND queueStatus = 'processingItem';

	UPDATE platformQueue.dbo.queue_ARByDayInvProf 
	SET statusID = @queueStatusID
	WHERE orgID = @orgID
	AND arDate = @reportDate;

	IF @@ROWCOUNT = 0
		goto on_done;


	-- run AR report
	INSERT INTO #tmpARDayInvProf (invoiceProfileID, ARAmount)
	EXEC dbo.tr_report_baseAR @orgID=@orgID, @asOfDate=@reportDate, @startdate=null, @sumBy='arcache';


	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;


	-- update cache
	MERGE dbo.cache_tr_ARByDayInvoiceProfile AS t USING #tmpARDayInvProf AS s
		ON t.orgID = @orgID 
		AND t.asOfDate = @reportDate
		AND t.invoiceProfileID = s.invoiceProfileID
	WHEN MATCHED THEN
		UPDATE 
		SET t.amount = s.ARAmount
	WHEN NOT MATCHED By Target THEN
		INSERT (orgID, invoiceProfileID, asOfDate, amount) 
		VALUES (@orgID, s.invoiceProfileID, @reportDate, s.ARAmount);


	-- remove from queue
	DELETE FROM platformQueue.dbo.queue_ARByDayInvProf 
	WHERE orgID = @orgID
	AND arDate = @reportDate;


	on_done:
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	IF OBJECT_ID('tempdb..#tmpARDayInvProf') IS NOT NULL 
		DROP TABLE #tmpARDayInvProf;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
