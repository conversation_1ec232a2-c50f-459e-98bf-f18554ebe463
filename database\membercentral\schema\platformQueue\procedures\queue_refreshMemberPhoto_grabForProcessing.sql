ALTER PROC dbo.queue_refreshMemberPhoto_grabForProcessing
@batchSize int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @statusReady int, @statusGrabbed int;
	SELECT @queueTypeID = queueTypeID FROM dbo.tblQueueTypes WHERE queueType = 'refreshMemberPhoto';
	SELECT @statusReady = queuestatusID FROM dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID and queueStatus = 'ReadyToProcess';
	SELECT @statusGrabbed = queuestatusID FROM dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID and queueStatus = 'GrabbedForProcessing';

	IF OBJECT_ID('tempdb..#tmprefreshMemberPhotos') IS NOT NULL
		DROP TABLE #tmprefreshMemberPhotos;
	CREATE TABLE #tmprefreshMemberPhotos (itemID int);

	-- dequeue in order of dateAdded. get @batchsize entries
	UPDATE qid WITH (UPDLOCK, READPAST)
	SET qid.statusID = @statusGrabbed,
		qid.dateUpdated = getdate()
		OUTPUT inserted.itemID
		INTO #tmprefreshMemberPhotos
	FROM dbo.queue_refreshMemberPhoto AS qid
	INNER JOIN (
		SELECT TOP (@batchSize) itemID 
		FROM dbo.queue_refreshMemberPhoto
		WHERE statusID = @statusReady
		ORDER BY dateAdded, itemID
	) AS batch ON batch.itemID = qid.itemID
	WHERE qid.statusID = @statusReady;

	SELECT qid.itemID,qid.orgID, qid.statusID
	FROM #tmprefreshMemberPhotos AS tmp
	INNER JOIN dbo.queue_refreshMemberPhoto AS qid ON qid.itemID = tmp.itemID
	ORDER BY qid.itemID;

	IF OBJECT_ID('tempdb..#tmprefreshMemberPhotos') IS NOT NULL
		DROP TABLE #tmprefreshMemberPhotos;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
