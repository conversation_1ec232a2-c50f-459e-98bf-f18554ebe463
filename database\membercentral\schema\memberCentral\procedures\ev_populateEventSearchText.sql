ALTER PROC dbo.ev_populateEventSearchText
@itemID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @siteID int, @eventID int, @queueTypeID int, @processingItemStatusID int, @readyToProcessStatusID int, @itemStatusID int,
		@searchText varchar(max), @categories varchar(max), @authorities varchar(max), @creditTypes varchar(max), 
		@delimiter char(4) = '****';

	select @queueTypeID = queueTypeID from platformQueue.dbo.tblQueueTypes where queueType = 'eventSearchText';
	select @readyToProcessStatusID = queueStatusID from platformQueue.dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'readyToProcess';
	select @processingItemStatusID = queueStatusID from platformQueue.dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'processingItem';

	SELECT @siteID = siteID, @eventID = eventID, @itemStatusID = statusID
	FROM platformQueue.dbo.queue_eventSearchText
	WHERE itemID = @itemID;

	IF @itemStatusID <> @readyToProcessStatusID
		GOTO on_done;

	UPDATE platformQueue.dbo.queue_eventSearchText
	SET statusID = @processingItemStatusID
	WHERE itemID = @itemID;

	SELECT @searchText = eventcontent.contentTitle + ISNULL(@delimiter + ev.eventSubTitle,'') + 
		isnull(@delimiter + nullIf(searchMC.dbo.fn_StripHTMLAndCompress(eventcontent.rawContent),''),'') + 
		isnull(@delimiter + nullIf(locationcontent.contentTitle,''),'') + 
		isnull(@delimiter + nullIf(searchMC.dbo.fn_StripHTMLAndCompress(locationcontent.rawContent),''),'')
	FROM dbo.ev_events AS ev
	CROSS APPLY dbo.fn_getContent(ev.eventcontentID,1) as eventcontent
	CROSS APPLY dbo.fn_getContent(ev.locationcontentID,1) as locationcontent
	WHERE ev.eventID = @eventID
	AND ev.siteID = @siteID;

	SELECT @categories = STRING_AGG(c.category,'|') WITHIN GROUP (ORDER BY c.category ASC)
	FROM dbo.ev_eventCategories AS ec
	INNER JOIN dbo.ev_categories AS c ON c.categoryID = ec.categoryID
	WHERE ec.eventID = @eventID;

	SELECT @authorities = STRING_AGG(ca.authorityName,'|') WITHIN GROUP (ORDER BY ca.authorityName ASC)
	FROM dbo.crd_offerings AS ec
	INNER JOIN dbo.crd_statuses AS cstat ON cstat.statusID = ec.statusID
		AND cstat.[status] IN ('Approved', 'Pending')
	INNER JOIN dbo.crd_authoritySponsors AS cas ON cas.ASID = ec.ASID
	INNER JOIN dbo.crd_authorities AS ca ON ca.authorityID = cas.authorityID
	WHERE ec.eventID = @eventID;

	SELECT @creditTypes = STRING_AGG(isnull(ast.ovTypeName,cat.typeName),'|') WITHIN GROUP (ORDER BY ast.ovTypeName ASC, cat.typeName ASC)
	FROM dbo.crd_offerings AS ec
	INNER JOIN dbo.crd_statuses AS cstat on cstat.statusID = ec.statusID
		AND cstat.[status] IN ('Approved', 'Pending')
		AND ec.eventID = @eventID
	INNER JOIN dbo.crd_offeringTypes AS ect ON ect.offeringID = ec.offeringID
	INNER JOIN dbo.crd_authoritySponsorTypes as ast ON ast.ASTID = ect.ASTID
	INNER JOIN dbo.crd_authorityTypes as cat ON cat.typeID = ast.typeID;


	SET @searchText = @searchText + 
		ISNULL(@delimiter + NULLIF(@categories,''),'') + 
		ISNULL(@delimiter + NULLIF(@authorities,''),'') + 
		ISNULL(@delimiter + NULLIF(@creditTypes,''),'');

	MERGE searchMC.dbo.ev_events as t
	USING (select @siteID as siteID, @eventID as eventID) as s
		ON t.siteID = s.siteID AND t.eventID = s.eventID
	WHEN MATCHED THEN
		UPDATE SET t.searchText = @searchText
	WHEN NOT MATCHED THEN 
		INSERT (siteID, eventID, searchText)
		VALUES (s.siteID, s.eventID, @searchText);

	DELETE FROM platformQueue.dbo.queue_eventSearchText
	WHERE itemID = @itemID;

	on_done:
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
