ALTER PROC dbo.cp_createContributionSalesFromQueue
@scheduleID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @queueTypeID int, @readyToProcessStatusID int, @processingStatusID int, @doneStatusID int, @minInstallmentID int, @orgID int, 
		@siteID int, @nowDate datetime, @today date, @deletedStatusID int, @dueDate date, @memberID int, @programID int, @invoiceDueDate date, 
		@runByMemberID int, @invoiceProfileID int, @invoiceID int, @invoiceNumber varchar(18), @programMerchantProfiles varchar(1000), 
		@GLAccountID int, @amount decimal(18,2), @adjAmount decimal(18,2), @detail varchar(500), @offsetInstallmentTID int, @offsetTID int, 
		@saleTID int, @contributionID int, @itemType varchar(50), @trTypeID int, @MPProfileID int, @payProfileID int, @payProcessFee bit, 
		@processFeePercent decimal(5,2), @invoiceIDList varchar(max), @stateIDForTax int, @zipForTax varchar(25);

	set @nowDate = GETDATE();
	set @today = @nowDate;
	select @queueTypeID = queueTypeID from platformQueue.dbo.tblQueueTypes where queueType = 'contributionsSales';
	select @readyToProcessStatusID = queueStatusID from platformQueue.dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'readyToProcess';
	select @processingStatusID = queueStatusID from platformQueue.dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'processing';
	select @doneStatusID = queueStatusID from platformQueue.dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'done';
	select @deletedStatusID = statusID from dbo.cp_statuses where statusName = 'Deleted';
	select @runByMemberID = dbo.fn_ams_getMCSystemMemberID();
	select @trTypeID = dbo.fn_tr_getRelationshipTypeID('InstallSaleTrans');

	select top 1 @programID = c.programID, @stateIDForTax = c.stateIDForTax, @zipForTax = c.zipForTax
	from dbo.cp_contributionSchedule as cs
	inner join dbo.cp_contributions as c on cs.contributionID = c.contributionID
	where cs.scheduleID = @scheduleID;

	IF OBJECT_ID('tempdb..#tmpInstallments') is not null
		DROP TABLE #tmpInstallments;
	IF OBJECT_ID('tempdb..#tmpInvoices') is not null
		DROP TABLE #tmpInvoices;
	IF OBJECT_ID('tempdb..#tmpAuditLog') IS NOT NULL 
		DROP TABLE #tmpAuditLog;
	CREATE TABLE #tmpInstallments (installmentID int PRIMARY KEY, siteID int, orgID int, transactionID int, contributionID int, 
		dueDate date, detail varchar(500), amount decimal(18,2), memberID int, GLAccountID int, invoiceProfileID int, 
		itemType varchar(50), MPProfileID int, payProfileID int, payProcessFee bit, processFeePercent decimal(5,2));
	CREATE TABLE #tmpInvoices (invoiceProfileID int PRIMARY KEY, invoiceID int);
	CREATE TABLE #tmpAuditLog (siteID int, orgID int, auditCode varchar(10), msg varchar(max));

	-- get info from queue (could be multiple installments per schedule)
	INSERT INTO #tmpInstallments (installmentID, siteID, orgID, transactionID, contributionID, dueDate, detail, amount, 
		memberID, GLAccountID, invoiceProfileID, itemType, MPProfileID, payProfileID, payProcessFee, processFeePercent)
	select installmentID, siteID, orgID, installmentTransactionID, contributionID, scheduleDueDate, installmentDetail, 
		installmentAmt, memberID, GLAccountID, invoiceProfileID, trItemType, MPProfileID, payProfileID, payProcessFee, processFeePercent
	from platformQueue.dbo.queue_contributionsSales
	where scheduleID = @scheduleID
	and statusID = @readyToProcessStatusID;
	
	IF @@ROWCOUNT = 0
		goto on_end;
	
	update platformQueue.dbo.queue_contributionsSales
	set statusID = @processingStatusID,
		dateUpdated = getdate()
	where scheduleID = @scheduleID;

	INSERT INTO #tmpInvoices (invoiceProfileID)
	select distinct invoiceProfileID
	from #tmpInstallments;

	select @programMerchantProfiles = dbo.sortedIntList(distinct p.profileID)
	from dbo.cp_distributions as cpd
	inner join dbo.tr_GLAccounts as gl on gl.GLAccountID = cpd.GLAccountID
	inner join dbo.tr_invoiceProfiles as ip on ip.profileID = gl.invoiceProfileID
	inner join dbo.tr_invoiceProfilesMerchantProfiles as invP on invP.invoiceProfileID = ip.profileID
	inner join dbo.mp_profiles as p on p.profileID = invP.merchantProfileID
	inner join dbo.mp_gateways as g on g.gatewayID = p.gatewayID
	where cpd.programID = @programID
	AND p.status = 'A'
	AND g.isActive = 1;

	BEGIN TRAN;
		select @minInstallmentID = min(installmentID) from #tmpInstallments;
		while @minInstallmentID is not null begin
			select @orgID=null, @siteID=null, @memberID=null, @invoiceProfileID=null, @memberID=null, @dueDate=null, @amount=null,
				@invoiceID=null, @invoiceDueDate=null, @invoiceNumber=null, @GLAccountID=null, @adjAmount=null, @detail=null,
				@offsetInstallmentTID=null, @offsetTID=null, @contributionID=null, @itemType=null, @MPProfileID=null, @payProfileID=null,
				@payProcessFee=null, @processFeePercent=null;

			select @orgID=orgID, @siteID=siteID, @memberID=memberID, @contributionID=contributionID, @invoiceProfileID=invoiceProfileID, 
				@dueDate=dueDate, @GLAccountID=GLAccountID, @amount=amount, @adjAmount=amount*-1, @detail=detail, @itemType=itemType, 
				@MPProfileID=MPProfileID, @payProfileID=payProfileID, @payProcessFee=payProcessFee, @processFeePercent=processFeePercent, 
				@offsetInstallmentTID=transactionID
			from #tmpInstallments
			where installmentID = @minInstallmentID;

			-- get or create invoice
			select @invoiceID=invoiceID 
			from #tmpInvoices 
			where invoiceProfileID = @invoiceProfileID;

			IF @invoiceID is null BEGIN
				if @dueDate < @today
					set @invoiceDueDate = @today;
				else 
					set @invoiceDueDate = @dueDate;

				EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@runByMemberID, 
					@assignedToMemberID=@memberID, @dateBilled=@today, @dateDue=@invoiceDueDate, 
					@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT;

				IF @payProfileID is not null BEGIN
					update dbo.tr_invoices
					set payProfileID = @payProfileID,
						MPProfileID = @MPProfileID,
						payProcessFee = @payProcessFee,
						processFeePercent = @processFeePercent
					where invoiceID = @invoiceID;

					INSERT INTO #tmpAuditLog (siteID, orgID, auditCode, msg)
					SELECT @siteID, @orgID, 'INV', 'Pay Profile ' + detail + ' associated to Invoice ' + @invoiceNumber
					FROM dbo.ams_memberPaymentProfiles
					WHERE payProfileID = @payProfileID;

					IF @payProcessFee = 1 BEGIN
						INSERT INTO #tmpAuditLog (siteID, orgID, auditCode, msg)
						SELECT @siteID, @orgID, 'INV', 'Pay Processing Fees changed from No to Yes for Invoice ' + @invoiceNumber;

						INSERT INTO #tmpAuditLog (siteID, orgID, auditCode, msg)
						SELECT @siteID, @orgID, 'INV', 'Processing Fee Percentage changed from 0% to ' + CAST(ISNULL(@processFeePercent ,0) AS varchar(10)) + '% for Invoice ' + @invoiceNumber;
					END
				END

				update #tmpInvoices
				set invoiceID = @invoiceID
				where invoiceProfileID = @invoiceProfileID;

				IF @programMerchantProfiles is not null
					EXEC dbo.tr_createInvoiceMerchantProfile @invoiceID=@invoiceID, @profileIDList=@programMerchantProfiles;
			END

			-- create offset trans
			EXEC dbo.tr_createTransaction_installment @ownedByOrgID=@orgID, @recordedOnSiteID=@siteID, 
				@assignedToMemberID=@memberID, @recordedByMemberID=@runByMemberID, @statsSessionID=null, 
				@detail=@detail, @amount=@adjAmount, @transactionDate=@dueDate, @revenueGLAccountID=@GLAccountID, 
				@scheduleID=@scheduleID, @offsetInstallmentTID=@offsetInstallmentTID, @transactionID=@offsetTID OUTPUT;

			-- create sales trans
			EXEC dbo.tr_createTransaction_sale @ownedByOrgID=@orgID, @recordedOnSiteID=@siteID, @assignedToMemberID=@memberID, 
				@recordedByMemberID=@runByMemberID, @statsSessionID=null, @status='Active', @detail=@detail, 
				@parentTransactionID=null, @amount=@amount, @transactionDate=@nowDate, 
				@creditGLAccountID=@GLAccountID, @invoiceID=@invoiceID, @stateIDForTax=@stateIDForTax, @zipForTax=@zipForTax, 
				@taxAmount=0, @bypassTax=1, @bypassInvoiceMessage=0, @bypassAccrual=0, @xmlSchedule=null, @transactionID=@saleTID OUTPUT;

			-- relationship between sale and offset
			INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID, orgID)
			VALUES (@trTypeID, @saleTID, @offsetTID, @orgID);

			-- the new sale trans needs to be linked in tr_applications to whatever the installment trans was linked to
			INSERT INTO dbo.tr_applications (applicationTypeID, transactionID, itemType, itemID, subItemID, [status], orgID)
			select applicationTypeID, @saleTID, itemType, itemID, subItemID, [status], @orgID
			from dbo.tr_applications
			where orgID = @orgID
			and transactionID = @offsetInstallmentTID;

			-- mark installment as converted
			UPDATE dbo.tr_transactionInstallments
			SET isConverted = 1
			WHERE orgID = @orgID
			and installmentID = @minInstallmentID;

			select @minInstallmentID = min(installmentID) from #tmpInstallments where installmentID > @minInstallmentID;
		end

		-- close any invoices we just created
		select @invoiceIDList = COALESCE(@invoiceIDList + ',', '') + cast(invoiceID as varchar(20)) from #tmpInvoices;
		EXEC dbo.tr_closeInvoice @orgID=@orgID, @enteredByMemberID=@runByMemberID, @invoiceIDList=@invoiceIDList;

		-- populate invoice items
		EXEC dbo.tr_populateInvoiceItems @siteID=@siteID, @invoiceIDList=@invoiceIDList;

		IF EXISTS (SELECT 1 FROM #tmpAuditLog) BEGIN
			INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
			SELECT ('{ "c":"auditLog", "d": {
				"AUDITCODE":"'+ auditCode +'",
				"ORGID":' + cast(orgID as varchar(10)) + ',
				"SITEID":' + cast(siteID as varchar(10)) + ',
				"ACTORMEMBERID":' + cast(@runByMemberID as varchar(20)) + ',
				"ACTIONDATE":"' + convert(varchar(20),@nowDate,120) + '",
				"MESSAGE":"' + replace(dbo.fn_cleanInvalidXMLChars(msg),'"','\"') + '" } }')
			FROM #tmpAuditLog;
		END
	COMMIT TRAN;
	
	update platformQueue.dbo.queue_contributionsSales
	set statusID = @doneStatusID,
		dateUpdated = getdate()
	where scheduleID = @scheduleID;

	delete from platformQueue.dbo.queue_contributionsSales
	where scheduleID = @scheduleID;

	select top 1 @orgID=orgID, @memberID=memberID
	from #tmpInstallments;

	-- process conditions
	IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL 
		DROP TABLE #tblMCQRun;
	CREATE TABLE #tblMCQRun (orgID int, memberID int INDEX IX_tblMCQRun_memberID, conditionID int);

	INSERT INTO #tblMCQRun (orgID, memberID, conditionID)
	select distinct @orgID, @memberID, c.conditionID
	from dbo.ams_virtualGroupConditions as c
	cross apply (
		select cv.conditionValue
		from dbo.ams_virtualGroupConditionValues as cv
		inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'programList'
		where cv.conditionID = c.conditionID
	) as programList(val)
	where c.orgID = @orgID
	and c.fieldCode in ('cp_entry','cp_valuesum')
	and programList.val = cast(@programID as varchar(10));

	EXEC platformQueue.dbo.queue_triggerMCGCache @runImmediately=0, @type='ConditionsAndGroupsChanged';

	IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL 
		DROP TABLE #tblMCQRun;

	on_end:
	IF OBJECT_ID('tempdb..#tmpInstallments') is not null
		DROP TABLE #tmpInstallments;
	IF OBJECT_ID('tempdb..#tmpInvoices') is not null
		DROP TABLE #tmpInvoices;
	IF OBJECT_ID('tempdb..#tmpAuditLog') IS NOT NULL 
		DROP TABLE #tmpAuditLog;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
