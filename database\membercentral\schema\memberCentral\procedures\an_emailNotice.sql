ALTER PROC dbo.an_emailNotice
@siteID int,
@noticeID int,
@message varchar(max),
@receiveRFID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @recordedByMemberID int, @maxEmailsAllowedPerNotice int, @messageID int, @messageTypeID int, @contentVersionID int,
		@messageTypeCode varchar(15), @resourceTypeID int, @sendingSiteResourceID int, @newContentID int, 
		@newResourceID int,@contentTitle varchar(100), @messageStatusIDInserting int, @messageStatusIDQueued int, 
		@rawcontent varchar(max), @fieldID int, @fieldName varchar(300), @environmentID int, @orgID int, 
		@emailExtMergeCodeQueueTypeID int, @itemGroupUID uniqueidentifier, @emailExtMergeCodeInsertingQueueStatusID int, 
		@emailExtMergeCodeReadyQueueStatusID int, @globalTmpHoldTable varchar(100), @globalTmpTable varchar(100), 
		@colList varchar(max), @joinList varchar(max), @vwSQL varchar(max), @ParamDefinition nvarchar(100), 
		@dynSQL varchar(max), @fieldValueString varchar(max), @mcSQL nvarchar(max), @environmentName varchar(50);
	declare @metadataFields TABLE (fieldName varchar(300), fieldID int NULL, noticeID int);

	select @environmentName = tier from dbo.fn_getServerSettings();
	select @environmentID = environmentID from dbo.platform_environments where environmentName = @environmentName;

	select @recordedByMemberID = dbo.fn_ams_getMCSystemMemberID();
	select @messageTypeID = messageTypeID from platformMail.dbo.email_messageTypes where messageTypeCode = 'ANNOUNCE';
	select @messageStatusIDInserting = statusID from platformMail.dbo.email_statuses where statusCode = 'I';
	select @messageStatusIDQueued = statusID from platformMail.dbo.email_statuses where statusCode = 'Q';
	select @orgID = orgID from dbo.sites where siteID = @siteID;
	
	select @emailExtMergeCodeQueueTypeID = queueTypeID from platformQueue.dbo.tblQueueTypes where queueType = 'emailExtMergeCode';
	select @itemGroupUID = NEWID();

	select @emailExtMergeCodeInsertingQueueStatusID = queueStatusID
	from platformQueue.dbo.tblQueueStatuses
	where queueTypeID = @emailExtMergeCodeQueueTypeID 
	and queueStatus = 'insertingItems';

	select @emailExtMergeCodeReadyQueueStatusID = queueStatusID
	from platformQueue.dbo.tblQueueStatuses
	where queueTypeID = @emailExtMergeCodeQueueTypeID 
	and queueStatus = 'readyToProcess';

	select @maxEmailsAllowedPerNotice = 25000;

	IF OBJECT_ID('tempdb..#tmpNotice') IS NOT NULL 
		DROP TABLE #tmpNotice;
	IF OBJECT_ID('tempdb..#tmpNoticeRecipientsMID') IS NOT NULL 
		DROP TABLE #tmpNoticeRecipientsMID;
	IF OBJECT_ID('tempdb..#tmpRecipientsCols') IS NOT NULL 
		DROP TABLE #tmpRecipientsCols;
	IF OBJECT_ID('tempdb..#tmpEmailMetaDataFields') IS NOT NULL 
		DROP TABLE #tmpEmailMetaDataFields;
	IF OBJECT_ID('tempdb..#tmpMergeMDMemberIDs') IS NOT NULL
		DROP TABLE #tmpMergeMDMemberIDs;
	IF OBJECT_ID('tempdb..#tmpMergeMDResults') IS NOT NULL
		DROP TABLE #tmpMergeMDResults;
	IF OBJECT_ID('tempdb..#tmpNoticeMessageIDs') IS NOT NULL
		DROP TABLE #tmpNoticeMessageIDs;
	CREATE TABLE #tmpNoticeRecipientsMID (noticeID int, memberID int, memberNumber varchar(50), fullname varchar(150), mc_emailBlast_email varchar(255), mc_emailBlast_emailTypeID int, 
		recipientID int null, itemUID uniqueidentifier);
	CREATE TABLE #tmpRecipientsCols (ORDINAL_POSITION int, COLUMN_NAME sysname, datatype varchar(40), noticeID int);
	CREATE TABLE #tmpEmailMetaDataFields (siteID int, referenceID int, referenceType varchar(20), fieldName VARCHAR(300), fieldID int, isExtMergeCode bit, fieldTextToReplace varchar(max));
	CREATE TABLE #tmpMergeMDMemberIDs (memberID int PRIMARY KEY);
	CREATE TABLE #tmpMergeMDResults (MCAutoID int IDENTITY(1,1) NOT NULL);
	CREATE TABLE #tmpNoticeMessageIDs (noticeID int, messageID int, PRIMARY KEY (noticeID, messageID));

	SELECT n.noticeID, s.siteID, o.orgID, o.orgCode, s.siteCode, n.siteResourceID, ISNULL(optout_cl.consentListID,s.defaultConsentListID) AS optOutEmailConsentListID,
		coalesce(nullif(n.emailFromName,''),nullif(c.emailFromName,''),s.sitename) as emailFromName, net.emailFrom as emailFromEmail,
		coalesce(nullif(n.emailFromAddress,''),nullif(c.emailFromAddress,''),net.supportProviderEmail) as emailReplyToEmail,
		net.supportProviderEmail as emailSenderEmail,
		cl.contentTitle as emailSubject, n.noticeContentID, n.notifyEmail, NEWID() as messageUID, 
		0 as numRecipients, @maxEmailsAllowedPerNotice as maxEmailsAllowedPerNotice, 0 as hasExtendedMergeCodes, 100 as initialQueuePriority
	INTO #tmpNotice
	FROM dbo.an_notices as n 
	INNER JOIN dbo.cms_siteResources as sr ON sr.siteID = @siteID
		AND n.siteResourceID = sr.siteResourceID 
		AND sr.siteResourceStatusID = 1
	INNER JOIN dbo.sites as s on s.siteID = sr.siteID
	INNER JOIN dbo.organizations as o on o.orgID = s.orgID
	INNER JOIN dbo.siteHostNames as sh on sh.siteID = s.siteID 
	inner join dbo.siteEnvironments as se on se.siteID = sh.siteID
		AND se.environmentID = @environmentID
		AND se.mainHostnameID = sh.hostNameID
	inner join dbo.networkSites as ns on ns.siteID = s.siteID AND ns.isLoginNetwork = 1
	INNER JOIN dbo.networks as net on net.networkID = ns.networkID
	INNER JOIN dbo.an_centers as c on c.centerID = n.centerID
	INNER JOIN dbo.cms_applicationInstances as Cai on Cai.siteID = @siteID
		AND Cai.applicationInstanceID = c.applicationInstanceID
	INNER JOIN dbo.cms_siteResources as Csr on Csr.siteID = @siteID
		AND Cai.siteResourceID = Csr.siteResourceID 
		AND Csr.siteResourceStatusID = 1
	INNER JOIN dbo.cms_siteResources as CparentResource on CparentResource.siteID = @siteID
		AND CparentResource.siteResourceID = Csr.parentSiteResourceID
	LEFT OUTER JOIN dbo.cms_siteResources as CgrandparentResource
		INNER JOIN dbo.cms_applicationInstances as CCommunityInstances on CCommunityInstances.siteID = @siteID
			AND CcommunityInstances.siteResourceID = CgrandParentResource.siteResourceID
		INNER JOIN dbo.cms_siteResourceTypes as Csrt on Csrt.resourceTypeID = CgrandparentResource.resourceTypeID AND Csrt.resourceType = 'Community'
		on CgrandparentResource.siteResourceID = CparentResource.parentSiteResourceID
	INNER JOIN dbo.cms_contentLanguages as cl ON cl.siteID = @siteID
		AND cl.contentID = n.noticeContentID 
		AND cl.languageID = 1
	INNER JOIN dbo.cms_contentVersions as cv on cv.siteID = @siteID
		AND cv.contentLanguageID = cl.contentLanguageID 
		AND cv.isActive = 1
	CROSS APPLY dbo.fn_getContent(n.noticeContentID,1) as noticeContent
	LEFT OUTER JOIN platformMail.dbo.email_consentLists AS optout_cl 
		INNER JOIN platformMail.dbo.email_consentListTypes AS clt ON clt.orgID = @orgID
			AND clt.consentListTypeID = optout_cl.consentListTypeID
		INNER JOIN platformMail.dbo.email_consentListModes AS clm ON clm.consentListModeID = optout_cl.consentListModeID
			AND clm.modeName = 'Opt-Out'
		ON optout_cl.consentListID = c.emailConsentListID
	WHERE n.emailNotice = 1
	AND n.emailDateScheduled < getDate()
	AND n.endDate > getDate()
	AND n.emailDateSent IS NULL
	AND noticeContent.rawContent LIKE '_%'
    AND n.noticeID = @noticeID AND s.siteID = @siteID;

    INSERT INTO #tmpNoticeRecipientsMID (noticeID, memberID, memberNumber, fullName, mc_emailBlast_email, mc_emailBlast_emailTypeID, itemUID)
    SELECT n.noticeID, min(m.memberID) as memberID, m.memberNumber, m.firstname + ' ' + m.lastname as fullname, me.email, metag.emailTypeID, NEWID()
    FROM dbo.an_notices as n
    INNER JOIN #tmpNotice as tmp on tmp.noticeID = n.noticeID and tmp.siteID = @siteID
    INNER JOIN dbo.cache_perms_siteResourceFunctionRightPrints as srfrp on srfrp.siteID = @siteID
        and srfrp.siteResourceID = n.siteResourceID
        and srfrp.functionID = @receiveRFID
    INNER JOIN dbo.cache_perms_groupPrintsRightPrints as gprp on gprp.siteID = @siteID
        and gprp.rightPrintID = srfrp.rightPrintID
    INNER JOIN dbo.cache_perms_groupPrints as gp on gp.groupPrintID = gprp.groupPrintID
    INNER JOIN dbo.ams_members as m on m.orgID = @orgID 
		and m.groupPrintID = gp.groupPrintID
        and m.status = 'A'
    INNER JOIN dbo.ams_memberEmails as me on me.orgID = @orgID
        and me.memberID = m.memberID 
    INNER JOIN dbo.ams_memberEmailTags as metag on metag.orgID = @orgID
		and metag.memberID = me.memberID
        and metag.emailTypeID = me.emailTypeID
    INNER JOIN dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = @orgID
		and metagt.emailTagTypeID = metag.emailTagTypeID
        and metagt.emailTagType = 'Primary'
    WHERE me.email <> ''
    GROUP BY n.noticeID, me.email, m.memberNumber, m.firstname, m.lastname, metag.emailTypeID;

	-- put recipient count in notices table
	update #tmpNotice set numRecipients = (select count(*) from #tmpNoticeRecipientsMID where noticeID = #tmpNotice.noticeID);

	update #tmpNotice set initialQueuePriority =  platformMail.dbo.fn_getInitialRecipientQueuePriority(@messageTypeID,	numRecipients);

	-- cancel notices that are over the threshold of recipients or have no recipients
	update n
	set n.emailNotice = 0
	from dbo.an_notices as n
	inner join #tmpNotice as tmp on tmp.noticeID = n.noticeID
	where tmp.numRecipients >= @maxEmailsAllowedPerNotice
	or tmp.numRecipients = 0;

	-- delete notices where there are no recipients
	delete from #tmpNotice where numRecipients = 0;

	IF NOT EXISTS (select 1 from #tmpNotice)
		GOTO on_done;

	-- add any necessary metadata fields
    select @globalTmpHoldTable=null, @globalTmpTable=null, @rawcontent=null, @colList=null, @joinList=null;

    select @globalTmpHoldTable = '##tmpHold' + replace(cast(messageUID as varchar(36)),'-', ''), 
        @globalTmpTable = '##tmp' + replace(cast(messageUID as varchar(36)),'-', ''), @rawcontent = @message
    from #tmpNotice
    where noticeID = @noticeID;

    IF OBJECT_ID('tempdb..' + @globalTmpHoldTable) IS NOT NULL 
        EXEC('DROP TABLE ' + @globalTmpHoldTable);
    IF OBJECT_ID('tempdb..' + @globalTmpTable) IS NOT NULL 
        EXEC('DROP TABLE ' + @globalTmpTable);

    -- find/insert merge code fields
    EXEC platformMail.dbo.email_massInsertMetaDataFields @siteID=@siteID, @referenceID=@noticeID, @referenceType='emailnotice', @messageToParse=@rawcontent, @extraMergeCodeList='';

	INSERT INTO #tmpMergeMDMemberIDs (memberID)
	SELECT DISTINCT memberID
	FROM #tmpNoticeRecipientsMID
	WHERE noticeID = @noticeID;

	EXEC dbo.ams_getMemberDataByMergeCodeContent @orgID=@orgID, @content=@rawcontent,
		@codePrefix='', @membersTableName='#tmpMergeMDMemberIDs', @membersResultTableName='#tmpMergeMDResults',
		@colList=@colList OUTPUT;

    set @vwSQL = null;
    IF @colList is null
        set @vwSQL = 'select memberID, noticeID into ' + @globalTmpHoldTable + ' from #tmpNoticeRecipientsMID where noticeID = ' + cast(@noticeID as varchar(10)) + ';'
    ELSE
        set @vwSQL = 'select m.memberID, m.noticeID, ' + @colList + ' 
            into ' + @globalTmpHoldTable + '
            from #tmpNoticeRecipientsMID as m 
			inner join #tmpMergeMDResults as vwmd on vwmd.memberID = m.memberID
            where m.noticeID = ' + cast(@noticeID as varchar(10)) + ';';
        
    EXEC(@vwSQL);

    set @dynSQL = null;
    set @dynSQL = 'select m.prefix, m.firstName, m.middlename, m.lastName, m.company, m.suffix, m.professionalsuffix, 
                    m.membernumber, m.firstname + '' '' + m.lastname as fullname, 
                    m.firstname + isnull('' '' + nullif(m.middlename,''''),'''') + '' '' + m.lastname + isnull('' '' + nullif(m.suffix,''''),'''') as extendedname, 
                    tmp.mc_emailBlast_email, tmp.mc_emailBlast_emailTypeID, vw.*
                into ' + @globalTmpTable + '
                from #tmpNoticeRecipientsMID as tmp
                inner join dbo.ams_members as m WITH(NOLOCK) on m.memberID = tmp.memberID
                inner join ' + @globalTmpHoldTable + ' as vw on vw.memberID = m.memberID 
                where tmp.noticeID = ' + cast(@noticeID as varchar(10)) + ';'
    EXEC(@dynSQL);

    IF OBJECT_ID('tempdb..' + @globalTmpHoldTable) IS NOT NULL 
        EXEC('DROP TABLE ' + @globalTmpHoldTable);

    -- get tmpRecipients columns
    INSERT INTO #tmpRecipientsCols
    select c.column_id, c.name, t.name, @noticeID
    from tempdb.sys.columns as c
    INNER JOIN tempdb.sys.types AS t ON c.user_type_id = t.user_type_id
    where c.object_id = object_id('tempdb..' + @globalTmpTable);

		
	-- update tmpNotices having extendedMergeCodes
	update tmp 
	set tmp.hasExtendedMergeCodes = 1
	from #tmpNotice as tmp
	where exists (select 1 from #tmpEmailMetaDataFields where referenceID = tmp.noticeID and isExtMergeCode = 1);

    -- to get contentVersionID for the message
	SET @resourceTypeID = dbo.fn_getResourceTypeID('ApplicationCreatedContent');
	SET @messageTypeCode = 'ANNOUNCE'

	select @messageTypeID = messageTypeID
	from platformMail.dbo.email_messageTypes 
	where messageTypeCode = @messageTypeCode;

	select @sendingSiteResourceID = ai.siteResourceID
	from dbo.cms_applicationInstances as ai
		inner join dbo.cms_siteResources as sr on 
			sr.siteID =  @siteID
			and sr.siteResourceID = ai.siteResourceID
		inner join dbo.an_notices as n  on
			n.noticeID = @noticeID
			and ai.siteResourceID = n.siteResourceID
	where ai.siteID =  @siteID
		and sr.siteResourceStatusID = 1;	
     
    SELECT @contentTitle =oi.organizationName 
    FROM #tmpNotice as tmpN
	INNER join membercentral.dbo.organizations o ON o.orgID = tmpN.orgID
	inner join membercentral.dbo.orgIdentities as oi on oi.orgID = o.orgID and oi.orgIdentityID = o.defaultOrgIdentityID
	where tmpN.noticeid= @noticeID;

	IF @recordedByMemberID = 0
		select @recordedByMemberID = dbo.fn_ams_getMCSystemMemberID();
    EXEC dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@resourceTypeID, 
		@parentSiteResourceID=@sendingSiteResourceID, @siteResourceStatusID=1, @isHTML=1, 
		@languageID=1, @isActive=1, @contentTitle=@contentTitle, @contentDesc='', @rawContent=@rawContent, 
		@memberID=@recordedByMemberID, @contentID=@newContentID OUTPUT, @siteResourceID=@newResourceID OUTPUT;

	select top 1 @contentVersionID = cv.contentVersionID
	from dbo.cms_content as c 
	inner join dbo.cms_contentLanguages as cl on c.contentID = cl.contentID and cl.languageID = 1
	inner join dbo.cms_contentVersions as cv on cv.contentLanguageID = cl.contentLanguageID and cv.isActive = 1
	where c.contentID = @newContentID;

	-- each notice becomes its own email_message (as queued status)
	insert into platformMail.dbo.email_messages (messageTypeID, siteID, sendingSiteResourceID, dateEntered, 
		recordedByMemberID, fromName, fromEmail, replyToEmail, senderEmail, [subject], contentVersionID, 
		messageWrapper, [uid], sendOnDate, referenceType, referenceID, isTestMessage)
		output inserted.messageID, inserted.referenceID into #tmpNoticeMessageIDs (messageID, noticeID)
	select @messageTypeID, siteID, siteResourceID, getdate(), @recordedByMemberID, emailFromName,
		emailFromEmail, emailReplyToEmail, emailSenderEmail, emailSubject, @contentVersionID, 
		'', messageUID, getdate(), 'AnnounceNotice', noticeID, 0
	from #tmpNotice
	where numRecipients < @maxEmailsAllowedPerNotice;

	-- email consentLists
	INSERT INTO platformMail.dbo.email_messageConsentLists (siteID, messageID, consentListID, isPrimary)
	SELECT n.siteID, tmp.messageID, n.optOutEmailConsentListID, 1
	FROM #tmpNotice AS n
	INNER JOIN #tmpNoticeMessageIDs AS tmp ON tmp.noticeID = n.noticeID
	WHERE n.optOutEmailConsentListID IS NOT NULL;

	-- add recipients
	insert into platformMail.dbo.email_messageRecipientHistory(messageID, memberID, 
		dateLastUpdated, toName, toEmail, emailStatusID, batchID, batchStartDate, emailTypeID, siteID,queuePriority)
	select tmp.messageID, tmpR.memberID, getdate(), tmpR.fullname, tmpR.mc_emailBlast_email, 
		@messageStatusIDInserting, null, null, tmpR.mc_emailBlast_emailTypeID, tmpN.siteID, tmpN.initialQueuePriority
	from #tmpNoticeMessageIDs tmp
	inner join #tmpNotice as tmpN on tmpN.noticeID = tmp.noticeID
	inner join #tmpNoticeRecipientsMID as tmpR on tmpR.noticeID = tmpN.noticeID

	-- no need to support multiple recipientIDs per memberID per noticeID, so simple join is sufficient
	update tmpR
	set tmpR.recipientID = r.recipientID
	from #tmpNoticeMessageIDs tmp
	inner join platformMail.dbo.email_messageRecipientHistory as r on r.siteID = @siteID
		and r.messageID = tmp.messageID
	inner join #tmpNotice as tmpN on tmpN.noticeID = tmp.noticeID
	inner join #tmpNoticeRecipientsMID as tmpR on tmpR.noticeID = tmpN.noticeID
		and tmpR.memberID = r.memberID;

	-- loop over each notice to add any necessary metadata fields

	select @messageID = min(tmp.messageID)
	from #tmpNoticeMessageIDs tmp

	while @messageID is not null BEGIN
		select @noticeID = null, @siteID = null, @globalTmpTable = null, @fieldID = null, @ParamDefinition = null;

		select @noticeID = tmpN.noticeID, @siteID = tmpN.siteID, @globalTmpTable = '##tmp' + replace(cast(tmpN.messageUID as varchar(36)),'-', '')
		from #tmpNoticeMessageIDs tmp
		inner join #tmpNotice as tmpN on tmpN.noticeID = tmp.noticeID
		where tmp.messageID = @messageID;
		
		-- add recipient metadata
		set @ParamDefinition = N'@messageID int, @fieldID int';	
		select @fieldID = min(fieldID) from #tmpEmailMetaDataFields where referenceID = @noticeID and isExtMergeCode = 0;
		while @fieldID is not null BEGIN
			select @fieldName = null, @mcSQL = null, @fieldValueString = null;
			select @fieldName = fieldName from #tmpEmailMetaDataFields where referenceID = @noticeID and fieldID = @fieldID and isExtMergeCode = 0;

			-- ensure field is available (could be a bad merge code)
			IF EXISTS (select ORDINAL_POSITION from #tmpRecipientsCols where column_name = @fieldName and noticeID = @noticeID) BEGIN
				set @fieldValueString = 'isnull(cast(tmp.[' + @fieldName + '] as varchar(max)),'''')';

				set @mcSQL = 'insert into platformMail.dbo.email_messageMetadataFields (messageID, fieldID, memberID, fieldValue, recipientID)
					select @messageID, @fieldID, tmp.memberID, fieldValue = ' + @fieldValueString + ', tmpR.recipientID
					from ' + @globalTmpTable +  ' as tmp
					inner join #tmpNoticeRecipientsMID as tmpR on tmpR.noticeID = tmp.noticeID
						and tmpR.memberID = tmp.memberID;';
				exec sp_executesql @mcSQL, @ParamDefinition, @messageID=@messageID, @fieldID=@fieldID;
			END

			select @fieldID = min(fieldID) from #tmpEmailMetaDataFields where referenceID = @noticeID and isExtMergeCode = 0 and fieldID > @fieldID;
		END

		-- has extended merge codes
		IF EXISTS (select top 1 fieldID from #tmpEmailMetaDataFields where referenceID = @noticeID and isExtMergeCode = 1) BEGIN
			
			-- queue recipient details with extended merge codes
			INSERT INTO platformQueue.dbo.tblQueueItems (itemUID, queueStatusID)
			select itemUID, @emailExtMergeCodeInsertingQueueStatusID
			from #tmpNoticeRecipientsMID
			where noticeID = @noticeID;

			-- recipientID and messageID
			INSERT INTO platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, columnValueInteger)
			select @itemGroupUID, tmp.itemUID, @recordedByMemberID, @siteID, dc.columnID, tmp.recipientID
			from #tmpNoticeRecipientsMID as tmp
			inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @emailExtMergeCodeQueueTypeID and dc.columnname = 'MCRecipientID'
			where tmp.noticeID = @noticeID
				union
			select @itemGroupUID, tmp.itemUID, @recordedByMemberID, @siteID, dc.columnID, @messageID
			from #tmpNoticeRecipientsMID as tmp
			inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @emailExtMergeCodeQueueTypeID and dc.columnname = 'MCMessageID'
			where tmp.noticeID = @noticeID;

			-- ext merge code fields
			INSERT INTO platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueInteger, columnValueText)
			select distinct @itemGroupUID, tmp.itemUID, @recordedByMemberID, @siteID, dc.columnID, mdf.fieldName, mdf.fieldID, mdf.fieldTextToReplace
			from #tmpEmailMetaDataFields as mdf
			inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @emailExtMergeCodeQueueTypeID and dc.columnname = 'MCExtMergeCodeFieldID'
			inner join #tmpNoticeRecipientsMID as tmp on tmp.noticeID = @noticeID
			where mdf.referenceID = @noticeID
			and mdf.isExtMergeCode = 1;

			-- update queue item groups to show ready to process
			UPDATE qi
			SET qi.queueStatusID = @emailExtMergeCodeReadyQueueStatusID,
				qi.dateUpdated = GETDATE()
			FROM platformQueue.dbo.tblQueueItems as qi
			INNER JOIN #tmpNoticeRecipientsMID as tmp on tmp.itemUID = qi.itemUID
			WHERE tmp.noticeID = @noticeID;
		END

		select @messageID = min(tmp.messageID)
		from #tmpNoticeMessageIDs tmp
		where tmp.messageID > @messageID;
	END

	-- mark recipient as ready for notice recipients which doesn't have extended merge codes
	update r
	set r.emailStatusID = @messageStatusIDQueued
	from #tmpNoticeMessageIDs tmp
	inner join platformMail.dbo.email_messageRecipientHistory as r on r.siteID = @siteID
		and r.messageID = tmp.messageID
	inner join #tmpNotice as tmpN on tmpN.noticeID = tmp.noticeID
	inner join #tmpNoticeRecipientsMID as tmpR on tmpR.noticeID = tmpN.noticeID
		and tmpR.memberID = r.memberiD
		and tmpN.hasExtendedMergeCodes = 0;

	-- update notices with datesent
	update n
	set n.emailDateSent = getDate()
	from dbo.an_notices as n
	inner join #tmpNotice as tmp on tmp.noticeID = n.noticeID
	where tmp.numRecipients < @maxEmailsAllowedPerNotice;


	on_done:

	-- return notices
	select noticeID, siteID, orgID, orgCode, siteCode, siteResourceID, emailFromName, emailFromEmail, emailReplyToEmail, emailSenderEmail, emailSubject,
		notifyEmail, messageUID, @message, numRecipients, maxEmailsAllowedPerNotice, hasExtendedMergeCodes
	from #tmpNotice;

	-- drop global tmp tables
	set @noticeID = null;
	select @noticeID = min(noticeID) from #tmpNotice;
	while @noticeID is not null BEGIN
		select @globalTmpTable=null;

		select @globalTmpTable = '##tmp' + replace(cast(messageUID as varchar(36)),'-', '')
		from #tmpNotice
		where noticeID = @noticeID;

		IF OBJECT_ID('tempdb..' + @globalTmpTable) IS NOT NULL 
			EXEC('DROP TABLE ' + @globalTmpTable);

		select @noticeID = min(noticeID) from #tmpNotice where noticeID > @noticeID;
	END


	IF OBJECT_ID('tempdb..#tmpNotice') IS NOT NULL 
		DROP TABLE #tmpNotice;
	IF OBJECT_ID('tempdb..#tmpNoticeRecipientsMID') IS NOT NULL 
		DROP TABLE #tmpNoticeRecipientsMID;
	IF OBJECT_ID('tempdb..#tmpRecipientsCols') IS NOT NULL 
		DROP TABLE #tmpRecipientsCols;
	IF OBJECT_ID('tempdb..#tmpEmailMetaDataFields') IS NOT NULL 
		DROP TABLE #tmpEmailMetaDataFields;
	IF OBJECT_ID('tempdb..#tmpMergeMDMemberIDs') IS NOT NULL
		DROP TABLE #tmpMergeMDMemberIDs;
	IF OBJECT_ID('tempdb..#tmpMergeMDResults') IS NOT NULL
		DROP TABLE #tmpMergeMDResults;
	IF OBJECT_ID('tempdb..#tmpNoticeMessageIDs') IS NOT NULL
		DROP TABLE #tmpNoticeMessageIDs;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
