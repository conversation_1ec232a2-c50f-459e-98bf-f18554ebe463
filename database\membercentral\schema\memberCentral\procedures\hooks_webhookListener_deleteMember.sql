ALTER PROC dbo.hooks_webhookListener_deleteMember
@xmlData xml

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @statusReady int, @siteID int, @webhookID int, @webhookURL varchar(400), 
		@eventID int, @payloadXML xml, @payloadMessage varchar(max), @SQSQueueName varchar(80), @environmentName varchar(50),
		@nowDate datetime = GETDATE(), @memberID int, @memberNumber varchar(50), @reason varchar(100);

	SELECT @queueTypeID = queueTypeID FROM platformQueue.dbo.tblQueueTypes WHERE queueType = 'webhook';
	SELECT @statusReady = queueStatusID FROM platformQueue.dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID AND queueStatus = 'readyToProcess';
	SELECT @eventID = eventID FROM dbo.hooks_events where [event] = 'deleteMember';
	SELECT @environmentName = tier FROM dbo.fn_getServerSettings();

	SELECT @siteID = @xmlData.value('(/wh/@s)[1]','int');
	SELECT @webhookID = @xmlData.value('(/wh/@w)[1]','int');
	SELECT @payloadXML = @xmlData.query('wh/data');
	SELECT @memberID = @payloadXML.value('(/data/memberid)[1]', 'int');
	SELECT @memberNumber = @payloadXML.value('(/data/membernumber)[1]', 'varchar(50)');
	SELECT @reason = @payloadXML.value('(/data/reason)[1]', 'varchar(100)');

	SELECT @webhookURL = hookURL, @SQSQueueName = SQSQueueName FROM dbo.hooks_webhooks WHERE webhookID = @webhookID;

	SELECT @payloadMessage = '{ "mcwh_event":"memberdelete", "mcwh_eventid":"' + CAST(NEWID() as varchar(36)) + '", "mcwh_env":"' + @environmentName + '", '+
		'"membernumber": "' + @memberNumber + '", "datelastupdated": "' +  FORMAT(dateLastUpdated,'MMMM, dd yyyy hh:mm:ss tt') + '", ' + '"reason":"'+@reason+'" }'
	FROM dbo.ams_members
	WHERE memberID = @memberID;
  
	INSERT INTO platformQueue.dbo.queue_webhook (siteID, webhookID, webhookURL, eventID, payloadMessage, SQSQueueName, statusID, dateAdded, dateUpdated, nextAttemptDate)
	VALUES (@siteID, @webhookID, @webhookURL, @eventID, @payloadMessage, @SQSQueueName, @statusReady, @nowDate, @nowDate, @nowDate);

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
