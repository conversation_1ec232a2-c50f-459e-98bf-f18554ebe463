ALTER PROC dbo.queue_featuredImages_grabForProcessing
@batchSize int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @queueTypeID int, @statusReady int, @statusGrabbed int, @batchStartDate datetime = getdate();
	select @queueTypeID = queueTypeID from dbo.tblQueueTypes where queueType = 'featuredImages';
	select @statusReady = queuestatusID from dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'readyToProcess';
	select @statusGrabbed = queuestatusID from dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'grabbedForProcessing';

	IF OBJECT_ID('tempdb..#tmpFeaturedImages') IS NOT NULL
		DROP TABLE #tmpFeaturedImages;
	CREATE TABLE #tmpFeaturedImages (itemID int);

	-- dequeue in order of dateAdded. get @batchsize subscribers
	UPDATE qid WITH (UPDLOCK, READPAST)
	SET qid.statusID = @statusGrabbed,
		qid.dateUpdated = @batchStartDate
		OUTPUT inserted.itemID
		INTO #tmpFeaturedImages
	FROM dbo.queue_featuredImages as qid
	INNER JOIN (
		SELECT TOP (@BatchSize) itemID 
		FROM dbo.queue_featuredImages
		WHERE statusID = @statusReady
		AND nextAttemptDate < @batchStartDate
		ORDER BY dateAdded, itemID
	) as batch on batch.itemID = qid.itemID
	WHERE qid.statusID = @statusReady;

	-- return featured images info without depending much on featuredImage tables since they may be altered after queuing and queue items will be orphaned
	select distinct qid.itemID, qid.featureImageID, qid.featureImageSizeID, s.siteID, o.orgID, s.siteCode, o.orgCode,
		qid.enteredByMemberID, fi.originalWidth, fi.originalHeight, fi.fileExtension as origFileExtension,
		fics.width, fics.height, fics.fileExtension, m.methodType, m.horizontalAlign, m.verticalAlign
	from #tmpFeaturedImages as tmp
	inner join dbo.queue_featuredImages as qid on qid.itemID = tmp.itemID
	left outer join membercentral.dbo.cms_featuredImages as fi
		inner join membercentral.dbo.cms_featuredImageUsages as fiu on fiu.featureImageID = fi.featureImageID
		inner join membercentral.dbo.cms_featuredImageConfigs as fic on fic.featureImageConfigID = fiu.featureImageConfigID
		inner join membercentral.dbo.sites as s on s.siteID = fi.siteID
		inner join membercentral.dbo.organizations as o on o.orgID = s.orgID
		on fi.featureImageID = qid.featureImageID
	inner join membercentral.dbo.cms_featuredImageConfigSizes as fics on fics.featureImageSizeID = qid.featureImageSizeID
	INNER JOIN membercentral.dbo.cms_featuredImageConfigSizeMethods as m on m.methodID = fics.methodID
	order by qid.itemID;


	IF OBJECT_ID('tempdb..#tmpFeaturedImages') IS NOT NULL
		DROP TABLE #tmpFeaturedImages;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
