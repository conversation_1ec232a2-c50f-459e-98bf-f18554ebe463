ALTER PROC dbo.cache_perms_autoCorrect 
@itemCount int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @errorSubject varchar(300),@errorTitle varchar(300), @messagetext varchar(max), @correctionID int, 
		@thisSiteID int, @startDate datetime, @endDate datetime, @4HoursAgo datetime, 
		@startingGroupPrintID int, @startingRightPrintID int;
	declare @myCorrections TABLE (siteID int NOT NULL, roleID int NOT NULL, resourceID int NOT NULL, functionID int NOT NULL,
		groupID int NOT NULL, [include] bit NOT NULL, universalRoleResourceRightsID int NULL, universalRoleResourceTypeID int NULL);

	set @4HoursAgo = dateadd(hour,-4,getdate());
	set @startDate = getdate();

	IF OBJECT_ID('tempdb..#siteResourcesToCheckPrep1') IS NOT NULL
		DROP TABLE #siteResourcesToCheckPrep1;
	IF OBJECT_ID('tempdb..#siteResourcesToCheck') IS NOT NULL
		DROP TABLE #siteResourcesToCheck;
	IF OBJECT_ID('tempdb..#platformWideSiteResourcesToProcess') IS NOT NULL
		DROP TABLE #platformWideSiteResourcesToProcess;
	IF OBJECT_ID('tempdb..#siteResourcesToProcess') IS NOT NULL
		DROP TABLE #siteResourcesToProcess;
    CREATE TABLE #platformWideSiteResourcesToProcess (siteResourceID int PRIMARY KEY, siteID int, resourceTypeID int);
	CREATE TABLE #siteResourcesToProcess (siteResourceID int PRIMARY KEY, resourceTypeID int);
	CREATE TABLE #siteResourcesToCheckPrep1 (siteID int, roleID int, resourceID int, groupID int, include bit, universalRoleResourceRightsID int);
	CREATE TABLE #siteResourcesToCheck (siteID int, roleID int, resourceID int, functionID int, groupID int, include bit, 
		universalRoleResourceRightsID int, universalRoleResourceTypeID int);

	insert into #siteResourcesToCheckPrep1 (siteID, roleID, resourceID, groupID, include, universalRoleResourceRightsID)
	select distinct s.siteID, roles.roleID, srr.resourceID, srr.groupID, srr.include, srr.resourceRightsID
	from dbo.cms_siteResourceRoles roles
	inner join dbo.cms_siteResourceRoleTypes srrt on srrt.roleTypeID = roles.roleTypeID
		and srrt.roleTypeName = 'UniversalRole'
	inner join dbo.cms_siteResourceRights srr on srr.roleID = roles.roleID
	inner join dbo.sites s on s.siteResourceID = srr.resourceID and s.siteID = srr.siteID;

	insert into #siteResourcesToCheck (siteID, roleID, resourceID, functionID, groupID, include, universalRoleResourceRightsID, universalRoleResourceTypeID)
	select distinct prep1.siteID, prep1.roleID, prep1.resourceID, srtf.functionID, prep1.groupID, prep1.include, 
		prep1.universalRoleResourceRightsID, srt.resourceTypeID as universalRoleResourceTypeID
	from #siteResourcesToCheckPrep1 as prep1
	inner join dbo.cms_siteResourceRoleFunctions srrf on prep1.roleID = srrf.roleID
	inner join dbo.cms_siteResourceTypeFunctions srtf on srtf.resourceTypeFunctionID = srrf.resourceTypeFunctionID
	inner join dbo.cms_siteResourceTypes srt on srt.resourceTypeID = srtf.resourceTypeID
	inner join dbo.cms_siteResourceFunctions srf on srf.functionID = srtf.functionID
	inner join dbo.cms_siteResources sr on sr.siteID = prep1.siteID and sr.resourceTypeID = srt.resourceTypeID;

	insert into @myCorrections (siteID, roleID, resourceID, functionID, groupID, [include], universalRoleResourceRightsID, universalRoleResourceTypeID)
	select t.siteID, t.roleID, t.resourceID, t.functionID, t.groupID, t.[include], t.universalRoleResourceRightsID, t.universalRoleResourceTypeID
	from #siteResourcesToCheck t
	left outer join dbo.cms_siteResourceRightsCache srrc with(nolock)
		on t.universalRoleResourceRightsID = srrc.universalRoleResourceRightsID
		and srrc.universalRoleResourceTypeID =  t.universalRoleResourceTypeID
		and srrc.functionID = t.functionID
		and srrc.siteID = t.siteID
	where srrc.functionID is null;	
	SET @itemCount = @@ROWCOUNT;

	if exists (select 1 from @myCorrections) BEGIN
		SET @errorTitle = 'Detected Missing Role Entries';
		SET @errorSubject = 'cache_perms_autoCorrect: Detected Missing Role Entries';
		SET @messagetext = 'There are entries missing in cms_siteResourceRightsCache -- which is the first level of the permission cache. Missing entries are being added and the second level of the cache is being updated for the affected siteResources. Check dbo.cms_siteResourceRightsCacheAutoCorrectDetail for more info.';
		EXEC platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@messagetext, @forDev=1;

	   	insert into dbo.cms_siteResourceRightsCacheAutoCorrect(dateEntered) values (getdate());
		select @correctionID = SCOPE_IDENTITY();

		insert into dbo.cms_siteResourceRightsCacheAutoCorrectDetail (correctionID, siteID, roleID, resourceID, functionID,
			groupID, [include], universalRoleResourceRightsID, universalRoleResourceTypeID)
		select @correctionID, siteID, roleID, resourceID, functionID, groupID, [include], universalRoleResourceRightsID, universalRoleResourceTypeID
		from @myCorrections;

		insert into dbo.cms_siteResourceRightsCache (siteID, resourceID, functionID, groupID, [include], universalRoleResourceRightsID, universalRoleResourceTypeID)
		select siteID, resourceID, functionID, groupID, [include], universalRoleResourceRightsID, universalRoleResourceTypeID
		from @myCorrections mC;

		insert into #platformWideSiteResourcesToProcess (siteResourceID, siteID)
		select distinct mc.resourceID as siteresourceID, mc.siteID
		from @myCorrections mc
			except
		select siteresourceID, siteID
		from #platformWideSiteResourcesToProcess;
	END

	-- ******************************************
	-- add missing permissions
	-- ******************************************
	declare @addMultiplePerms_autoID int, @addMultiplePerms_siteResourceID int, @addMultiplePerms_include bit,
		@addMultiplePerms_functionID int, @addMultiplePerms_roleID int, @addMultiplePerms_groupID int,
		@addMultiplePerms_inheritedRightsResourceID int, @addMultiplePerms_inheritedRightsFunctionID int,
		@addMultiplePerms_siteID int, @siteAdminRoleID int, 
		@missingSiteAdminRoleAffectedSiteList varchar(1000);
	declare @addMultiplePerms_permsToAdd TABLE (autoID int IDENTITY(1,1) PRIMARY KEY, siteID int, siteResourceID int, 
		[include] bit, functionID int, roleID int, groupID int, inheritedRightsResourceID int, inheritedRightsFunctionID int);
	declare @siteIDsMissingAdminRole TABLE (siteID int PRIMARY KEY, siteCode varchar(10));

	-- add perms for siteAdmin groups missing the siteAdmin role and email alert if any are found
	select @siteAdminRoleID = srroles.roleID
	from dbo.cms_siteResourceRoles srroles
	inner join dbo.cms_siteResourceRoleTypes srroleTypes on srroleTypes.roleTypeID = srroles.roleTypeID
		and srroles.roleName = 'Site Administrator'
		and srroleTypes.roleTypeName = 'UniversalRole';

	insert into @siteIDsMissingAdminRole (siteID, siteCode)
	select s.siteID, s.sitecode
	from dbo.organizations o
	inner join dbo.sites s on s.orgID = o.orgID
		except
	select s.siteID, s.sitecode
	from dbo.organizations o
	inner join dbo.sites s on s.orgID = o.orgID
	inner join dbo.ams_groups g on g.orgID = o.orgID
		and g.status='A'
	inner join dbo.cms_siteResourceRIghts srr on srr.groupID = g.groupID
		and s.siteResourceID = srr.resourceID
		and srr.roleID = @siteAdminRoleID
		and srr.siteID = s.siteID
	order by s.sitecode;

	if exists (select * from @siteIDsMissingAdminRole) BEGIN
		select @missingSiteAdminRoleAffectedSiteList = STRING_AGG(sitecode,'<br/>') WITHIN GROUP (ORDER BY sitecode ASC)
		from @siteIDsMissingAdminRole;

		SET @messagetext = 'There are sites that don''t have the Site Administrator role assigned to any group. We will attempt to automatically add these entries to the SiteAdmins group.<br/><br/>' + @missingSiteAdminRoleAffectedSiteList;
		SET @errorTitle = 'Add Missing SiteAdmin Role Assignments';
		SET @errorSubject = 'cache_perms_autoCorrect: Add Missing SiteAdmin Role Assignments';
		EXEC platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@messagetext, @forDev=1;

		insert into @addMultiplePerms_permsToAdd (siteID, siteResourceID, [include], functionID, roleID, groupID, inheritedRightsResourceID, inheritedRightsFunctionID)
		select s.siteID , s.siteResourceID , 1 as include , null as functionID , @siteAdminRoleID as roleID , 
			g.groupID , null as inheritedRightsResourceID , null as inheritedRightsFunctionID 
		from @siteIDsMissingAdminRole ms
		inner join dbo.sites s on s.siteID = ms.siteID
		inner join dbo.ams_groups g on g.orgID = s.orgID
			and g.groupCode = 'siteAdmins'
			and g.isSystemGroup = 1;			
		SET @itemCount = @itemCount + @@ROWCOUNT;
	END


	-- add perms for specific default pages missing the public view permissions and email alert if any are found
	declare @viewFunctionID int = 4;
	declare @defaultAppsMissingPublicViewPermissions TABLE (siteID int PRIMARY KEY, siteResourceID int, publicGroupID int, siteCode varchar(10), applicationTypeName varchar(100));

	insert into @defaultAppsMissingPublicViewPermissions (siteID, siteResourceID, publicGroupID, sitecode, applicationTypeName)
	select ai.siteID, ai.siteResourceID, g.groupID, s.sitecode, at.applicationTypeName
	from sites s
	inner join cms_siteResources sr 
		on s.siteID=sr.siteID 
		and s.siteResourceID = sr.siteResourceID
		and sr.siteResourceStatusID = 1
	inner join organizations o 
		on s.orgID = o.orgID
	inner join cms_applicationInstances ai 
		on ai.siteID = s.siteID
	inner join cms_siteResources aisr 
		on s.siteID=aisr.siteID 
		and ai.siteResourceID = aisr.siteResourceID
		and aisr.siteResourceStatusID = 1
	inner join cms_applicationTypes at 
		on ai.applicationTypeID = at.applicationTypeID
		and at.applicationTypeName in ('Login','OrgDocDownload','StoreDocDownload','ReportDocDownload','listAttachmentDownload','TSDocDownload','accountLocator','UpdateMember','ContentEditor','Ajax','appProxy','invoices','buyNow','viewCart','Support','emailPreferences')
	inner join ams_groups g 
		on g.orgID=o.orgID 
		and g.isSystemGroup=1 
		and g.groupCode = 'Public'
	left outer join cms_siteResourceRights srr 
		on srr.siteID=s.siteID 
		and srr.resourceID = ai.siteResourceID
		and srr.functionID = @viewFunctionID
		and srr.[include]=1
		and srr.groupID=g.groupID
	where srr.resourceRightsID is null


	if exists (select * from @defaultAppsMissingPublicViewPermissions) BEGIN
		set @messagetext = 'The system found site default applications missing required Public View permissions. They may have been recently deleted. They are being automatically added back.<br/><br/>';

		select @messagetext= @messagetext + STRING_AGG(msg,'<br/>') WITHIN GROUP (ORDER BY msg ASC)
		from (
			select mp.sitecode + ': ' + mp.applicationTypeName as msg
			from @defaultAppsMissingPublicViewPermissions mp
		) as t;

		SET @errorTitle = 'Add Missing Public View Permissions to Default Apps';
		SET @errorSubject = 'cache_perms_autoCorrect: Add Missing Public View Permissions to Default Apps';
		EXEC platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@messagetext, @forDev=0;

		insert into @addMultiplePerms_permsToAdd (siteID, siteResourceID, [include], functionID, roleID, groupID, inheritedRightsResourceID, inheritedRightsFunctionID)
		select mp.siteID , mp.siteResourceID , 1 as include , @viewFunctionID as functionID , null as roleID , mp.publicGroupID , null as inheritedRightsResourceID , null as inheritedRightsFunctionID 
		from @defaultAppsMissingPublicViewPermissions mp
		SET @itemCount = @itemCount + @@ROWCOUNT;
	END


	if exists (select * from @addMultiplePerms_permsToAdd) BEGIN
		select @addMultiplePerms_autoID = min(autoID) from @addMultiplePerms_permsToAdd;
		while @addMultiplePerms_autoID is not null begin
			select @addMultiplePerms_siteID = siteID, @addMultiplePerms_siteResourceID = siteResourceID, 
				@addMultiplePerms_include = include, @addMultiplePerms_functionID = functionID, 
				@addMultiplePerms_roleID = roleID, @addMultiplePerms_groupID = groupID, 
				@addMultiplePerms_inheritedRightsResourceID = inheritedRightsResourceID, 
				@addMultiplePerms_inheritedRightsFunctionID = inheritedRightsFunctionID
			from @addMultiplePerms_permsToAdd
			where autoID = @addMultiplePerms_autoID;

			exec dbo.cms_createSiteResourceRight @siteID=@addMultiplePerms_siteID, @siteResourceID=@addMultiplePerms_siteResourceID, 
				@include=@addMultiplePerms_include, @functionIDList=@addMultiplePerms_functionID, 
				@roleID=@addMultiplePerms_roleID, @groupID=@addMultiplePerms_groupID, 
				@inheritedRightsResourceID=@addMultiplePerms_inheritedRightsResourceID, 
				@inheritedRightsFunctionID=@addMultiplePerms_inheritedRightsFunctionID;

			WAITFOR DELAY '00:00:02';

			select @addMultiplePerms_autoID = min(autoID) from @addMultiplePerms_permsToAdd where autoID > @addMultiplePerms_autoID;
		end
	end


	-- ******************************************
	-- Remove invalid permission assignments
	-- ******************************************
	declare @deleteMultiplePerms_autoID int, @deleteMultiplePerms_siteResourceID int, @deleteMultiplePerms_siteResourceRightID int,
		@deleteMultiplePerms_siteID int, @deleteMultiplePerms_totalCount int, @deleteMultiplePerms_message varchar(100),
		@startingResourceRightsID int, @deleteMultiplePerms_emailmessage varchar(max);

	select top 1 @startingResourceRightsID = resourceRightsID
	from cms_siteResourceRights 
	where dateCreated > @4HoursAgo
	order by dateCreated;

	declare @deleteMultiplePerms_permsToRemove TABLE (autoID int IDENTITY(1,1) PRIMARY KEY, siteID int, siteResourceID int, siteResourceRightID int);

	insert into @deleteMultiplePerms_permsToRemove (siteID, siteResourceID, siteResourceRightID)
	select sr2.siteID, sr2.siteResourceID, badassignments.resourceRightsID
	from (
		select srr.resourceRightsID, srr.resourceID
		from dbo.cms_siteResourceRights srr
		where resourceRightsID >= @startingResourceRightsID 
		and roleID is null
			except
		select srr.resourceRightsID, srr.resourceID
		from dbo.cms_siteResourceRights srr
		inner join dbo.cms_siteResources sr on sr.siteResourceID = srr.resourceID
			and srr.resourceRightsID >= @startingResourceRightsID
		inner join dbo.cms_siteResourceTypeFunctions srtf on srtf.resourceTypeID = sr.resourceTypeID
			and srtf.functionID = srr.functionID
	) as badassignments
	inner join dbo.cms_siteResources sr2 on sr2.siteResourceID = badassignments.resourceID;	
	SET @itemCount = @itemCount + @@ROWCOUNT;

	if exists (select * from @deleteMultiplePerms_permsToRemove) BEGIN
		set @messagetext = 'There are entries in cms_siteResourceRights assigning perms to functions that aren''t associated with the resourceType of the siteResource. They will be removed.<br/><br/>';

		select @messagetext= @messagetext + STRING_AGG(msg,'<br/>') WITHIN GROUP (ORDER BY msg ASC)
		from (
			select srt.resourceType + ': ' + srf.functionName + ' (' + cast(count(*) as varchar(10)) + ' entries)' as msg
			from @deleteMultiplePerms_permsToRemove rv
			inner join dbo.cms_siteResourceRights srr on srr.resourceRightsID = rv.siteResourceRightID
			inner join dbo.cms_siteResources sr on sr.siteResourceID = srr.resourceID and sr.siteID = srr.siteID
			inner join dbo.cms_siteResourceTypes srt on srt.resourceTypeID = sr.resourceTypeID
			inner join dbo.cms_siteResourceFunctions srf on srf.functionID = srr.functionID
			group by srt.resourceType, srf.functionName
		) t;

		SET @errorTitle = 'Removing invalid permission assignments';
		SET @errorSubject = 'cache_perms_autoCorrect: Removing invalid permission assignments';
		EXEC platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@messagetext, @forDev=1;

		select @deleteMultiplePerms_totalCount = count(*) from @deleteMultiplePerms_permsToRemove;

		select @deleteMultiplePerms_autoID = min(autoID) from @deleteMultiplePerms_permsToRemove;
		while @deleteMultiplePerms_autoID is not null begin
			select @deleteMultiplePerms_siteResourceID = siteResourceID ,
				@deleteMultiplePerms_siteResourceRightID = siteResourceRightID ,
				@deleteMultiplePerms_siteID = siteID
			from @deleteMultiplePerms_permsToRemove
			where autoID = @deleteMultiplePerms_autoID;

			exec dbo.cms_deleteSiteResourceRight @siteID=@deleteMultiplePerms_siteID, @siteResourceID=@deleteMultiplePerms_siteResourceID, 
				@siteResourceRightID=@deleteMultiplePerms_siteResourceRightID;

			WAITFOR DELAY '00:00:02';
    
			select @deleteMultiplePerms_autoID = min(autoID) from @deleteMultiplePerms_permsToRemove where autoID > @deleteMultiplePerms_autoID;
		end
	END

	-- ******************************************
	-- FIX missing entries from rightprintsAndGroups table
	-- ******************************************
	declare @rightPrints TABLE (rightPrintID int PRIMARY KEY);
	declare @siteresourcesAndSites TABLE (siteResourceID int, functionID int, siteID int);
	declare @currentSiteID int;

	select @startingRightPrintID = min(rightPrintID)
	from cache_perms_rightPrints gp
	where gp.dateCreated > @4HoursAgo;

	insert into @rightPrints (rightPrintID) 
	(
		-- make sure that entries in rpg table match groupList column
		select rp.rightPrintID
		from dbo.cache_perms_rightPrints rp
		inner join dbo.cache_perms_rightPrintsAndGroups rpg on rpg.rightPrintID = rp.rightPrintID and rpg.siteID = rp.siteID
		where rp.rightPrintID > @startingRightPrintID
		group by rp.rightPrintID, rp.hashGroupList
		having rp.hashGroupList <> HASHBYTES('MD5',cast(dbo.sortedIntList(case when rpg.include > 0 then rpg.groupID else (-1 * rpg.groupID) end) as varchar(max)))
	)
	union
	(
		-- make sure that entries exist in rpg table
		select rp.rightPrintID
		from dbo.cache_perms_rightPrints rp
		where rp.rightPrintID > @startingRightPrintID
		except
		select rightPrintID
		from dbo.cache_perms_rightPrintsAndGroups rpg 
	);	
	SET @itemCount = @itemCount + @@ROWCOUNT;

	if exists (select 1 from @rightPrints) BEGIN
		SET @errorTitle = 'Detected GroupList mismatch between from rightprints and rightprintsAndGroups table';
		SET @errorSubject = 'cache_perms_autoCorrect: Detected GroupList mismatch between from rightprints and rightprintsAndGroups table';
		SET @messagetext = 'Detected mismatch entries between rightPrints grouplist column and rightprintsAndGroups table. Reprocessing affected rightPrints now.';
		EXEC platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@messagetext, @forDev=1;
	END

	insert into @SiteresourcesAndSites (siteResourceID, functionID, siteID)
	select srfrp.siteResourceID, srfrp.functionID,rp.siteID
	from dbo.cache_perms_siteResourceFunctionRightPrints srfrp
	inner join dbo.cache_perms_rightPrints rp on rp.rightPrintID = srfrp.rightPrintID 
	inner join @rightPrints temp on temp.rightPrintID = srfrp.rightPrintID;

	delete srfrp
	from dbo.cache_perms_siteResourceFunctionRightPrints srfrp
	inner join @rightPrints temp on temp.rightPrintID = srfrp.rightPrintID;

	delete gprp
	from dbo.cache_perms_groupPrintsRightPrints gprp
	inner join @rightPrints temp on temp.rightPrintID = gprp.rightPrintID;

	delete rpg
	from dbo.cache_perms_rightPrintsAndGroups rpg
	inner join @rightPrints temp on temp.rightPrintID = rpg.rightPrintID;

	delete rp
	from dbo.cache_perms_rightPrints rp
	inner join @rightPrints temp on temp.rightPrintID = rp.rightPrintID;

	insert into #platformWideSiteResourcesToProcess (siteResourceID, siteID)
	select distinct siteResourceID, siteID
	from @SiteresourcesAndSites
		except
	select siteresourceID, siteID
	from #platformWideSiteResourcesToProcess;
	SET @itemCount = @itemCount + @@ROWCOUNT;

	--get resourceTypeID for resources that need to be processed
	update temp 
	set temp.resourceTypeID = sr.resourceTypeID
	from #platformWideSiteResourcesToProcess temp
	inner join dbo.cms_siteResources sr on sr.siteResourceID = temp.siteResourceID;

	-- process right prints
	select @currentSiteID = min(siteID) from #platformWideSiteResourcesToProcess;
	while @currentSiteID is not null begin
		delete from #siteResourcesToProcess;
		
		insert into #siteResourcesToProcess (siteResourceID,resourceTypeID)
		select siteResourceID, resourceTypeID
		from #platformWideSiteResourcesToProcess
		where siteID = @currentSiteID;

		exec dbo.cache_perms_updateSiteResourceFunctionRightPrintsForSiteResourcesBulk @siteID=@currentSiteID;

		WAITFOR DELAY '00:00:02';

		select @currentSiteID = min(siteID) from #platformWideSiteResourcesToProcess where siteID > @currentSiteID;
	end

	-- double check recently created groupPrints for missing entries in groupprintsAndGroups table
	declare @groupPrints TABLE (groupPrintID int PRIMARY KEY);
	declare @membersAndOrgs TABLE (memberID int PRIMARY KEY, orgID int);
	declare @currentOrgID int;

	select @startingGroupPrintID = min(groupPrintID)
	from cache_perms_groupPrints gp
	where gp.dateCreated > @4HoursAgo;

	insert into @groupPrints (groupPrintID)
	(
		-- make sure that entries in gpg table match groupList column
		select gp.groupPrintID
		from dbo.cache_perms_groupPrints gp
		inner join dbo.cache_perms_groupPrintsAndGroups gpg on gpg.groupPrintID = gp.groupPrintID and gpg.orgID = gp.orgID
		where gp.groupPrintID > @startingGroupPrintID
		group by gp.groupPrintID, gp.hashGroupList
		having gp.hashGroupList <> HASHBYTES('MD5',cast(dbo.sortedIntList(gpg.groupID) as varchar(max)))
	)
	union
	(
		-- make sure that entries exist in gpg table
		select gp.groupPrintID
		from dbo.cache_perms_groupPrints gp
		where gp.groupPrintID > @startingGroupPrintID
		except
		select groupPrintID
		from dbo.cache_perms_groupPrintsAndGroups gpg 
	);
	SET @itemCount = @itemCount + @@ROWCOUNT;

	if exists (select 1 from @groupPrints) BEGIN
		SET @errorTitle = 'Detected GroupList mismatch between from groupprints and groupprintsAndGroups table';
		SET @errorSubject = 'cache_perms_autoCorrect: Detected GroupList mismatch between from groupprints and groupprintsAndGroups table';
		SET @messagetext = 'Detected mismatch between groupPrint grouplist column and entries from groupprintsAndGroups table. Reprocessing affected groupPrints now.';
		EXEC platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@messagetext, @forDev=1;
	END

	insert into @membersAndOrgs (memberID, orgID)
	select m.memberID, m.orgID
	from dbo.ams_members m
	inner join @groupPrints temp on temp.groupPrintID = m.groupPrintID ;
	SET @itemCount = @itemCount + @@ROWCOUNT;

	update m 
	set groupPrintID = null
	from dbo.ams_members m
	inner join @groupPrints temp on temp.groupPrintID = m.groupPrintID;

	delete gprp
	from dbo.cache_perms_groupPrintsRightPrints gprp
	inner join @groupPrints temp on temp.groupPrintID = gprp.groupPrintID;

	delete gpg
	from dbo.cache_perms_groupPrintsAndGroups gpg
	inner join @groupPrints temp on temp.groupPrintID = gpg.groupPrintID;

	delete gp
	from dbo.cache_perms_groupPrints gp
	inner join @groupPrints temp on temp.groupPrintID = gp.groupPrintID;

	-- process group prints
	declare @MGPqueueTypeID int, @MGPqueueStatusID int, @itemGroupUID uniqueidentifier, @dateAdded datetime;
	select @MGPqueueTypeID = queueTypeID from platformQueue.dbo.tblQueueTypes where queueType = 'MemberGroupPrints';
	select @MGPqueueStatusID = queuestatusID from platformQueue.dbo.tblQueueStatuses where queueTypeID = @MGPqueueTypeID and queueStatus = 'readyToProcess';

	select @currentOrgID = min(orgID) from @membersAndOrgs;
	while @currentOrgID is not null begin
		set @itemGroupUID = NEWID();
		set @dateAdded = GETDATE();
		
		INSERT INTO platformQueue.dbo.queue_memberGroupPrints (itemGroupUID, orgID, memberID, statusID, dateAdded, dateUpdated)
		select @itemGroupUID, orgID, memberID, @MGPqueueStatusID, @dateAdded, @dateAdded
		from @membersAndOrgs
		where orgID = @currentOrgID;

		exec dbo.cache_perms_updateGroupPrintsForMembersBulk @itemGroupUID=@itemGroupUID;
		
		WAITFOR DELAY '00:00:01';

		select @currentOrgID = min(orgID) from @membersAndOrgs where orgID > @currentOrgID;
	end

	set @endDate = getdate();

	if datediff(second, @startDate, @endDate) > 60 BEGIN
		SET @errorTitle = 'Long running time';
		set @messagetext = 'The proc completed, but it took too long. Runtime in seconds: ' + cast(datediff(second, @startDate, @endDate) as varchar(50));
		SET @errorSubject = 'cache_perms_autoCorrect: Long running time';
		EXEC platformMail.dbo.email_sendMessageSupport @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@messagetext, @forDev=1;
	END


	IF OBJECT_ID('tempdb..#siteResourcesToProcess') IS NOT NULL
		DROP TABLE #siteResourcesToProcess;
	IF OBJECT_ID('tempdb..#platformWideSiteResourcesToProcess') IS NOT NULL
		DROP TABLE #platformWideSiteResourcesToProcess;
	IF OBJECT_ID('tempdb..#siteResourcesToCheckPrep1') IS NOT NULL
		DROP TABLE #siteResourcesToCheckPrep1;
	IF OBJECT_ID('tempdb..#siteResourcesToCheck') IS NOT NULL
		DROP TABLE #siteResourcesToCheck;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
