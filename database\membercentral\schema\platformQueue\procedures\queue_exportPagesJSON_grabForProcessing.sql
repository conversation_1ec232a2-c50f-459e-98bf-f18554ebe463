ALTER PROC dbo.queue_exportPagesJSON_grabForProcessing
@batchSize int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @statusReady int, @statusGrabbed int, @totalItemCount int;
	SELECT @queueTypeID = queueTypeID FROM dbo.tblQueueTypes WHERE queueType = 'exportPagesJSON';
	SELECT @statusReady = queuestatusID FROM dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID and queueStatus = 'ReadyToProcess';
	SELECT @statusGrabbed = queuestatusID FROM dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID and queueStatus = 'GrabbedForProcessing';

	IF OBJECT_ID('tempdb..#tmpExportPagesJSON') IS NOT NULL
		DROP TABLE #tmpExportPagesJSON;
	CREATE TABLE #tmpExportPagesJSON (itemID int);

	-- dequeue in order of dateAdded. get @batchsize entries
	UPDATE qid WITH (UPDLOCK, READPAST)
	SET qid.statusID = @statusGrabbed,
		qid.dateUpdated = getdate()
		OUTPUT inserted.itemID
		INTO #tmpExportPagesJSON
	FROM dbo.queue_exportPagesJSON AS qid
	INNER JOIN (
		SELECT TOP (@batchSize) itemID 
		FROM dbo.queue_exportPagesJSON
		WHERE statusID = @statusReady
		ORDER BY dateAdded, itemID
	) AS batch ON batch.itemID = qid.itemID
	WHERE qid.statusID = @statusReady;

	SELECT @totalItemCount = COUNT(itemID)
	FROM #tmpExportPagesJSON;

	SELECT DISTINCT qid.itemID, qid.itemGroupUID, qid.siteID, qid.submittedMemberID, qid.pageID, @totalItemCount as totalItemCount
	FROM #tmpExportPagesJSON AS tmp
	INNER JOIN dbo.queue_exportPagesJSON AS qid ON qid.itemID = tmp.itemID
	ORDER BY qid.itemID;

	IF OBJECT_ID('tempdb..#tmpExportPagesJSON') IS NOT NULL
		DROP TABLE #tmpExportPagesJSON;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
