<cfcomponent output="false" extends="model.scheduledTasks.scheduledTasks">

	<cffunction name="runTask" access="public" returntype="void" output="false">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structnew()>

		<cfsetting requesttimeout="600">

		<cfset local.strResult = processQueue()>
		<cfif NOT local.strResult.success>
			<cfthrow message="Error running processQueue()">
		</cfif>

		<cfset local.historyID = (arguments.strTask.historyID ?: 0)>
		<cfset addBatchIdentifierToHistory(historyID=local.historyID, batchIdentifier="", itemCount=local.strResult.itemCount)>
	</cffunction>

	<cffunction name="updateToProcessingItem" access="private" output="false" returntype="void">
		<cfargument name="itemID" type="numeric" required="true">

		<cfquery name="local.qryUpdateToProcessingItem" datasource="#application.dsn.platformQueue.dsn#">
			SET NOCOUNT ON;

			declare @queueTypeID int, @statusProcessing int;
			select @queueTypeID = queueTypeID from dbo.tblQueueTypes where queueType = 'monthBillTSRoyalty';
			select @statusProcessing = queueStatusID from dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'processingItem';

			UPDATE dbo.queue_monthBillTSRoyalty
			SET statusID = @statusProcessing,
				dateUpdated = getdate()
			WHERE itemID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.itemID#">;
		</cfquery>
	</cffunction>

	<cffunction name="updateToDone" access="private" output="false" returntype="void">
		<cfargument name="itemID" type="numeric" required="true">
		<cfargument name="folderPath" type="string" required="true">
		<cfargument name="filename" type="string" required="true">

		<cfquery name="local.qryUpdateToDone" datasource="#application.dsn.platformQueue.dsn#">
			SET NOCOUNT ON;

			declare @queueTypeID int, @statusDone int;
			select @queueTypeID = queueTypeID from dbo.tblQueueTypes where queueType = 'monthBillTSRoyalty';
			select @statusDone = queueStatusID from dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'done';

			UPDATE dbo.queue_monthBillTSRoyalty
			SET statusID = @statusDone,
				folderPath = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.folderPath#">,
				fileName = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.fileName#">,
				dateUpdated = getdate()
			WHERE itemID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.itemID#">;
		</cfquery>
	</cffunction>

	<cffunction name="processQueue" access="private" output="false" returntype="struct">
		<cfset var local = structnew()>
		<cfset local.strReturn = { success:true, itemCount:0 }>

		<cftry>
			<!--- ---------------------- --->
			<!--- grab and process queue --->
			<!--- ---------------------- --->
			<cfstoredproc procedure="queue_monthBillTSRoyalty_grabForProcessing" datasource="#application.dsn.platformQueue.dsn#">
				<cfprocresult name="local.qryStatements" resultset="1">
			</cfstoredproc>
			<cfset local.strReturn.itemCount = local.qryStatements.recordCount>

			<cfif local.strReturn.itemCount gt 0>
				<!--- this doesnt use parallel because of the constant pdf generation errors --->
				<cfset local.qryStatements.each(function(row) {
					updateToProcessingItem(itemID=arguments.row.itemID);

					local.strPDF = generateRoyaltyStatement(orgData=arguments.row);

					updateToDone(itemID=arguments.row.itemID, folderPath=local.strPDF.strFolder.folderPath, fileName=local.strPDF.fileName);
					}, false)>
			</cfif>

		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.strReturn.success = false>
		</cfcatch>
		</cftry>			
					
		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="generateRoyaltyStatement" access="public" output="false" returntype="struct">
		<cfargument name="orgData" type="struct" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = { strFolder={}, fileName="", sourceFilePath="" }>

		<cfsetting requesttimeout="900">

		<cfset local.strReturn.strFolder = application.objDocDownload.createHoldingFolder(prefix="TS")>
		<cfset local.strReturn.fileName = "TrialSmithRoyaltyStatement#DateFormat(arguments.orgData.EOMPeriod,"YYYYMM")#.pdf">
		<cfset local.strReturn.sourceFilePath = "#local.strReturn.strFolder.folderPath#/#local.strReturn.fileName#">
		<cfset local.df = createObject("java", "java.text.DecimalFormat")>

		<cfsavecontent variable="local.pdfHeader">
			<cfoutput>
			<table align="center" style="width:321px;">
			<tr>
				<td align="center" height="32"><img src="file:///#application.paths.RAIDAssetRoot.path#common/images/trialsmithInvoice.png" width="321" height="32"></td>
			</tr>
			</table>
			</cfoutput>
		</cfsavecontent>

		<cfsavecontent variable="local.pdfFooter">
			<cfoutput>
			<div style="font-family:verdana;font-size:9px;text-align:center;">
				MemberCentral, SeminarWeb, and TrialSmith are registered service marks of TrialSmith, Inc, Austin, Texas. 
				This report is confidential and should not be reproduced, transmitted, or distributed in any form whatsoever without written permission from TrialSmith, Inc.
			</div>
			</cfoutput>
		</cfsavecontent>

		<!--- the bp ending 10/31/2021 (and only this bp) produced a catchup royalty statement from July 1 - Oct 31. --->
		<cfset local.statementDate = DateFormat(arguments.orgData.EOMPeriod,"MMMM YYYY")>
		<cfif local.statementDate eq "October 2021">
			<cfset local.statementDate = "July-October 2021">
		</cfif>

		<cfsavecontent variable="local.pdfSummary">
			<cfoutput>
			<div style="margin-top:10px;text-align:center;font-size:1.1em;font-weight:bold;font-family:verdana;">#arguments.orgData.orgName#</div>
			<div style="margin-top:4px;text-align:center;font-size:1.1em;font-weight:bold;font-family:verdana;">TrialSmith Royalty Statement</div>
			<div style="margin-top:4px;text-align:center;font-size:1em;font-weight:bold;font-family:verdana;">#local.statementDate#</div>
			<div style="margin-top:40px;">
				<table width="100%" cellspacing="0" cellpadding="3">
				<cfif arguments.orgData.DepoContributionsRoyalty IS NOT 0>
					<tr valign="top">
						<td colspan="3" style="font-size:.8em;font-family:verdana;font-weight:bold;">
							Document Contributions<br/>
							<i>Sponsored by Counsel Financial</i>
						</td>
					</tr>
					<tr valign="top">
						<td style="font-size:.8em;font-family:verdana;padding-left:30px;">
							Documents Contributed by Your Members
						</td>
						<td align="right" style="font-size:.8em;font-family:verdana;padding-left:30px;">
							#arguments.orgData.DepoContributions#
						</td>
						<td></td>
					</tr>
					<tr valign="top">
						<td style="font-size:.8em;font-family:verdana;padding-left:30px;">
							Document Bonus Paid to you at #DollarFormat(arguments.orgData.DepoContribAMT)# Per Document
						</td>
						<td></td>
						<td align="right" style="font-size:.8em;font-family:verdana;padding-left:30px;font-weight:bold;">
							#local.df.init("$##,####0.00;-$##,####0.00").format(javacast("double",arguments.orgData.DepoContributionsRoyalty))#
						</td>
					</tr>
					<td><td colspan="3" style="font-size:.8em;font-family:verdana;">&nbsp;</td></tr>
				</cfif>
				<cfif arguments.orgData.DepoSalesRoyalty is not 0 OR arguments.orgData.DepoSpecialSalesRoyalty is not 0>
					<tr valign="top">
						<td colspan="3" style="font-size:.8em;font-family:verdana;font-weight:bold;">
							Royalties from Sales of Contributed Documents<br/>
							<i>Sponsored by Counsel Financial</i>
						</td>
					</tr>
					<tr valign="top">
						<td style="font-size:.8em;font-family:verdana;padding-left:30px;">
							Document Sales (Adjusted for refunds & credits) 
						</td>
						<td align="right" style="font-size:.8em;font-family:verdana;padding-left:30px;">
							#local.df.init("$##,####0.00;-$##,####0.00").format(javacast("double",arguments.orgData.DepoSales))#
						</td>
						<td></td>
					</tr>
					<tr valign="top">
						<td style="font-size:.8em;font-family:verdana;padding-left:30px;">
							Document Royalty at #int(arguments.orgData.DepoSalePCT*100)#%
						</td>
						<td></td>
						<td align="right" style="font-size:.8em;font-family:verdana;padding-left:30px;font-weight:bold;">
							#local.df.init("$##,####0.00;-$##,####0.00").format(javacast("double",arguments.orgData.DepoSalesRoyalty))#
						</td>
					</tr>
					<cfif arguments.orgData.DepoSpecialSalesRoyalty is not 0>
						<tr valign="top">
							<td style="font-size:.8em;font-family:verdana;padding-left:30px;">
								Other Royalties and Adjustments
							</td>
							<td></td>
							<td align="right" style="font-size:.8em;font-family:verdana;padding-left:30px;font-weight:bold;">
								#local.df.init("$##,####0.00;-$##,####0.00").format(javacast("double",arguments.orgData.DepoSpecialSalesRoyalty))#
							</td>
						</tr>
					</cfif>
					<td><td colspan="3">&nbsp;</td></tr>
				</cfif>
				<cfif arguments.orgData.SubscriptionSalesRoyalty is not 0>
					<tr valign="top">
						<td colspan="3" style="font-size:.8em;font-family:verdana;font-weight:bold;">
							TrialSmith Subscription Royalties<br/>
							<i>Sponsored by Counsel Financial</i>
						</td>
					</tr>
					<tr valign="top">
						<td style="font-size:.8em;font-family:verdana;padding-left:30px;">
							Subscriptions to TrialSmith
						</td>
						<td align="right" style="font-size:.8em;font-family:verdana;padding-left:30px;">
							#local.df.init("$##,####0.00;-$##,####0.00").format(javacast("double",arguments.orgData.subscriptionSales))#
						</td>
						<td></td>
					</tr>
					<tr valign="top">
						<td style="font-size:.8em;font-family:verdana;padding-left:30px;">
							Subscription Royalty at #INT(arguments.orgData.subSalePCT*100)#%
						</td>
						<td></td>
						<td align="right" style="font-size:.8em;font-family:verdana;padding-left:30px;font-weight:bold;">
							#local.df.init("$##,####0.00;-$##,####0.00").format(javacast("double",arguments.orgData.SubscriptionSalesRoyalty))#
						</td>
					</tr>
					<td><td colspan="3">&nbsp;</td></tr>
				</cfif>
				<cfif arguments.orgData.eclipsRoyalty is not 0>
					<tr valign="top">
						<td colspan="3" style="font-size:.8em;font-family:verdana;font-weight:bold;">
							EClips Royalty<br/>
							<i>Sponsored by Counsel Financial</i>
						</td>
					</tr>
					<tr valign="top">
						<td style="font-size:.8em;font-family:verdana;padding-left:30px;">
							EClips Royalty
						</td>
						<td></td>
						<td align="right" style="font-size:.8em;font-family:verdana;padding-left:30px;font-weight:bold;">
							#local.df.init("$##,####0.00;-$##,####0.00").format(javacast("double",arguments.orgData.eclipsRoyalty))#
						</td>
					</tr>
					<td><td colspan="3">&nbsp;</td></tr>
				</cfif>
				<tr valign="top">
					<td style="font-size:.8em;font-family:verdana;font-weight:bold;border-top:1px solid ##000;">
						Total Royalty Payable to You
					</td>
					<td style="font-size:.8em;font-family:verdana;font-weight:bold;border-top:1px solid ##000;">
						&nbsp;
					</td>
					<td align="right" style="font-size:.8em;font-family:verdana;padding-left:30px;font-weight:bold;border-top:1px solid ##000;">
						#local.df.init("$##,####0.00;-$##,####0.00").format(javacast("double",arguments.orgData.TotalRoyalty))#
					</td>
				</tr>
				</table>
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfset local.headercol = { type="header", evalAtPrint=true, txt=local.pdfHeader } >
		<cfset local.footercol = { type="footer", evalAtPrint=true, txt=local.pdfFooter } >

		<cfdocument filename="#local.strReturn.sourceFilePath#" pagetype="letter" margintop=".25" marginbottom=".5" marginright=".5" marginleft=".5" format="PDF" backgroundvisible="Yes" orientation="portrait" unit="in" fontembed="Yes" scale="100">
			<cfoutput>
				<cfdocumentsection margintop=".25" marginbottom=".5" marginright=".5" marginleft=".5">
					<cfdocumentitem attributeCollection="#local.headercol#">
						#local.pdfHeader#
					</cfdocumentitem>
					#local.pdfSummary#
					<cfdocumentitem attributeCollection="#local.footercol#">
						#local.pdfFooter#
					</cfdocumentitem>
				</cfdocumentsection>
			</cfoutput>
		</cfdocument>

		<cfreturn local.strReturn>
	</cffunction>

</cfcomponent>