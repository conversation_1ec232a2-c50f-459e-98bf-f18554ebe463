ALTER PROC dbo.cms_deleteDocuments

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @s3DeleteReadyStatusID int;

	IF OBJECT_ID('tempdb..#tmpDocumentsToDelete') IS NULL 
		RAISERROR('Designated source table does not exist.',16,1);

	DELETE tmp
	FROM #tmpDocumentsToDelete AS tmp
	LEFT OUTER JOIN dbo.cms_documents AS d ON d.documentID = tmp.documentID
		AND d.siteID = tmp.siteID
	WHERE d.documentID IS NULL;

	IF NOT EXISTS (SELECT 1 FROM #tmpDocumentsToDelete)
		GOTO on_done;
	
	SELECT @s3DeleteReadyStatusID = qs.queueStatusID
	FROM platformQueue.dbo.tblQueueTypes AS qt
	INNER JOIN platformQueue.dbo.tblQueueStatuses AS qs ON qs.queueTypeID = qt.queueTypeID
	WHERE qt.queueType = 's3Delete'
	AND qs.queueStatus = 'readyToProcess';

	BEGIN TRAN;

		-- add to s3deletequeue
		INSERT INTO platformQueue.dbo.queue_S3Delete (statusID, s3bucketName, objectKey, dateAdded, dateUpdated)
		SELECT @s3DeleteReadyStatusID, s3bucketName, objectKey, GETDATE(), GETDATE()
		FROM (
			select 'membercentralcdn' as s3bucketName, lower('sitedocuments/' + o.orgcode + '/' + s.sitecode + '/' + right('0000' + cast(dv.documentVersionID % 1000 as varchar(4)),4) + '/' + cast(dv.documentVersionID as varchar(10)) + '.' + dv.fileExt) as objectKey
			from #tmpDocumentsToDelete as tmp
			inner join dbo.cms_documents as d on d.documentID = tmp.documentID
			inner join dbo.cms_documentLanguages as dl on dl.documentID = d.documentID
			inner join dbo.cms_documentVersions as dv on dv.documentLanguageID = dl.documentLanguageID
			inner join dbo.sites as s on s.siteID = d.siteid
			inner join dbo.organizations as o on o.orgID = s.orgID
				EXCEPT
			select s3bucketName, objectKey
			from platformQueue.dbo.queue_S3Delete
		) tmp;

		-- mark resource as deleted
		UPDATE sr
		SET sr.siteResourceStatusID = 3
		FROM dbo.cms_siteResources AS sr
		INNER JOIN dbo.cms_documents AS d ON d.siteResourceID = sr.siteResourceID
		INNER JOIN #tmpDocumentsToDelete AS tmp ON tmp.documentID = d.documentID;

		-- inactivate all versions
		UPDATE dv
		SET dv.isActive = 0
		FROM dbo.cms_documentVersions as dv
		INNER JOIN dbo.cms_documentLanguages as dl ON dl.documentLanguageID = dv.documentLanguageID
		INNER JOIN dbo.cms_documents as d on d.documentID = dl.documentID
		INNER JOIN #tmpDocumentsToDelete AS tmp ON tmp.documentID = d.documentID
		WHERE dv.isActive = 1;

	COMMIT TRAN;
	
	on_done:
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
