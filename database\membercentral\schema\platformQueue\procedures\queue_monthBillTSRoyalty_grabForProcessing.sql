ALTER PROC dbo.queue_monthBillTSRoyalty_grabForProcessing
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @batchSize int, @queueTypeID int, @statusReady int, @statusGrabbed int;

	set @batchSize = 100;
	select @queueTypeID = queueTypeID from dbo.tblQueueTypes where queueType = 'monthBillTSRoyalty';
	select @statusReady = queueStatusID from dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'readyToProcess';
	select @statusGrabbed = queueStatusID from dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'grabbedForProcessing';

	IF OBJECT_ID('tempdb..#tmpStatements') IS NOT NULL 
		DROP TABLE #tmpStatements;
	CREATE TABLE #tmpStatements (itemID int);

	-- dequeue in order of dateAdded. get @batchsize statements
	UPDATE qi WITH (UPDLOCK, READPAST)
	SET qi.statusID = @statusGrabbed,
		qi.dateUpdated = getdate()
		OUTPUT inserted.itemID
		INTO #tmpStatements
	FROM dbo.queue_monthBillTSRoyalty as qi
	INNER JOIN (
		SELECT top(@BatchSize) qi2.itemID 
		from dbo.queue_monthBillTSRoyalty as qi2
		WHERE qi2.statusID = @statusReady
		ORDER BY qi2.dateAdded, qi2.itemID
	) as batch on batch.itemID = qi.itemID
	WHERE qi.statusID = @statusReady;

	-- final data
	select qi.itemID, qi.orgCode, oi.organizationName as orgName, qi.DepoSalePCT, qi.DepoContribAMT, qi.SubSalePCT, qi.eclipsMonthAMT, 
		qi.DepoSales, qi.DepoSpecialSales, qi.DepoContributions, qi.SubscriptionSales, qi.DepoSalesRoyalty, 
		qi.DepoSpecialSalesRoyalty, qi.DepoContributionsRoyalty, qi.SubscriptionSalesRoyalty, qi.eclipsRoyalty, 
		qi.TotalRoyalty, bp.EOMPeriod
	from #tmpStatements as tmp
	inner join dbo.queue_monthBillTSRoyalty as qi on qi.itemID = tmp.itemID
	inner join trialsmith.dbo.billingPeriods as bp on bp.periodID = qi.billingPeriodID
	inner join membercentral.dbo.sites as s on s.siteCode = qi.orgCode
	inner join membercentral.dbo.organizations as o on o.orgID = s.orgID
	inner join membercentral.dbo.orgIdentities as oi on oi.orgID = o.orgID and oi.orgIdentityID = o.defaultOrgIdentityID
	order by qi.itemID;

	IF OBJECT_ID('tempdb..#tmpStatements') IS NOT NULL 
		DROP TABLE #tmpStatements;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
