ALTER PROC dbo.queue_sendgridSubuserCreate_markSubuserBatch
@batchSize int,
@restrictToSubuserID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpSubusers') IS NOT NULL 
		DROP TABLE #tmpSubusers;
	IF OBJECT_ID('tempdb..#tmpDomainValidation') IS NOT NULL 
		DROP TABLE #tmpDomainValidation;
	IF OBJECT_ID('tempdb..#tmpBrandValidation') IS NOT NULL 
		DROP TABLE #tmpBrandValidation;

	CREATE TABLE #tmpSubusers (itemID int, batchUID uniqueidentifier, subuserID int);
	CREATE TABLE #tmpDomainValidation (itemID int, batchUID uniqueidentifier, subuserID int);
	CREATE TABLE #tmpBrandValidation (itemID int, batchUID uniqueidentifier, subuserID int);

	DECLARE @batchSizeMultiplier int = 1, @realbatchSize int, @batchUID uniqueidentifier, @processingStatusID int, 
		@recipientID int, @scheduledStatusID int, @scheduledJobsFound bit = 0, @futureDatedQueuedMessagesFound bit = 0,
		@queueTypeID int, @statusReady int, @statusGrabbed int, @statusWaitingDomain int, @statusWaitingBrand int, 
		@statusGrabbedDomain int, @statusGrabbedBrand int, @statusDone int, @threadCount int = 1; 
	DECLARE @taskParams TABLE (threadCount int, batchUID uniqueidentifier, requestedBatchSize int, batchSizeMultiplier int);

    set @batchUID = newID();
    set @realbatchSize = @batchSize * @batchSizeMultiplier;

	INSERT INTO @taskParams (threadCount, batchUID, requestedBatchSize, batchSizeMultiplier) 
	VALUES (@threadCount, @batchUID, @batchSize, @batchSizeMultiplier);

	select @queueTypeID = queueTypeID from dbo.tblQueueTypes where queueType = 'SendgridSubuserCreate'
	select @statusReady = queueStatusID from dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'ReadyToCreate';
	select @statusGrabbed = queueStatusID from dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'GrabbedForCreation';
	select @statusWaitingDomain = queueStatusID from dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'waitingForDomainValidation';
	select @statusGrabbedDomain = queueStatusID from dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'grabbedForDomainValidation';
	select @statusWaitingBrand = queueStatusID from dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'waitingForBrandingValidation';
	select @statusGrabbedBrand = queueStatusID from dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'grabbedForBrandingValidation';
	select @statusDone = queueStatusID from dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'Done';

	
	-- dequeue in order of dateAdded. 
	UPDATE qi WITH (UPDLOCK, READPAST)
	SET qi.statusID = @statusGrabbed,
		qi.dateUpdated = getdate(),
		qi.batchUID = @batchUID
		OUTPUT inserted.itemID, inserted.batchUID, inserted.subUserID
		INTO #tmpSubusers
	FROM dbo.queue_sendgridSubuserCreate as qi
	INNER JOIN (
		SELECT top(@realbatchSize) qi2.itemID 
		from dbo.queue_sendgridSubuserCreate as qi2
		WHERE qi2.statusID = @statusReady
		ORDER BY qi2.dateAdded, qi2.itemID
	) as batch on batch.itemID = qi.itemID
	WHERE qi.statusID = @statusReady;

	-- return subuser information
	select qid.itemID, qid.batchUID, qid.subuserID, ss.siteID, ss.firstname, ss.lastname, ss.email, ss.username, ss.password
	from #tmpSubusers as qid
	inner join platformMail.dbo.sendgrid_subusers as ss on ss.subuserID = qid.subuserID
	order by ss.subuserID;

	-- return subuser domain information
	select qid.itemID, qid.batchUID, qid.subuserID, ssd.subuserDomainID, ssd.sendingHostname, ssd.linkBrandHostname
	from #tmpSubusers as qid
	inner join platformMail.dbo.sendgrid_subuserDomains as ssd on ssd.subuserID = qid.subuserID
	order by ssd.subuserID;

	-- return subuser waiting domain validation
	UPDATE qi WITH (UPDLOCK, READPAST)
	SET qi.statusID = @statusGrabbedDomain,
		qi.dateUpdated = getdate(),
		qi.batchUID = @batchUID
		OUTPUT inserted.itemID, inserted.batchUID, inserted.subUserID
		INTO #tmpDomainValidation
	FROM dbo.queue_sendgridSubuserCreate as qi
	WHERE qi.statusID = @statusWaitingDomain;

	select qid.itemID, qid.batchUID, qid.subuserID, ssd.subuserDomainID, ssd.sendgrid_domain_id, ss.username
	from #tmpDomainValidation as qid
	inner join platformMail.dbo.sendgrid_subusers as ss on ss.subuserID = qid.subuserID
	inner join platformMail.dbo.sendgrid_subuserDomains as ssd on ssd.subuserID = qid.subuserID
	order by ssd.subuserID;
	
	-- return subuser waiting link brand validation
	UPDATE qi WITH (UPDLOCK, READPAST)
	SET qi.statusID = @statusGrabbedBrand,
		qi.dateUpdated = getdate(),
		qi.batchUID = @batchUID
		OUTPUT inserted.itemID, inserted.batchUID, inserted.subuserID
		INTO #tmpBrandValidation
	FROM dbo.queue_sendgridSubuserCreate as qi
	WHERE qi.statusID = @statusWaitingBrand;

	select qid.itemID, qid.batchUID, qid.subuserID, ssd.subuserDomainID, ssd.sendgrid_linkbrand_id, ss.username
	from #tmpBrandValidation as qid
	inner join platformMail.dbo.sendgrid_subusers as ss on ss.subuserID = qid.subuserID
	inner join platformMail.dbo.sendgrid_subuserDomains as ssd on ssd.subuserID = qid.subuserID
	order by ssd.subuserID;

	-- return qryDone
	select itemID, batchUID, subuserID
	from dbo.queue_sendgridSubuserCreate
	WHERE statusID = @statusDone;
	
	-- qryTaskParams
	select threadCount, batchUID, requestedBatchSize, batchSizeMultiplier
	from @taskParams;

	on_done:
	IF OBJECT_ID('tempdb..#tmpSubusers') IS NOT NULL 
		DROP TABLE #tmpSubusers;
	IF OBJECT_ID('tempdb..#tmpDomainValidation') IS NOT NULL 
		DROP TABLE #tmpDomainValidation;
	IF OBJECT_ID('tempdb..#tmpBrandValidation') IS NOT NULL 
		DROP TABLE #tmpBrandValidation;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
