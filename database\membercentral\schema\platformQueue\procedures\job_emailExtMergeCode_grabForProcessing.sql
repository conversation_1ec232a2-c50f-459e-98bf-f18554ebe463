ALTER PROC dbo.job_emailExtMergeCode_grabForProcessing
@jobUID uniqueIdentifier OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @statusReady int, @statusGrabbed int, @recipientIDDataColumnID int, @messageIDDataColumnID int, 
		@batchSize int, @itemsToProcess int =0;

	select @queueTypeID = queueTypeID
		from dbo.tblQueueTypes as qt
		where qt.queueType = 'emailExtMergeCode';
	select @statusReady = qs.queueStatusID 
		from dbo.tblQueueStatuses as qs
		inner join dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueTypeID = @queueTypeID
		and qs.queueStatus = 'readyToProcess';
	select @statusGrabbed = qs.queueStatusID 
		from dbo.tblQueueStatuses as qs
		inner join dbo.tblQueueTypes as qt on qt.queueTypeID = qs.queueTypeID
		where qt.queueTypeID = @queueTypeID
		and qs.queueStatus = 'grabbedForProcessing';
	select @recipientIDDataColumnID = columnID
		from dbo.tblQueueTypeDataColumns
		where queueTypeID = @queueTypeID
		and columnName = 'MCRecipientID';
	select @messageIDDataColumnID = columnID
		from dbo.tblQueueTypeDataColumns
		where queueTypeID = @queueTypeID
		and columnName = 'MCMessageID';

	IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
		DROP TABLE #tmpRecipients;
	CREATE TABLE #tmpRecipients (itemUID uniqueidentifier, jobUID uniqueidentifier, recipientID int, messageID int, recordedByMemberID int, siteID int, INDEX tmpRecipients_siteID_messageID_recipientID (siteID, messageID, recipientID));

	SET @jobUID = NEWID();
	SET @batchsize = 2500;

	declare @processRecipientThreadCount int = 8; 
	declare @filterMergeCodesThreadCount int = 2; 
	declare @recipientsPerMiniBatch int = 150; 
	

	declare @taskParams TABLE (processRecipientThreadCount int, filterMergeCodesThreadCount int, recipientsPerMiniBatch int, requestedBatchSize int)
	insert into @taskParams (processRecipientThreadCount,filterMergeCodesThreadCount, recipientsPerMiniBatch, requestedBatchSize) values (@processRecipientThreadCount, @filterMergeCodesThreadCount, @recipientsPerMiniBatch, @batchSize)

	select @itemsToProcess=count(*)
	from dbo.tblQueueItems as qi2
	WHERE qi2.queueStatusID = @statusReady

	IF @itemsToProcess > 0 BEGIN

		-- dequeue by queue priority
		UPDATE qi WITH (UPDLOCK, READPAST)
		SET qi.queueStatusID = @statusGrabbed,
			qi.dateUpdated = getdate(),
			qi.jobUID = @jobUID,
			qi.jobDateStarted = getdate()
			OUTPUT	inserted.itemUID, inserted.jobUID, temp.recipientID, 
					temp.messageID, temp.recordedByMemberID, temp.siteID
			INTO #tmpRecipients
		FROM dbo.tblQueueItems as qi
		INNER JOIN (
			SELECT top(@BatchSize) qi2.itemUID, mrh2.recipientID, m2.messageID, qid2.recordedByMemberID, m2.siteID
			from dbo.tblQueueItems as qi2
			INNER JOIN dbo.tblQueueItemData as qid2 on qi2.itemUID = qid2.itemUID 
				and qid2.columnID = @recipientIDDataColumnID
				and qi2.queueStatusID = @statusReady
			inner join platformMail.dbo.email_messageRecipientHistory as mrh2 
				on mrh2.emailStatusID = 1
				and mrh2.siteID = qid2.siteID
				and mrh2.recipientID = qid2.columnValueInteger
				AND mrh2.batchID is null
			INNER JOIN platformMail.dbo.email_messages as m2 on m2.siteID = mrh2.siteID
				AND m2.status = 'A'
				AND m2.messageID = mrh2.messageID
			ORDER BY mrh2.queuePriority
		) as temp on temp.itemUID = qi.itemUID;
	END

	-- return recipients
	select tmp.itemUID, tmp.jobUID, mrh.recipientID, em.messageID, em.orgIdentityID, mActive.memberID as recipientMemberID, mActive.memberNumber,
		mrh.toEmail as recipientEmail, s.siteID, s.siteCode, o.orgID, o.orgCode, mt.messageTypeCode
	from #tmpRecipients as tmp
	inner join platformMail.dbo.email_messages as em 
		on em.siteID = tmp.siteID
		and em.messageID = tmp.messageID
		and em.[status] = 'A'
	inner join memberCentral.dbo.sites as s on s.siteID = em.siteID
	inner join platformMail.dbo.email_messageRecipientHistory as mrh 
		on mrh.siteID = em.siteID
		and mrh.messageID = em.messageID
		and mrh.recipientID = tmp.recipientID
	inner join platformMail.dbo.email_messageTypes as mt on mt.messageTypeID = em.messageTypeID
	inner join memberCentral.dbo.organizations as o on o.orgID = s.orgID
	inner join memberCentral.dbo.ams_members as m on (m.orgID = o.orgID or m.orgID = 1) and m.memberID = mrh.memberID
	inner join memberCentral.dbo.ams_members as mActive on mActive.memberID = m.activeMemberID;

	-- return recipient data
	select tmp.itemUID, tmp.recipientID, tmp.messageID, dc.columnID, dc.columnName, qid.dataKey, qid.columnValueBit, qid.columnvalueDate, 
		qid.columnValueDecimal2, qid.columnValueInteger, qid.columnValueString, qid.columnValueText, qid.columnValueXML
	from #tmpRecipients as tmp
	inner join dbo.tblQueueItemData as qid on qid.itemUID = tmp.itemUID
	inner join dbo.tblQueueTypeDataColumns as dc on dc.columnID = qid.columnID
	where dc.columnID not in (@recipientIDDataColumnID,@messageIDDataColumnID);
	
	-- qryTaskParams
	select processRecipientThreadCount, filterMergeCodesThreadCount, recipientsPerMiniBatch, requestedBatchSize
	from @taskParams tp

	IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
		DROP TABLE #tmpRecipients;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
