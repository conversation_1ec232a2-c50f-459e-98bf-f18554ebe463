ALTER PROC dbo.tasks_importTasks_validate
@siteID int,
@importResult xml OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	set @importResult = null;

	DECLARE @orgID int, @minColID int, @mincol varchar(255), @good bit, @reqMsg varchar(800), @customTypeID int, 
			@dataTypeCode varchar(12), @displayTypeCode varchar(12), @dynSQL nvarchar(max), @queueTypeID int;

	select @orgID = dbo.fn_getOrgIDFromSiteID(@siteID);
	select @queueTypeID = queueTypeID from platformQueue.dbo.tblQueueTypes where queueType = 'TaskImport';

	BEGIN TRY
		-- no blank Project
		update #tasks_tasksImport set Project = '' where Project is null;

		UPDATE tmp
		SET tmp.Project = projectContent.contentTitle
		FROM dbo.tasks_projects as p
		INNER JOIN #tasks_tasksImport as tmp on tmp.MCProjectID = p.projectID
		INNER JOIN dbo.cms_siteResources as sr on sr.siteResourceID = p.siteResourceID
		INNER JOIN dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
			and srs.siteResourceStatusDesc = 'Active'
		CROSS APPLY dbo.fn_getContent(p.projectContentID,1) as projectContent;
		
		INSERT INTO #tblImportErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' is missing Project.'
		FROM #tasks_tasksImport
		WHERE Project = ''
		ORDER BY rowID;
			IF @@ROWCOUNT > 0 GOTO on_done;

		-- match on project
		UPDATE tmp
		SET tmp.MCProjectID = p.projectID
		FROM dbo.tasks_projects as p
		INNER JOIN dbo.tasks_workspaces as w on w.workspaceID = p.workspaceID
		INNER JOIN dbo.cms_applicationInstances as ai on ai.applicationInstanceID = w.applicationInstanceID and ai.siteID = @siteID
		INNER JOIN dbo.cms_applicationTypes as at on at.applicationTypeID = ai.applicationTypeID and at.applicationTypeName = 'workspace'
		INNER JOIN dbo.cms_siteResources as sr on sr.siteResourceID = p.siteResourceID
		INNER JOIN dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
			and srs.siteResourceStatusDesc = 'Active'
		CROSS APPLY dbo.fn_getContent(p.projectContentID,1) as projectContent
		INNER JOIN #tasks_tasksImport as tmp on tmp.Project = projectContent.contentTitle
		WHERE tmp.MCProjectID is null;


		-- invalid project
		INSERT INTO #tblImportErrors (msg)
		SELECT TOP 100 PERCENT 'Project "' + cast(isnull(Project,'') as varchar(200)) + '" doesn''t match any valid workspace projects.'
		FROM #tasks_tasksImport
		WHERE MCProjectID is null
		ORDER BY rowID;
			IF @@ROWCOUNT > 0 GOTO on_done;


		-- solicitor member numbers
		INSERT INTO #tblSolicitorMemberNumbers (itemUID, memberNumber)
		SELECT DISTINCT tmp.itemUID, sm.listitem
		FROM #tasks_tasksImport as tmp
		CROSS APPLY dbo.fn_varcharListToTable(tmp.[Solicitor MemberNumber],'|') as sm;

		UPDATE tmp
		SET tmp.memberID = m.memberID
		FROM #tblSolicitorMemberNumbers as tmp
		INNER JOIN dbo.ams_members as m on m.orgID = @orgID
		AND m.membernumber = tmp.memberNumber
		AND m.memberID = m.activeMemberID
		AND m.status in ('A','I');

		-- invalid memberumber
		INSERT INTO #tblImportErrors (msg)
		SELECT TOP 100 PERCENT 'Solicitor MemberNumber "' + cast(isnull(memberNumber,'') as varchar(50)) + '" doesn''t match any valid member accounts.'
		FROM #tblSolicitorMemberNumbers
		WHERE memberID is null
		AND isnull(memberNumber,'') <> '';
			IF @@ROWCOUNT > 0 GOTO on_done;

		-- associated member number
		UPDATE tmp
		SET tmp.MCProspectMemberID = m.memberID
		FROM #tasks_tasksImport as tmp
		INNER JOIN dbo.ams_members as m on m.orgID = @orgID
		AND m.membernumber = tmp.[Prospect MemberNumber]
		AND m.memberID = m.activeMemberID
		AND m.status in ('A','I');

		-- invalid memberumber
		INSERT INTO #tblImportErrors (msg)
		SELECT TOP 100 PERCENT 'Prospect MemberNumber "' + cast(isnull([Prospect MemberNumber],'') as varchar(50)) + '" doesn''t match any valid member accounts.'
		FROM #tasks_tasksImport
		WHERE MCProspectMemberID is null
		AND isnull([Prospect MemberNumber],'') <> ''
		ORDER BY rowID;
			IF @@ROWCOUNT > 0 GOTO on_done;

		-- no blank taskstatus
		update #tasks_tasksImport set TaskStatus = '' where TaskStatus is null;

		INSERT INTO #tblImportErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' is missing Task Status.'
		FROM #tasks_tasksImport
		WHERE TaskStatus = ''
		ORDER BY rowID;
			IF @@ROWCOUNT > 0 GOTO on_done;
		
		-- match on taskstatus
		UPDATE tmp
		SET tmp.MCTaskStatusID = s.taskStatusID
		FROM #tasks_tasksImport as tmp
		INNER JOIN dbo.tasks_statuses as s on s.statusName = tmp.[TaskStatus];

		-- invalid taskstatus
		INSERT INTO #tblImportErrors (msg)
		SELECT TOP 100 PERCENT 'Task Status "' + cast(isnull([TaskStatus],'') as varchar(50)) + '" doesn''t match any valid status.'
		FROM #tasks_tasksImport
		WHERE MCTaskStatusID is null
		ORDER BY rowID;
			IF @@ROWCOUNT > 0 GOTO on_done;

		-- task due date
		BEGIN TRY
			UPDATE #tasks_tasksImport SET TaskDueDate = null WHERE TaskDueDate = '';
			ALTER TABLE #tasks_tasksImport ALTER COLUMN TaskDueDate date null;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblImportErrors (msg) VALUES ('TaskDueDate contains invalid dates.');
			GOTO on_done;
		END CATCH

		-- task reminder date
		BEGIN TRY
			UPDATE #tasks_tasksImport SET TaskReminderDate = null WHERE TaskReminderDate = '';
			ALTER TABLE #tasks_tasksImport ALTER COLUMN TaskReminderDate date null;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblImportErrors (msg) VALUES ('TaskReminderDate contains invalid dates.');
			GOTO on_done;
		END CATCH

		-- task objective as not null
		UPDATE #tasks_tasksImport SET TaskObjective = '' WHERE TaskObjective IS NULL;

		-- validate project and task tag custom fields
		IF EXISTS (select 1 from #tblCustomColumns) BEGIN
			
			select @minColID = min(fieldID) from #tblCustomColumns;
			WHILE @minColID is not null BEGIN
				select @mincol=null, @reqMsg = null, @dataTypeCode=null, @displayTypeCode=null;

				select @mincol=columnName, @dataTypeCode=dataTypeCode, @displayTypeCode=displayTypeCode
				from #tblCustomColumns 
				where fieldID = @minColID;

				IF @displayTypeCode in ('SELECT','RADIO','CHECKBOX') BEGIN
					set @reqMsg = 'Column "' + @mincol + '" option "';
					
					-- bit columns support only select box and radio buttons
					IF @dataTypeCode = 'BIT' BEGIN 
						set @good = 1;
						set @dynSQL = '
							set @good = 1
							BEGIN TRY
								UPDATE #tasks_tasksImport SET ' + quotename(@mincol) + ' = 1 where ' + quotename(@mincol) + ' = ''TRUE'' OR ' + quotename(@mincol) + ' = ''YES'';
								UPDATE #tasks_tasksImport SET ' + quotename(@mincol) + ' = 0 where ' + quotename(@mincol) + ' = ''FALSE'' OR ' + quotename(@mincol) + ' = ''NO'';
								ALTER TABLE #tasks_tasksImport ALTER COLUMN ' + quotename(@mincol) + ' bit null
							END TRY
							BEGIN CATCH
								set @good = 0
							END CATCH';
							exec sp_executesql @dynSQL, N'@good bit output', @good output;
						IF @good = 0 BEGIN
							INSERT INTO #tblImportErrors (msg)
							VALUES ('The column ' + @mincol + ' contains invalid boolean values.');	

							GOTO on_done;
						END
					END

					set @dynSQL = '
						SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' ' + @reqMsg + ''' + tbl.listitem  + ''" is invalid.''
						from #tasks_tasksImport
						cross apply dbo.fn_varcharListToTable(' + quotename(@mincol) + ',''|'') as tbl
						inner join dbo.cf_fields as f on f.fieldID = ' + cast(@minColID as varchar(10)) + ' 
						inner join dbo.cf_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID
						left outer join dbo.cf_fieldValues as fv on fv.fieldID = f.fieldID
							and case when ft.dataTypeCode  = ''DATE'' then cast(cast(tbl.listitem as date) as varchar(15)) else tbl.listitem end
								 = case when ft.dataTypeCode = ''STRING'' then cast(fv.valueString as varchar(max))
										 when ft.dataTypeCode = ''DECIMAL2'' then cast(fv.valueDecimal2 as varchar(15))
										 when ft.dataTypeCode = ''INTEGER'' then cast(fv.valueInteger as varchar(10))
										 when ft.dataTypeCode = ''BIT'' then cast(fv.valueBit as varchar(1))
										 when ft.dataTypeCode = ''DATE'' then cast(fv.valueDate as varchar(15))
									else '''' end
						where fv.valueID is null
						and len(ltrim(rtrim(isnull(' + quotename(@mincol) + ','''')))) > 0
						ORDER BY rowID';

					INSERT INTO #tblImportErrors (msg)
					EXEC(@dynSQL)
						IF @@ROWCOUNT > 0 GOTO on_done;
				END

				IF @dataTypeCode ='INTEGER' and @displayTypeCode = 'TEXTBOX' BEGIN 
					set @good = 1;
					set @dynSQL = '
						set @good = 1
						BEGIN TRY
							UPDATE #tasks_tasksImport SET ' + quotename(@mincol) + ' = replace(' + quotename(@mincol) + ','','','''');
							UPDATE #tasks_tasksImport SET ' + quotename(@mincol) + ' = null where ' + quotename(@mincol) + ' = '''';
							ALTER TABLE #tasks_tasksImport ALTER COLUMN ' + quotename(@mincol) + ' int null;
						END TRY
						BEGIN CATCH
							set @good = 0
						END CATCH';
						exec sp_executesql @dynSQL, N'@good bit output', @good output;
					IF @good = 0
						INSERT INTO #tblImportErrors (msg)
						VALUES ('The column "' + @mincol + '" contains invalid whole numbers.');
				END

				IF @dataTypeCode ='DECIMAL2' and @displayTypeCode = 'TEXTBOX' BEGIN 
					set @good = 1;
					set @dynSQL = '
						set @good = 1
						BEGIN TRY
							UPDATE #tasks_tasksImport SET ' + quotename(@mincol) + ' = replace(' + quotename(@mincol) + ','','','''');
							UPDATE #tasks_tasksImport SET ' + quotename(@mincol) + ' = null where ' + quotename(@mincol) + ' = '''';
							ALTER TABLE #tasks_tasksImport ALTER COLUMN ' + quotename(@mincol) + ' decimal(14,2) null;
						END TRY
						BEGIN CATCH
							set @good = 0
						END CATCH';
						exec sp_executesql @dynSQL, N'@good bit output', @good output;
					IF @good = 0
						INSERT INTO #tblImportErrors (msg)
						VALUES ('The column "' + @mincol + '" contains invalid decimal values.');
				END

				IF @dataTypeCode ='DATE' and @displayTypeCode = 'DATE' BEGIN 
					set @good = 1;
					set @dynSQL = '
						set @good = 1
						BEGIN TRY
							UPDATE #tasks_tasksImport SET ' + quotename(@mincol) + ' = replace(' + quotename(@mincol) + ','','','''');
							UPDATE #tasks_tasksImport SET ' + quotename(@mincol) + ' = null where ' + quotename(@mincol) + ' = '''';
							ALTER TABLE #tasks_tasksImport ALTER COLUMN ' + quotename(@mincol) + ' date null;
						END TRY
						BEGIN CATCH
							set @good = 0
						END CATCH';
						exec sp_executesql @dynSQL, N'@good bit output', @good output;
					IF @good = 0
						INSERT INTO #tblImportErrors (msg)
						VALUES ('The column "' + @mincol + '" contains invalid dates.');
				END

				if exists (select 1 from #tblImportErrors)
					GOTO on_done;

				select @minColID = min(fieldID) from #tblCustomColumns where fieldID > @minColID;
			END

		END

		IF EXISTS (select 1 from #tblTaskTags) BEGIN
			select @minColID=null;

			select @minColID = min(categoryID) from #tblTaskTags;
			WHILE @minColID is not null BEGIN
				select @mincol=null, @dynSQL=null;

				select @mincol=columnName
				from #tblTaskTags 
				where categoryID = @minColID;

				set @dynSQL = '
					SELECT TOP 100 PERCENT ''Row '' + cast(rowID as varchar(10)) + '' Column ' + @mincol + ' contains invalid values. It must be either [blank] or ' + @mincol + '.''
					FROM #tasks_tasksImport
					WHERE len(ltrim(rtrim(isnull(' + quotename(@mincol) + ','''')))) > 0
					AND ' + quotename(@mincol) + ' <> ''' + @mincol + '''
					ORDER BY rowID';

				INSERT INTO #tblImportErrors (msg)
				EXEC(@dynSQL)
					IF @@ROWCOUNT > 0 GOTO on_done;

				select @minColID = min(categoryID) from #tblTaskTags where categoryID > @minColID;
			END
		END

	END TRY
	BEGIN CATCH
		INSERT INTO #tblImportErrors (msg) VALUES ('Unable to validate import data for import.');
		INSERT INTO #tblImportErrors (msg) VALUES (left(error_message(),300));
		GOTO on_done;
	END CATCH

	-- ensure custom columns are included in the queue tables
	BEGIN TRY
		insert into platformQueue.dbo.tblQueueTypeDataColumns (queueTypeID, columnName, dataTypeID) 
		select @queueTypeID, tmp.columnName, tmp.dataTypeID
		from (
			select tmp.columnName, dt.dataTypeID
			from #tblCustomColumns as tmp
			inner join platformQueue.dbo.tblQueueTypesDataColumnDataTypes as dt on dt.dataTypeCode = tmp.dataTypeCode
		) as tmp
			except 
		select queueTypeID, columnName, dataTypeID
		from platformQueue.dbo.tblQueueTypeDataColumns
		where queueTypeID = @queueTypeID;
	END TRY
	BEGIN CATCH
		INSERT INTO #tblImportErrors (msg)
		VALUES ('Unable to add custom field columns to the queue tables.');

		INSERT INTO #tblImportErrors (msg)
		VALUES (left(error_message(),300));

		GOTO on_done;
	END CATCH
	
	
	-- ************************
	-- generate result xml file 
	-- ************************
	on_done:
	select @importResult = (
		select getdate() as "@date",
			isnull((select top 100 PERCENT dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg"
			from #tblImportErrors
			order by rowID
			FOR XML path('error'), root('errors'), type),'<errors/>')
		for xml path('import'), TYPE);
	
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
