ALTER PROC dbo.ev_queueEventSearchText
@siteID int,
@eventID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @readyToProcessStatusID int, @itemID int, @itemGroupUID uniqueidentifier, @xmlMessage xml;

	select @queueTypeID = queueTypeID from platformQueue.dbo.tblQueueTypes where queueType = 'eventSearchText';
	select @readyToProcessStatusID = queueStatusID from platformQueue.dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'readyToProcess';

	SELECT @itemID = itemID
	FROM platformQueue.dbo.queue_eventSearchText
	WHERE siteID = @siteID
	AND eventID = @eventID;

	IF @itemID IS NULL BEGIN
		SET @itemGroupUID = NEWID();

		INSERT INTO platformQueue.dbo.queue_eventSearchText (itemGroupUID, siteID, eventID, dateAdded, dateUpdated, statusID)
		VALUES (@itemGroupUID, @siteID, @eventID, GETDATE(), GETDATE(), @readyToProcessStatusID);

		select @xmlMessage = isnull((
			select 'eventSearchTextLoad' as t, cast(@itemGroupUID as varchar(60)) as u
			FOR XML RAW('mc'), TYPE
		),'<mc/>');
		EXEC platformQueue.dbo.queue_DataImport_sendMessage @xmlMessage=@xmlMessage;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
