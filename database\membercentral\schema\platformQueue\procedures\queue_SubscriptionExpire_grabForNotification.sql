ALTER PROC dbo.queue_SubscriptionExpire_grabForNotification
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @queueTypeID int, @statusReady int, @statusGrabbed int;

	select @queueTypeID = queueTypeID from platformQueue.dbo.tblQueueTypes where queueType = 'expireSubs';
	select @statusReady = queueStatusID from platformQueue.dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'readyToNotify';
	select @statusGrabbed = queueStatusID from platformQueue.dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'grabbedForNotifying';

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	CREATE TABLE #tmpNotify (itemGroupUID uniqueidentifier);

	-- dequeue. 
	; WITH itemGroupUIDs AS (
		select distinct itemGroupUID
		from dbo.queue_subscriptionExpire
		where statusID = @statusReady
			except
		select distinct itemGroupUID
		from dbo.queue_subscriptionExpire
		where statusID <> @statusReady
	)
	UPDATE qi
	SET qi.statusID = @statusGrabbed,
		qi.dateUpdated = getdate()
		OUTPUT itemGroupUIDs.itemGroupUID
		INTO #tmpNotify
	FROM dbo.queue_subscriptionExpire as qi
	INNER JOIN itemGroupUIDs on itemGroupUIDs.itemGroupUID = qi.itemGroupUID
	where qi.statusID = @statusReady;

	-- return report information
	select tmpN.itemGroupUID, qid.itemUID, qid.errorMessage, sub.subscriptionName, 
		m2.memberID, m2.lastname + ', ' + m2.firstname + isnull(' ' + nullif(m2.middlename,''),'') + ' (' + m2.membernumber + ')' as subscriberName,
		m4.memberID as reportMemberID, me.email as reportEmail, m4.orgID as reportOrgID, sites.siteName, sites.sitecode
	from (select distinct itemGroupUID from #tmpNotify) as tmpN
	INNER JOIN dbo.queue_subscriptionExpire as qid ON qid.itemGroupUID = tmpN.itemGroupUID
	INNER JOIN membercentral.dbo.sub_subscribers as s on s.subscriberID = qid.subscriberID
	INNER JOIN membercentral.dbo.ams_members as m on m.memberID = s.memberID
	INNER JOIN membercentral.dbo.ams_members as m2 on m2.memberid = m.activeMemberID
	INNER JOIN membercentral.dbo.sub_subscriptions as sub on sub.subscriptionID = s.subscriptionID
	INNER JOIN membercentral.dbo.sub_types as st on st.typeID = sub.typeID
	INNER JOIN membercentral.dbo.sites on sites.siteID = st.siteID
	INNER JOIN membercentral.dbo.ams_members as m3 on m3.memberID = qid.recordedByMemberID
	INNER JOIN membercentral.dbo.ams_members as m4 on m4.memberid = m3.activeMemberID
	INNER JOIN membercentral.dbo.ams_memberEmailTags as metag on metag.orgID = m4.orgID and metag.memberID = m4.memberID
	INNER JOIN membercentral.dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = m4.orgID
		and metagt.emailTagTypeID = metag.emailTagTypeID
		and metagt.emailTagType = 'Primary'
	INNER JOIN membercentral.dbo.ams_memberEmails as me on me.orgID = m4.orgID
		and me.memberID = metag.memberID
		and me.emailTypeID = metag.emailTypeID
	order by tmpN.itemGroupUID, case when errorMessage is null then 0 else 1 end desc, subscriberName, subscriptionName;

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
