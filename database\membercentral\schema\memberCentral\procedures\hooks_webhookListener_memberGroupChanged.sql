ALTER PROC dbo.hooks_webhookListener_memberGroupChanged
@xmlData xml

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @statusReady int, @siteID int, @orgID int, @webhookID int, @webhookURL varchar(400), 
		@eventID int, @runID int, @groupsList varchar(max), @payloadXML xml, @SQSQueueName varchar(80),
		@environmentName varchar(50), @changeDate datetime, @nowDate datetime = GETDATE(), @eventUID uniqueIdentifier = NEWID();

	SELECT @queueTypeID = queueTypeID FROM platformQueue.dbo.tblQueueTypes WHERE queueType = 'webhook';
	SELECT @statusReady = queueStatusID FROM platformQueue.dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID AND queueStatus = 'readyToProcess';
	SELECT @eventID = eventID FROM dbo.hooks_events where [event] = 'memberGroupChanged';
	SELECT @environmentName = tier FROM dbo.fn_getServerSettings();

	SELECT @siteID = @xmlData.value('(/wh/@s)[1]','int');
	SELECT @webhookID = @xmlData.value('(/wh/@w)[1]','int');	
	SELECT @payloadXML = @xmlData.query('wh/data');
	SELECT @runID = @payloadXML.value('(/data/runid)[1]', 'int');
	SELECT @changeDate = @payloadXML.value('(/data/changedate)[1]', 'datetime');

	SELECT @orgID = dbo.fn_getOrgIDFromSiteID(@siteID);

	SELECT @webhookURL = hookURL, @SQSQueueName = SQSQueueName FROM dbo.hooks_webhooks WHERE webhookID = @webhookID;

	INSERT INTO platformQueue.dbo.queue_webhook (siteID, webhookID, webhookURL, eventID, payloadMessage, SQSQueueName, statusID, dateAdded, dateUpdated, nextAttemptDate)
	SELECT @siteID, @webhookID, @webhookURL, @eventID, 
		'{ "mcwh_event":"membergroupchange", "mcwh_eventid":"' + CAST(@eventUID as varchar(36)) + '", "mcwh_env":"' + @environmentName + '", '+
			CASE WHEN glr.isAdded = 1 
					THEN '"action": "added", "indirectmanual": ' + CAST(glr.isManualIndirect AS varchar(1)) + ', "directvirtual": ' + CAST(glr.isVirtualDirect AS varchar(1)) + ', ' +
							'"indirectvirtual":' + CAST(glr.isVirtualIndirect AS varchar(1)) + ', "directmanual": ' + CAST(glr.isManualDirect AS varchar(1)) + ', '
					ELSE '"action": "removed", "indirectmanual": 0, "directvirtual": 0, "indirectvirtual": 0, "directmanual": 0, ' END + 
			'"changedate": "' + FORMAT(@changeDate,'MMMM, dd yyyy hh:mm:ss tt') + '", ' +
			'"api_id": "' + cast(g.[uid] as varchar(36)) + '", "membernumber": "' + m.memberNumber + '", ' +
			'"x-member-api-uri": "' + '/v1/member/' + m.membernumber + '", ' +
			'"x-api-uri": "' + '/v1/member/'+m.membernumber+'/group/' + cast(g.[uid] as char(36)) + '", ' +
			'"x-group-api-uri": "' + '/v1/group/' + cast(g.[uid] as char(36)) + '", "grouppath": "' + g.groupPathExpanded + '", ' +
			'"groupcode": "' + ISNULL(g.groupCode,'') + '", "description": "' + ISNULL(g.groupDesc,'') + '", "group": "' + g.groupName + '" }',
		@SQSQueueName, @statusReady, @nowDate, @nowDate, @nowDate
	FROM dbo.hooks_webhooks_memberGroupChanged AS wmg
	INNER JOIN platformStatsMC.dbo.cache_groupsLogRunChanges AS glr ON glr.runID = @runID
		AND glr.groupID = wmg.groupID
	INNER JOIN dbo.ams_members as m on m.orgID = @orgID AND m.memberID = glr.memberID
	INNER JOIN dbo.ams_groups as g on g.orgID = @orgID AND g.groupID = glr.groupID
	WHERE wmg.webhookID = @webhookID;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
