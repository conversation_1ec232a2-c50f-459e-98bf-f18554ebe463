<cfsavecontent variable="local.ticketJS">
	<cfoutput>
	<script language="javascript">
		function validateTicketForm() {
			mca_hideAlert('ev_ticket_err_div');
			$('##frmTicket :submit').prop('disabled',true);
			if ($('##frmTicket ##ticketName').val() == ''){
				mca_showAlert('ev_ticket_err_div', 'Enter the name of this ticket');
				$('##frmTicket :submit').prop('disabled',false);
				return false;
			}
			return true;
		}
		function selectGLAccount() {
			$('##divTicketForm').hide();
			toggleGLASelectorGridArea(true);
		}
		function selectGLAccountResult(objGL) {
			if (objGL.thepathexpanded.length > 0) {
				$('##GLAccountPath').html(objGL.thepathexpanded + ' (<span class="font-weight-bold text-danger">Remember to save!</span>)');
				$('##GLAccountID').val(objGL.glaccountid);
				$('##elGLClear').removeClass('d-none');
			} else { 
				var msg = 'There was a problem selecting the GL Account for this ticket.<br/>Try again; if the issue persists, contact MemberCentral for assistance.';
				$('##divGLerr').html(msg).show();
			}
			$('##divTicketForm').show();
			toggleGLASelectorGridArea(false,true);
		}
		function clearGLAccount() {
			$('##GLAccountPath').html('(No account selected; uses event rates\'s designated GL Account.) <span class="font-weight-bold text-danger">Remember to save!</span>');
			$('##GLAccountID').val(0);
			$('##elGLClear').addClass('d-none');
		}
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.ticketJS)#">

<cfoutput>
<div id="divTicketForm">
	<form name="frmTicket" id="frmTicket" class="p-2" action="#local.formlink#" method="post" onsubmit="return validateTicketForm();">
	<input type="hidden" name="registrationID" id="registrationID" value="#arguments.event.getValue('registrationID')#">
	<input type="hidden" name="ticketid" id="ticketid" value="#arguments.event.getValue('ticketid')#">
	
	<div id="ev_ticket_err_div" class="alert alert-danger mb-2 d-none"></div>
	
	<div class="form-row">
		<div class="col">
			<div class="form-label-group">
				<input type="text" name="ticketName" id="ticketName" value="#arguments.event.getValue('ticketName')#" class="form-control" maxlength="100" />
				<label for="ticketName">Name of Ticket *</label>
			</div>
		</div>
	</div>
	
	<div class="form-row">
		<div class="col">
			<div class="form-label-group">
				<textarea name="ticketDescription" id="ticketDescription" rows="2" class="form-control">#arguments.event.getValue('ticketDescription')#</textarea>
				<label for="ticketDescription">Description</label>
			</div>
		</div>
	</div>
	
	<cfif variables.enableEventGuestTracking>
		<div class="form-row">
			<div class="col">
				<div class="form-label-group">
					<select name="assignToMembers" id="assignToMembers" class="form-control">
						<option value="0" <cfif arguments.event.getValue('assignToMembers') is not 1>selected</cfif>>No, this ticket will NOT track guests.</option>
						<option value="1" <cfif arguments.event.getValue('assignToMembers') is 1>selected</cfif>>Yes, this ticket will track guests.</option>
					</select>
					<label for="assignToMembers">Track Guests *</label>
				</div>
			</div>
		</div>
	<cfelse>
		<input type="hidden" name="assignToMembers" id="assignToMembers" value="0">
	</cfif>
	
	<div class="form-row mb-3">
		<div class="col">
			<div class="row no-gutters align-items-center">
				<div class="col-md col-sm-12">
					<div class="form-label-group m-0">
						<input type="text" name="inventory" id="inventory" value="#arguments.event.getValue('inventory')#" class="form-control" maxlength="8" />
						<label for="inventory">Max Tickets Available</label>
					</div>
				</div>
				<div class="ml-1 col-md-auto align-items-center">(leave blank if there is no limit)</div>
			</div>
		</div>
	</div>
	
	<div class="form-row">
		<div class="col">
			<div class="form-label-group">
				<select name="autoManageInventory" id="autoManageInventory" class="form-control">
					<option value="0" <cfif arguments.event.getValue('autoManageInventory') is not 1>selected</cfif>>Do NOT automatically remove ticket packages that exceed the Max Tickets Available.</option>
					<option value="1" <cfif arguments.event.getValue('autoManageInventory') is 1>selected</cfif>>Automatically remove ticket packages that exceed the Max Tickets Available.</option>
				</select>
				<label for="autoManageInventory">Exceeding Package Options *</label>
			</div>
			<small class="form-text text-black-50">If enabled, ticket packages that would exceed the cap will not be available for purchase by registrant.</small>
		</div>
	</div>
	
	<div class="form-row mt-2">
		<label class="col-sm-4 col-form-label-sm font-size-md">Revenue GL Override</label>
		<div class="col-sm-8">
			<input type="hidden" name="GLAccountID" id="GLAccountID" value="#val(arguments.event.getValue('GLAccountID'))#">
			<span id="GLAccountPath">
				<cfif len(arguments.event.getValue('GLAccountPath'))><span class="font-weight-bold">#arguments.event.getValue('GLAccountPath')#</span><cfelse>(No account selected; uses event rate's designated GL Account.)</cfif>
			</span>
			<div class="mt-1"><a href="javascript:selectGLAccount();">Choose GL Account</a><a id="elGLClear" href="javascript:clearGLAccount();" class="ml-2 <cfif not len(arguments.event.getValue('GLAccountPath'))>d-none</cfif>">Clear Selected GL Account</a></div>
			<div id="divGLerr" class="alert alert-danger" style="display:none;"></div>
		</div>
	</div>
	<button type="submit" class="d-none"></button>
	</form>
</div>
#local.showGLSelector.data#
</cfoutput>