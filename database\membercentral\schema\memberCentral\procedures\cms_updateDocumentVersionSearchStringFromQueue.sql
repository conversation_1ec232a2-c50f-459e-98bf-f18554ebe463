ALTER PROC dbo.cms_updateDocumentVersionSearchStringFromQueue
@itemID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @sdvID int, @queueTypeID int, @queueStatusID int;

	select @queueTypeID = queueTypeID from platformQueue.dbo.tblQueueTypes where queueType = 'documentSearchStrings';
	select @queueStatusID = queueStatusID from platformQueue.dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'ReadyToProcess';

	SELECT @sdvID = sdvID
	FROM platformQueue.dbo.queue_documentSearchStrings
	WHERE itemID = @itemID
	AND statusID = @queueStatusID;

	IF @sdvID IS NULL
		GOTO on_done;

	-- update doc version search strings
	UPDATE sdv 
	SET sdv.documentLanguageID = dl.documentLanguageID,
		sdv.documentID = d.documentID,
		sdv.siteID = s.siteID,
		sdv.sectionID = d.sectionID,
		sdv.resourceTypeID = srt.resourceTypeID,
		sdv.searchtext = isnull(sdv.searchtext,'') + ' |||mcsearchcodes||| ' +
		'mcsearchDocumentID' + cast(d.documentID as varchar(15)) + 'xxx' + ' ' +
		'mcsearchSiteID' + cast(s.siteID as varchar(15)) + 'xxx' + ' ' +
		'mcsearchSiteCode' + s.sitecode + 'xxx' + ' ' +
		'mcsearchSectionID' + cast(d.sectionID as varchar(15)) + 'xxx' + ' ' +
		'mcsearchResourceType' + srt.resourceType + 'xxx' + ' ' +
		case when parentsrt.resourceType is not null then 'mcsearchParentResourceType' + parentsrt.resourceType + 'xxx' + ' ' else '' end +
		case when parentsr.SiteResourceID is not null then 'mcsearchParentSiteResourceID' + cast(parentsr.SiteResourceID as varchar(15)) + 'xxx' + ' ' else '' end
	FROM searchMC.dbo.cms_documentVersions AS sdv
	INNER JOIN dbo.cms_documentVersions AS dv ON dv.documentVersionID = sdv.documentVersionID
		AND sdv.id = @sdvID
		AND sdv.documentID IS NULL
	INNER JOIN dbo.cms_documentLanguages AS dl ON dl.documentLanguageID = dv.documentLanguageID
	INNER JOIN dbo.cms_documents AS d ON d.documentID = dl.documentID
	INNER JOIN dbo.sites AS s ON s.siteID = d.siteID
	INNER JOIN dbo.cms_siteResources AS sr ON sr.siteID = s.siteID 
		AND sr.siteResourceID = d.siteResourceID
	INNER JOIN dbo.cms_siteResourceTypes AS srt ON srt.resourceTypeID = sr.resourceTypeID
	LEFT OUTER JOIN dbo.cms_siteResources AS parentsr 
		INNER JOIN dbo.cms_siteResourceTypes AS parentsrt ON parentsrt.resourceTypeID = parentsr.resourceTypeID
		ON sr.parentsiteResourceID = parentsr.siteResourceID;

	-- delete queue item
	DELETE FROM platformQueue.dbo.queue_documentSearchStrings
	WHERE itemID = @itemID;

	on_done:
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
