ALTER PROC dbo.ams_runScheduledQueries
@itemCount int OUTPUT
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpQueueItems') IS NOT NULL
		DROP TABLE #tmpQueueItems;
	CREATE TABLE #tmpQueueItems (itemID int);

	DECLARE @queueTypeID int, @queueStatusID int, @batchsize int = 10, @minItemID int, @xmlMessage xml;

	SELECT @queueTypeID = queueTypeID FROM platformQueue.dbo.tblQueueTypes WHERE queueType = 'schQueries';
	SELECT @queueStatusID = queueStatusID FROM platformQueue.dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID AND queueStatus = 'ReadyToProcess';

	-- queue items
	INSERT INTO platformQueue.dbo.queue_schQueries (siteID, queryID, statusID, dateAdded, dateUpdated)
		OUTPUT INSERTED.itemID INTO #tmpQueueItems
	SELECT TOP (@batchsize) sq.siteID, sq.queryID, @queueStatusID, GETDATE(), GETDATE()
	FROM dbo.ams_savedQueries AS sq
	LEFT OUTER JOIN platformQueue.dbo.queue_schQueries AS qi ON qi.queryID = sq.queryID
	WHERE sq.nextRunDate < GETDATE()
	AND qi.itemID IS NULL
	ORDER BY sq.nextRunDate;
	SET @itemCount = @@ROWCOUNT;

	SELECT @minItemID = min(itemID) FROM #tmpQueueItems;
	WHILE @minItemID IS NOT NULL BEGIN
		SET @xmlMessage = '<mc i="' + CAST(@minItemID AS varchar(10)) + '" />';
		EXEC platformQueue.dbo.queue_schQueries_sendMessage @xmlMessage=@xmlMessage;
		SELECT @minItemID = min(itemID) FROM #tmpQueueItems WHERE itemID > @minItemID;
	END

	IF OBJECT_ID('tempdb..#tmpQueueItems') IS NOT NULL
		DROP TABLE #tmpQueueItems;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
