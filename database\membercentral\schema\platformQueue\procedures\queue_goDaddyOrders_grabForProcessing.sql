ALTER PROC dbo.queue_goDaddyOrders_grabForProcessing
@batchSize int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	
	DECLARE @queueTypeID int, @statusReady int;
	SELECT @queueTypeID = queueTypeID FROM dbo.tblQueueTypes WHERE queueType = 'goDaddyOrders';
	SELECT @statusReady = queueStatusID FROM dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID AND queueStatus = 'readyToProcess';

	SELECT TOP (@batchSize) itemID, orderID, dateAdded, dateUpdated
	FROM dbo.queue_goDaddyOrders
	WHERE statusID = @statusReady
	ORDER BY itemID;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
