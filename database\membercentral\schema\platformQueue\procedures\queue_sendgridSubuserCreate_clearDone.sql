ALTER PROC dbo.queue_sendgridSubuserCreate_clearDone
@itemID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @statusDone int;

	select @queueTypeID = queueTypeID
	from dbo.tblQueueTypes
	where queueType = 'SendgridSubuserCreate';

	select @statusDone = queueStatusID 
	from dbo.tblQueueStatuses
	where queueTypeID = @queueTypeID
	and queueStatus = 'Done';
	
	DELETE from dbo.queue_sendgridSubuserCreate
	WHERE itemID = @itemID
	AND statusID = @statusDone;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
