ALTER PROC dbo.ams_refreshAllHasMemberPhoto
@itemCount int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @recordedByMemberID int, @statusReady int, @queueTypeID int, @nowDate datetime = getdate();
	select @queueTypeID = queueTypeID from platformQueue.dbo.tblQueueTypes where queueType = 'refreshMemberPhoto';
	select @statusReady = queueStatusID from platformQueue.dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'readyToProcess';
	select @recordedByMemberID = dbo.fn_ams_getMCSystemMemberID();

	insert into platformQueue.dbo.queue_refreshMemberPhoto (orgID, recordedByMemberID, statusID, dateAdded, dateUpdated)
	select distinct o.orgID, @recordedByMemberID, @statusReady, @nowDate, @nowDate
	from dbo.organizations o
	inner join dbo.sites s on s.orgID = o.orgID
	inner join dbo.cms_siteResources as sr on sr.siteResourceID = s.siteResourceID and sr.siteResourceStatusID = 1
		except
	select orgID, @recordedByMemberID, @statusReady, @nowDate, @nowDate
	from platformQueue.dbo.queue_refreshMemberPhoto;

	SET @itemCount = @@ROWCOUNT;

	IF @itemCount > 0
		EXEC dbo.sched_resumeTask @name='Process Refresh MemberPhoto Queue', @engine='BERLinux';

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
