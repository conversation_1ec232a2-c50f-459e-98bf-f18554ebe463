<cfcomponent>
	
	<cffunction name="getBlogData" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="blogID" type="numeric" required="true">

		<cfset var qryBlog = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryBlog">
			set nocount on;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @RTID int;
			select @RTID = dbo.fn_getResourceTypeID('Community');

			SELECT ai.siteresourceID, ai.applicationInstanceID, b.blogID, b.applicationInstanceID, b.rootSectionID, b.NameDesc, b.Plural, 
				b.Singular, b.Title, b.Type, b.URL, b.WYSIWYG, b.notifyEmails, b.notifyOnAdd, b.notifyOnUpdate, b.preview<PERSON><PERSON>ryCount, 
				b.publish<PERSON><PERSON><PERSON><PERSON><PERSON>, b.showC<PERSON><PERSON><PERSON>n<PERSON>ist<PERSON><PERSON><PERSON>, b.blog<PERSON><PERSON>or<PERSON>up<PERSON>, b.showPostedBy, b.showPhoto, b.showSummary, b.categoryTreeForTopNavigation,
				b.enableFeaturedImage, b.featuredImageLocation, b.showDefaultFeaturedImageOnPost, b.defaultPostTypeID,
				b.shortDescForContentSummary, b.shortDescForContentBody, ai.applicationInstanceName, 
				fileShareName = ai.applicationInstanceName + case WHEN communityInstances.applicationInstanceName is not null THEN ' (' + communityInstances.applicationInstanceName + ')' ELSE '' END,
				isnull(communityInstances.applicationInstanceName,'') as communityName, sr.siteResourceStatusID as srStatusID,
				srs.siteResourceStatusDesc as srsStatus, ficu.featureImageConfigID, b.editorToolBarForContentSummary, b.editorToolBarForContentBody
			FROM dbo.bl_blog b
			INNER JOIN dbo.cms_applicationInstances ai ON ai.applicationInstanceID = b.applicationInstanceID 
				AND ai.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">
			INNER JOIN dbo.cms_siteResources sr ON ai.siteResourceID = sr.siteResourceID
			INNER JOIN dbo.cms_siteResources AS parentResource ON parentResource.siteResourceID = sr.parentSiteResourceID
				left outer join dbo.cms_siteResources AS grandparentResource
				inner join dbo.cms_applicationInstances AS CommunityInstances on communityInstances.siteResourceID = grandParentResource.siteResourceID
					on grandparentResource.siteResourceID = parentResource.parentSiteResourceID
					and grandparentResource.resourceTypeID = @RTID
			INNER JOIN dbo.cms_siteResourceStatuses srs ON sr.siteResourceStatusID = srs.siteResourceStatusID
			LEFT OUTER JOIN dbo.cms_featuredImageConfigUsages AS ficu ON ficu.referenceID = b.blogID AND ficu.referenceType = 'blogEntry'		
			WHERE b.blogID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.blogID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryBlog>
	</cffunction>
	
	<cffunction name="saveBlogOptions" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.returnStruct 	= { success=true };
		</cfscript>
		
		<cftry>
		<cfif arguments.event.getValue('bID',0) gt 0>
			<cfif NOT arguments.event.getValue('enableFeaturedImg',0)>
				<cfset arguments.event.setValue('featureImageConfigID',0)>
			</cfif>

			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryUpdateBlog">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @blogID int, @siteID int,@afterPostContentID int, @addEntryFormBeforeContentID int, @addEntryFormAfterContentID int, @enteredByMemberID int, 
						@rawcontentAfterPostContent varchar(max), @rawcontentAddEntryFormBeforeContent varchar(max), @rawcontentAddEntryFormAfterContent varchar(max), 
						@enableFeaturedImage bit, @currentEnableFeaturedImage bit, @defaultFeatureImageID int;
					
					set @blogID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('bID')#">;
					set @siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('siteID')#">;
					set @enteredByMemberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=arguments.event.getValue('mc_siteInfo.orgID'))#">;
					set @rawcontentAfterPostContent = <cfqueryparam cfsqltype="cf_sql_longvarchar" value="#arguments.event.getValue('afterPostContent','')#">;
					set @rawcontentAddEntryFormBeforeContent = <cfqueryparam cfsqltype="cf_sql_longvarchar" value="#arguments.event.getValue('addEntryFormBeforeContent','')#">;
					set @rawcontentAddEntryFormAfterContent = <cfqueryparam cfsqltype="cf_sql_longvarchar" value="#arguments.event.getValue('addEntryFormAfterContent','')#">;
					set @enableFeaturedImage = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getValue('enableFeaturedImg',0)#">;

					select @afterPostContentID = afterPostContentID, @addEntryFormBeforeContentID = addEntryFormBeforeContentID, 
						@addEntryFormAfterContentID = addEntryFormAfterContentID, @currentEnableFeaturedImage = enableFeaturedImage
					from dbo.bl_blog
					where blogID = @blogID;

					IF @currentEnableFeaturedImage = 1
						select @defaultFeatureImageID = fi.featureImageID
						from dbo.cms_featuredImageConfigUsages ficu
						inner join dbo.cms_featuredImageUsages as fiu on fiu.featureImageConfigID = ficu.featureImageConfigID
							and fiu.referenceID = @blogID
							and fiu.referenceType = 'defaultBlogEntry'
						inner join dbo.cms_featuredImages as fi on fi.featureImageID = fiu.featureImageID
						where ficu.referenceID =  @blogID
						and ficu.referenceType = 'blogEntry';

					BEGIN TRAN;
						<cfif arguments.event.getValue('newapplicationInstanceName') NEQ arguments.event.getValue('oldapplicationInstanceName')>
							UPDATE ai
							SET ai.applicationInstanceName = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('newapplicationInstanceName')#">
							FROM dbo.cms_applicationInstances ai
							INNER JOIN dbo.bl_blog b ON ai.applicationInstanceID = b.applicationInstanceID
							WHERE b.blogID = @blogID;
						</cfif>

						UPDATE dbo.bl_blog
						SET NameDesc = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('newNameDesc','')#">,
							Plural = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('newPlural')#">,
							Singular = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('newSingular')#">,
							Title = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getValue('showTitle')#">,
							showCommentsOnListView = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('showCommentsOnListView')#">,
							blogAuthorSupport = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('blogAuthorSupport')#">,
							showPostedBy = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('showPostedBy')#">,
							showPhoto = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('showPhoto')#">,
							showSummary = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('showSummary')#">,
							categoryTreeForTopNavigation = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('categoryTreeForTopNavigation')#">,
							showAttachmentsAfterPost = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('showAttachmentsAfterPost')#">,
							showCategoryListOnPost = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('showCategoryListOnPost')#">,
							showCategoryListOnListView = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('showCategoryListOnListView')#">,
							showPostedOnDate = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('showPostedOnDate')#">,
							URL = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getValue('showURL')#">,
							WYSIWYG = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getValue('showWYSIWYG')#">,
							notifyEmails = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('newNotifyEmails','')#">,
							notifyOnAdd = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getValue('notifyOnAdd')#">,
							notifyOnUpdate = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getValue('notifyOnUpdate')#">,
							previewEntryCount = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('newPreviewEntryCount')#">,
							publishDateLabel = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('newPublishDateLabel','')#">,
							enableFeaturedImage = @enableFeaturedImage,
							featuredImageLocation = nullIf(<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('featuredImageLocation','')#">,''),
							showDefaultFeaturedImageOnPost = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getValue('showDefaultFeaturedImageOnPost',0)#">,
							defaultPostTypeID = nullif(<cfqueryparam cfsqltype="cf_sql_integer" value="#val(arguments.event.getValue('defaultPostTypeID',0))#">,0),
							shortDescForContentSummary = nullif(<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('shortDescForContentSummary','')#">,''),
							shortDescForContentBody = nullif(<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('shortDescForContentBody','')#">,''),
							editorToolBarForContentSummary = ISNULL(NULLIF(<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('editorToolBarForContentSummary','')#">,''), editorToolBarForContentSummary),
							editorToolBarForContentBody = ISNULL(NULLIF(<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('editorToolBarForContentBody','')#">,''), editorToolBarForContentBody)
						WHERE blogID = @blogID;

						EXEC dbo.cms_updateContent @contentID=@afterPostContentID, @languageID=1, @isHTML=1, @contentTitle='',
							@contentDesc='', @rawcontent=@rawcontentAfterPostContent, @memberID=@enteredByMemberID;
							
						EXEC dbo.cms_updateContent @contentID=@addEntryFormBeforeContentID, @languageID=1, @isHTML=1, @contentTitle='',
							@contentDesc='', @rawcontent=@rawcontentAddEntryFormBeforeContent, @memberID=@enteredByMemberID;
							
						EXEC dbo.cms_updateContent @contentID=@addEntryFormAfterContentID, @languageID=1, @isHTML=1, @contentTitle='',
							@contentDesc='', @rawcontent=@rawcontentAddEntryFormAfterContent, @memberID=@enteredByMemberID;

						-- Allowed Post Types
						DELETE pt
						FROM dbo.bl_allowedPostTypes AS pt
						LEFT JOIN dbo.fn_intListToTable(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('allowedPostTypes','')#">,',') tmp
							ON tmp.listitem = pt.postTypeID 
						WHERE pt.blogID = @blogID
						AND tmp.listitem IS NULL;

						<cfif len(arguments.event.getValue('allowedPostTypes',''))>
							INSERT INTO dbo.bl_allowedPostTypes (postTypeID, blogID)
							SELECT tmp.listitem, @blogID
							FROM dbo.fn_intListToTable(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('allowedPostTypes','')#">,',') tmp
							LEFT JOIN dbo.bl_allowedPostTypes AS pt ON pt.blogID = @blogID AND pt.postTypeID = tmp.listitem
							WHERE pt.postTypeID IS NULL;
						</cfif>

						-- Cross-Posting to Other Blogs
						DELETE bct
						FROM bl_blogAllowedCrossPostingTargets as bct
						INNER JOIN dbo.bl_blog as b on b.blogID = bct.sourceBlogID
						INNER JOIN dbo.cms_applicationInstances as ai on ai.applicationInstanceID = b.applicationInstanceID
							AND ai.siteID = @siteID
						LEFT JOIN dbo.fn_intListToTable(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('targetBlogs','')#">,',') tmp
							ON tmp.listitem = bct.targetBlogID
						WHERE bct.sourceBlogID = @blogID
						AND tmp.listitem IS NULL;

						<cfif len(arguments.event.getValue('targetBlogs',''))>
							INSERT INTO dbo.bl_blogAllowedCrossPostingTargets (sourceBlogID, targetBlogID)
							SELECT @blogID, tmp.listitem
							FROM dbo.fn_intListToTable(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('targetBlogs','')#">,',') tmp
							LEFT JOIN dbo.bl_blogAllowedCrossPostingTargets AS t ON t.sourceBlogID = @blogID AND t.targetBlogID = tmp.listitem
							WHERE t.targetBlogID IS NULL;
						</cfif>

						-- Post Type Category Trees for Filtering
						DELETE ct
						FROM dbo.bl_filteringCategoryTrees as ct
						LEFT JOIN dbo.fn_intListToTable(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('categoryTreesForFiltering','')#">,',') tmp
							ON tmp.listitem = ct.categoryTreeID
						WHERE ct.blogID = @blogID
						AND tmp.listitem IS NULL;

						<cfif len(arguments.event.getValue('categoryTreesForFiltering',''))>
							INSERT INTO dbo.bl_filteringCategoryTrees (categoryTreeID, blogID)
							SELECT tmp.listitem, @blogID
							FROM dbo.fn_intListToTable(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('categoryTreesForFiltering')#">,',') tmp
							LEFT JOIN dbo.bl_filteringCategoryTrees AS ct ON ct.blogID = @blogID AND ct.categoryTreeID = tmp.listitem
							WHERE ct.categoryTreeID IS NULL;
						</cfif>
						
						IF @currentEnableFeaturedImage = 1 AND ISNULL(@defaultfeatureImageID,0) > 0 AND @enableFeaturedImage = 0
							EXEC dbo.cms_deleteFeaturedImageUsage @referenceID=@blogID, @referenceType='defaultBlogEntry';
					COMMIT TRAN;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.arrBlogFeaturedImageSizes = [ { "referenceType":"viewEntries", "sizeID":arguments.event.getValue('viewEntriesFeatureImageSizeID',0) },
													  	{ "referenceType":"blogEntry", "sizeID":arguments.event.getValue('blogEntryFeatureImageSizeID',0) } ]>
			<cfset createObject("component","model.admin.common.modules.featuredImages.featuredImages").saveFeaturedImageConfigSettings(
					featureImageConfigID=arguments.event.getValue('featureImageConfigID',0), referenceID=arguments.event.getValue('bID'), 
					referenceType="blogEntry", arrFeaturedImageSizes=local.arrBlogFeaturedImageSizes)>
		</cfif>
		<cfcatch type="any">
			<cfset local.returnStruct.success = false>
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>
	
	<cffunction name="removeBlog" access="public" output="false" returntype="struct">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="blogID" type="numeric" required="true">
		<cfargument name="blogSRID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.tmpRights = application.objSiteResource.buildRightAssignments(siteResourceID=arguments.blogSRID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.siteID)>

		<cfif local.tmpRights.deleteOwn is 1 or local.tmpRights.deleteAny is 1>
			<cfquery name="local.qryRemoveBlog" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON
				
				DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">,
					@siteResourceID int;
				
				SELECT @siteResourceID = sr.parentSiteResourceID
				FROM dbo.bl_blog AS b
				INNER JOIN dbo.cms_applicationInstances AS ai ON ai.applicationInstanceID = b.applicationInstanceID 
					AND ai.siteID = @siteID
				INNER JOIN dbo.cms_siteResources AS sr ON sr.siteID = @siteID
					AND ai.siteResourceID = sr.siteResourceID
				WHERE b.blogID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.blogID#">;
				
				EXEC dbo.cms_deleteSiteResourceAndChildren @siteID=@siteID, @siteResourceID=@siteResourceID;
			</cfquery>

			<cfset local.data.success = true>
		<cfelse>
			<cfset local.data.success = false>
		</cfif>
			
		<cfreturn local.data>
	</cffunction>

	<cffunction name="addBlogAuthor" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="false">
		<cfargument name="blogEntryID" type="numeric" required="true">
		<cfargument name="blogID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryAddBlogAuthor">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @blogEntryID int, @memberID int, @authorID int, @blogSRID int, @blogAuthorSupport char(1), @rightsXML xml;
				SET @blogEntryID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.blogEntryID#">;
				SET @memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">;

				SELECT @blogSRID = ai.siteResourceID, @blogAuthorSupport = b.blogAuthorSupport
				FROM dbo.bl_blog b
				INNER JOIN dbo.cms_applicationInstances ai on ai.applicationInstanceID = b.applicationInstanceID 
				WHERE blogID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.blogID#">;

				SELECT @rightsXML = dbo.fn_cache_perms_getResourceRightsXML(@blogSRID,
					<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberData.memberID#">,
					<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">);

				IF @blogAuthorSupport <> 'D' AND @rightsXML.exist('/rights/right[@functionName="manageAllEntryAuthors"][@allowed="1"]') = 1 BEGIN
					IF NOT EXISTS (select authorID from dbo.bl_authors where blogEntryID = @blogEntryID and memberID = @memberID) BEGIN
						INSERT INTO dbo.bl_authors (blogEntryID, memberID)
						VALUES (@blogEntryID, @memberID);
							SELECT @authorID = SCOPE_IDENTITY();
					END
					ELSE 
						SET @authorID = 0;

					SELECT @authorID AS authorID;
				END
				ELSE RAISERROR('No permissions.', 16, 1);

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfif local.qryAddBlogAuthor.authorID gt 0>
				<cfset local.data.success = true>
			<cfelse>
				<cfset local.data.msg = "The selected member is already an author.">
				<cfset local.data.success = false>
			</cfif>
			<cfcatch type="any">
				<cfset local.data.success = false>
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="removeBlogAuthor" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="false">
		<cfargument name="blogEntryID" type="numeric" required="true">
		<cfargument name="blogID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.data.success = false>

		<cftry>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryRemoveBlogAuthor">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @blogEntryID int, @memberID int, @blogSRID int, @blogAuthorSupport char(1), @rightsXML xml, @loggedInMemberID int;
				SET @blogEntryID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.blogEntryID#">;
				SET @memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">;
				SET @loggedInMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberData.memberID#">;

				SELECT @blogSRID = ai.siteResourceID, @blogAuthorSupport = b.blogAuthorSupport
				FROM dbo.bl_blog b
				INNER JOIN dbo.cms_applicationInstances ai on ai.applicationInstanceID = b.applicationInstanceID 
				WHERE blogID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.blogID#">;

				SELECT @rightsXML = dbo.fn_cache_perms_getResourceRightsXML(@blogSRID,
					@loggedInMemberID,
					<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">);

				IF @blogAuthorSupport <> 'D' AND (@rightsXML.exist('/rights/right[@functionName="manageAllEntryAuthors"][@allowed="1"]') = 1 OR	@memberID = @loggedInMemberID) BEGIN
					DELETE bla
					FROM dbo.bl_authors as bla
					INNER JOIN dbo.ams_members as m on m.memberID = bla.memberID
					WHERE bla.blogEntryID = @blogEntryID
					AND m.activeMemberID = @memberID;
				END
				ELSE RAISERROR('No permissions.', 16, 1);

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfset local.data.success = true>
			<cfcatch type="any">
				<cfset local.data.success = false>
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getAvailableBlogInstances" access="public" output="false" returntype="struct">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="blogEntryID" type="numeric" required="yes">
		
		<cfset var local = structNew()>

		<cfquery name="local.data.arrBlogInstances" datasource="#application.dsn.membercentral.dsn#" returntype="array">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			select b.blogID, ai.applicationInstanceName
			from dbo.bl_blog as b
			inner join dbo.bl_blogAllowedCrossPostingTargets bct on bct.sourceBlogID = b.blogID
			inner join dbo.cms_applicationInstances as ai on ai.applicationInstanceID = b.applicationInstanceID
			inner join dbo.cms_applicationTypes as at on at.applicationTypeID = ai.applicationTypeID and at.applicationTypeName = 'blog'
			inner join dbo.cms_siteResources as sr on sr.siteResourceID = ai.siteResourceID
			inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
				and srs.siteResourceStatusDesc = 'Active'
			left outer join dbo.bl_blogsAndEntries as bae on bae.blogID = b.blogID
				and bae.blogEntryID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.blogEntryID#">
			where ai.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			and bae.blogEntryLinkID is null
			order by ai.applicationInstanceName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfset local.data.success = true>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getAllowedCrossPostingTargets" access="public" output="false" returntype="struct">
		<cfargument name="blogID" type="numeric" required="yes">
		<cfargument name="siteID" type="numeric" required="yes">
		
		<cfset var local = structNew()>
		<cfset local.editOwnRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="Blog", functionName="editOwn")>
		<cfset local.editAnyRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="Blog", functionName="editAny")>
		<cfset local.AddBlogRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="Blog", functionName="AddBlog")>

		<cfquery name="local.data.arrBlogCrossPostingTargets" datasource="#application.dsn.membercentral.dsn#" returntype="array">
			SET NOCOUNT ON;

			declare @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">, @orgID int,
				@blogID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.blogID#">,
				@groupPrintID int, @memberID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberData.memberID#">,
				@editOwn int, @editAny int, @AddBlog int;
			set @editOwn = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.editOwnRFID#">;
			set @editAny = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.editAnyRFID#">;
			set @AddBlog = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.AddBlogRFID#">;
			select @orgID = dbo.fn_getOrgIDfromSiteID(@siteID);

			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select @groupPrintID = groupPrintID from ams_members where memberID = @memberID;

			select b.blogID, ai.applicationInstanceName, functions=STRING_AGG(srf.functionName,'|')
			from dbo.bl_blog as b
			inner join dbo.bl_blogAllowedCrossPostingTargets bcp 
				on bcp.targetBlogID = b.blogID
				and bcp.sourceBlogID = @blogID
			inner join dbo.cms_applicationInstances as ai 
				on ai.siteID = @siteID 
				and ai.applicationInstanceID = b.applicationInstanceID
			inner join dbo.cms_siteResources as sr 
				on sr.siteID = @siteID
				and sr.siteResourceID = ai.siteResourceID
			inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
				and srs.siteResourceStatusDesc = 'Active'
			inner join dbo.cache_perms_siteResourceFunctionRightPrints srfrp 
				on srfrp.siteID = @siteID 
				and srfrp.siteResourceID = ai.siteResourceID
				and srfrp.functionID in (@AddBlog,@editAny,@editOwn)
			inner join dbo.cache_perms_groupPrintsRightPrints gprp 
				on gprp.siteID = @siteID
				and gprp.rightPrintID = srfrp.rightPrintID
				and gprp.groupPrintID = @groupPrintID
			inner join dbo.cms_siteResourceFunctions srf 
				on srf.functionID = srfrp.functionID
			group by b.blogID, ai.applicationInstanceName
			order by ai.applicationInstanceName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.data.success = true>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getAvailableBlogInstancesBySite" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="yes">
		
		<cfset var local = structNew()>

		<cfquery name="local.qryBlogInstances" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			select b.blogID, ai.applicationInstanceName
			from dbo.bl_blog as b
			inner join dbo.cms_applicationInstances as ai on ai.applicationInstanceID = b.applicationInstanceID
			inner join dbo.cms_siteResources as sr on sr.siteResourceID = ai.siteResourceID
			inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
				and srs.siteResourceStatusDesc = 'Active'
			where ai.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			order by ai.applicationInstanceName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn local.qryBlogInstances>
	</cffunction>

	<cffunction name="getLinkedBlogInstances" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="blogEntryID" type="numeric" required="yes">
		
		<cfset var local = structNew()>

		<cfquery name="local.qryLinkedBlogInstances" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			
			select b.blogID, ai.applicationInstanceName
			from dbo.bl_blogsAndEntries as bae
			inner join dbo.bl_blog as b on b.blogID = bae.blogID
				and bae.blogEntryID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.blogEntryID#">
			inner join dbo.cms_applicationInstances as ai on ai.applicationInstanceID = b.applicationInstanceID
			inner join dbo.cms_siteResources as sr on sr.siteResourceID = ai.siteResourceID
			inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
				and srs.siteResourceStatusDesc = 'Active'
			where ai.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			order by ai.applicationInstanceName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn local.qryLinkedBlogInstances>
	</cffunction>

	<cffunction name="addConnectedBlogInstances" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="false">
		<cfargument name="blogEntryID" type="numeric" required="yes">
		<cfargument name="blogIDList" type="string" required="yes">
		
		<cfset var local = structNew()>

		<cftry>
			<cfquery name="local.qryInsertConnectedBlogInstances" datasource="#application.dsn.membercentral.dsn#">
				set nocount on;

				declare @blogEntryID int, @siteID int, @enteredByMemberID int;
				declare @tblNewBlogIDs table(blogID int, rightsXML xml);
				set @blogEntryID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.blogEntryID#">;
				set @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">;
				set @enteredByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberData.memberID#">;

				insert into @tblNewBlogIDs(blogID, rightsXML)
				select tmp.listitem, dbo.fn_cache_perms_getResourceRightsXML(ai.siteResourceID,@enteredByMemberID,@siteID)
				from dbo.fn_intListToTable(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.blogIDList#">,',') as tmp
				inner join dbo.bl_blog as b on b.blogID = tmp.listitem
				inner join dbo.cms_applicationInstances as ai on ai.applicationInstanceID = b.applicationInstanceID
				left outer join dbo.bl_blogsAndEntries as bae on bae.blogID = tmp.listitem
					and bae.blogEntryID = @blogEntryID
				where bae.blogEntryLinkID is null;

				if exists (select 1 from @tblNewBlogIDs where rightsXML.exist('/rights/right[@functionName="AddBlog"][@allowed="1"]') = 0)
					raiserror('No permissions.', 16, 1);

				INSERT INTO dbo.bl_blogsAndEntries (blogID, blogEntryID, dateEntered, enteredByMemberID)
				SELECT blogID, @blogEntryID, GETDATE(), @enteredByMemberID
				FROM @tblNewBlogIDs;

				SELECT blogID FROM @tblNewBlogIDs;
			</cfquery>
			
			<cfif hasBlogFeaturedImage(blogEntryID=arguments.blogEntryID) and local.qryInsertConnectedBlogInstances.recordCount gt 0>
				<cfset generateFeaturedImageThumbnailsForConnectedBlog(blogEntryID=arguments.blogEntryID, blogIDList=valueList(local.qryInsertConnectedBlogInstances.blogID))>
			</cfif>

			<cfset local.data.success = true>
			<cfcatch type="any">
				<cfset local.data.success = false>
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="removeConnectedBlogInstance" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="false">
		<cfargument name="blogEntryID" type="numeric" required="yes">
		<cfargument name="blogID" type="string" required="yes">
		
		<cfset var local = structNew()>

		<cftry>
			<cfquery name="local.qryDeleteConnectedBlogInstance" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @blogEntryID int, @blogID int, @ownerBlogID int, @ownerBlogSRID int, @memberID int, @isMemberInAuthorList bit = 0,
					@rightsXML xml, @siteID int;

				SET @blogEntryID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.blogEntryID#">;
				SET @blogID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.blogID#">;
				SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">;
				SET @memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberData.memberID#">;

				IF EXISTS (SELECT 1 FROM dbo.bl_authors WHERE blogEntryID = @blogEntryID AND memberID = @memberID)
					SET @isMemberInAuthorList = 1;
				
				SELECT @ownerBlogID = be.blogID, @ownerBlogSRID = ai.siteResourceID
				FROM dbo.bl_entry as be
				INNER JOIN dbo.bl_blog as b on b.blogID = be.blogID
				INNER JOIN dbo.cms_applicationInstances as ai on ai.applicationInstanceID = b.applicationInstanceID
				WHERE be.blogEntryID = @blogEntryID;

				SELECT @rightsXML = dbo.fn_cache_perms_getResourceRightsXML(@ownerBlogSRID,@memberID,@siteID);

				IF @blogID <> @ownerBlogID AND (
					@rightsXML.exist('/rights/right[@functionName="editAny"][@allowed="1"]') = 1 OR
					(@rightsXML.exist('/rights/right[@functionName="editOwn"][@allowed="1"]') = 1 AND @isMemberInAuthorList = 1)
				) BEGIN
					DELETE bae
					FROM dbo.bl_blogsAndEntries as bae
					INNER JOIN dbo.bl_entry as be on be.blogEntryID = bae.blogEntryID
					WHERE bae.blogEntryID = @blogEntryID
					AND bae.blogID = @blogID
					AND bae.blogID <> be.blogID;
				END
				ELSE RAISERROR('No permissions.', 16, 1);

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfif hasBlogFeaturedImage(blogEntryID=arguments.blogEntryID)>
				<cfset deleteFeaturedImageThumbnailsForConnectedBlog(blogEntryID=arguments.blogEntryID, blogID=arguments.blogID)>
			</cfif>

			<cfset local.data.success = true>
			<cfcatch type="any">
				<cfset local.data.success = false>
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="isBlogEntryConnectedWithOtherBlogs" access="public" output="false" returntype="struct">
		<cfargument name="blogEntryID" type="numeric" required="yes">
		
		<cfset var local = structNew()>

		<cfquery name="local.qryHasBlogs" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select top 1 bae.blogID
			from dbo.bl_blogsAndEntries as bae
			inner join dbo.bl_entry as be on be.blogEntryID = bae.blogEntryID
			where bae.blogEntryID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.blogEntryID#">
			and bae.blogID <> be.blogID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfset local.data.hasBlogs = local.qryHasBlogs.recordCount gt 0>

		<cfset local.data.success = true>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="generateFeaturedImageThumbnailsForConnectedBlog" access="private" output="false" returntype="void">
		<cfargument name="blogEntryID" type="numeric" required="yes">
		<cfargument name="blogIDList" type="string" required="yes">

		<cfquery name="local.qryBlogFeatureImageConfig" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			
			SELECT DISTINCT cu.featureImageConfigID
			FROM dbo.cms_featuredImageConfigUsages cu
			INNER JOIN dbo.fn_intListToTable(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.blogIDList#">,',') as tmp ON tmp.listitem = cu.referenceID
			WHERE cu.referenceType = 'blogEntry'
			AND NOT EXISTS (
				SELECT featureImageUsageID 
				FROM dbo.cms_featuredImageUsages iu 
				WHERE iu.referenceType = 'blogEntry'
				AND iu.referenceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.blogEntryID#">
				AND iu.featureImageConfigID = cu.featureImageConfigID
			);

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfif local.qryBlogFeatureImageConfig.recordCount gt 0>
			<cfquery name="local.qryFeaturedImageInfo" datasource="#application.dsn.memberCentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				SELECT DISTINCT fi.featureImageID, fi.fileExtension, s.siteCode, o.orgCode
				FROM dbo.cms_featuredImages AS fi
				INNER JOIN dbo.cms_featuredImageUsages fiu ON fiu.featureImageID = fi.featureImageID 
					AND fiu.referenceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.blogEntryID#"> 
					AND fiu.referenceType = 'blogEntry'
				INNER JOIN dbo.cms_featuredImageConfigs AS fic ON fic.featureImageConfigID = fiu.featureImageConfigID
				INNER JOIN dbo.sites AS s ON s.siteID = fi.siteID
				INNER JOIN dbo.organizations AS o ON o.orgID = s.orgID;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfif local.qryFeaturedImageInfo.recordCount>
				<cfset createObject("component","model.admin.common.modules.featuredImages.featuredImages").generateFeaturedImageUsageAndThumbnailsByConfigIDs(orgCode=local.qryFeaturedImageInfo.orgCode, siteCode=local.qryFeaturedImageInfo.siteCode, 
					referenceID=arguments.blogEntryID, referenceType="blogEntry", featureImageID=local.qryFeaturedImageInfo.featureImageID, 
					featureImageConfigIDList=valueList(local.qryBlogFeatureImageConfig.featureImageConfigID), fileExt=local.qryFeaturedImageInfo.fileExtension,
					generateThumbnailsByQueue=0)>
			</cfif>
		</cfif>
	</cffunction>

	<cffunction name="deleteFeaturedImageThumbnailsForConnectedBlog" access="private" output="false" returntype="void">
		<cfargument name="blogEntryID" type="numeric" required="yes">
		<cfargument name="blogID" type="string" required="yes">

		<cfquery name="local.qryBlogFeatureImageConfig" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;			
			
			DECLARE @featureImageConfigID INT;

			SELECT TOP 1 @featureImageConfigID = featureImageConfigID
			FROM dbo.cms_featuredImageConfigUsages			
			WHERE referenceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.blogID#">
			AND referenceType = 'blogEntry';

			/* Checks whether another connected blog exists with same configuration as selected blog, thumbnail deletion not needed in that case */
			SELECT COUNT(icu.featureImageConfigID) AS similarConfigCount, @featureImageConfigID AS featureImageConfigID
			FROM dbo.cms_featuredImageConfigUsages icu
			INNER JOIN dbo.bl_blogsAndEntries bae ON bae.blogID = icu.referenceID 
				AND bae.blogEntryID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.blogEntryID#">
			INNER JOIN dbo.bl_entry e ON e.blogEntryID = bae.blogEntryID
			WHERE icu.referenceType = 'blogEntry'
			AND bae.blogID <> <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.blogID#">
			AND icu.featureImageConfigID = @featureImageConfigID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfif local.qryBlogFeatureImageConfig.recordCount gt 0 and local.qryBlogFeatureImageConfig.similarConfigCount eq 0>
			<cfquery name="local.qryFeaturedImageInfo" datasource="#application.dsn.memberCentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				SELECT TOP 1 fi.featureImageID
				FROM dbo.cms_featuredImages AS fi
				INNER JOIN dbo.cms_featuredImageUsages fiu ON fiu.featureImageID = fi.featureImageID 
					AND fiu.referenceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.blogEntryID#">
					AND fiu.referenceType = 'blogEntry';

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfif local.qryFeaturedImageInfo.recordCount and val(local.qryBlogFeatureImageConfig.featureImageConfigID) gt 0>
				<cfset createObject("component","model.admin.common.modules.featuredImages.featuredImages").deleteFeaturedImageThumbnailsByConfigID(featureImageID=local.qryFeaturedImageInfo.featureImageID, featureImageConfigID=local.qryBlogFeatureImageConfig.featureImageConfigID)>
			</cfif>
		</cfif>
	</cffunction>

	<cffunction name="hasBlogFeaturedImage" access="private" output="false" returntype="boolean">
		<cfargument name="blogEntryID" type="numeric" required="yes">

		<cfset var local = structNew()>
		<cfset local.result = false>

		<cfquery name="local.qryHasFeaturedImage" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;			
			
			SELECT TOP 1 fiu.featureImageID
			FROM dbo.bl_entry AS be
			INNER JOIN dbo.bl_blog AS b ON b.blogID = be.blogID
			LEFT OUTER JOIN dbo.cms_featuredImageConfigUsages ficu ON ficu.referenceID = b.blogID AND ficu.referenceType = 'blogEntry'
			LEFT OUTER JOIN dbo.cms_featuredImageUsages AS fiu 
				INNER JOIN dbo.cms_featuredImages AS fi ON fi.featureImageID = fiu.featureImageID
				ON fiu.featureImageConfigID = ficu.featureImageConfigID AND fiu.referenceID = be.blogEntryID AND fiu.referenceType = 'blogEntry'
			WHERE be.blogEntryID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.blogEntryID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfif local.qryHasFeaturedImage.recordCount gt 0>
			<cfset local.result = true>
		</cfif>

		<cfreturn local.result>
	</cffunction>

	<cffunction name="getBlogEntriesFromFilters" access="public" output="false" returntype="query">
		<cfargument name="Event" type="any" required="true">
		<cfargument name="mode" type="string" required="true">

		<cfset var local = structNew()>

		<cfif arguments.mode is 'entryTabGrid' OR arguments.mode is 'entryListTab'>
			<cfset local.arrCols = arrayNew(1)>
			<cfset arrayAppend(local.arrCols,"isnull(be.postDate,be.dateCreated)")>
			<cfset arrayAppend(local.arrCols,"bs.statusName")>
			<cfset arrayAppend(local.arrCols,"be.blogTitle")>
			<cfif arguments.event.getValue('direct') eq "DES"><cfset arguments.event.setValue('direct','DESC')></cfif>
			<cfset local.orderby = "#local.arrcols[arguments.event.getValue('orderby')+1]# #arguments.event.getValue('direct')#">
		</cfif>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryBlogEntries" result="local.qryBlogEntriesResult">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				IF OBJECT_ID('tempdb..##tmpBlogEntriesSearch') IS NOT NULL 
					DROP TABLE ##tmpBlogEntriesSearch;
				CREATE TABLE ##tmpBlogEntriesSearch (blogEntryID int PRIMARY KEY);

				DECLARE @siteID int, @blogID int, @fBlogTitle varchar(1000), @fPublishedFrom date, @fPublishedTo datetime, @fArticleFrom date, 
					@fArticleTo datetime, @fAssociatedMemberID int, @fAssociatedGroupID int;
				SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteID')#">;
				SET @blogID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('bID',0)#">;

				DECLARE @RTID int;
				SELECT @RTID = dbo.fn_getResourceTypeID('Community');

				<cfif arguments.event.getTrimValue('fBlogTitle','') NEQ ''>
					SET @fBlogTitle = replace(<cfqueryparam value="#arguments.event.getTrimValue('fBlogTitle')#" cfsqltype="CF_SQL_VARCHAR">,'_','\_');
				</cfif>
				<cfif arguments.event.getTrimValue('fPublishedFrom','') NEQ ''>
					SET @fPublishedFrom = <cfqueryparam value="#arguments.event.getTrimValue('fPublishedFrom')#" cfsqltype="CF_SQL_DATE">;
				</cfif>
				<cfif arguments.event.getTrimValue('fPublishedTo','') NEQ ''>
					SET @fPublishedTo = <cfqueryparam value="#arguments.event.getTrimValue('fPublishedTo')# 23:59:59.997" cfsqltype="CF_SQL_TIMESTAMP">;
				</cfif>
				<cfif arguments.event.getTrimValue('fArticleFrom','') NEQ ''>
					SET @fArticleFrom = <cfqueryparam value="#arguments.event.getTrimValue('fArticleFrom')#" cfsqltype="CF_SQL_DATE">;
				</cfif>
				<cfif arguments.event.getTrimValue('fArticleTo','') NEQ ''>
					SET @fArticleTo = <cfqueryparam value="#arguments.event.getTrimValue('fArticleTo')# 23:59:59.997" cfsqltype="CF_SQL_TIMESTAMP">;
				</cfif>
				<cfif arguments.event.getValue('fAssociatedMemberID',0) gt 0>
					SET @fAssociatedMemberID = <cfqueryparam value="#arguments.event.getValue('fAssociatedMemberID')#" cfsqltype="CF_SQL_INTEGER">;
				</cfif>
				<cfif arguments.event.getValue('fAssociatedGroupID',0) gt 0>
					SET @fAssociatedGroupID = <cfqueryparam value="#arguments.event.getValue('fAssociatedGroupID')#" cfsqltype="CF_SQL_INTEGER">;
				</cfif>
				
				INSERT INTO ##tmpBlogEntriesSearch (blogEntryID)
				select distinct be.blogEntryID
				from dbo.bl_blogsAndEntries as bae
				inner join dbo.bl_entry as be on be.blogEntryID = bae.blogEntryID
				<cfif arguments.event.getValue('bID',0) gt 0>
					and bae.blogID = @blogID
				</cfif>	
				inner join dbo.bl_blog as b on b.blogID = be.blogID
				inner join dbo.bl_statuses as bs on bs.statusID = be.statusID
				inner join dbo.cms_applicationInstances as ai on ai.siteID = @siteID AND ai.applicationInstanceID = b.applicationInstanceID
				inner join dbo.cms_siteResources as sr on sr.siteID = @siteID AND sr.siteResourceID = be.siteResourceID
				inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
					and srs.siteResourceStatusDesc = 'Active'
				where ai.siteID = @siteID
				<cfif arguments.event.getTrimValue('fBlogTitle','') NEQ ''>
					and be.blogTitle like '%'+@fBlogTitle+'%' ESCAPE('\')
				</cfif>
				<cfif arguments.event.getTrimValue('fPublishedFrom','') NEQ ''>
					and isnull(be.postDate,be.dateCreated) >= @fPublishedFrom
				</cfif>
				<cfif arguments.event.getTrimValue('fPublishedTo','') NEQ ''>
					and isnull(be.postDate,be.dateCreated) <= @fPublishedTo
				</cfif>
				<cfif arguments.event.getTrimValue('fArticleFrom','') NEQ ''>
					and be.articleDate >= @fArticleFrom
				</cfif>
				<cfif arguments.event.getTrimValue('fArticleTo','') NEQ ''>
					and be.articleDate <= @fArticleTo
				</cfif>
				<cfif ListLen(arguments.event.getValue('fBlogStatus',''))>
					and be.statusID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('fBlogStatus')#" list="true">)
				</cfif>
				<cfif ListLen(arguments.event.getValue('fBlogs',''))>
					and be.blogID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('fBlogs')#" list="true">)
				</cfif>
				<cfif arguments.event.getValue('fAssociatedMemberID',0) gt 0>
					and exists (select 1
								from dbo.bl_authors as ba
								inner join dbo.ams_members as m on m.memberID = ba.memberID 
								inner join dbo.ams_members as mActive on mActive.memberID = m.activeMemberID
									and mActive.memberID = @fAssociatedMemberID
								where ba.blogEntryID = be.blogEntryID)
				</cfif>
				<cfif arguments.event.getValue('fAssociatedGroupID',0) gt 0>
					and exists (select 1
								from dbo.bl_authors as ba
								inner join dbo.ams_members as m on m.memberID = ba.memberID 
								inner join dbo.ams_members as mActive on mActive.memberID = m.activeMemberID
								inner join dbo.cache_members_groups as mg on mg.memberID = mActive.memberID AND mg.groupid = @fAssociatedGroupID
								where ba.blogEntryID = be.blogEntryID)
				</cfif>
				<cfif ListLen(arguments.event.getValue('fBlogCategory',''))>
					and exists (select 1 
								from dbo.cms_categorySiteResources 
								where siteResourceID = be.siteResourceID
								and categoryID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('fBlogCategory')#" list="true">))
				</cfif>;

				<cfif arguments.mode is 'entryTabGrid'>
					DECLARE @posStart int, @posStartAndCount int, @totalCount int;
					SET @posStart = <cfqueryparam value="#arguments.event.getValue('posStart')#" cfsqltype="CF_SQL_INTEGER">;
					SET @posStartAndCount = @posStart + <cfqueryparam value="#arguments.event.getValue('count')#" cfsqltype="CF_SQL_INTEGER">;

					IF OBJECT_ID('tempdb..##tmpBlogEntries') IS NOT NULL 
						DROP TABLE ##tmpBlogEntries;
					CREATE TABLE ##tmpBlogEntries (blogEntryID int, postDate date, blogTitle varchar(1000), statusName varchar(30), row int,blogID int);

					INSERT INTO ##tmpBlogEntries
					select distinct be.blogEntryID, isnull(be.postDate,be.dateCreated), be.blogTitle, bs.statusName, ROW_NUMBER() OVER (ORDER BY #local.orderby#) as row, be.blogID
					from ##tmpBlogEntriesSearch as tmp
					inner join dbo.bl_entry as be on be.blogEntryID = tmp.blogEntryID
					inner join dbo.bl_statuses as bs on bs.statusID = be.statusID;

					select @totalCount = @@ROWCOUNT;

					select tmp.blogEntryID, tmp.postDate, tmp.blogTitle, tmp.statusName, @totalCount as totalCount, 
						authorMemberIDList = stuff((
													select ',' + cast(mactive.memberID as varchar(10))
													from dbo.bl_authors as ba
													inner join dbo.ams_members as m on m.memberID = ba.memberID 
													inner join dbo.ams_members as mActive on mActive.memberID = m.activeMemberID
													where ba.blogEntryID = tmp.blogEntryID
													FOR XML PATH ('')
											),1,1,'')
						, tmp.blogID, tmp1.blogName
					from ##tmpBlogEntries as tmp
					inner join (
						SELECT b.blogID,
						blogName = ai.applicationInstanceName +
						CASE WHEN communityInstances.applicationInstanceName IS NOT NULL THEN ' (' + communityInstances.applicationInstanceName + ')' ELSE '' END
						FROM dbo.bl_blog AS b
						INNER JOIN dbo.cms_applicationInstances AS ai ON ai.applicationInstanceID = b.applicationInstanceID
						AND ai.siteID = @siteID
						INNER JOIN dbo.cms_applicationTypes AS appType ON appType.applicationTypeID = ai.applicationTypeID
						INNER JOIN dbo.cms_siteResources AS sr ON sr.siteID = @siteID AND ai.siteResourceID = sr.siteResourceID
						INNER JOIN dbo.cms_siteResourceStatuses AS srs ON sr.siteResourceStatusID = srs.siteResourceStatusID AND srs.siteResourceStatusDesc = 'Active'
						INNER JOIN dbo.cms_siteResources AS parentResource ON parentResource.siteID = @siteID AND parentResource.siteResourceID = sr.parentSiteResourceID
						LEFT OUTER JOIN dbo.cms_siteResources AS grandparentResource
						INNER JOIN dbo.cms_applicationInstances AS CommunityInstances ON communityInstances.siteResourceID = grandParentResource.siteResourceID
						ON grandparentResource.siteID = @siteID
						AND grandparentResource.siteResourceID = parentResource.parentSiteResourceID
						AND grandparentResource.resourceTypeID = @RTID
						) AS tmp1 ON tmp.blogid=tmp1.blogid

					where tmp.row > @posStart
					and tmp.row <= @posStartAndCount
					order by tmp.row;

					IF OBJECT_ID('tempdb..##tmpBlogEntries') IS NOT NULL 
						DROP TABLE ##tmpBlogEntries;
				<cfelseif arguments.mode is 'entryListTab'>
					DECLARE @posStart int, @posStartAndCount int, @totalCount int;
					SET @posStart = <cfqueryparam value="#arguments.event.getValue('posStart')#" cfsqltype="CF_SQL_INTEGER">;
					SET @posStartAndCount = @posStart + <cfqueryparam value="#arguments.event.getValue('count')#" cfsqltype="CF_SQL_INTEGER">;

					IF OBJECT_ID('tempdb..##tmpBlogEntries') IS NOT NULL 
						DROP TABLE ##tmpBlogEntries;
					CREATE TABLE ##tmpBlogEntries (blogEntryID int, postDate date, blogTitle varchar(1000), statusName varchar(30), row int, siteResourceID int, blogID int, applicationInstanceName varchar(255));

					INSERT INTO ##tmpBlogEntries
					select distinct be.blogEntryID, isnull(be.postDate,be.dateCreated), be.blogTitle, bs.statusName, ROW_NUMBER() OVER (ORDER BY #local.orderby#) as row, ai.siteResourceID, be.blogID, tmp1.blogName
					from ##tmpBlogEntriesSearch as tmp
					inner join dbo.bl_entry as be on be.blogEntryID = tmp.blogEntryID
					inner join dbo.bl_statuses as bs on bs.statusID = be.statusID
					inner join dbo.bl_blog as b on b.blogID = be.blogID
					inner join dbo.cms_applicationInstances as ai on ai.siteID = @siteID AND ai.applicationInstanceID = b.applicationInstanceID
					inner join dbo.cms_siteResources as sr on sr.siteID = @siteID AND sr.siteResourceID = be.siteResourceID
					inner join (
						SELECT b.blogID,
						blogName = ai.applicationInstanceName +
						CASE WHEN communityInstances.applicationInstanceName IS NOT NULL THEN ' (' + communityInstances.applicationInstanceName + ')' ELSE '' END
						FROM dbo.bl_blog AS b
						INNER JOIN dbo.cms_applicationInstances AS ai ON ai.applicationInstanceID = b.applicationInstanceID
						AND ai.siteID = @siteID
						INNER JOIN dbo.cms_applicationTypes AS appType ON appType.applicationTypeID = ai.applicationTypeID
						INNER JOIN dbo.cms_siteResources AS sr ON sr.siteID = @siteID AND ai.siteResourceID = sr.siteResourceID
						INNER JOIN dbo.cms_siteResourceStatuses AS srs ON sr.siteResourceStatusID = srs.siteResourceStatusID AND srs.siteResourceStatusDesc = 'Active'
						INNER JOIN dbo.cms_siteResources AS parentResource ON parentResource.siteID = @siteID AND parentResource.siteResourceID = sr.parentSiteResourceID
						LEFT OUTER JOIN dbo.cms_siteResources AS grandparentResource
						INNER JOIN dbo.cms_applicationInstances AS CommunityInstances ON communityInstances.siteResourceID = grandParentResource.siteResourceID
						ON grandparentResource.siteID = @siteID
						AND grandparentResource.siteResourceID = parentResource.parentSiteResourceID
						AND grandparentResource.resourceTypeID = @RTID
						) AS tmp1 ON be.blogid=tmp1.blogid;

					select @totalCount = @@ROWCOUNT;

					select tmp.blogEntryID, tmp.postDate, tmp.blogTitle, tmp.statusName, @totalCount as totalCount, 
						authorMemberIDList = stuff((
													select ',' + cast(mactive.memberID as varchar(10))
													from dbo.bl_authors as ba
													inner join dbo.ams_members as m on m.memberID = ba.memberID 
													inner join dbo.ams_members as mActive on mActive.memberID = m.activeMemberID
													where ba.blogEntryID = tmp.blogEntryID
													FOR XML PATH ('')
											),1,1,'')
						, siteResourceID, blogID, applicationInstanceName				
					from ##tmpBlogEntries as tmp
					where tmp.row > @posStart
					and tmp.row <= @posStartAndCount
					order by tmp.row;

					IF OBJECT_ID('tempdb..##tmpBlogEntries') IS NOT NULL 
						DROP TABLE ##tmpBlogEntries;		
				<cfelse>
					select blogEntryID
					from ##tmpBlogEntriesSearch;
				</cfif>

				IF OBJECT_ID('tempdb..##tmpBlogEntriesSearch') IS NOT NULL 
					DROP TABLE ##tmpBlogEntriesSearch;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfreturn local.qryBlogEntries>
	</cffunction>

	<cffunction name="getBlogEntryPreviewDetails" access="public" output="false" returntype="struct">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="blogID" type="numeric" required="true">
		<cfargument name="blogEntryID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>

		<cfquery name="local.qryBlogEntry" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select be.blogEntryID, be.blogTitle, isnull(be.postDate,be.dateCreated) as postDate, b.publishDateLabel, b.showPostedBy, b.showSummary, b.NameDesc, bs.statusName, 
				blogContent.rawContent as fullArticle, summaryContent.rawContent as summary, ai.siteResourceID as blogSiteResourceID, 
				authors = stuff((
							select '^~~~^' + mActive.firstname + ' ' + mActive.lastname
							from dbo.bl_authors as ba
							inner join dbo.ams_members as m on m.memberID = ba.memberID 
							inner join dbo.ams_members as mActive on mActive.memberID = m.activeMemberID
							where ba.blogEntryID = be.blogEntryID
							FOR XML PATH ('')
					),1,5,''), 
				authorMemberIDList = stuff((
							select ',' + cast(mactive.memberID as varchar(10))
							from dbo.bl_authors as ba
							inner join dbo.ams_members as m on m.memberID = ba.memberID 
							inner join dbo.ams_members as mActive on mActive.memberID = m.activeMemberID
							where ba.blogEntryID = be.blogEntryID
							FOR XML PATH ('')
					),1,1,'')
			from dbo.bl_blogsAndEntries as bae
			inner join dbo.bl_entry as be on be.blogEntryID = bae.blogEntryID
				and bae.blogID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.blogID#">
				and bae.blogEntryID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.blogEntryID#">
			inner join dbo.bl_blog as b on b.blogID = be.blogID
			inner join dbo.bl_statuses as bs on bs.statusID = be.statusID
			inner join dbo.cms_applicationInstances as ai on ai.applicationInstanceID = b.applicationInstanceID
			inner join dbo.cms_siteResources as sr on sr.siteResourceID = be.siteResourceID
			inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
				and srs.siteResourceStatusDesc = 'Active'
			cross apply dbo.fn_getContent(be.blogContentID,1) as blogContent
			cross apply dbo.fn_getContent(be.summaryContentID,1) as summaryContent
			where ai.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfif local.qryBlogEntry.recordCount>
			<cfset local.tmpBlogRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.qryBlogEntry.blogSiteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.siteID)>

			<cfif local.tmpBlogRights.previewEntries or local.tmpBlogRights.view or local.tmpBlogRights.viewEntries>
				<cfset local.numOfAuthors = listLen(local.qryBlogEntry.authorMemberIDList)>
				<cfset local.thisNum = 1>
				<cfset local.blogAuthor = "">
				<cfloop list="#local.qryBlogEntry.authors#" index="local.thisAuthor" delimiters="^~~~^">
					<cfset local.blogAuthor = local.blogAuthor & local.thisAuthor>
					<cfif local.numOfAuthors gt 1 and local.thisNum lt local.numOfAuthors>
						<cfif local.thisNum + 1 eq local.numOfAuthors>
							<cfset local.blogAuthor = "#local.blogAuthor# & ">
						<cfelse>
							<cfset local.blogAuthor = "#local.blogAuthor#, ">
						</cfif>
					</cfif>
					<cfset local.thisNum = local.thisNum + 1>
				</cfloop>
				
				<cfsavecontent variable="local.returnStruct.data">
					<cfoutput>
						<div style="mt-2 pl-2">
							<div class="d-flex">
								<h4>#local.qryBlogEntry.blogTitle#<cfif local.qryBlogEntry.statusName neq 'Approved'><span class="text-darkred"> (#local.qryBlogEntry.statusName#)</span></cfif></h4>
								<cfif local.tmpBlogRights.editAny or (local.tmpBlogRights.editOwn and ListFind(local.qryBlogEntry.authorMemberIDList,session.cfcuser.memberdata.memberID))>
									<div class="ml-auto">
										<button type="button" class="btn btn-sm btn-outline-primary" onclick="editBlogEntry(#local.qryBlogEntry.blogEntryID#,#arguments.blogID#);">
											<i class="fa-solid fa-pencil"></i> Edit
										</button>
									</div>
								</cfif>
							</div>
							
							<cfif local.qryBlogEntry.showPostedBy or len(local.qryBlogEntry.postDate)>
								<div class="mb-3">
									<cfif local.qryBlogEntry.showPostedBy and len(local.blogAuthor)>
										<span class="mr-3"><b>Author<cfif local.numOfAuthors gt 1>s</cfif>:</b> #local.blogAuthor#</span>
									</cfif>
									<cfif len(local.qryBlogEntry.postDate)>
										<b><cfif len(local.qryBlogEntry.publishDateLabel)>#local.qryBlogEntry.publishDateLabel#<cfelse>Published</cfif>:</b>
										#DateFormat(local.qryBlogEntry.postDate,'m/d/yyyy')#<cfif local.qryBlogEntry.postDate gt now()>(Scheduled)</cfif>
									</cfif>
								</div>
							</cfif>

							<cfif local.qryBlogEntry.showSummary>
								<div class="mcblog_justifytext mb-3">
									<b>SUMMARY:</b><br/>
									#local.qryBlogEntry.summary#
								</div>
							</cfif>

							<div class="mcblog_justifytext">
								<b>#uCase(local.qryBlogEntry.NameDesc)#:</b><br/>
								#local.qryBlogEntry.fullArticle#
							</div>
						</div>
					</cfoutput>
				</cfsavecontent>

				<cfset local.returnStruct.success = true>
			<cfelse>
				<cfset local.returnStruct.success = false>
			</cfif>
		<cfelse>
			<cfset local.returnStruct.success = false>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getBlogEntryStatuses" access="public" output="false" returntype="query">
		<cfset var qryBlogEntryStatuses = "">

		<cfquery name="qryBlogEntryStatuses" datasource="#application.dsn.memberCentral.dsn#" cachedwithin="#createTimeSpan(0,1,0,0)#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select statusID, statusName
			from dbo.bl_statuses;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryBlogEntryStatuses>
	</cffunction>

	<cffunction name="getAllowedPostTypesForBlog" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="blogID" type="numeric" required="true">
		<cfargument name="blogEntryID" type="numeric" required="true">

		<cfset var qryAllowedPostTypesForBlog = ''>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryAllowedPostTypesForBlog" cachedWithin="request">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @siteID int, @blogID int, @blogEntryID int;
			SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
			SET @blogID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.blogID#">;
			SET @blogEntryID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.blogEntryID#">;

			SELECT tmp.postTypeID, CASE WHEN tmp.siteID = @siteID THEN tmp.typeName ELSE tmp.typeName + ' (' + s.siteName + ')' END typeName,
				tmp.description, tmp.categoryTreeIDList, tmp.siteID,
				CASE WHEN tmp.siteID = @siteID THEN 0 ELSE 1 END isCrossSite
			FROM (
				SELECT pt.siteID, pt.postTypeID, pt.typeName, pt.description, STRING_AGG(ptct.categoryTreeID,'|') as categoryTreeIDList
				FROM dbo.bl_allowedPostTypes AS apt
				INNER JOIN dbo.cms_postTypes AS pt ON pt.postTypeID = apt.postTypeID
					AND apt.blogID = @blogID
				LEFT OUTER JOIN dbo.cms_postTypeCategoryTrees as ptct 
					INNER JOIN dbo.cms_categoryTrees as ct on ct.categoryTreeID = ptct.categoryTreeID
					INNER JOIN dbo.cms_siteResources as sr on sr.siteResourceID = ct.siteResourceID and sr.siteResourceStatusID = 1
					ON ptct.postTypeID = pt.postTypeID
				GROUP BY pt.siteID, pt.postTypeID, pt.typeName, pt.description
				<cfif arguments.blogEntryID>
						UNION
					SELECT pt.siteID, pt.postTypeID, pt.typeName, pt.description, STRING_AGG(ptct.categoryTreeID,'|') as categoryTreeIDList
					FROM dbo.bl_entry AS be
					INNER JOIN dbo.cms_postTypes AS pt ON pt.postTypeID = be.postTypeID
						AND be.blogEntryID = @blogEntryID
					LEFT OUTER JOIN dbo.cms_postTypeCategoryTrees as ptct 
						INNER JOIN dbo.cms_categoryTrees as ct on ct.categoryTreeID = ptct.categoryTreeID
						INNER JOIN dbo.cms_siteResources as sr on sr.siteResourceID = ct.siteResourceID and sr.siteResourceStatusID = 1
						ON ptct.postTypeID = pt.postTypeID
					GROUP BY pt.siteID, pt.postTypeID, pt.typeName, pt.description
				</cfif>
			) tmp
			INNER JOIN dbo.sites AS s ON s.siteID = tmp.siteID
			ORDER BY isCrossSite, s.siteName, tmp.typeName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryAllowedPostTypesForBlog>
	</cffunction>

	<cffunction name="getPostTypeCategoryTrees" access="public" output="false" returntype="query">
		<cfargument name="postTypeID" type="numeric" required="true">

		<cfset var qryPostTypeCategoryTrees = ''>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryPostTypeCategoryTrees"  cachedWithin="request">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select ptct.categoryTreeID
			from dbo.cms_postTypes as pt
			inner join dbo.cms_postTypeCategoryTrees as ptct on ptct.postTypeID = pt.postTypeID
			inner join dbo.cms_categoryTrees as ct on ct.categoryTreeID = ptct.categoryTreeID
			inner join dbo.cms_siteResources as sr on sr.siteResourceID = ct.siteResourceID and sr.siteResourceStatusID = 1
			where pt.postTypeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.postTypeID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryPostTypeCategoryTrees>
	</cffunction>
	
	<cffunction name="getBlogCategoriesForFilters" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cfset local.PostTypesAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='PostTypesAdmin',siteID=arguments.siteID)>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryBlogCategories">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @siteID int, @controllingSiteResourceID int;
			set @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
			set @controllingSiteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.PostTypesAdminSRID#">;
			
			select distinct ct.categoryTreeID, ct.categoryTreeName, c.categoryID, c.categoryName, c.categoryPath, ct.sortOrder
			from dbo.cms_categoryTrees as ct
			inner join dbo.cms_siteResources as sr on sr.siteResourceStatusID = 1 and sr.siteResourceID = ct.siteResourceID
			inner join dbo.cms_categories as c on c.categoryTreeID = ct.categoryTreeID and c.isActive = 1
			inner join dbo.cms_postTypeCategoryTrees as pct on pct.categoryTreeID = ct.categoryTreeID
			where ct.siteID = @siteID
			and ct.controllingSiteResourceID = @controllingSiteResourceID
			order by ct.sortOrder, c.categoryPath;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn local.qryBlogCategories>
	</cffunction>

	<cffunction name="getAllPostTypeCategoryTrees" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cfset local.PostTypesAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='PostTypesAdmin',siteID=arguments.siteID)>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryAllPostTypeCategoryTrees">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @siteID int, @controllingSiteResourceID int;
			set @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
			set @controllingSiteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.PostTypesAdminSRID#">;
			
			select distinct ct.categoryTreeID, ct.categoryTreeName, ct.sortOrder
			from dbo.cms_categoryTrees as ct
			inner join dbo.cms_siteResources as sr on sr.siteResourceStatusID = 1 and sr.siteResourceID = ct.siteResourceID
			inner join dbo.cms_postTypeCategoryTrees as ptct on ptct.categoryTreeID = ct.categoryTreeID
			where ct.siteID = @siteID
			and ct.controllingSiteResourceID = @controllingSiteResourceID
			order by ct.sortOrder;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn local.qryAllPostTypeCategoryTrees>
	</cffunction>

	<cffunction name="getFilteringCategoryTreeListForBlog" access="public" output="false" returntype="string">
		<cfargument name="blogID" type="numeric" required="true">

		<cfset var qryFilteringCategoryTreesForBlog = ''>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryFilteringCategoryTreesForBlog">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select fc.categoryTreeID
			from dbo.bl_filteringCategoryTrees as fc
			inner join dbo.cms_categoryTrees as ct on ct.categoryTreeID = fc.categoryTreeID
			inner join dbo.cms_siteResources as sr on sr.siteResourceID = ct.siteResourceID and sr.siteResourceStatusID = 1
			where fc.blogID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.blogID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn valuelist(qryFilteringCategoryTreesForBlog.categoryTreeID)>
	</cffunction>

	<cffunction name="getAllowedPostTypesForSite" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">

		<cfset var qryAllPostTypeCategoryTrees = ''>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryAllPostTypeCategoryTrees">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;

			SELECT DISTINCT pt.postTypeID, pt.typeName, s.siteName,
				CASE WHEN pt.siteID = @siteID THEN pt.typeName ELSE pt.typeName + ' (' + s.siteName + ')' END displayTypeName,
				CASE WHEN pt.siteID = @siteID THEN 0 ELSE 1 END isCrossSite
			FROM dbo.cms_postTypes AS pt
			INNER JOIN dbo.sites as s on s.siteID = pt.siteID
			LEFT JOIN dbo.networks AS n
				INNER JOIN dbo.networkSites AS ns ON ns.networkID = n.networkID
				ON n.networkID = pt.networkID
			WHERE pt.siteID = @siteID OR ns.siteID = @siteID
			ORDER BY isCrossSite, s.siteName, pt.typeName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryAllPostTypeCategoryTrees>
	</cffunction>

	<cffunction name="getAllowedPostTypesListForBlog" access="public" output="false" returntype="string">
		<cfargument name="blogID" type="numeric" required="true">

		<cfset var qryFilteringCategoryTreesForBlog = ''>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryAllowedPostTypesForBlog">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT postTypeID
			FROM dbo.bl_allowedPostTypes
			WHERE blogID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.blogID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn valuelist(qryAllowedPostTypesForBlog.postTypeID)>
	</cffunction>

	<cffunction name="getBlogEntryDetails" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="blogID" type="numeric" required="true">
		<cfargument name="blogEntryID" type="numeric" required="true">

		<cfset var qryBlogEntry = ''>

		<cfquery name="qryBlogEntry" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select be.blogEntryID, be.siteResourceID, be.blogTitle, be.postDate, be.articleDate, be.isSticky, be.dateCreated, 
				be.expirationDate, be.blogContentID, be.summaryContentID, be.statusID, bs.statusName, be.blogID as ownBlogID,
				be.postTypeID, blogContent.rawContent as fullArticle, summaryContent.rawContent as summary,
				fiu.featureImageID, fi.fileExtension as featureImageFileExt, isnull(rd.redirectID,'0') as redirectID, rd.redirectName,
				authorMemberIDList = stuff((
						select ',' + cast(mactive.memberID as varchar(10))
						from dbo.bl_authors as ba
						inner join dbo.ams_members as m on m.memberID = ba.memberID 
						inner join dbo.ams_members as mActive on mActive.memberID = m.activeMemberID
						where ba.blogEntryID = be.blogEntryID
						FOR XML PATH ('')),1,1,''),
				blogIDList = stuff((
						select ',' + cast(bae.blogID as varchar(10))
						from dbo.bl_blogsAndEntries as bae
						where bae.blogEntryID = be.blogEntryID
						FOR XML PATH ('')),1,1,'')
			from dbo.bl_blogsAndEntries as bae
			inner join dbo.bl_entry as be on be.blogEntryID = bae.blogEntryID
				and bae.blogID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.blogID#">
				and bae.blogEntryID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.blogEntryID#">
			inner join dbo.bl_blog as b on b.blogID = be.blogID
			inner join dbo.bl_statuses as bs on bs.statusID = be.statusID
			inner join dbo.cms_applicationInstances as ai on ai.applicationInstanceID = b.applicationInstanceID
			inner join dbo.sites as s on s.siteID = ai.siteID
			inner join dbo.organizations as o on o.orgID = s.orgID
			inner join dbo.cms_siteResources as sr on sr.siteResourceID = be.siteResourceID
			inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
				and srs.siteResourceStatusDesc = 'Active'
			cross apply dbo.fn_getContent(be.blogContentID,1) as blogContent
			cross apply dbo.fn_getContent(be.summaryContentID,1) as summaryContent
			left outer join dbo.cms_featuredImageConfigUsages ficu on ficu.referenceID = bae.blogID and ficu.referenceType = 'blogEntry'
			left outer join dbo.cms_featuredImageUsages as fiu 
				inner join dbo.cms_featuredImages as fi on fi.featureImageID = fiu.featureImageID
				on fiu.featureImageConfigID = ficu.featureImageConfigID and fiu.referenceID = be.blogEntryID and fiu.referenceType = 'blogEntry'
			left outer join dbo.siteRedirects rd ON rd.redirectID = be.redirectID
			where ai.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryBlogEntry>
	</cffunction>

	<cffunction name="getBlogCategoryTrees" access="public" output="false" returntype="query">
		<cfargument name="blogID" type="numeric" required="true">

		<cfset var qryCategoryTrees = "">
		
		<cfquery name="qryCategoryTrees" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @blogID int;
			set @blogID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.blogID#">;

			select distinct ct.categoryTreeID, ct.siteID, ct.siteResourceID, ct.categoryTreeName, ct.categoryTreeDesc, ct.categoryTreeCode, ct.controllingSiteResourceID, ct.sortOrder
			from dbo.bl_allowedPostTypes as apt
			inner join dbo.cms_postTypes as pt on pt.postTypeID = apt.postTypeID
				and apt.blogID = @blogID
			inner join dbo.cms_categoryTrees as ct
				inner join dbo.cms_siteResources as sr on sr.siteResourceStatusID = 1 and sr.siteResourceID = ct.siteResourceID
				on ct.siteID = pt.siteID
				and ct.controllingSiteResourceID = dbo.fn_getSiteResourceIDForResourceType('PostTypesAdmin',pt.siteID)
			order by ct.siteID, ct.sortOrder

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
	
		<cfreturn qryCategoryTrees>
	</cffunction>

	<cffunction name="getBlogEntryBeforeArchiveCategoryDetails" access="public" output="false" returntype="struct">
		<cfargument name="entrySiteResourceID" type="numeric" required="true">

		<cfset var strCategory = structNew()>
		
		<cfquery name="strCategory" datasource="#application.dsn.membercentral.dsn#" returntype="struct" columnKey="categorytreename">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select ct.categoryTreeID as categorytreeid, ct.categoryTreeName as categorytreename, ct.categoryTreeCode as categorytreecode, 
				c.categoryID as categoryid, c.categoryCode as categorycode, c.categoryName as categoryname, c.categoryDesc as categorydesc,
				c.categoryPath as categorypath
			from dbo.cms_categorySiteResources as csr
			inner join dbo.cms_categories as c 
				on c.categoryID = csr.categoryID
				and csr.siteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.entrySiteResourceID#">
				and c.isActive = 1
			inner join dbo.cms_categoryTrees as ct on c.categoryTreeID = ct.categoryTreeID
			order by ct.categoryTreeID,c.sortOrder;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset strCategory.map(function(thisKey,thisValue) { 
			strCategory[thisKey]["categorypatharray"] = listToArray(thisValue["categorypath"],"\").map(function(item){
				return trim(item);
			});
			StructDelete(thisValue,"categorypath");
		})>

		<cfreturn strCategory>
	</cffunction>

	<cffunction name="getBlogEntryBeforeArchiveCategories" access="public" output="false" returntype="struct">
		<cfargument name="controllingSiteResourceID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		
		<cfquery name="local.qryArchiveCategories" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select ct.categoryTreeID, ct.categoryTreeName, STRING_AGG(csr.categoryID,',') as categoryID
			from dbo.cms_categorySiteResources as csr
			INNER JOIN dbo.cms_categories c on c.categoryID = csr.categoryID
			INNER JOIN dbo.cms_categoryTrees ct on c.categoryTreeID = ct.categoryTreeID
			where csr.siteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.controllingSiteResourceID#">
			group by ct.categoryTreeID, ct.categoryTreeName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfloop query="local.qryArchiveCategories">
			<cfset local.returnStruct["s_#local.qryArchiveCategories.categoryTreeID#ID"] = local.qryArchiveCategories.categoryID>
		</cfloop>
		
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getBlogEntryAfterArchiveCategories" access="public" output="false" returntype="struct">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="blogID" type="numeric" required="true">
		<cfargument name="blogEntryID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>

		<cfset local.PostTypesAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='PostTypesAdmin',siteID=arguments.siteID)>

		<cfquery name="local.qryArchiveCategories" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @siteID int, @controllingSiteResourceID int;
			set @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
			set @controllingSiteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.PostTypesAdminSRID#">;

			select ct.categoryTreeID, ct.categoryTreeName, STRING_AGG(c.categoryID,',') as archiveCategoryIDList
			from dbo.bl_blogsAndEntries as bae
			inner join dbo.bl_entry as be on be.blogEntryID = bae.blogEntryID
				and bae.blogEntryID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.blogEntryID#">
				and bae.blogID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.blogID#">
			inner join dbo.bl_blog as b on b.blogID = be.blogID
			inner join dbo.bl_entryArchiveCategories as eac on eac.blogEntryID = be.blogEntryID
			inner join dbo.cms_categories as c on c.categoryID = eac.categoryID
			inner join dbo.cms_categoryTrees as ct on ct.categoryTreeID = c.categoryTreeID
			where ct.controllingSiteResourceID = @controllingSiteResourceID
			group by ct.categoryTreeID, ct.categoryTreeName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfloop query="local.qryArchiveCategories">
			<cfset local.returnStruct["s_#local.qryArchiveCategories.categoryTreeID#ID"] = local.qryArchiveCategories.archiveCategoryIDList>
		</cfloop>
	
		<cfreturn local.returnStruct>
	</cffunction>
	
	<cffunction name="insertEntry" access="public" output="true" returntype="numeric" hint="Insert blog entry">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="blogID" type="numeric" required="true">
		<cfargument name="blogTitle" type="string" required="true">
		<cfargument name="statusID" type="numeric" required="true">
		<cfargument name="postTypeID" type="numeric" required="true">
		<cfargument name="blogURL" type="string" required="true">
		<cfargument name="blogBody" type="string" required="true">
		<cfargument name="summaryContent" type="string" required="true">
		<cfargument name="articleDate" type="string" required="true">
		<cfargument name="postDate" type="string" required="true">
		<cfargument name="expirationDate" type="string" required="true">
		<cfargument name="isSticky" type="boolean" required="true">
		<cfargument name="authorMemberIDList" type="string" required="true">
		<cfargument name="blogIDList" type="string" required="true">
		<cfargument name="categoryIDList" type="string" required="true">
		<cfargument name="archiveCategoryIDList" type="string" required="true">
		<cfargument name="arrPostTypeCustomFields" type="array" required="true">
		
		<cfset var local = structNew()>

		<cfif arrayLen(arguments.arrPostTypeCustomFields)>
			<cfsavecontent variable="local.insertCustomFieldsSQL">
				<cfoutput>
					<cfloop array="#arguments.arrPostTypeCustomFields#" index="local.cf">
						<cfif listFindNoCase("SELECT,RADIO,CHECKBOX",local.cf.displayTypeCode)>
							<cfset local.tempSQL = insertBlogEntry_cf_option(itemType='PostTypeCustom', fieldID=local.cf.fieldID, valueIDList=local.cf.value)>
							#local.tempSQL#
						<cfelseif len(local.cf.value)>
							<cfset local.tempSQL = insertBlogEntry_cf_nonOption(itemType='PostTypeCustom', fieldID=local.cf.fieldID, customText=local.cf.value)>
								#local.tempSQL#
						</cfif>
					</cfloop>
				</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryInsertBlogEntry">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				DECLARE @siteID int, @blogID int, @blogTitle varchar(1000), @statusID int, @postTypeID int, @blogBody varchar(max), 
					@summaryContent varchar(max), @articleDate datetime, @postDate datetime, @expirationDate datetime, @isSticky bit, 
					@blogEntryID int, @blogURL varchar(500), @authorMemberIDList varchar(8000), @blogIDList varchar(8000), @enteredByMemberID int, 
					@blogSiteResourceID int, @fieldID int, @valueID int, @dataID int, @detail varchar(max);
							
				SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
				SET @blogID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.blogID#">;
				SET @blogTitle = nullIf(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(arguments.blogTitle)#">, '');
				SET @statusID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.statusID#">;
				SET @postTypeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.postTypeID#">;
				SET @blogBody = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.blogBody#">;
				SET @summaryContent = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.summaryContent#">;
				<cfif len(arguments.articleDate)>
					SET @articleDate = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.articleDate#">;
				</cfif>
				<cfif len(arguments.postDate)>
					SET @postDate = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.postDate#">;
				</cfif>
				<cfif len(arguments.expirationDate)>
					SET @expirationDate = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.expirationDate#">;
				</cfif>
				SET @isSticky = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.isSticky#">;
				SET @authorMemberIDList = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.authorMemberIDList#">;
				SET @blogIDList = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.blogIDList#">;
				SET @blogURL = nullIf(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(arguments.blogURL)#">, '');
				SET @enteredByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberData.memberID#">;
				
				BEGIN TRAN;
					EXEC dbo.bl_createEntry @siteID=@siteID, @languageID=1, @blogID=@blogID, @blogTitle=@blogTitle, @statusID=@statusID, 
						@postTypeID=@postTypeID, @blogBody=@blogBody, @summaryContent=@summaryContent, @blogURL=@blogURL, @articleDate=@articleDate, 
						@postDate=@postDate, @expirationDate=@expirationDate, @isSticky=@isSticky, @authorMemberIDList=@authorMemberIDList,
						@blogIDList=@blogIDList, @enteredByMemberID=@enteredByMemberID, @blogEntryID=@blogEntryID OUTPUT;
					
					select @blogSiteResourceID = siteResourceID
					from dbo.bl_entry
					where blogEntryID = @blogEntryID;

					<cfif arguments.categoryIDList neq ''>
						INSERT INTO dbo.cms_categorySiteResources (CategoryID, siteResourceID)
						select listitem, @blogSiteResourceID
						from dbo.fn_intListToTable(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.categoryIDList#">,',');
					</cfif>

					<cfif arguments.archiveCategoryIDList neq ''>
						INSERT INTO dbo.bl_entryArchiveCategories (blogEntryID, categoryID)
						select @blogEntryID, listitem
						from dbo.fn_intListToTable(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.archiveCategoryIDList#">,',');
					</cfif>

					<cfif arrayLen(arguments.arrPostTypeCustomFields)>
						#preserveSingleQuotes(local.insertCustomFieldsSQL)#
					</cfif>
				COMMIT TRAN;

				SELECT @blogEntryID AS blogEntryID;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
		
		<cfreturn local.qryInsertBlogEntry.blogEntryID>
	</cffunction>

	<cffunction name="updateEntry" access="public" output="false" returntype="void" hint="Update Entry">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="blogEntryID" type="numeric" required="true">
		<cfargument name="blogID" type="numeric" required="true">
		<cfargument name="blogTitle" type="string" required="true">
		<cfargument name="statusID" type="numeric" required="true">
		<cfargument name="postTypeID" type="numeric" required="true">
		<cfargument name="blogURL" type="string" required="true">
		<cfargument name="blogBody" type="string" required="true">
		<cfargument name="summaryContent" type="string" required="true">
		<cfargument name="articleDate" type="string" required="true">
		<cfargument name="postDate" type="string" required="true">
		<cfargument name="expirationDate" type="string" required="true">
		<cfargument name="isSticky" type="boolean" required="true">
		<cfargument name="categoryIDList" type="string" required="true">
		<cfargument name="archiveCategoryIDList" type="string" required="true">
		<cfargument name="arrPostTypeCustomFields" type="array" required="true">
		<cfargument name="arrBlogEntryDocs" type="array" required="true">

		<cfset var local = structNew()>

		<cfif arrayLen(arguments.arrPostTypeCustomFields)>
			<cfsavecontent variable="local.updateCustomFieldsSQL">
				<cfoutput>
					<cfloop array="#arguments.arrPostTypeCustomFields#" index="local.cf">
						<cfif listFindNoCase("SELECT,RADIO,CHECKBOX",local.cf.displayTypeCode)>
							<cfset local.tempSQL = updateBlogEntry_cf_option(itemType='PostTypeCustom', blogEntryID=arguments.blogEntryID, fieldID=local.cf.fieldID, valueIDList=local.cf.value)>
							#local.tempSQL#
						<cfelse>
							<cfset local.tempSQL = updateBlogEntry_cf_nonOption(itemType='PostTypeCustom', blogEntryID=arguments.blogEntryID, fieldID=local.cf.fieldID, customText=local.cf.value)>
								#local.tempSQL#
						</cfif>
					</cfloop>
				</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryUpdateBlogEntry">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				<cfif arrayLen(arguments.arrBlogEntryDocs)>
					IF OBJECT_ID('tempdb..##tmpBlogEntryDocs') IS NOT NULL
						DROP TABLE ##tmpBlogEntryDocs;
					CREATE TABLE ##tmpBlogEntryDocs (documentID int PRIMARY KEY, docTitle varchar(500));
				</cfif>

				DECLARE @siteID int, @blogID int, @blogTitle varchar(1000), @blogBody varchar(max), @summaryContent varchar(max), 
					@articleDate datetime, @postDate datetime, @expirationDate datetime, @isSticky bit, @blogEntryID int, @statusID int, 
					@postTypeID int, @blogURL varchar(500), @enteredByMemberID int, @blogSiteResourceID int, @fieldID int, @valueID int, 
					@dataID int, @detail varchar(max);
							
				SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
				SET @blogID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.blogID#">;
				SET @blogEntryID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.blogEntryID#">;
				SET @blogTitle = nullIf(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(arguments.blogTitle)#">, '');
				SET @statusID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.statusID#">;
				SET @postTypeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.postTypeID#">;
				SET @blogBody = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.blogBody#">;
				SET @summaryContent = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.summaryContent#">;
				<cfif len(arguments.articleDate)>
					SET @articleDate = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.articleDate#">;
				</cfif>
				<cfif len(arguments.postDate)>
					SET @postDate = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.postDate#">;
				</cfif>
				<cfif len(arguments.expirationDate)>
					SET @expirationDate = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.expirationDate#">;
				</cfif>
				SET @isSticky = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.isSticky#">;
				SET @blogURL = nullIf(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(arguments.blogURL)#">, '');
				SET @enteredByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberData.memberID#">;

				select @blogSiteResourceID = siteResourceID
				from dbo.bl_entry
				where blogEntryID = @blogEntryID;

				<cfif arrayLen(arguments.arrBlogEntryDocs)>
					<cfloop array="#arguments.arrBlogEntryDocs#" index="local.thisBlogEntryDoc">
						INSERT INTO ##tmpBlogEntryDocs (documentID, docTitle)
						VALUES (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisBlogEntryDoc.documentID#">,
								<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.thisBlogEntryDoc.docTitle#">);
					</cfloop>
				</cfif>
				
				BEGIN TRAN;
					EXEC dbo.bl_updateEntry @siteID=@siteID, @blogEntryID=@blogEntryID, @blogID=@blogID, @blogTitle=@blogTitle, @statusID=@statusID, 
						@postTypeID=@postTypeID, @blogBody=@blogBody, @summaryContent=@summaryContent, @blogURL=@blogURL, @articleDate=@articleDate, 
						@postDate=@postDate, @expirationDate=@expirationDate, @isSticky=@isSticky, @enteredByMemberID=@enteredByMemberID;
					
					DELETE FROM dbo.cms_categorySiteResources
					WHERE siteResourceID = @blogSiteResourceID;

					DELETE FROM dbo.bl_entryArchiveCategories
					WHERE blogEntryID = @blogEntryID;

					<cfif arguments.categoryIDList neq ''>
						INSERT INTO dbo.cms_categorySiteResources (CategoryID, siteResourceID)
						select listitem, @blogSiteResourceID
						from dbo.fn_intListToTable(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.categoryIDList#">,',');
					</cfif>

					<cfif arguments.archiveCategoryIDList neq ''>
						INSERT INTO dbo.bl_entryArchiveCategories (blogEntryID, categoryID)
						select @blogEntryID, listitem
						from dbo.fn_intListToTable(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.archiveCategoryIDList#">,',');
					</cfif>

					<cfif arrayLen(arguments.arrPostTypeCustomFields)>
						#preserveSingleQuotes(local.updateCustomFieldsSQL)#
					</cfif>

					<cfif arrayLen(arguments.arrBlogEntryDocs)>
						UPDATE cdl
						SET cdl.docTitle = tmpDoc.docTitle,
							cdl.dateModified = GETDATE()
						FROM dbo.cms_documentLanguages AS cdl
						INNER JOIN dbo.bl_entryDocuments AS ed ON ed.documentID = cdl.documentID
						INNER JOIN ##tmpBlogEntryDocs AS tmpDoc ON tmpDoc.documentID = ed.documentID
						WHERE ed.blogEntryID = @blogEntryID;
					</cfif>
				COMMIT TRAN;

				<cfif arrayLen(arguments.arrBlogEntryDocs)>
					IF OBJECT_ID('tempdb..##tmpBlogEntryDocs') IS NOT NULL
						DROP TABLE ##tmpBlogEntryDocs;
				</cfif>

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
	</cffunction>

	<cffunction name="getBlogsOnSiteByPermission" access="public" returntype="array">
		<cfargument name="functionName" type="string" required="yes"/>
		<cfargument name="siteID" type="numeric" required="yes"/>
		
		<cfset var local = structNew()>
		<cfset local.arrBlogsWithRights = arrayNew()>
		
		<cfquery name="local.qryBlogsOnSite" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @RTID int, @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;

			SELECT @RTID = dbo.fn_getResourceTypeID('Community');

			SELECT b.blogID, ai.siteResourceID,
				blogName = ai.applicationInstanceName + 
					CASE WHEN communityInstances.applicationInstanceName IS NOT NULL THEN ' (' + communityInstances.applicationInstanceName + ')' ELSE '' END
			FROM dbo.bl_blog AS b
			INNER JOIN dbo.cms_applicationInstances AS ai ON ai.applicationInstanceID = b.applicationInstanceID
				AND ai.siteID = @siteID
			INNER JOIN dbo.cms_siteResources AS sr ON sr.siteID = @siteID AND sr.siteResourceID = ai.siteResourceID
			INNER JOIN dbo.cms_siteResourceStatuses AS srs ON srs.siteResourceStatusID = sr.siteResourceStatusID
				AND srs.siteResourceStatusDesc = 'Active'
			INNER JOIN dbo.cms_siteResources AS parentResource ON parentResource.siteID = @siteID AND parentResource.siteResourceID = sr.parentSiteResourceID
			LEFT OUTER JOIN dbo.cms_siteResources AS grandparentResource
				INNER JOIN dbo.cms_applicationInstances AS CommunityInstances ON communityInstances.siteResourceID = grandParentResource.siteResourceID
				ON grandparentResource.siteID = @siteID 
					AND grandparentResource.siteResourceID = parentResource.parentSiteResourceID
					AND grandparentResource.resourceTypeID = @RTID
			ORDER BY ai.applicationInstanceName, communityInstances.applicationInstanceName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfloop query="local.qryBlogsOnSite">
			<cfset local.tmpBlogRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.qryBlogsOnSite.siteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.siteID)>
			<cfif local.tmpBlogRights[arguments.functionName]>
				<cfset arrayAppend(local.arrBlogsWithRights, { blogID=local.qryBlogsOnSite.blogID, blogName=local.qryBlogsOnSite.blogName })>
			</cfif>
		</cfloop>

		<cfreturn local.arrBlogsWithRights />
	</cffunction>
	<cffunction name="uploadEntryDocument" access="public" output="false" returntype="void">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="blogEntryID" type="numeric" required="true">
		<cfargument name="parentSiteResourceID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.objDocument = CreateObject("component","model.system.platform.document")>

		<cftry>
			<cfset local.newFile = local.objDocument.uploadFile("form.file")>
			<cfif local.newFile.uploadComplete>
				<cfset local.objDocument.forceFileExtentionIfBlank(local.newFile)>
				<cfset local.insertResults = local.objDocument.insertDocument(siteID=arguments.siteID, 
					resourceType='ApplicationCreatedDocument', parentSiteResourceID=arguments.parentSiteResourceID, sectionID=0, 
					docTitle=local.newFile.clientFile, docDesc='', author='', fileData=local.newFile, isActive=1, isVisible=true, 
					contributorMemberID=session.cfcuser.memberdata.memberid, recordedByMemberID=session.cfcuser.memberdata.memberid,
					oldFileExt=local.newFile.serverFileExt)>

				<cfset saveEntryDocument(siteID=arguments.siteID,
					orgID=arguments.orgID, documentSiteResourceID=local.insertResults.documentSiteResourceID,
					blogEntryID=arguments.blogEntryID, documentID=local.insertResults.documentID)>
			</cfif>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfif fileExists("#local.newFile.ServerDirectory#/#local.newFile.ServerFile#")>
				<cffile action="delete" file="#local.newFile.ServerDirectory#/#local.newFile.ServerFile#">
			</cfif>
		</cfcatch>
		</cftry>
	</cffunction>
	<cffunction name="saveEntryDocument" access="public" output="false" returntype="void">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="documentSiteResourceID" type="numeric" required="true">
		<cfargument name="blogEntryID" type="numeric" required="true">
		<cfargument name="documentID" type="numeric" required="true">

		<cfset var local.qrySaveEntryDoc = "">
		
		<cfquery name="local.qrySaveEntryDoc" datasource="#application.dsn.membercentral.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				DECLARE @rfid int, @siteID int, @orgID int, @docSRID int, @publicgroupID int;
				SET @rfid = 4;
				SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
				SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">;
				SET @docSRID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.documentSiteResourceID#">;

				SELECT @publicgroupID = groupID
				FROM dbo.ams_groups
				WHERE orgID = @orgID
				AND groupCode = 'Public'
				AND isSystemGroup = 1;

				BEGIN TRAN;
					INSERT INTO dbo.bl_entryDocuments (blogEntryID, documentID)
					VALUES (
						<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.blogEntryID#">,
						<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.documentID#">
					);

					EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@docSRID, @include=1, 
						@functionIDList=@rfid, @roleID=null, @groupID=@publicgroupID, @inheritedRightsResourceID=null, 
						@inheritedRightsFunctionID=null;
				COMMIT TRAN;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
	</cffunction>

	<cffunction name="copyEntry" access="public" output="true" returntype="struct" hint="Copy blog entry">
		<cfargument name="sourceSiteID" type="numeric" required="true">
		<cfargument name="sourceBlogID" type="numeric" required="true">
		<cfargument name="sourceBlogEntryID" type="numeric" required="true">
		<cfargument name="destinationBlogApplicationInstanceID" type="numeric" required="true">
		<cfargument name="destinationSiteID" type="numeric" required="true">
		<cfargument name="newTitle" type="string" required="true">
		<cfargument name="statusID" type="numeric" required="true">
		<cfargument name="linkedBlogIDList" type="string" required="true">
		
		<cfset var local = structNew()>
		<cfset local.data = { "success" = false, "blogEntryID" = 0 }>
		<cfset local.objDocument = createObject("model.system.platform.document")>
		
		<cfset local.sourceSiteCode = application.objSiteInfo.getSiteCodeFromSiteID(siteID=arguments.sourceSiteID)>
		<cfset local.strSourceSiteInfo = application.objSiteInfo.getSiteInfo(siteCode=local.sourceSiteCode)>
		<cfset local.destinationSiteCode = application.objSiteInfo.getSiteCodeFromSiteID(siteID=arguments.destinationSiteID)>
		<cfset local.strDestinationSiteInfo = application.objSiteInfo.getSiteInfo(siteCode=local.destinationSiteCode)>
		<cfset local.isCrossSiteCopy = arguments.sourceSiteID neq arguments.destinationSiteID>

		<cftry>
		<cfstoredproc datasource="#application.dsn.membercentral.dsn#"  procedure="bl_copyEntry">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.sourceBlogEntryID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.destinationBlogApplicationInstanceID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.destinationSiteID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.newTitle#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.statusID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.linkedBlogIDList#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberData.memberID#">
			<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.newBlogEntryID">
		</cfstoredproc>

		<cfif val(local.newBlogEntryID) gt 0>
			<!--- copying documents --->
			<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.qryDocuments">
				SELECT DISTINCT ed.documentID, dv.fileName
				FROM dbo.bl_entryDocuments as ed
				INNER JOIN dbo.cms_documents as d ON ed.documentID = d.documentID
				INNER JOIN dbo.cms_documentLanguages as dl ON d.documentID = dl.documentID
				INNER JOIN dbo.cms_documentVersions as dv ON dl.documentLanguageID = dv.documentLanguageID and dv.isActive = 1
				INNER JOIN dbo.cms_siteResources as sr on sr.siteResourceID = d.siteResourceID
				INNER JOIN dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID and srs.siteResourceStatusDesc = 'Active'
				WHERE ed.blogEntryID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.sourceBlogEntryID#">
				ORDER BY dv.fileName;
			</cfquery>

			<cfset local.arrCopiedDocuments = []>
			<cfloop query="local.qryDocuments">
				<cfset local.copyDocumentResult = local.objDocument.copyDocument(sourceDocumentID=local.qryDocuments.documentID, destinationSiteID=arguments.destinationSiteID,
					destinationSectionID=0, contributorMemberID=session.cfcuser.memberData.memberID, recordedByMemberID=session.cfcuser.memberData.memberID)>

				<cfset saveEntryDocument(siteID=arguments.destinationSiteID, orgID=local.strDestinationSiteInfo.orgID, documentSiteResourceID=val(local.copyDocumentResult.documentSiteResourceID),
					blogEntryID=local.newBlogEntryID, documentID=val(local.copyDocumentResult.documentID))>

				<cfset arrayAppend(local.arrCopiedDocuments, { sourceDocID = local.qryDocuments.documentID, destinationDocID = local.copyDocumentResult.documentID })>
			</cfloop>

			<!--- HTML cleanup --->
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryUpdateContent">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @siteID int, @blogEntryID int, @defaultLanguageID int, @enteredByMemberID int, @blogContentID int, @summaryContentID int,
						@blogBody varchar(max), @summaryContent varchar(max);

					SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.destinationSiteID#">;
					SET @blogEntryID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.newBlogEntryID#">;
					SET @enteredByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberData.memberID#">;
					SELECT @defaultLanguageID = defaultLanguageID FROM dbo.sites WHERE siteID = @siteID;

					SELECT @blogContentID = be.blogContentID, @blogBody = blogContent.rawContent,
						@summaryContentID = be.summaryContentID, @summaryContent = summaryContent.rawContent
					FROM dbo.bl_entry as be
					CROSS APPLY dbo.fn_getContent(be.blogContentID,1) as blogContent
					CROSS APPLY dbo.fn_getContent(be.summaryContentID,1) as summaryContent
					WHERE be.blogEntryID = @blogEntryID;

					<cfif local.isCrossSiteCopy>
						SET @blogBody = REPLACE(@blogBody, '#local.strSourceSiteInfo.mainhostname#', '#local.strDestinationSiteInfo.mainhostname#');
						SET @summaryContent = REPLACE(@summaryContent, '#local.strSourceSiteInfo.mainhostname#', '#local.strDestinationSiteInfo.mainhostname#');
					</cfif>

					<cfloop array="#local.arrCopiedDocuments#" index="local.thisCopiedDocument">
						SET @blogBody = REPLACE(@blogBody, '/docDownload/#local.thisCopiedDocument.sourceDocID#', '/docDownload/#local.thisCopiedDocument.destinationDocID#');
						SET @summaryContent = REPLACE(@summaryContent, '/docDownload/#local.thisCopiedDocument.sourceDocID#', '/docDownload/#local.thisCopiedDocument.destinationDocID#');
					</cfloop>
					
					BEGIN TRAN;
						EXEC dbo.cms_updateContent @contentID=@blogContentID, @languageID=@defaultLanguageID, @isHTML=1,
							@contentTitle='', @contentDesc='', @rawcontent=@blogBody, @memberID=@enteredByMemberID;
						EXEC dbo.cms_updateContent @contentID=@summaryContentID, @languageID=@defaultLanguageID, @isHTML=1,
							@contentTitle='', @contentDesc='', @rawcontent=@summaryContent, @memberID=@enteredByMemberID;
					COMMIT TRAN;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<!--- copy featured image --->
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryDestinationBlog">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				SELECT b.enableFeaturedImage, ficu.featureImageConfigID
				FROM dbo.bl_blog b
				LEFT OUTER JOIN dbo.cms_featuredImageConfigUsages AS ficu ON ficu.referenceID = b.blogID AND ficu.referenceType = 'blogEntry'
				WHERE b.applicationInstanceID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.destinationBlogApplicationInstanceID#">;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
			
			<cfset local.qrySourceBlog = getBlogData(siteID=arguments.sourceSiteID, blogID=arguments.sourceBlogID)>
			<cfset local.qrySourceBlogEntry = getBlogEntryDetails(siteID=arguments.sourceSiteID, blogID=arguments.sourceBlogID, blogEntryID=arguments.sourceBlogEntryID)>

			<cfif val(local.qrySourceBlogEntry.featureImageID) and local.qryDestinationBlog.enableFeaturedImage and val(local.qryDestinationBlog.featureImageConfigID) gt 0>
				<cfset local.objFeaturedImages = createObject("component","model.admin.common.modules.featuredImages.featuredImages")>
				
				<cfif local.isCrossSiteCopy>
					<cfset local.objFeaturedImages.doCopyFeaturedImage(sourceOrgCode=local.strSourceSiteInfo.orgCode, sourceSiteCode=local.strSourceSiteInfo.siteCode,
						destinationOrgCode=local.strDestinationSiteInfo.orgCode, destinationSiteCode=local.strDestinationSiteInfo.siteCode,
						copyFromFtdImgID=val(local.qrySourceBlogEntry.featureImageID), featureImageConfigID=val(local.qryDestinationBlog.featureImageConfigID),
						referenceID=local.newBlogEntryID, referenceType="blogEntry", generateThumbnailsByQueue=1)>
				<cfelse>
					<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="cms_createFeaturedImageUsage">
						<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#val(local.qrySourceBlogEntry.featureImageID)#">
						<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#val(local.qryDestinationBlog.featureImageConfigID)#">
						<cfprocparam type="IN" cfsqltype="CF_SQL_VARCHAR" value="blogEntry">
						<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#local.newBlogEntryID#">
						<cfprocparam type="OUT" cfsqltype="CF_SQL_INTEGER" variable="local.featureImageUsageID">
					</cfstoredproc>

					<!--- queue thumbnail generation for featured image with new config --->
					<cfif val(local.qrySourceBlog.featureImageConfigID) neq val(local.qryDestinationBlog.featureImageConfigID)>
						<cfset local.objFeaturedImages.queueFeaturedImageByConfigID(featureImageID=val(local.qrySourceBlogEntry.featureImageID), featureImageConfigID=val(local.qryDestinationBlog.featureImageConfigID))>
					</cfif>
				</cfif>
			</cfif>

			<cfset local.data.success = true>
			<cfset local.data.blogEntryID = local.newBlogEntryID>
		</cfif>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>
		
		<cfreturn local.data>
	</cffunction>

	<cffunction name="insertBlogEntry_cf_nonOption" access="private" output="false" returntype="string">
		<cfargument name="itemType" type="string" required="true">
		<cfargument name="fieldID" type="numeric" required="true">
		<cfargument name="customText" type="string" required="true">
		
		<cfset var local = structNew()>

		<cfif len(arguments.customText)>
			<cfsavecontent variable="local.insertBlogEntrySQL">
				<cfoutput>
				set @fieldID = #arguments.fieldID#;
				set @detail = '#replace(arguments.customText,"'","''","ALL")#';
				set @dataID = null;

				EXEC dbo.cf_setFieldData @fieldID=@fieldID, @itemID=@blogEntryID, @itemType='#arguments.itemType#', 
						@valueID=null, @fieldValue=@detail, @dataID=@dataID OUTPUT;
				</cfoutput>
			</cfsavecontent>
		<cfelse>
			<cfset local.insertBlogEntrySQL = "">
		</cfif>

		<cfreturn local.insertBlogEntrySQL>
	</cffunction>

	<cffunction name="updateBlogEntry_cf_nonOption" access="private" output="false" returntype="string">
		<cfargument name="itemType" type="string" required="true">
		<cfargument name="blogEntryID" type="numeric" required="true">
		<cfargument name="fieldID" type="numeric" required="true">
		<cfargument name="customText" type="string" required="true">
		
		<cfset var local = structNew()>

		<cfquery name="local.qryGetDataID" datasource="#application.dsn.membercentral.dsn#">
			select dataID 
			from dbo.cf_fieldData 
			where fieldID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.fieldID#">
			and itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.blogEntryID#">
			and itemType = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.itemType#">
		</cfquery>

		<cfif NOT local.qryGetDataID.recordcount>
			<cfset local.updateBlogEntrySQL = insertBlogEntry_cf_nonOption(itemType=arguments.itemType, fieldID=arguments.fieldID, customText=arguments.customText)>
		<cfelse>
			<cfsavecontent variable="local.updateBlogEntrySQL">
				<cfoutput>
				set @dataID = #local.qryGetDataID.dataID#;
					
				<cfif len(arguments.customText)>
					set @detail = '#replace(arguments.customText,"'","''","ALL")#'
					set @fieldID = #arguments.fieldID#;
					set @valueID = null;

					EXEC dbo.cf_createFieldValue @fieldID=@fieldID, @fieldValue=@detail, @amount=0, @inventory=null,
						@enteredByMemberID=NULL, @skipAuditLog=1, @valueID=@valueID OUTPUT;

					UPDATE dbo.cf_fieldData
					SET valueID = @valueID
					WHERE dataID = @dataID;
				<cfelse>
					DELETE FROM dbo.cf_fieldData WHERE dataID = @dataID;
				</cfif>
				</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfreturn local.updateBlogEntrySQL>
	</cffunction>

	<cffunction name="insertBlogEntry_cf_option" access="private" output="false" returntype="string">
		<cfargument name="itemType" type="string" required="true">
		<cfargument name="fieldID" type="numeric" required="true">
		<cfargument name="valueIDList" type="string" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.insertBlogEntrySQL">
			<cfoutput>
			<cfloop list="#arguments.valueIDList#" index="local.valueitem">
				<cfif val(local.valueitem) gt 0>
					set @fieldID = #arguments.fieldID#;
					set @valueID = #val(local.valueitem)#;
					set @dataID = null;
					
					EXEC dbo.cf_setFieldData @fieldID=@fieldID, @itemID=@blogEntryID, @itemType='#arguments.itemType#', 
							@valueID=@valueID, @fieldValue=NULL, @dataID=@dataID OUTPUT;
				</cfif>
			</cfloop>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.insertBlogEntrySQL>
	</cffunction>
	
	<cffunction name="updateBlogEntry_cf_option" access="private" output="false" returntype="string">
		<cfargument name="itemType" type="string" required="true">
		<cfargument name="blogEntryID" type="numeric" required="true">
		<cfargument name="fieldID" type="numeric" required="true">
		<cfargument name="valueIDList" type="string" required="true">

		<cfset var local = structNew()>

		<!--- existing options --->
		<cfquery name="local.qryExistingOptions" datasource="#application.dsn.membercentral.dsn#">
			select dataID, valueID
			from dbo.cf_fieldData 
			where fieldID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.fieldID#">
			and itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.blogEntryID#">
			and itemType = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.itemType#">
		</cfquery>

		<!--- get any options we need to remove --->
		<cfquery name="local.qryOptionsToRemove" dbtype="query">
			select dataID, valueID
			from [local].qryExistingOptions
			<cfif listLen(arguments.valueIDList)>
				where valueID NOT IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.valueIDList#" list="true">)
			</cfif>
		</cfquery>
		
		<!--- get any options we need to add --->
		<cfif listLen(arguments.valueIDList)>
			<cfquery name="local.qryOptionsToAdd" datasource="#application.dsn.membercentral.dsn#">
				select valueID
				from dbo.cf_fieldValues
				where fieldID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.fieldID#">
				and valueID IN (<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.valueIDList#" list="true">)
				<cfif local.qryExistingOptions.recordcount>
					and valueID NOT IN (<cfqueryparam cfsqltype="cf_sql_integer" value="#valueList(local.qryExistingOptions.valueID)#" list="true">)
				</cfif>
			</cfquery>
			<cfset local.optionsToAdd = valueList(local.qryOptionsToAdd.valueID)>
		<cfelse>
			<cfset local.optionsToAdd = "">
		</cfif>

		<cfsavecontent variable="local.updateBlogEntrySQL">
			<cfoutput>
			<!--- remove options we dont want --->
			<cfloop query="local.qryOptionsToRemove">
				set @dataID = #val(local.qryOptionsToRemove.dataID)#;
				DELETE FROM dbo.cf_fieldData WHERE dataID = @dataID;
			</cfloop>

			<!--- add new options. pass in the new options only --->
			<cfif len(local.optionsToAdd)>
				<cfset local.tempSQL = insertBlogEntry_cf_option(itemType=arguments.itemType, fieldID=arguments.fieldID, valueIDList=local.optionsToAdd)>
				#local.tempSQL#
			</cfif>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.updateBlogEntrySQL>
	</cffunction>

	<cffunction name="deleteBlogEntry" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="false">
		<cfargument name="blogEntryID" type="numeric" required="true">
		<cfargument name="blogID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfif not hasDeleteBlogRights(siteID=arguments.mcproxy_siteID, blogEntryID=arguments.blogEntryID)>
				<cfthrow message="Invalid request">
			</cfif>

			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="bl_deleteEntry">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.blogEntryID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.blogID#">
			</cfstoredproc>
			<cfset local.data.success = true>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getBlogRSSFeedLink" access="public" output="false" returntype="string">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="blogID" type="numeric" required="true">
		<cfargument name="languageID" type="numeric" required="true">
		<cfargument name="ignorePostDate" type="boolean" required="true">

		<cfset var local = structNew()>
		<cfset local.urlStruct.bID = arguments.blogID>
		<cfset local.urlStruct.limitTo = 30>
		<cfset local.urlStruct.startDate = ''>
		<cfset local.urlStruct.endDate = ''>
		<cfset local.urlStruct.lID = arguments.languageID>
		<cfset local.urlStruct.sID = arguments.siteID>
		<cfset local.urlStruct.mID = application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=arguments.orgID)>
		<cfset local.urlStruct.ignorePostDate = arguments.ignorePostDate>
		<cfset local.urlStruct.cB = DayOfYear(now()) & randRange(1,99)>
		<cfset local.urlStruct.feedType = "blog">
		<cfset local.JSONString = serializeJSON(local.urlStruct)>
		<cfset local.blogQueryString = "id=" & encrypt(local.JSONString,"TRiaL_SMiTH", "CFMX_COMPAT", "Hex")>

		<cfreturn "#application.paths.backendPlatform.url#?event=feed.rss&#local.blogQueryString#">
	</cffunction>

	<cffunction name="getPostTypeFieldsInfo" access="public" output="false" returntype="struct">
		<cfargument name="fd" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.data = structNew()>
		<cfset arguments.fd = DeserializeJSON(arguments.fd)>

		<cfif not structKeyExists(arguments.fd,"siteID")>
			<cfset arguments.fd['siteID'] = 0>
		</cfif>
		<cfif not structKeyExists(arguments.fd,"blogID")>
			<cfset arguments.fd['blogID'] = 0>
		</cfif>
		<cfif not structKeyExists(arguments.fd,"blogEntryID")>
			<cfset arguments.fd['blogEntryID'] = 0>
		</cfif>
		<cfif not structKeyExists(arguments.fd,"hideAdminOnly")>
			<cfset arguments.fd['hideAdminOnly'] = 1>
		</cfif>

		<cftry>
			<cfif not hasEditBlogRights(siteID=arguments.fd.siteID, blogEntryID=arguments.fd.blogEntryID, passForAddBlogRights=1, ownerBlogID=(arguments.fd.blogEntryID eq 0 ? arguments.fd.blogID : 0))>
				<cfthrow message="Invalid request">
			</cfif>

			<cfset local.data['arrPostTypes'] = getEntryPostTypeFieldsInfo(siteID=arguments.fd.siteID, blogID=arguments.fd.blogID, blogEntryID=arguments.fd.blogEntryID, hideAdminOnly=arguments.fd.hideAdminOnly)>
			
			<cfset local.data['success'] = true>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfset local.data['success'] = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getEntryPostTypeFieldsInfo" access="private" output="false" returntype="array">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="blogID" type="numeric" required="true">
		<cfargument name="blogEntryID" type="numeric" required="true">
		<cfargument name="hideAdminOnly" type="boolean" required="true">

		<cfset var local = structNew()>
		<cfset local.arrPostTypes = arrayNew(1)>
		<cfset local.objCustomFields = createObject("component","model.admin.common.modules.customFields.customFields")>

		<cfset local.qryAllowedPostTypesForBlog = getAllowedPostTypesForBlog(siteID=arguments.siteID, blogID=arguments.blogID, blogEntryID=arguments.blogEntryID)>

		<cfloop query="local.qryAllowedPostTypesForBlog">
			<cfset local.tmp = structNew()>
			<cfset local.tmp['postTypeID'] = local.qryAllowedPostTypesForBlog.postTypeID>
			<cfset local.tmp['typeName'] = local.qryAllowedPostTypesForBlog.typeName>

			<cfset local.postTypesAdminSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='PostTypesAdmin',siteID=local.qryAllowedPostTypesForBlog.siteID)>
			<cfset local.thisPostTypeFieldsXML = local.objCustomFields.getFieldsXML(siteID=local.qryAllowedPostTypesForBlog.siteID, resourceType='PostTypesAdmin', areaName='PostType', csrid=local.postTypesAdminSiteResourceID, detailID=local.qryAllowedPostTypesForBlog.postTypeID, hideAdminOnly=arguments.hideAdminOnly)>
			<cfset local.postTypeFieldsXML = xmlParse(local.thisPostTypeFieldsXML.returnXML).xmlRoot>
			
			<cfset local.tmp['hasPostTypeFields'] = arrayLen(local.postTypeFieldsXML.xmlChildren) gt 0>
			<cfif local.tmp['hasPostTypeFields']>
				<cfset local.tmp['arrPostTypeFields'] = local.objCustomFields.getResourceFieldsArrayFromFieldsXML(itemID=arguments.blogEntryID, itemType='PostTypeCustom', usageRT='PostTypesAdmin', 
						usageAN='PostType', csrid=local.postTypesAdminSiteResourceID, detailID=local.qryAllowedPostTypesForBlog.postTypeID, fieldsXML=local.postTypeFieldsXML)>
			<cfelse>
				<cfset local.tmp['arrPostTypeFields'] = arrayNew(1)>
			</cfif>
			
			<cfset arrayAppend(local.arrPostTypes, local.tmp)>
		</cfloop>

		<cfreturn local.arrPostTypes>
	</cffunction>

	<cffunction name="prepareResourceFieldsArr" access="public" output="false" returntype="array">
		<cfargument name="itemID" type="numeric" required="true">
		<cfargument name="itemType" type="string" required="true">
		<cfargument name="arrResourceFields" type="array" required="true">
		<cfargument name="objCustomFields" type="any" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfset local.JSONInsanityChars = "^~~~^">
			<cfset local.thisResourceFieldsArr = arrayNew(1)>
			<cfset local.arrResourceFields = duplicate(arguments.arrResourceFields)>
			
			<cfloop array="#local.arrResourceFields#" index="local.thisfield">
				<cfset local.strThisField = duplicate(local.thisfield)>
				<cfset local.strThisFieldXMLAttributes = duplicate(local.strThisField.xmlattributes)>

				<cfset local.tmpFieldsStr = structNew()>
				<cfset local.tmpFieldsStr['itemID'] = arguments.itemID>
				<cfset local.tmpFieldsStr['fieldID'] = local.strThisFieldXMLAttributes.fieldID>
				<cfset local.tmpFieldsStr['attributes'] = duplicate(local.strThisFieldXMLAttributes)>
				<cfset local.tmpFieldsStr['attributes']['fieldText'] = local.JSONInsanityChars & local.tmpFieldsStr['attributes']['fieldText']>
				<cfset local.tmpFieldsStr['attributes']['fieldReference'] = local.JSONInsanityChars & local.tmpFieldsStr['attributes']['fieldReference']>
				<cfset local.tmpFieldsStr['dataTypeCode'] = local.strThisFieldXMLAttributes.dataTypeCode>
				<cfset local.tmpFieldsStr['displayTypeCode'] = local.strThisFieldXMLAttributes.displayTypeCode>
				<cfset local.tmpFieldsStr['fieldTypeCode'] = local.strThisFieldXMLAttributes.fieldTypeCode>
				<cfset local.tmpFieldsStr['supportAmt'] = val(local.strThisFieldXMLAttributes.supportAmt)>
				<cfset local.tmpFieldsStr['supportQty'] = val(local.strThisFieldXMLAttributes.supportQty)>
				<cfset local.tmpFieldsStr['isRequired'] = val(local.strThisFieldXMLAttributes.isRequired)>
				<cfset local.tmpFieldsStr['requiredMsg'] = local.strThisFieldXMLAttributes.requiredMsg>
				<cfset local.tmpFieldsStr['children'] = arrayNew(1)>
				<cfset local.tmpFieldsStr['allOptionEmptyOrDisabled'] = 0>
				<cfset local.tmpFieldsStr['value'] = "">

				<cfif arguments.itemID gt 0>
					<cfif listFind("SELECT,RADIO,CHECKBOX",local.tmpFieldsStr.displayTypeCode)>
						<cfset local.tmpFieldsStr['value'] = arguments.objCustomFields.getFieldOptionsSelected(itemType=arguments.itemType, itemID=arguments.itemID, fieldID=local.tmpFieldsStr.fieldID)>
					<cfelse>
						<cfset local.tmpFieldsStr['value'] = arguments.objCustomFields.getFieldResponseEntered(itemType=arguments.itemType, itemID=arguments.itemID, fieldID=local.tmpFieldsStr.fieldID)>
						<cfif local.strThisFieldXMLAttributes.displayTypeCode is 'TEXTBOX' and local.strThisFieldXMLAttributes.supportQty is 1>
							<cfset local.tmpFieldsStr['value'] = val(local.tmpFieldsStr['value'])>
						</cfif>
					</cfif>
				</cfif>

				<cfif local.strThisFieldXMLAttributes.displayTypeCode eq 'TEXTBOX' and local.strThisFieldXMLAttributes.supportQty is 1>
					<cfset local.maxQtyAllowed = 99999>
					<cfif local.strThisFieldXMLAttributes.fieldInventory gt 0>
						<cfif local.strThisFieldXMLAttributes.fieldInventory lte local.strThisFieldXMLAttributes.fieldinventoryCount>
							<cfset local.maxQtyAllowed = 0>
							<cfset local.tmpFieldsStr['isRequired'] = 0>
						<cfelse>
							<cfset local.maxQtyAllowed = local.strThisFieldXMLAttributes.fieldInventory-local.strThisFieldXMLAttributes.fieldinventoryCount>
							<!--- edit case of non monetary qty-field --->
							<cfif local.strThisFieldXMLAttributes.supportAmt is 0 and val(local.tmpFieldsStr['value']) gt 0>
								<cfset local.maxQtyAllowed = local.maxQtyAllowed + val(local.tmpFieldsStr['value'])>
							</cfif>
						</cfif>
					</cfif>
					<cfset local.tmpFieldsStr['maxQtyAllowed'] = local.maxQtyAllowed>
				</cfif>

				<!--- dont show question at all if select,radio,checkbox and no options defined --->
				<cfif listFind("SELECT,RADIO,CHECKBOX",local.tmpFieldsStr.displayTypeCode)>
					<cfif arrayLen(local.strThisField.xmlchildren) is 0>
						<cfset local.tmpFieldsStr.allOptionEmptyOrDisabled = 1>
					<cfelse>
						<cfloop array="#local.strThisField.xmlchildren#" index="local.thisoption">
							<cfset local.strThisOption = duplicate(local.thisoption)>
							<cfset local.strThisOptionXMLAttributes = duplicate(local.strThisOption.xmlattributes)>

							<cfset local.tmpOptionStr = structNew()>
							<cfset local.tmpOptionStr['attributes'] = duplicate(local.strThisOptionXMLAttributes)>
							<cfset local.tmpOptionStr['attributes']['fieldValue'] = local.JSONInsanityChars & local.tmpOptionStr['attributes']['fieldValue']>
							<cfset local.tmpOptionStr['unavailable'] = 0>
							
							<!--- skip unavailability check for an already selected option --->
							<cfif len(local.tmpFieldsStr['value']) and listFind(local.tmpFieldsStr['value'],local.strThisOptionXMLAttributes.valueID)>
							
							<cfelseif local.strThisOptionXMLAttributes.optionInventory gt 0 and local.strThisOptionXMLAttributes.optionInventory lte local.strThisOptionXMLAttributes.optioninventoryCount>
								<cfset local.tmpOptionStr.unavailable = 1>
							</cfif>
							<cfset arrayAppend(local.tmpFieldsStr.children, local.tmpOptionStr)>
						</cfloop>

						<cfif local.tmpFieldsStr.displayTypeCode eq 'CHECKBOX' and len(local.tmpFieldsStr['value'])>
							<cfset local.tmpFieldsStr['value'] = listToArray(local.tmpFieldsStr['value'])>
						</cfif>
					</cfif>
				<!--- append json insanity vars for other display type values --->
				<cfelseif len(local.tmpFieldsStr['value'])>
					<cfset local.tmpFieldsStr['value'] = local.JSONInsanityChars & local.tmpFieldsStr['value']>
				</cfif>
		
				<cfset arrayAppend(local.thisResourceFieldsArr, local.tmpFieldsStr)>
			</cfloop>
			<cfcatch type="any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump={strThisField = local.strThisField, arrResourceFields = local.arrResourceFields})>
				<cfrethrow>
			</cfcatch>
		</cftry>
		
		<cfreturn local.thisResourceFieldsArr>
	</cffunction>

	<cffunction name="deleteEntryDocument" access="public" returntype="struct">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="blogEntryID" type="numeric" required="true">
		<cfargument name="documentID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfif not hasEditBlogRights(siteID=arguments.siteID, blogEntryID=arguments.blogEntryID)>
				<cfthrow message="Invalid request">
			</cfif>

			<cfset CreateObject("component","model.system.platform.document").deleteDocument(siteID=arguments.siteID, documentID=arguments.documentID)>

			<cfquery name="local.qryDeleteEntryDoc" datasource="#application.dsn.membercentral.dsn#">
				DELETE FROM dbo.bl_entryDocuments
				WHERE blogEntryID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.blogEntryID#">
				AND documentID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.documentID#">
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getBlogEntryDocuments" access="public" returntype="struct">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="blogEntryID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.data = { "success":false, "arrDocuments":arrayNew(1) }>

		<cfif hasEditBlogRights(siteID=arguments.siteID, blogEntryID=arguments.blogEntryID)>
			<cfset local.data.arrDocuments = getBlogEntryDocumentsArray(blogEntryID=arguments.blogEntryID)>
			<cfset local.data.success = true>
		</cfif>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getBlogEntryDocumentsArray" access="public" returntype="array">
		<cfargument name="blogEntryID" type="numeric" required="true">

		<cfset var arrDocuments = arrayNew(1)>

		<cfquery name="arrDocuments" datasource="#application.dsn.memberCentral.dsn#" returntype="array">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT ed.entryDocumentID, ed.documentID, dv.fileName, dl.docTitle
			FROM dbo.bl_entryDocuments as ed
			INNER JOIN dbo.cms_documents as d ON ed.documentID = d.documentID
			INNER JOIN dbo.cms_documentLanguages as dl ON d.documentID = dl.documentID
			INNER JOIN dbo.cms_documentVersions as dv ON dl.documentLanguageID = dv.documentLanguageID
				and dv.isActive = 1
			INNER JOIN dbo.cms_siteResources as sr on sr.siteResourceID = d.siteResourceID
			INNER JOIN dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
				and srs.siteResourceStatusDesc = 'Active'
			WHERE ed.blogEntryID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.blogEntryID#">
			ORDER BY dv.fileName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn arrDocuments>
	</cffunction>

	<cffunction name="getRelatedFeaturedImageConfigIDs" access="public" output="false" returntype="string">
		<cfargument name="blogEntryID" type="numeric" required="true">
		<cfargument name="blogID" type="numeric" required="false" default="0">
		<cfargument name="includeParentConfig" type="boolean" required="false" default="0">

		<cfset var qryFeaturedImageConfig = "">

		<cfquery name="qryFeaturedImageConfig" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			
			SELECT DISTINCT icu.featureImageConfigID
			FROM dbo.bl_blogsAndEntries bae
			INNER JOIN dbo.bl_entry AS be ON be.blogEntryID = bae.blogEntryID AND bae.blogEntryID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.blogEntryID#">
			INNER JOIN dbo.cms_featuredImageConfigUsages icu ON icu.referenceID = bae.blogID AND referenceType = 'blogEntry'
			<cfif arguments.includeParentConfig NEQ 1>
				WHERE bae.blogID <> <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.blogID#">
			</cfif>;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn ValueList(qryFeaturedImageConfig.featureImageConfigID)>
	</cffunction>

	<cffunction name="getConnectedBlogsAndImageConfigInfo" access="public" output="false" returntype="query">
		<cfargument name="blogEntryID" type="numeric" required="true">
		<cfargument name="blogID" type="numeric" required="false" default="0">
		<cfargument name="includeParentConfig" type="boolean" required="false" default="0">

		<cfset var qryConnectedBlogAndImageConfigInfo = "">

		<cfquery name="qryConnectedBlogAndImageConfigInfo" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			
			SELECT DISTINCT icu.featureImageConfigID, ai.applicationInstanceName as blogName
			FROM dbo.bl_blogsAndEntries bae
			INNER JOIN dbo.bl_entry AS be ON be.blogEntryID = bae.blogEntryID AND bae.blogEntryID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.blogEntryID#">
			INNER JOIN dbo.bl_blog b ON b.blogID = bae.blogID
			INNER JOIN dbo.cms_featuredImageConfigUsages icu ON icu.referenceID = bae.blogID AND referenceType = 'blogEntry'
			INNER JOIN dbo.cms_applicationInstances AS ai ON ai.applicationInstanceID = b.applicationInstanceID
			<cfif arguments.includeParentConfig NEQ 1>
				WHERE bae.blogID <> <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.blogID#">
			</cfif>;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryConnectedBlogAndImageConfigInfo>
	</cffunction>

	<cffunction name="getBlogEntryAuthors" access="public" output="false" returntype="struct">
		<cfargument name="fd" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.fd = DeserializeJSON(arguments.fd)>

		<cfset local.currentSiteID = application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).siteID>
		<cfset local.currentOrgID = application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).orgID>

		<cfset local.fd['siteID'] = local.currentSiteID>

		<cfif not structKeyExists(local.fd,"srID")>
			<cfset local.fd['srID'] = 0>
		</cfif>
		<cfif not structKeyExists(local.fd,"beID")>
			<cfset local.fd['beID'] = 0>
		</cfif>

		
		<cfset local.data = structNew()>
		<cfset local.data['success'] = false>
		<cfset local.data['addLogInMemAsAuthor'] = false>

		<cfquery name="local.qryBlogEntryAuthors" datasource="#application.dsn.membercentral.dsn#">
			select mActive.memberID, mActive.lastName + ', ' + mActive.firstName + ' (' + mActive.memberNumber + ')' as memberName
			from dbo.bl_authors as ba 
			inner join dbo.bl_entry as be on be.blogEntryID = ba.blogEntryID
			inner join dbo.ams_members as m on m.memberID = ba.memberID
			inner join dbo.ams_members as mActive on mActive.memberID = m.activeMemberID
			where be.blogEntryID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.fd['beID']#">
			order by mActive.lastName, mActive.firstName, mActive.memberNumber
		</cfquery>

		<cfset local.tmpBlogRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.fd['srID'], memberID=session.cfcuser.memberdata.memberID, siteID=local.fd['siteID'])>

		<cfif local.fd['beID'] is 0 and (application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=local.currentOrgID)) and (local.tmpBlogRights.editAny or local.tmpBlogRights.editOwn)>
			<cfset local.data['addLogInMemAsAuthor'] = true>
		</cfif>

		<cfset local.data['arrAuthors'] = arrayNew(1)>
		
		<cfloop query="local.qryBlogEntryAuthors">
			<cfset local.tmp = structNew()>
			<cfset local.tmp['memberID'] = local.qryBlogEntryAuthors.memberID>
			<cfset local.tmp['memberName'] = local.qryBlogEntryAuthors.memberName>
			<cfif local.tmpBlogRights.manageAllEntryAuthors or (local.qryBlogEntryAuthors.memberID eq session.cfcuser.memberdata.memberID)>
				<cfset local.tmp['canRemove'] = true>
			<cfelse>
				<cfset local.tmp['canRemove'] = false>
			</cfif>
			
			<cfset arrayAppend(local.data['arrAuthors'], local.tmp)>
		</cfloop>

		<cfset local.data['success'] = true>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getSharedBlogInstances" access="public" output="false" returntype="struct">
		<cfargument name="fd" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.fd = DeserializeJSON(arguments.fd)>

		<cfset local.currentSiteID = application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).siteID>
		<cfset local.currentOrgID = application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).orgID>

		<cfset local.fd['siteID'] = local.currentSiteID>

		<cfif not structKeyExists(local.fd,"srID")>
			<cfset local.fd['srID'] = 0>
		</cfif>
		<cfif not structKeyExists(local.fd,"beID")>
			<cfset local.fd['beID'] = 0>
		</cfif>

		<cfset local.data = structNew()>
		<cfset local.data['success'] = false>
		<cfset local.data['addCurrentBlog'] = false>

		<cfquery name="local.qrySharedBlogInstances" datasource="#application.dsn.membercentral.dsn#">
			set nocount on;

			declare @blogEntryID int, @authorMemberIDList varchar(max), @ownBlogID int;
			set @blogEntryID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.fd['beID']#">;

			select @authorMemberIDList = coalesce(@authorMemberIDList + ',', '') + cast(ba.memberID as varchar(10)), @ownBlogID = be.blogID
			from dbo.bl_authors as ba
			inner join dbo.bl_entry as be on be.blogEntryID = ba.blogEntryID
			where be.blogEntryID = @blogEntryID;

			select bae.blogID, ai.applicationInstanceName, @authorMemberIDList as authorMemberIDList, @ownBlogID as ownBlogID
			from dbo.bl_blogsAndEntries as bae
			inner join dbo.bl_entry as be on be.blogEntryID = bae.blogEntryID
				and bae.blogEntryID = @blogEntryID
			inner join dbo.bl_blog as b on b.blogID = bae.blogID
			inner join dbo.cms_applicationInstances as ai on ai.applicationInstanceID = b.applicationInstanceID
			inner join dbo.cms_siteResources as sr on sr.siteResourceID = be.siteResourceID
			inner join dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID
				and srs.siteResourceStatusDesc = 'Active'
			where ai.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.fd['siteID']#">
			order by case when bae.blogID = @ownBlogID then 0 else 1 end, ai.applicationInstanceName;
		</cfquery>

		<cfset local.tmpBlogRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.fd['srID'], memberID=session.cfcuser.memberdata.memberID, siteID=local.fd['siteID'])>

		<cfif local.fd['beID'] is 0>
			<cfset local.data.addCurrentBlog = true>
		</cfif>

		<cfset local.data['arrInstances'] = arrayNew(1)>

		<cfloop query="local.qrySharedBlogInstances">
			<cfset local.tmp = structNew()>
			<cfset local.tmp['blogID'] = local.qrySharedBlogInstances.blogID>
			<cfset local.tmp['name'] = local.qrySharedBlogInstances.applicationInstanceName>
			<cfif local.qrySharedBlogInstances.blogID neq local.qrySharedBlogInstances.ownBlogID and (local.tmpBlogRights.editAny or (local.tmpBlogRights.editOwn and listFind(local.qrySharedBlogInstances.authorMemberIDList,session.cfcuser.memberdata.memberID)))>
				<cfset local.tmp['canRemove'] = true>
			<cfelse>
				<cfset local.tmp['canRemove'] = false>
			</cfif>
			
			<cfset arrayAppend(local.data['arrInstances'], local.tmp)>
		</cfloop>

		<cfset local.data['success'] = true>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getUploadedFeaturedImage" access="public" output="false" returntype="struct">
		<cfargument name="fd" type="string" required="true">
		<cfset var local = structNew()>
		<cfset local.data = structNew()>
		<cfset arguments.fd = DeserializeJSON(arguments.fd)>
		<cfif not structKeyExists(arguments.fd,"blogEntryID")>
			<cfset arguments.fd['blogEntryID'] = 0>
		</cfif>
		<cfif not structKeyExists(arguments.fd,"bID")>
			<cfset arguments.fd['bID'] = 0>
		</cfif>
		<cfif not structKeyExists(arguments.fd,"siteID")>
			<cfset arguments.fd['siteID'] = 0>
		</cfif>
		<cfif not structKeyExists(arguments.fd,"orgCode")>
			<cfset arguments.fd['orgCode'] = 0>
		</cfif>
		<cfif not structKeyExists(arguments.fd,"siteCode")>
			<cfset arguments.fd['siteCode'] = 0>
		</cfif>
		<cfif arguments.fd.blogEntryID gt 0>
			<cfset local.objAdminBlog = CreateObject("component","blog")>
			<cfset local.qryBlogEntry = local.objAdminBlog.getBlogEntryDetails(siteID=arguments.fd.siteID, blogID=arguments.fd.bID, blogEntryID=arguments.fd.blogEntryID)>
			<cfset local.blogEntryFtdImgFooter = ''>
			<cfset local.objFeaturedImages = createObject("component","model.admin.common.modules.featuredImages.featuredImages")>
			<cfset local.arrConfigs = [{ "ftdExt":"#local.qryBlogEntry.siteResourceID#_1", "controllingReferenceID":arguments.fd.bID, "controllingReferenceType":"blogEntry", "referenceID":arguments.fd.blogEntryID, "referenceType":"blogEntry", "resourceType":"BlogAdmin", "resourceTypeTitle":local.qryBlogEntry.blogTitle, "onDeleteImageHandler":"", "onSaveImageHandler":"saveImageResult", "header":"", "footer":local.blogEntryFtdImgFooter, "ftdImgClassList":"mt-2" }]>
			<cfset local.strFeaturedImages = local.objFeaturedImages.manageFeaturedImages(orgCode=arguments.fd.orgCode, siteCode=arguments.fd.siteCode, arrConfigs=local.arrConfigs)>
			<cfset local.data.success = true>
			<cfset local.data.ftdImageHTML = local.strFeaturedImages.html>
		<cfelse>
			<cfset local.data.success = false>
			<cfset local.data.ftdImageHTML = ''>
		</cfif>
		<cfreturn local.data>
	</cffunction>

	<cffunction name="getBlogSourceLibraryByID" access="public" returntype="query">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="sourceLibraryID" type="numeric" required="true">

		<cfset var local = structNew() />
		<cfquery name="local.qryBlogSourceLibraries" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @siteID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">;

			SELECT sl.sourceLibraryID, sl.sourceLibraryName, sl.siteResourceID, isnull(syndicationNetworkID,0) as syndicationNetworkID,
				(
					SELECT count(pt.postTypeID)
					FROM dbo.cms_postTypes as pt
					WHERE isnull(pt.sourceLibraryID,0) = sl.sourceLibraryID
					AND pt.siteID <> @siteID
				) AS crossSitePostTypeUsageCount
			FROM dbo.bl_sourceLibraries sl
			WHERE sl.sourceLibraryID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.sourceLibraryID#">
			AND sl.siteID = @siteID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfreturn local.qryBlogSourceLibraries />
	</cffunction>

	<cffunction name="getSelectableNetworksForSourceLibrary" access="public" output="false" returntype="query">
		<cfargument name="sourceLibraryID" type="numeric" required="true">
		<cfargument name="siteID" type="numeric" required="true">

		<cfquery name="local.qrySelectableNetworks" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @siteID INT, @referencesCount INT;
			SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
			DECLARE @crossSiteReferences TABLE (siteID INT);

			INSERT INTO @crossSiteReferences (siteID)
			SELECT DISTINCT siteID
			FROM dbo.cms_postTypes
			WHERE isnull(sourceLibraryID,0) = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.sourceLibraryID#">
			AND siteID <> @siteID;

			IF EXISTS (SELECT 1 FROM @crossSiteReferences) BEGIN
				INSERT INTO @crossSiteReferences VALUES (@siteID);

				SELECT @referencesCount = COUNT(siteID) FROM @crossSiteReferences;

				SELECT DISTINCT n.networkID, n.networkName
				FROM dbo.networks AS n
				INNER JOIN dbo.networkSites AS ns ON ns.networkID = n.networkID
				INNER JOIN @crossSiteReferences AS s ON s.siteID = ns.siteID
				WHERE n.networkID <> 1
				GROUP BY n.networkID, n.networkName
				HAVING COUNT(ns.siteID) = @referencesCount
				ORDER BY n.networkName;
			END
			ELSE BEGIN
				SELECT DISTINCT n.networkID, n.networkName
				FROM dbo.networks AS n
				INNER JOIN dbo.networkSites AS ns ON ns.networkID = n.networkID
				WHERE ns.siteID = @siteID
				ORDER BY n.networkName
			END

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn local.qrySelectableNetworks>
	</cffunction>

	<cffunction name="getBlogSourcesForPostType" access="public" returntype="struct">
		<cfargument name="postTypeID" type="numeric" required="yes"/>
		<cfset var local = structNew()>
		<cfset local.data = structNew()>
		<cfquery name="local.qryGetBlogSources" datasource="#application.dsn.membercentral.dsn#">
			SELECT s.[description], s.sourceID,
				CASE s.approved 
					WHEN 'P' then 'Pending Approval' 
					WHEN 'Y' then 'Approved for Linking'
					ELSE 'Not Approved for Linking'
				END
				AS [linkingStatus]
			FROM dbo.cms_postTypes pt
			INNER JOIN dbo.bl_sourceLibraries sl ON sl.sourceLibraryID = pt.sourceLibraryID 
			INNER JOIN dbo.bl_sources s ON s.sourceLibraryID = sl.sourceLibraryID
			WHERE pt.postTypeID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.postTypeID#">
			ORDER BY [linkingStatus], s.[description]
		</cfquery>

		<cfset local.data['arrLinkingStatuses'] = arrayNew(1)>
		<cfoutput query="local.qryGetBlogSources" group="linkingStatus">
			<cfset local.strTmp = structNew()>
			<cfset local.strTmp['statusName'] = local.qryGetBlogSources.linkingStatus>
			<cfset local.strTmp['arrSources'] = arrayNew(1)>
			<cfoutput>
				<cfset local.strSource = structNew()>
				<cfset local.strSource['sourceID'] = local.qryGetBlogSources.sourceID>
				<cfset local.strSource['sourceName'] = local.qryGetBlogSources.description>
				<cfset arrayAppend(local.strTmp['arrSources'],local.strSource)>
			</cfoutput>
			<cfset arrayAppend(local.data['arrLinkingStatuses'],local.strTmp)>
		</cfoutput>
		<cfset local.data['success'] = true>
		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="checkSourceLibrary" access="public" returntype="struct">
		<cfargument name="postTypeID" type="numeric" required="yes"/>
		<cfset var local = structNew()>
		<cfset local.data = structNew()>
		<cfquery name="local.qryGetBlogSourceLibrary" datasource="#application.dsn.membercentral.dsn#">
			select pt.sourceLibraryID, sl.siteResourceID
			from dbo.cms_postTypes pt
			left outer join dbo.bl_sourceLibraries sl on sl.sourceLibraryID = pt.sourceLibraryID
			where pt.postTypeID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.postTypeID#">
		</cfquery>
		<cfset local.data['success'] = true>
		<cfset local.data['sourceLibraryID'] = val(local.qryGetBlogSourceLibrary.sourceLibraryID)>
		<cfset local.data['siteResourceID'] = val(local.qryGetBlogSourceLibrary.siteResourceID)>
		<cfreturn local.data>
	</cffunction>

	<cffunction name="getBlogSourceLinks" access="public" returntype="struct">
		<cfargument name="fd" type="string" required="true">
		<cfset var local = structNew()>
		<cfset local.data = structNew()>
		<cfset arguments.fd = DeserializeJSON(arguments.fd)>
		<cfif not structKeyExists(arguments.fd,"beID")>
			<cfset arguments.fd['beID'] = 0>
		</cfif>
		<cfquery name="local.qryGetBlogSourceLinks" datasource="#application.dsn.membercentral.dsn#">
			SELECT sl.linkID, s.description, sl.articleDate, sl.sourceLinkURL, sl.byline, s.sourceID
			FROM bl_sourceLinks sl
			INNER JOIN bl_sources s ON s.sourceID = sl.sourceID 
			WHERE sl.blogEntryID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.fd.beID#">
			ORDER BY sl.orderNum
		</cfquery>
		<cfset local.data['arrSourceLinks'] = arrayNew(1)>
		<cfloop query="local.qryGetBlogSourceLinks">
			<cfset local.strTmp = structNew()>
			<cfset local.strTmp['linkID'] = local.qryGetBlogSourceLinks.linkID>
			<cfset local.strTmp['sourceID'] = local.qryGetBlogSourceLinks.sourceID>
			<cfset local.strTmp['sourceName'] = local.qryGetBlogSourceLinks.description>
			<cfset local.strTmp['sourceArticleDate'] = dateFormat(local.qryGetBlogSourceLinks.articleDate,'m/d/yyyy')>
			<cfset local.strTmp['sourceLink'] = local.qryGetBlogSourceLinks.sourceLinkURL>
			<cfset local.strTmp['byline'] = local.qryGetBlogSourceLinks.byline>
			<cfset arrayAppend(local.data['arrSourceLinks'],local.strTmp)>
		</cfloop>
		<cfset local.data['sourceIDList'] = valueList(local.qryGetBlogSourceLinks.sourceID)>
		<cfset local.data['success'] = true>
		<cfreturn local.data>
	</cffunction>

	<cffunction name="insertSourceLink" access="public" output="false" returntype="struct">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="blogEntryID" type="numeric" required="true">
		<cfargument name="sourceID" type="numeric" required="true">
		<cfargument name="sourceArticleDate" type="string" required="true">
		<cfargument name="sourceLink" type="string" required="true">
		<cfargument name="byline" type="string" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfif not hasEditBlogRights(siteID=arguments.siteID, blogEntryID=arguments.blogEntryID, passForAddBlogRights=1)>
				<cfthrow message="Invalid request">
			</cfif>

			<cfstoredproc procedure="bl_createSourceLink" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.blogEntryID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.sourceID#">
				<cfif len(arguments.sourceArticleDate)>
					<cfprocparam type="In" cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.sourceArticleDate#">
				<cfelse>
					<cfprocparam type="IN" cfsqltype="CF_SQL_TIMESTAMP" null="true">
				</cfif>
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.sourceLink#" null="No">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.byline#">
				<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.data.linkid">
			</cfstoredproc>
			
			<cfset local.data.success = true>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="removeSourceLink" access="public" returntype="struct" output="no">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="linkID" type="numeric" required="true">
		<cfargument name="blogEntryID" type="numeric" required="true">

		<cfset var local = structnew()>

		<cftry>
			<cfif not hasEditBlogRights(siteID=arguments.mcproxy_siteID, blogEntryID=arguments.blogEntryID)>
				<cfthrow message="Invalid request">
			</cfif>

			<cfstoredproc procedure="bl_removeSourceLink" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.linkID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.blogEntryID#">
			</cfstoredproc>

			<cfset local.data.success = true>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="removeAllSourceLinks" access="public" output="false" returntype="void">
		<cfargument name="blogEntryID" type="numeric" required="true">
		<cfset var local = structNew()>

		<cfquery name="local.qryRemoveSourceLink" datasource="#application.dsn.membercentral.dsn#">
			DELETE FROM dbo.bl_sourceLinks
			WHERE blogEntryID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.blogEntryID#">;
		</cfquery>
	</cffunction>

	<cffunction name="doMoveSourceLink" access="public" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="linkID" type="numeric" required="yes">
		<cfargument name="blogEntryID" type="numeric" required="true">
		<cfargument name="dir" type="string" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfif not hasEditBlogRights(siteID=arguments.mcproxy_siteID, blogEntryID=arguments.blogEntryID)>
				<cfthrow message="Invalid request">
			</cfif>

			<cfstoredproc procedure="bl_moveSourceLink" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.linkID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.blogEntryID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.dir#">
			</cfstoredproc>

			<cfset local.data.success = true>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="hasEditBlogRights" access="private" returntype="boolean" output="no">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="blogEntryID" type="numeric" required="true">
		<cfargument name="passForAddBlogRights" type="string" required="false" default="0">
		<cfargument name="ownerBlogID" type="numeric" required="false" default="0">

		<cfset var local = structNew()>
		<cfset local.hasRights = false>

		<cftry>
			<cfquery name="local.qryPermissions" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @hasPermissions bit = 0, @blogEntryID int, @memberID int, @ownerBlogSRID int,
						@entryStatusName varchar(20), @isAuthor bit = 0, @rightsXML xml;
					SET @blogEntryID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.blogEntryID#">;
					SET @memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberData.memberID#">;

					<cfif arguments.blogEntryID gt 0>
						SELECT @ownerBlogSRID = ai.siteResourceID, @entryStatusName = bs.statusName
						FROM dbo.bl_entry as be
						INNER JOIN dbo.bl_blog as b on b.blogID = be.blogID
						INNER JOIN dbo.cms_applicationInstances as ai on ai.applicationInstanceID = b.applicationInstanceID
						INNER JOIN dbo.bl_statuses AS bs ON bs.statusID = be.statusID
						WHERE be.blogEntryID = @blogEntryID;

						IF EXISTS (SELECT 1 FROM dbo.bl_authors WHERE blogEntryID = @blogEntryID AND memberID = @memberID)
							SET @isAuthor = 1;
					<cfelseif arguments.ownerBlogID gt 0>
						SELECT @ownerBlogSRID = ai.siteResourceID
						FROM dbo.bl_blog as b
						INNER JOIN dbo.cms_applicationInstances as ai on ai.applicationInstanceID = b.applicationInstanceID
						WHERE b.blogID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.ownerBlogID#">;

						SET @isAuthor = 1; -- no blogEntryID, so this is blog entry creation
					</cfif>

					SELECT @rightsXML = dbo.fn_cache_perms_getResourceRightsXML(@ownerBlogSRID,@memberID,<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">);

					<cfif arguments.passForAddBlogRights>
						IF @rightsXML.exist('/rights/right[@functionName="AddBlog"][@allowed="1"]') = 1 BEGIN
							SET @hasPermissions = 1;
							GOTO on_done;
						END
					</cfif>

					IF @rightsXML.exist('/rights/right[@functionName="editAny"][@allowed="1"]') = 1 OR
						(@rightsXML.exist('/rights/right[@functionName="editOwn"][@allowed="1"]') = 1 AND @isAuthor = 1) OR
						(@rightsXML.exist('/rights/right[@functionName="AddBlog"][@allowed="1"]') = 1 AND @isAuthor = 1 AND @entryStatusName IN ('Draft','Awaiting Approval')) BEGIN
						SET @hasPermissions = 1;
						GOTO on_done;
					END

					on_done:
					SELECT @hasPermissions AS hasPermissions;
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.hasRights = val(local.qryPermissions.hasPermissions) ? true : false>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfset local.hasRights = false>
		</cfcatch>
		</cftry>

		<cfreturn local.hasRights>
	</cffunction>

	<cffunction name="hasDeleteBlogRights" access="private" returntype="boolean" output="no">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="blogEntryID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.hasRights = false>

		<cftry>
			<cfquery name="local.qryPermissions" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @hasPermissions bit = 0, @blogEntryID int, @memberID int, @ownerBlogSRID int,
						@isMemberInAuthorList bit = 0, @rightsXML xml;
					SET @blogEntryID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.blogEntryID#">;
					SET @memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberData.memberID#">;

					SELECT @ownerBlogSRID = ai.siteResourceID
					FROM dbo.bl_entry as be
					INNER JOIN dbo.bl_blog as b on b.blogID = be.blogID
					INNER JOIN dbo.cms_applicationInstances as ai on ai.applicationInstanceID = b.applicationInstanceID
					WHERE be.blogEntryID = @blogEntryID;

					IF EXISTS (SELECT 1 FROM dbo.bl_authors WHERE blogEntryID = @blogEntryID AND memberID = @memberID)
						SET @isMemberInAuthorList = 1;

					SELECT @rightsXML = dbo.fn_cache_perms_getResourceRightsXML(@ownerBlogSRID,@memberID,<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">);

					IF @rightsXML.exist('/rights/right[@functionName="deleteAny"][@allowed="1"]') = 1 OR
						(@rightsXML.exist('/rights/right[@functionName="deleteOwn"][@allowed="1"]') = 1 AND @isMemberInAuthorList = 1) BEGIN
						SET @hasPermissions = 1;
					END

					SELECT @hasPermissions AS hasPermissions;
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.hasRights = val(local.qryPermissions.hasPermissions) ? true : false>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfset local.hasRights = false>
		</cfcatch>
		</cftry>

		<cfreturn local.hasRights>
	</cffunction>

	<cffunction name="addRedirect" access="public" output="false" returntype="void" hint="add Quick Link for blog entry">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="blogEntryID" type="numeric" required="true">
		<cfargument name="redirectName" type="string" required="true">
		<cfargument name="redirectURL" type="string" required="true">
		<cfargument name="oldRedirectID" type="numeric" required="true">
		
		<cfscript>
			var local = structNew();
			local.objAlias = CreateObject("component","model.admin.alias.alias");
			local.redirectID = local.objAlias.insertAlias(siteID=arguments.siteID, redirectName=arguments.redirectName, redirectURL=arguments.redirectURL);
			updateBlogEntryRedirect(blogEntryID=arguments.blogEntryID, redirectID=local.redirectID);
			if(arguments.oldRedirectID){
				local.objAlias.deleteAlias(redirectIDList=arguments.oldRedirectID,siteID=arguments.siteID);
			}
		</cfscript>
	</cffunction>
	
	<cffunction name="updateBlogEntryRedirect" access="public" output="false" returntype="void" hint="update blog entry">
		<cfargument name="blogEntryID" type="numeric" required="true">
		<cfargument name="redirectID" type="numeric" required="false">

		<cfset var local = structNew()>
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.update">
			UPDATE dbo.bl_entry 
			SET redirectID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.redirectID#">
			WHERE blogEntryID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.blogEntryID#">
		</cfquery>
	</cffunction>

	<cffunction name="addToImportBlogEntryJSONQueue" access="public" output="false" returntype="struct">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="blogID" type="numeric" required="true">
		<cfargument name="itemGroupUID" type="string" required="true">
		<cfargument name="entryJSONContent" type="string" required="true">
		
		<cfset var local = structNew()>

		<cfquery name="local.qryImportJSONInsert" datasource="#application.dsn.platformQueue.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				DECLARE @submittedMemberID int, @statusReady INT, @queueTypeID INT, @nowDate DATETIME;
				SET @submittedMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">;
				SELECT @queueTypeID = queueTypeID FROM platformQueue.dbo.tblQueueTypes WHERE queueType = 'importBlogEntryJSON';
				SELECT @statusReady = queueStatusID FROM platformQueue.dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID AND queueStatus = 'ReadyToProcess';
				SET @nowDate = getdate();

				INSERT INTO dbo.queue_importBlogEntryJSON (itemGroupUID, siteID, blogID, submittedMemberID, blogEntryJSON, statusID, dateAdded, dateUpdated)
				VALUES (
					<cfqueryparam cfsqltype="CF_SQL_IDSTAMP" value="#arguments.itemGroupUID#">,
					<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">,
					<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.blogID#">,
					@submittedMemberID,
					<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.entryJSONContent#">,
					@statusReady,
					@nowDate,
					@nowDate
				);

				SELECT SCOPE_IDENTITY() AS itemID;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfset local.result["success"] = true>
		<cfset local.result["itemid"] = local.qryImportJSONInsert.itemID>

		<cfreturn local.result>
	</cffunction>

	<cffunction name="getBlogEntryDocumentIDs" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="blogEntryID" type="numeric" required="true">

		<cfset var qryDocuments = "">

		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="qryDocuments">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT ed.documentID
			FROM dbo.bl_entryDocuments as ed
			INNER JOIN dbo.cms_documents as d ON ed.documentID = d.documentID
			INNER JOIN dbo.cms_siteResources as sr on sr.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
				AND sr.siteResourceID = d.siteResourceID
			INNER JOIN dbo.cms_siteResourceStatuses as srs on srs.siteResourceStatusID = sr.siteResourceStatusID 
				AND srs.siteResourceStatusDesc = 'Active'
			WHERE ed.blogEntryID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.blogEntryID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryDocuments>
	</cffunction>

	<cffunction name="deleteAllBlogEntryDocs" access="public" output="false" returntype="struct">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="blogEntryID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.objDocument = CreateObject("component","model.system.platform.document")>

		<cftry>
			<cfif not hasEditBlogRights(siteID=arguments.siteID, blogEntryID=arguments.blogEntryID)>
				<cfthrow message="Invalid request">
			</cfif>

			<cfset local.qryDocuments = getBlogEntryDocumentIDs(siteID=arguments.siteID, blogEntryID=arguments.blogEntryID)>
		
			<cfif local.qryDocuments.recordCount>
				<cfloop query="local.qryDocuments">
					<cfset local.objDocument.deleteDocument(siteID=arguments.siteID, documentID=local.qryDocuments.documentID)>
				</cfloop>

				<cfquery name="local.qryDeleteBlogEntryDocs" datasource="#application.dsn.membercentral.dsn#">
					DELETE FROM dbo.bl_entryDocuments
					WHERE blogEntryID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.blogEntryID#">
				</cfquery>
			</cfif>

			<cfset local.data.success = true>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>
		
		<cfreturn local.data>
	</cffunction>

	<cffunction name="getBlogsForFilters" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">

		<cfset var qryBlogs = ''>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryBlogs">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			IF OBJECT_ID('tempdb..##tmpBlogs') IS NOT NULL
			DROP TABLE ##tmpBlogs;
			CREATE TABLE ##tmpBlogs (blogID int PRIMARY KEY, blogName varchar(250),  row int);
			DECLARE @RTID int, @siteID int;
			SELECT @RTID = dbo.fn_getResourceTypeID('Community');
			SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;;

			INSERT INTO ##tmpBlogs (blogID, blogName,   row)
			SELECT blogID, blogName,  ROW_NUMBER() OVER (ORDER BY blogname asc)
			FROM(
			SELECT b.blogID,
			blogName = ai.applicationInstanceName +
			CASE WHEN communityInstances.applicationInstanceName IS NOT NULL THEN ' (' + communityInstances.applicationInstanceName + ')' ELSE '' END
			FROM dbo.bl_blog AS b
			INNER JOIN dbo.cms_applicationInstances AS ai ON ai.applicationInstanceID = b.applicationInstanceID
			AND ai.siteID = @siteID
			INNER JOIN dbo.cms_applicationTypes AS appType ON appType.applicationTypeID = ai.applicationTypeID
			INNER JOIN dbo.cms_siteResources AS sr ON sr.siteID = @siteID AND ai.siteResourceID = sr.siteResourceID
			INNER JOIN dbo.cms_siteResourceStatuses AS srs ON sr.siteResourceStatusID = srs.siteResourceStatusID AND srs.siteResourceStatusDesc = 'Active'
			INNER JOIN dbo.cms_siteResources AS parentResource ON parentResource.siteID = @siteID AND parentResource.siteResourceID = sr.parentSiteResourceID
			LEFT OUTER JOIN dbo.cms_siteResources AS grandparentResource
			INNER JOIN dbo.cms_applicationInstances AS CommunityInstances ON communityInstances.siteResourceID = grandParentResource.siteResourceID
			ON grandparentResource.siteID = @siteID
			AND grandparentResource.siteResourceID = parentResource.parentSiteResourceID
			AND grandparentResource.resourceTypeID = @RTID
			) AS tmp
			;
			SELECT blogID, blogName
			FROM ##tmpBlogs

			ORDER BY row;
			IF OBJECT_ID('tempdb..##tmpBlogs') IS NOT NULL
			DROP TABLE ##tmpBlogs;
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryBlogs>
	</cffunction>

</cfcomponent>