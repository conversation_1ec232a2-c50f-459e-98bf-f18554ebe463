ALTER PROC dbo.cms_importPagesFromQueue
@itemID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @statusReady int, @statusProcessing int, @statusNotify int, @itemStatusID int,
		@orgID int, @siteID int, @pageName varchar(50), @sectionID int, @pageTitle varchar(200), @keywords varchar(400),
		@description varchar(400), @published bit, @quickLink varchar(1000), @inheritPlacements bit, @ovModeID int,
		@ovTemplateID int, @applyNoIndex bit, @applyNoFollow bit, @applyNoArchive bit, @runByMemberID int, @languageID int,
		@resourceTypeID int, @mainZoneID int, @pageID int, @pageSiteResourceID int, @contentResourceTypeID int, 
		@useSiteResourceStatusID int, @pageContentID int, @contentSiteResourceID int, @functionIDList varchar(max),
		@pageDirectives varchar(30), @redirectURL varchar(500), @environmentName varchar(50), @mainHostname varchar(1000), 
		@scheme varchar(10), @environmentID int, @redirectID int, @publicgroupID int;

	select @queueTypeID = queueTypeID from platformQueue.dbo.tblQueueTypes where queueType = 'importPages';
	select @statusReady = queueStatusID from platformQueue.dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'ReadyToProcess';
	select @statusProcessing = queueStatusID from platformQueue.dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'Processing';
	select @statusNotify = queueStatusID from platformQueue.dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'ReadyToNotify';

	SELECT @orgID = orgID, @siteID = siteID, @pageName = pageName, @sectionID = sectionID, @pageTitle = pageTitle,
		@keywords = keywords, @description = description, @published = published, @quickLink = quickLink,
		@inheritPlacements = inheritPlacements, @ovModeID = ovModeID, @ovTemplateID = ovTemplateID,
		@applyNoIndex = applyNoIndex, @applyNoFollow = applyNoFollow, @applyNoArchive = applyNoArchive,
		@runByMemberID = runByMemberID, @itemStatusID = statusID
	FROM platformQueue.dbo.queue_importPages
	WHERE itemID = @itemID;
	
	-- if itemID is not readyToProcess, kick out now
	IF @itemStatusID <> @statusReady
		RAISERROR('Item not in readyToProcess state',16,1);

	SELECT @languageID = dbo.fn_getLanguageID('en');
	SELECT @resourceTypeID = dbo.fn_getResourceTypeID('UserCreatedPage');
	SELECT @mainZoneID = dbo.fn_getZoneID('Main');
	SELECT @contentResourceTypeID = dbo.fn_getResourceTypeID('UserCreatedContent');
	SELECT @useSiteResourceStatusID = CASE WHEN @published = 1 THEN 1 ELSE 2 END;

	IF @applyNoIndex = 1
		SET @pageDirectives = COALESCE(@pageDirectives + ',','') + 'noindex';
	IF @applyNoFollow = 1
		SET @pageDirectives = COALESCE(@pageDirectives + ',','') + 'nofollow';
	IF @applyNoArchive = 1
		SET @pageDirectives = COALESCE(@pageDirectives + ',','') + 'noarchive';

	SELECT @publicgroupID = groupID
	FROM dbo.ams_groups
	WHERE orgID = @orgID
	AND groupCode = 'Public'
	AND isSystemGroup = 1;

	IF @quickLink IS NOT NULL BEGIN
		SELECT @environmentName = tier FROM dbo.fn_getServerSettings();
		SELECT @environmentID = environmentID FROM dbo.platform_environments WHERE environmentName = @environmentName;

		SELECT @mainHostname = sh.hostname, @scheme = CASE WHEN sh.hasssl = 1 THEN 'https' ELSE 'http' END
		FROM dbo.siteHostnames AS sh 
		INNER JOIN dbo.siteEnvironments AS se ON se.siteID = sh.siteID
			AND se.environmentID = @environmentID
			AND se.mainHostnameID = sh.hostNameID
			AND sh.siteID = @siteID;

		SET @redirectURL = @scheme + '://' + @mainHostname + '/' + @quickLink;
	END

	BEGIN TRAN;
		-- update status
		UPDATE platformQueue.dbo.queue_importPages
		SET statusID = @statusProcessing
		WHERE itemID = @itemID;

		-- create page
		EXEC dbo.cms_createPage @siteID=@siteID, @languageID=@languageID, @resourceTypeID=@resourceTypeID, @siteResourceStatusID=2,
			@pgParentResourceID= NULL, @isVisible=1, @sectionID=@sectionID, @ovTemplateID=@ovTemplateID, @ovTemplateIDMobile=NULL, @ovModeID=@ovModeID,
			@pageName=@pageName, @pageTitle=@pageTitle, @pageDesc=@description, @keywords=@keywords, @inheritPlacements=@inheritPlacements,
			@allowReturnAfterLogin=1, @checkReservedNames=1, @pageID=@pageID OUTPUT;

		IF @pageID IS NOT NULL BEGIN
			SELECT @pageSiteResourceID = siteResourceID
			FROM dbo.cms_pages 
			WHERE pageID = @pageID;

			EXEC dbo.cms_createContent @siteID=@siteID, @pageID=@pageID, @zoneID=@mainZoneID, @resourceTypeID=@contentResourceTypeID,
				@siteResourceStatusID=1, @isHTML=1, @languageID=@languageID, @isActive=1,
				@contentTitle=@pageTitle, @contentDesc=@description, @rawContent='', @memberID=@runByMemberID,
				@contentID=@pageContentID OUTPUT, @contentSiteResourceID=@contentSiteResourceID OUTPUT;

			IF @pageDirectives IS NOT NULL
				UPDATE dbo.cms_pages
				SET pageDirectives = @pageDirectives
				WHERE pageID = @pageID;

			-- published
			IF @useSiteResourceStatusID <> 2
				UPDATE dbo.cms_siteResources
				SET siteResourceStatusID = @useSiteResourceStatusID
				WHERE siteID = @siteID
				AND siteResourceID = @pageSiteResourceID;

			-- assign public rights
			SELECT @functionIDList = COALESCE(@functionIDList + ',','') + cast(f.functionID AS varchar(10))
			FROM dbo.cms_siteResourceFunctions AS f
			INNER JOIN dbo.cms_siteResourceTypeFunctions AS srtf ON f.functionID = srtf.functionID
			INNER JOIN dbo.cms_siteResources AS sr ON sr.siteID = @siteID AND sr.resourceTypeID = srtf.resourceTypeID
			WHERE sr.siteResourceID = @contentSiteResourceID
			AND srtf.isHidden = 0
			AND f.functionName = 'View';

			EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@contentSiteResourceID, @include=1, 
				@functionIDList=@functionIDList, @roleID=null, @groupID=@publicgroupID, @inheritedRightsResourceID=null, 
				@inheritedRightsFunctionID=null;

			-- quick links
			IF @redirectURL IS NOT NULL BEGIN
				INSERT INTO dbo.siteRedirects(siteID, redirectName, redirectURL)
				VALUES (@siteID, @quickLink, @redirectURL);
					SET @redirectID = SCOPE_IDENTITY();

				UPDATE dbo.cms_pages 
				SET redirectID = @redirectID
				WHERE pageID = @pageID;
			END
		END
		
		-- update status to ReadyToNotify
		UPDATE platformQueue.dbo.queue_importPages
		SET statusID = @statusNotify
		WHERE itemID = @itemID;
	COMMIT TRAN;
	
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
