ALTER PROC dbo.queue_tsSubscriberDepos_check

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @issueCount int, @timeToUse datetime, @queueTypeID int, @queueStatusID int, @nextQueueStatusID int, 
		@errorSubject VARCHAR(400), @errorTitle VARCHAR(400), @itemAsStr varchar(60), @xmlMessage xml;
	select @queueTypeID = queueTypeID from dbo.tblQueueTypes where queueType = 'tsSubscriberDepos';

	-- tsSubscriberDepos / processingItem autoreset to readyToProcess
	set @issueCount = 0;
	set @timeToUse = DATEADD(minute, -10, GETDATE());
	select @queueStatusID = queuestatusID from dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'processingItem';
	select @nextQueueStatusID = queuestatusID from dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'readyToProcess';
	SELECT @issueCount = count(itemID) FROM dbo.queue_tsSubscriberDepos WHERE statusID = @queueStatusID and dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		update dbo.queue_tsSubscriberDepos
		set statusID = @nextQueueStatusID, 
			dateUpdated = getdate()
			OUTPUT inserted.itemID 
			INTO #tmpCheckQueueMsgs (itemAsStr)
		WHERE statusID = @queueStatusID 
		and dateUpdated < @timeToUse;

		SELECT @itemAsStr = MIN(itemAsStr) FROM #tmpCheckQueueMsgs;
		WHILE @itemAsStr IS NOT NULL BEGIN
			SET @xmlMessage = '<mc i="' + @itemAsStr + '" />';
			EXEC dbo.queue_tsSubscriberDepos_sendMessage @xmlMessage=@xmlMessage;
			SELECT @itemAsStr = MIN(itemAsStr) FROM #tmpCheckQueueMsgs WHERE itemAsStr > @itemAsStr;
		END

		TRUNCATE TABLE #tmpCheckQueueMsgs;

		SET @errorTitle = 'tsSubscriberDepos Queue Issue';
		SET @errorSubject = 'tsSubscriberDepos queue moved items from processingItem to readyToProcess';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	-- tsSubscriberDepos catchall
	set @issueCount = 0;
	set @timeToUse = DATEADD(hour, -4, GETDATE())
	SELECT @issueCount = count(itemID) FROM dbo.queue_tsSubscriberDepos WHERE dateUpdated < @timeToUse;
	IF @issueCount > 0 BEGIN
		SET @errorTitle = 'tsSubscriberDepos Queue Issue';
		SET @errorSubject = 'tsSubscriberDepos queue has items last updated more than 4 hours ago';
		EXEC dbo.up_checkQueues_email @errorSubject=@errorSubject, @errorTitle=@errorTitle, @messageContent=@errorSubject;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
