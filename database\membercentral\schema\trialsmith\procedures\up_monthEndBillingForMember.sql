ALTER PROC dbo.up_monthEndBillingForMember
@depomemberdataID int,
@eomPeriod date

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @periodID int, @SOMClosedBillingPeriod date, @isOrg bit = 0, @sendEmail bit = 0;

	select @periodID = periodID, @SOMClosedBillingPeriod = DATEFROMPARTS(year(EOMPeriod),MONTH(EOMPeriod),1)
	from dbo.billingPeriods
	where EOMPeriod = @eomPeriod;

	-- DATES FOR REST OF PROCESS
	declare @BPStartD date, @BPStartDT datetime, @BPStartSDT smalldatetime, @BPEndD date, @BPEndDT datetime, @BPEndSDT smalldatetime;
	SET @BPStartD = @SOMClosedBillingPeriod;
	SET @BPStartDT = DATETIMEFROMPARTS(YEAR(@BPStartD),MONTH(@BPStartD),DAY(@BPStartD),0,0,0,0);
	SET @BPStartSDT = SMALLDATETIMEFROMPARTS(YEAR(@BPStartD),MONTH(@BPStartD),DAY(@BPStartD),0,0);
	SET @BPEndD = DATEFROMPARTS(YEAR(@BPStartD),MONTH(@BPStartD),DAY(EOMONTH(@BPStartD)));
	SET @BPEndDT = DATETIMEFROMPARTS(YEAR(@BPEndD),MONTH(@BPEndD),DAY(@BPEndD),23,59,59,997);
	SET @BPEndSDT = SMALLDATETIMEFROMPARTS(YEAR(@BPEndD),MONTH(@BPEndD),DAY(@BPEndD),23,59);

	-- is this an org record?
	IF EXISTS (select orgCode from dbo.memberCentralBilling where depoMemberDataID = @depomemberdataID)
		SET @isOrg = 1;

	IF @isOrg = 1 BEGIN
		IF OBJECT_ID('tempdb..#tmpSiteCodes') IS NOT NULL 
			DROP TABLE #tmpSiteCodes;
		CREATE TABLE #tmpSiteCodes (sitecode varchar(10));

		INSERT INTO #tmpSiteCodes (sitecode)
		SELECT orgCode
		from dbo.memberCentralBilling 
		where depoMemberDataID = @depomemberdataID;

		BEGIN TRAN;
			-- CHECK AND QUEUE THE ORG SW STATEMENTS FOR BILLING PERIOD
			DECLARE @SWrunID int;
			select @SWrunID = runID from platformStatsMC.dbo.sw_MonthlyBillingRun where activityReportMonth = @BPStartD;

			IF @SWrunID IS NOT NULL BEGIN
				DECLARE @SWqueueTypeID int, @SWstatusReady int;
				select @SWqueueTypeID = queueTypeID from platformQueue.dbo.tblQueueTypes where queueType = 'monthBillSW';
				select @SWstatusReady = queuestatusID from platformQueue.dbo.tblQueueStatuses where queueTypeID = @SWqueueTypeID and queueStatus = 'readyToProcess';

				-- join to sites because the orgemail processing requires it to be there
				insert into platformQueue.dbo.queue_monthBillSW (runID, participantID, siteCode, billingPeriodID, statusID)
				select distinct rd.runID, rd.participantID, rd.siteCode, @periodID, @SWstatusReady
				from platformStatsMC.dbo.sw_MonthlyBillingRunDetail as rd
				inner join membercentral.dbo.sites as s on s.siteCode = rd.siteCode
				inner join #tmpSiteCodes as tmp on tmp.siteCode = s.siteCode
				where rd.runID = @SWrunID;

				IF @@ROWCOUNT > 0 BEGIN
					SET @sendEmail = 1;
					EXEC membercentral.dbo.sched_resumeTask @name='Process SeminarWeb Statements Queue', @engine='MCLuceeLinux';
				END
			END


			-- CHECK AND QUEUE THE ORG TS ROYALTY STATEMENTS FOR BILLING PERIOD
			DECLARE @TSRrunID int;
			select @TSRrunID = runID from platformStatsMC.dbo.ts_MonthlyRoyaltyRun where reportEndDate = @BPEndD;

			IF @TSRrunID IS NOT NULL BEGIN
				DECLARE @TSRqueueTypeID int, @TSRstatusReady int;
				select @TSRqueueTypeID = queueTypeID from platformQueue.dbo.tblQueueTypes where queueType = 'monthBillTSRoyalty';
				select @TSRstatusReady = queuestatusID from platformQueue.dbo.tblQueueStatuses where queueTypeID = @TSRqueueTypeID and queueStatus = 'readyToProcess';

				-- join to sites because the orgemail processing requires it to be there
				insert into platformQueue.dbo.queue_monthBillTSRoyalty (billingPeriodID, orgCode, DepoSalePCT, DepoContribAMT, SubSalePCT, 
					eclipsMonthAMT, DepoSales, DepoSpecialSales, DepoContributions, SubscriptionSales, DepoSalesRoyalty, DepoSpecialSalesRoyalty, 
					DepoContributionsRoyalty, SubscriptionSalesRoyalty, eclipsRoyalty, TotalRoyalty, statusID)
				SELECT @periodID, rd.orgcode, rd.DepoSalePCT, rd.DepoContribAMT, rd.SubSalePCT, rd.eclipsMonthAMT, rd.DepoSales, 
					rd.DepoSpecialSales, rd.DepoContributions, rd.SubscriptionSales, rd.DepoSalesRoyalty, rd.DepoSpecialSalesRoyalty, 
					rd.DepoContributionsRoyalty, rd.SubscriptionSalesRoyalty, rd.eclipsRoyalty, rd.TotalRoyalty, @TSRstatusReady
				FROM platformStatsMC.dbo.ts_MonthlyRoyaltyDetail as rd
				INNER JOIN membercentral.dbo.sites as s on s.siteCode = rd.orgCode
				INNER JOIN #tmpSiteCodes as tmpSC on tmpSC.siteCode = s.siteCode
				where rd.runID = @TSRrunID;

				IF @@ROWCOUNT > 0 BEGIN
					SET @sendEmail = 1;
					EXEC membercentral.dbo.sched_resumeTask @name='Process TrialSmith Royalty Statements Queue', @engine='MCLuceeLinux';
				END
			END


			-- QUEUE THE ORG TS STATEMENTS FOR BILLING PERIOD
			DECLARE @TSAqueueTypeID int, @TSAstatusReady int;
			select @TSAqueueTypeID = queueTypeID from platformQueue.dbo.tblQueueTypes where queueType = 'monthBillTSA';
			select @TSAstatusReady = queuestatusID from platformQueue.dbo.tblQueueStatuses where queueTypeID = @TSAqueueTypeID and queueStatus = 'readyToProcess';

			IF OBJECT_ID('tempdb..#tmpOrgTSA') IS NOT NULL 
				DROP TABLE #tmpOrgTSA;
			CREATE TABLE #tmpOrgTSA (depomemberdataID int PRIMARY KEY, EOPBalance decimal(18,2), numInPeriod int);

			-- join to sites because the orgemail processing requires it to be there
			INSERT INTO #tmpOrgTSA (depomemberdataID, EOPBalance, numInPeriod)
			select mcb.depoMemberDataID, SUM(t.amountBilled+t.salesTaxAmount) as EOPBalance,
				SUM(case when t.DatePurchased between @BPStartSDT and @BPEndSDT then 1 else 0 end) as numInPeriod
			from dbo.memberCentralBilling as mcb
			inner join dbo.depoTransactions as t on t.depoMemberDataID = mcb.depoMemberDataID
				and t.DatePurchased <= @BPEndSDT
			inner join membercentral.dbo.sites as s on s.siteCode = mcb.orgCode
			where mcb.depoMemberDataID = @depomemberdataID
			group by mcb.depoMemberDataID;

			-- we only want statements when either have activity in billing month OR have a non-zero balance at end of month
			DELETE FROM #tmpOrgTSA
			WHERE EOPBalance = 0
			AND numInPeriod = 0;

			insert into platformQueue.dbo.queue_monthBillTSA (depomemberDataID, TransStartDate, TransEndDate, StatementDate, billingPeriodID, isOrg, statusID)
			select depomemberdataID, @BPStartSDT, @BPEndSDT, @BPEndSDT, @periodID, 1, @TSAstatusReady
			from #tmpOrgTSA;

			IF @@ROWCOUNT > 0 BEGIN
				SET @sendEmail = 1;
				EXEC membercentral.dbo.sched_resumeTask @name='Process TrialSmith Statements Queue', @engine='TSAdminLinux';
			END

			IF OBJECT_ID('tempdb..#tmpOrgTSA') IS NOT NULL 
				DROP TABLE #tmpOrgTSA;


			-- QUEUE THE EMAILING OF ORG STATEMENTS
			IF @sendEmail = 1 BEGIN
				DECLARE @EmailOrgQueueTypeID int, @EmailOrgStatusWaiting int;
				select @EmailOrgQueueTypeID = queueTypeID from platformQueue.dbo.tblQueueTypes where queueType = 'monthBillEmailOrg';
				select @EmailOrgStatusWaiting = queuestatusID from platformQueue.dbo.tblQueueStatuses where queueTypeID = @EmailOrgQueueTypeID and queueStatus = 'waitingToProcess';

				INSERT INTO platformQueue.dbo.queue_monthBillEmailOrg (billingPeriodID, siteCode, statusID)
				select @periodID, siteCode, @EmailOrgStatusWaiting
				from #tmpSiteCodes;

				IF @@ROWCOUNT > 0
					EXEC membercentral.dbo.sched_resumeTask @name='Process Monthly Billing Email Queue', @engine='MCLuceeLinux';
			END
		COMMIT TRAN;

	END ELSE BEGIN

		BEGIN TRAN;

			-- QUEUE THE TS STATEMENTS FOR BILLING PERIOD for INDIVIDUALS
			-- QUEUE THE EMAILING OF INDIV STATEMENTS
			DECLARE @TSAINDqueueTypeID int, @TSAINDstatusReady int, @EmailIndivQueueTypeID int, @EmailIndivStatusWaiting int;
			select @TSAINDqueueTypeID = queueTypeID from platformQueue.dbo.tblQueueTypes where queueType = 'monthBillTSA';
			select @TSAINDstatusReady = queuestatusID from platformQueue.dbo.tblQueueStatuses where queueTypeID = @TSAINDqueueTypeID and queueStatus = 'readyToProcess';
			select @EmailIndivQueueTypeID = queueTypeID from platformQueue.dbo.tblQueueTypes where queueType = 'monthBillEmailIndiv';
			select @EmailIndivStatusWaiting = queuestatusID from platformQueue.dbo.tblQueueStatuses where queueTypeID = @EmailIndivQueueTypeID and queueStatus = 'waitingToProcess';

			IF OBJECT_ID('tempdb..#tmpIndivTSA') IS NOT NULL 
				DROP TABLE #tmpIndivTSA;
			CREATE TABLE #tmpIndivTSA (depomemberdataID int PRIMARY KEY, paymentType char(1), EOPBalance decimal(18,2), 
				nonSWActivity int, email1 varchar(400), email2 varchar(400), firmPlanMasterDepoID int);

			-- nonSWActivity = if acctcode is not 7000,7001,7002,7003,7004,7005,7006,7007,7008,7009,7010,7011,7012 (SW codes) and not 1000 ($0 basic plan fees that all sw accts have)
			INSERT INTO #tmpIndivTSA (depomemberdataID, paymentType, EOPBalance, nonSWActivity, email1, email2, firmPlanMasterDepoID)
			select d.depomemberdataID, d.paymenttype, ISNULL(SUM(t.amountBilled+t.salesTaxAmount),0) as EOPBalance, 
				SUM(case when t.DatePurchased between @BPStartSDT and @BPEndSDT and t.accountCode not in ('1000','7000','7001','7002','7003','7004','7005','7006','7007','7008','7009','7010','7011','7012') then 1 else 0 end) as nonSWActivity,
				case when d.Email is not null and d.Email <> '' and membercentral.dbo.fn_RegExReplace(d.email,'^[a-zA-Z_0-9-''\&\+~]+(\.[a-zA-Z_0-9-''\&\+~]+)*@([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,63}$','') = '' then d.email else null end as email1,
				nullIF(d.BillingContactEmail,'') as email2,
				(
					select fplMaster.depoMemberDataID
					from dbo.tlaFirmPlanLink as fpl
					INNER JOIN dbo.tlaFirmPlanLink AS fplMaster ON fplMaster.firmPlanID = fpl.firmPlanID
						and fplMaster.isMaster = 1
					where fpl.depoMemberDataID = d.depomemberdataID
					and fpl.isMaster = 0
				) as firmPlanMasterDepoID
			from dbo.depomemberdata as d
			INNER JOIN dbo.depoTransactions as t on t.depoMemberDataID = d.depoMemberDataID
				and t.DatePurchased <= @BPEndSDT
			where d.depomemberdataID = @depomemberdataID
			OR d.depomemberdataID in (
				select fpl.depomemberdataID
				from dbo.tlaFirmPlanLink as fpl
				INNER JOIN dbo.tlaFirmPlanLink AS fplMaster ON fplMaster.firmPlanID = fpl.firmPlanID
					and fplMaster.isMaster = 1
					and fplMaster.depoMemberDataID = @depomemberdataID
			)
			group by d.depomemberdataID, d.paymenttype, d.Email, d.BillingContactEmail;

			-- When Invoiced, we only want the ones with a EOP balance or non-swactivity in period and 0 balance
			-- When CreditCard, we only want the ones with non-swactivity in period and 0 balance
			delete from #tmpIndivTSA
			where (paymentType = 'I' AND NOT (EOPBalance > 0 OR (nonSWActivity > 0 AND EOPBalance = 0)))
			OR (paymentType = 'C' AND NOT (nonSWActivity > 0 AND EOPBalance = 0));

			-- delete the non-firm plans that dont have an email
			delete from #tmpIndivTSA
			where firmPlanMasterDepoID is null
			and email1 is null
			and email2 is null;

			-- add in the firm plan masters
			INSERT INTO #tmpIndivTSA (depomemberdataID, paymentType, EOPBalance, nonSWActivity, email1, email2, firmPlanMasterDepoID)
			select fpm.depomemberDataID, 'I', 0, 0, fpm.email1, fpm.email2, null
			from (
				select distinct d.depomemberdataID,  
					case when d.Email is not null and d.Email <> '' and membercentral.dbo.fn_RegExReplace(d.email,'^[a-zA-Z_0-9-''\&\+~]+(\.[a-zA-Z_0-9-''\&\+~]+)*@([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,63}$','') = '' then d.email else null end as email1,
					nullIF(d.BillingContactEmail,'') as email2
				from #tmpIndivTSA as tmp
				inner join dbo.depomemberdata as d on d.depomemberdataID = tmp.firmPlanMasterDepoID
				where tmp.firmPlanMasterDepoID is not null
			) as fpm
			where (fpm.email1 is not null OR fpm.email2 is not null)
			and NOT EXISTS (select top 1 depomemberdataID from #tmpIndivTSA where depomemberdataID = fpm.depomemberdataID);

			-- delete the firm plan members since the master may have been added
			delete from #tmpIndivTSA
			where firmPlanMasterDepoID is not null;

			insert into platformQueue.dbo.queue_monthBillTSA (depomemberDataID, TransStartDate, TransEndDate, StatementDate, billingPeriodID, isOrg, statusID)
				OUTPUT INSERTED.billingPeriodID, INSERTED.depoMemberDataID, @EmailIndivStatusWaiting
				INTO platformQueue.dbo.queue_monthBillEmailIndiv (billingPeriodID, depoMemberDataID, statusID)
			select depomemberdataID, @BPStartSDT, @BPEndSDT, @BPEndSDT, @periodID, 0, @TSAINDstatusReady
			from #tmpIndivTSA;

			IF @@ROWCOUNT > 0 BEGIN
				EXEC membercentral.dbo.sched_resumeTask @name='Process TrialSmith Statements Queue', @engine='TSAdminLinux';
				EXEC membercentral.dbo.sched_resumeTask @name='Process Monthly Billing Email Queue', @engine='MCLuceeLinux';
			END

			IF OBJECT_ID('tempdb..#tmpIndivTSA') IS NOT NULL 
				DROP TABLE #tmpIndivTSA;
		COMMIT TRAN;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
