ALTER PROC dbo.queue_processPreApproveDepoDocs_grabForProcessing

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpQueueItems') IS NOT NULL 
		DROP TABLE #tmpQueueItems;
	CREATE TABLE #tmpQueueItems(itemID int);

	declare @queueTypeID int, @statusReady int, @statusGrabbed int, @batchSize tinyint = 45;

	select @queueTypeID = queueTypeID from dbo.tblQueueTypes where queueType = 'processPreApproveDepoDocs';
	select @statusReady = queueStatusID from dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'readyToProcess';
	select @statusGrabbed = queueStatusID from dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'grabbedForProcessing';

	-- dequeue
	UPDATE qi WITH (UPDLOCK, READPAST)
	SET qi.statusID = @statusGrabbed,
		qi.dateUpdated = getdate()
		OUTPUT inserted.itemID INTO #tmpQueueItems
	FROM dbo.queue_processPreApproveDepoDocs as qi
	INNER JOIN (
		SELECT top (@batchSize) qi2.itemID 
		FROM dbo.queue_processPreApproveDepoDocs as qi2
		WHERE qi2.statusID = @statusReady
		ORDER BY qi2.dateAdded, qi2.itemID
	) as batch on batch.itemID = qi.itemID
	WHERE qi.statusID = @statusReady;

	SELECT qid.itemID, qid.depoDocumentID
	FROM #tmpQueueItems as qi
	INNER JOIN dbo.queue_processPreApproveDepoDocs as qid on qid.itemID = qi.itemID;
	
	IF OBJECT_ID('tempdb..#tmpQueueItems') IS NOT NULL 
		DROP TABLE #tmpQueueItems;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
END CATCH
GO
