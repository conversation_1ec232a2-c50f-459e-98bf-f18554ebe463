ALTER PROC dbo.ams_runScheduledLinkedRecordsQueryFromQueue
@itemID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @siteID int, @queryID int, @queueTypeID int, @readyToProcessStatusID int, @processingItemStatusID int, 
		@environmentName varchar(50);

	SELECT @queueTypeID = queueTypeID 
	FROM platformQueue.dbo.tblQueueTypes 
	WHERE queueType = 'schLinkedRecordsQuery';

	SELECT @readyToProcessStatusID = queueStatusID 
	FROM platformQueue.dbo.tblQueueStatuses 
	WHERE queueTypeID = @queueTypeID 
	AND queueStatus = 'readyToProcess';

	SELECT @processingItemStatusID = queueStatusID 
	FROM platformQueue.dbo.tblQueueStatuses 
	WHERE queueTypeID = @queueTypeID 
	AND queueStatus = 'processingItem';

	SELECT @environmentName = tier FROM dbo.fn_getServerSettings();

	SELECT @siteID = siteID, @queryID = queryID
	FROM platformQueue.dbo.queue_schLinkedRecordsQuery
	WHERE itemID = @itemID
	AND statusID = @readyToProcessStatusID;

	IF @queryID IS NULL
		GOTO on_done;

	-- update queue item status
	UPDATE platformQueue.dbo.queue_schLinkedRecordsQuery
	SET statusID = @processingItemStatusID,
		dateUpdated = GETDATE()
	WHERE itemID = @itemID;

	-- run scheduled linked records query
	EXEC dbo.ams_scheduledLinkedRecordsQueryImportMembers @queryID=@queryID, @siteID=@siteID, @environmentName=@environmentName;

	-- schedule next run date
	EXEC dbo.ams_setScheduledLinkedRecordsQueryNextRunDate @queryID=@queryID;

	-- delete queue item
	DELETE FROM platformQueue.dbo.queue_schLinkedRecordsQuery
	WHERE itemID = @itemID;

	on_done:
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
