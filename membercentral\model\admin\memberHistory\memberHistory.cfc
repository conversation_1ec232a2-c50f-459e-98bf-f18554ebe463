<cfcomponent output="false">

	<cffunction name="getMemberHistoryData" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="historyID" type="numeric" required="true">

		<cfset var qryData = "">

		<cfquery name="qryData" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">,
				@orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">,
				@historyID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.historyID#">;

			select mh.historyID, mh.typeID, mh.categoryID, mh.subCategoryID, mh.userDate, mh.userEndDate,
				mh.quantity, mh.dollarAmt, mh.description, mh.dateEntered,
				mActive.memberID, mActive.lastName + ', ' + mActive.firstName + ' (' + mActive.memberNumber + ')' as MemberName,
				mEnteredActive.lastName + ', ' + mEnteredActive.firstName + ' (' + mEnteredActive.memberNumber + ')' as enteredByMemberName,
				mLinkActive.memberID as linkMemberID, mLinkActive.lastName + ', ' + mLinkActive.firstName + ' (' + mLinkActive.memberNumber + ')' as linkedMemberName,
				case when mh.subCategoryID is null then cast(mh.categoryID as varchar(10))
					else cast(mh.categoryID as varchar(10)) + '_'  + cast(mh.subCategoryID as varchar(10)) 
				end as parentChildCategoryID
			from dbo.ams_memberHistory as mh
			inner join dbo.ams_members as m on m.orgID = @orgID and m.memberID = mh.memberID
			inner join dbo.ams_members as mActive on mActive.orgID = @orgID and mActive.memberID = m.activeMemberID
			inner join dbo.ams_members as mEntered on mEntered.orgID in (1,@orgID) and mEntered.memberID = mh.EnteredBymemberID
			inner join dbo.ams_members as mEnteredActive on mEnteredActive.orgID in (1,@orgID) and mEnteredActive.memberID = mEntered.activeMemberID
			left outer join dbo.ams_members as mLink 
				inner join dbo.ams_members as mLinkActive on mLinkActive.orgID = @orgID and mLinkActive.memberID = mLink.activeMemberID
				on mLink.orgID = @orgID 
					and mLink.memberID = mh.linkMemberID
			where mh.historyID = @historyID
			and mh.status = 'A';

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryData>
	</cffunction>

	<cffunction name="updateMemberHistory" access="public" output="false" returntype="void" hint="Update member history data">
		<cfargument name="historyID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="categoryID" type="numeric" required="true">
		<cfargument name="subCategoryID" type="numeric" required="true">
		<cfargument name="userDate" type="string" required="true">
		<cfargument name="userEndDate" type="string" required="true">
		<cfargument name="description" type="string" required="true">
		<cfargument name="quantity" type="numeric" required="true">
		<cfargument name="dollarAmt" type="string" required="true">
		<cfargument name="linkMemberID" type="numeric" required="true">

		<cfset var qryUpdate = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryUpdate">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				declare @memberID int, @historyID int, @categoryID int, @subCategoryID int, @orig_categoryID int, @orig_memberID int, @orgID int;
				set @memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">;
				set @historyID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.historyID#">;
				select @orgID = orgID from dbo.ams_members where memberID = @memberID;

				set @categoryID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.categoryID#">;
				set @subCategoryID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.subCategoryID#">;
				
				<cfif arguments.categoryID eq 0 and arguments.subCategoryID gt 0>
					select @categoryID = parentCategoryID from dbo.cms_categories where categoryID = @subCategoryID;
				</cfif>

				select @orig_categoryID=categoryID, @orig_memberID=memberID
				from dbo.ams_memberHistory
				where historyID = @historyID;

				update dbo.ams_memberHistory
				set memberID = @memberID, 
					categoryID = @categoryID, 
					<cfif arguments.subCategoryID gt 0>
						subCategoryID = @subCategoryID, 
					<cfelse>
						subCategoryID = null, 
					</cfif>
					<cfif len(arguments.userDate) eq 0>
						userDate = null, 
					<cfelse>
						userDate = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.userDate#">, 
					</cfif>
					<cfif len(arguments.userEndDate) eq 0>
						userEndDate = null, 
					<cfelse>
						userEndDate = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.userEndDate#">, 
					</cfif>				
					quantity = nullif(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(arguments.quantity)#">,0), 
					<cfif len(arguments.dollarAmt)>
						dollarAmt = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#arguments.dollarAmt#">, 
					<cfelse>
						dollarAmt = null, 
					</cfif>
					description = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.description#">,
					<cfif not val(arguments.linkMemberID)>
						linkMemberID = null
					<cfelse>
						linkMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.linkMemberID#">
					</cfif>
				where historyID = @historyID;


				IF OBJECT_ID('tempdb..##tblMCQRun') IS NOT NULL 
					DROP TABLE ##tblMCQRun;
				CREATE TABLE ##tblMCQRun (orgID int, memberID int INDEX IX_tblMCQRun_memberID, conditionID int);

				INSERT INTO ##tblMCQRun (orgID, memberID, conditionID)
				SELECT distinct @orgID, m.memberID, c.conditionID
				from (
					select @orig_memberID as memberID
					union 
					select @memberID as memberID
				) as m
				cross join (
					select c.conditionID
					from dbo.ams_virtualGroupConditions as c
					CROSS APPLY (
						SELECT cv.conditionValue
						FROM dbo.ams_virtualGroupConditionValues as cv
						inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey in ('historyCategory','noteCategory','relationshipCategory')
						WHERE cv.conditionID = c.conditionID
					) as historyCategory(val)
					where c.orgID = @orgID
					and c.fieldCode in ('mh_entry','mn_entry','rel_entry')
					and historyCategory.val in (cast(@categoryID as varchar(10)),cast(@orig_categoryID as varchar(10)))
				) as c;

				EXEC platformQueue.dbo.queue_triggerMCGCache @runImmediately=0, @type='ConditionsAndGroupsChanged';

				IF OBJECT_ID('tempdb..##tblMCQRun') IS NOT NULL 
					DROP TABLE ##tblMCQRun;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
	</cffunction>

	<cffunction name="updateCategoriesMemberHistory" access="public" output="false" returntype="void">
		<cfargument name="hIDList" type="string" required="true">
		<cfargument name="categoryID" type="numeric" required="true">
		<cfargument name="subCategoryID" type="numeric" required="true">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		
		<cfset var qryUpdate = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryUpdate">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				IF OBJECT_ID('tempdb..##tmpMHEntries') IS NOT NULL 
					DROP TABLE ##tmpMHEntries;
				CREATE TABLE ##tmpMHEntries (historyID int, memberNumber varchar(50), lastName varchar(75), firstName varchar(75), company varchar(200),
					LinkedMemberNumber varchar(50), LinkedMemberLastName varchar(75), LinkedMemberFirstName varchar(75), LinkedMemberCompany varchar(200),
					Category varchar(max), NewCategory varchar(max), Status varchar(10));

				DECLARE @memberID int, @historyIDList varchar(max), @categoryID int, @subCategoryID int, 
					@orgID int, @siteID int, @runByMemberID int, @siteCode varchar(10), @orgCode varchar(10), 
					@documentSectionID int, @documentSRTID int, @siteDocumentsPath varchar(40), 
					@docTitle varchar(100), @fileName varchar(100), @fileExt varchar(3), @documentID int, 
					@documentVersionID int, @documentSiteResourceID int, @destinationFile varchar(400), @s3keyMod varchar(4), 
					@objectKey varchar(400), @s3UploadReadyStatusID int, @historyTypeID int;
				
				SET @historyIDList = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.hIDList#">;
				SET @runByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">;
				SET @categoryID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.categoryID#">;
				SET @subCategoryID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.subCategoryID#">;
				SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">;
				SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
				
				<cfif arguments.categoryID eq 0 and arguments.subCategoryID gt 0>
					SELECT @categoryID = parentCategoryID FROM dbo.cms_categories WHERE categoryID = @subCategoryID;
				</cfif>

				-- create backup				
				select @orgCode = o.orgcode, @siteCode = s.siteCode
				from dbo.sites as s
				inner join dbo.organizations as o on o.orgID = s.orgID
				where s.siteID = @siteID;

				select @documentSectionID = dbo.fn_getRootSectionID(@siteID);
				select @documentSRTID = dbo.fn_getResourceTypeId('ApplicationCreatedDocument');
				select @siteDocumentsPath = siteDocumentsPath from dbo.fn_getServerSettings();

				select @s3UploadReadyStatusID = qs.queueStatusID
				from platformQueue.dbo.tblQueueTypes as qt
				inner join platformQueue.dbo.tblQueueStatuses as qs on qs.queueTypeID = qt.queueTypeID
				where qt.queueType = 's3Upload'
				and qs.queueStatus = 'readyToProcess';

				INSERT INTO ##tmpMHEntries (historyID, memberNumber, lastName, firstName, company, LinkedMemberNumber, LinkedMemberLastName, 
					LinkedMemberFirstName, LinkedMemberCompany, Category, NewCategory, Status)
				SELECT mh.historyID, mActive.memberNumber, mActive.lastName, mActive.firstName, mActive.company, 
					mLinkActive.memberNumber, mLinkActive.lastName, mLinkActive.firstName, mLinkActive.company, 
					CASE WHEN sc.categoryID IS NOT NULL THEN sc.categoryPath ELSE c.categoryPath END,
					CASE 
						WHEN @subCategoryID > 0 THEN (SELECT categoryPath FROM dbo.cms_categories WHERE categoryID = @subCategoryID)
						ELSE (SELECT categoryPath FROM dbo.cms_categories WHERE categoryID = @categoryID)
					END,
					'Active'
				FROM dbo.ams_memberHistory AS mh
				INNER JOIN dbo.fn_intListToTable(@historyIDList,',') AS tbl ON tbl.listitem = mh.historyID
				INNER JOIN dbo.cms_categories AS c ON c.categoryID = mh.categoryID
				LEFT OUTER JOIN dbo.cms_categories AS sc ON sc.categoryID = mh.subCategoryID
				INNER JOIN dbo.ams_members AS m on m.orgID = @orgID AND m.memberID = mh.memberID
				INNER JOIN dbo.ams_members AS mActive on mActive.orgID = @orgID AND mActive.memberID = m.activeMemberID
				LEFT OUTER JOIN dbo.ams_members AS mLink on mLink.orgID = @orgID AND mLink.memberID = mh.linkMemberID
				LEFT OUTER JOIN dbo.ams_members AS mLinkActive on mLinkActive.orgID = @orgID AND mLinkActive.memberID = mLink.activeMemberID
				WHERE mh.siteID = @siteID
				AND (
					mh.categoryID <> @categoryID
					<cfif arguments.subCategoryID>
						OR ISNULL(mh.subCategoryID,0) <> @subCategoryID
					<cfelse>
						OR mh.subCategoryID IS NOT NULL
					</cfif>
				);

				IF EXISTS (SELECT 1 FROM ##tmpMHEntries) BEGIN
					SELECT TOP 1 @historyTypeID = mh.typeID
					FROM dbo.ams_memberHistory AS mh
					INNER JOIN ##tmpMHEntries AS tmp ON tmp.historyID = mh.historyID
					WHERE mh.siteID = @siteID;

					SELECT @docTitle = 'Backup of Recategorized ' + CASE @historyTypeID WHEN 1 THEN 'Member History' WHEN 2 THEN 'Relationship' WHEN 3 THEN 'Note' END + ' entries', 
						@fileName = 'backup.csv', @fileExt = 'csv';

					EXEC dbo.cms_createDocument @siteID=@siteID, @resourceTypeID=@documentSRTID, @siteResourceStatusID=1,
						@languageID=1, @sectionID=@documentSectionID, @contributorMemberID=@runByMemberID, 
						@recordedByMemberID=@runByMemberID, @isActive=1, @isVisible=1, @docTitle=@docTitle, 
						@docDesc='', @author=null, @publicationDate=null, @fileName=@fileName, @fileExt=@fileExt, 
						@documentID=@documentID OUTPUT, @documentVersionID=@documentVersionID OUTPUT, 
						@documentSiteResourceID=@documentSiteResourceID OUTPUT;

					SET @destinationFile = lower(@siteDocumentsPath + @orgCode + '\' + @siteCode + '\' + cast(@documentVersionID as varchar(10)) + '.' + @fileExt);
					SET @s3keyMod = FORMAT(@documentVersionID % 1000, '0000');
					SET @objectKey = LOWER('sitedocuments/' + @orgCode + '/' + @siteCode + '/' + @s3keyMod + '/' + cast(@documentVersionID as varchar(10)) + '.' + @fileExt);

					DECLARE @selectsql varchar(max) = 'SELECT historyID, memberNumber, lastName, firstName, company, LinkedMemberNumber, LinkedMemberLastName, 
						LinkedMemberFirstName, LinkedMemberCompany, Category, NewCategory, Status, ROW_NUMBER() OVER(order by historyID) as mcCSVorder 
					*FROM* ##tmpMHEntries';
					EXEC dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename=@destinationFile, @returnColumns=0;
					
					INSERT INTO platformQueue.dbo.queue_S3Upload (statusID, s3bucketName, objectKey, filePath, deleteOnSuccess, dateAdded, dateUpdated)
					VALUES (@s3UploadReadyStatusID, 'membercentralcdn', @objectKey, @destinationFile, 1, GETDATE(), GETDATE());
					
					INSERT INTO platformStatsMC.dbo.ams_memberHistoryBackups (siteID, dateEntered, runByMemberID, historyTypeID, action, documentID)
					VALUES (@siteID, GETDATE(), @runByMemberID, @historyTypeID, 'recat', @documentID);
				
					-- recategorize
					IF OBJECT_ID('tempdb..##tblMCQRun') IS NOT NULL 
						DROP TABLE ##tblMCQRun;
					IF OBJECT_ID('tempdb..##tblMHOriginal') IS NOT NULL 
						DROP TABLE ##tblMHOriginal;
					CREATE TABLE ##tblMHOriginal (memberID INT, categoryID INT);
					CREATE TABLE ##tblMCQRun (orgID INT, memberID int INDEX IX_tblMCQRun_memberID, conditionID INT);

					INSERT INTO ##tblMHOriginal (memberID, categoryID)
					SELECT DISTINCT mActive.memberID, mh.categoryID
					FROM dbo.ams_memberHistory mh
					INNER JOIN ##tmpMHEntries AS tmp ON tmp.historyID = mh.historyID
					INNER JOIN dbo.ams_members AS m on m.orgID = @orgID AND m.memberID = mh.memberID
					INNER JOIN dbo.ams_members AS mActive on mActive.orgID = @orgID AND mActive.memberID = m.activeMemberID
					WHERE mh.siteID = @siteID;

					UPDATE mh
					SET mh.categoryID = @categoryID, 
						<cfif arguments.subCategoryID gt 0>
							mh.subCategoryID = @subCategoryID 
						<cfelse>
							mh.subCategoryID = null
						</cfif>
					FROM dbo.ams_memberHistory AS mh
					INNER JOIN ##tmpMHEntries AS tmp ON tmp.historyID = mh.historyID
						AND mh.siteID = @siteID
					INNER JOIN dbo.ams_members AS m ON m.memberID = mh.memberID
						AND m.orgID = @orgID;

					
					-- vgc reprocess
					INSERT INTO ##tblMCQRun (orgID, memberID, conditionID)
					SELECT DISTINCT @orgID, m.memberID, c.conditionID
					FROM (
						SELECT memberID FROM ##tblMHOriginal
					) AS m
					CROSS JOIN (
						SELECT c.conditionID
						FROM dbo.ams_virtualGroupConditions AS c
						CROSS APPLY (
							SELECT cv.conditionValue
							FROM dbo.ams_virtualGroupConditionValues AS cv
							INNER JOIN dbo.ams_virtualGroupConditionKeys AS k ON k.conditionKeyID = cv.conditionKeyID AND k.conditionKey IN ('historyCategory','noteCategory','relationshipCategory')
							WHERE cv.conditionID = c.conditionID
						) AS historyCategory(val)
						where c.orgID = @orgID
						AND c.fieldCode IN ('mh_entry','mn_entry','rel_entry')
						AND (historyCategory.val IN (cast(@categoryID AS varchar(10))) OR historyCategory.val IN (SELECT cast(categoryID AS varchar(10)) FROM ##tblMHOriginal))
					) AS c;

					EXEC platformQueue.dbo.queue_triggerMCGCache @runImmediately=0, @type='ConditionsAndGroupsChanged';

					IF OBJECT_ID('tempdb..##tblMCQRun') IS NOT NULL 
						DROP TABLE ##tblMCQRun;
					IF OBJECT_ID('tempdb..##tblMHOriginal') IS NOT NULL 
						DROP TABLE ##tblMHOriginal;
				END

				IF OBJECT_ID('tempdb..##tmpMHEntries') IS NOT NULL 
					DROP TABLE ##tmpMHEntries;
				
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
	</cffunction>

	<cffunction name="deleteMemberHistory" access="public" output="false" returntype="void">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="hIDList" type="string" required="true">

		<cfset var qryDeleteMemberHistory = "">

		<cfquery name="qryDeleteMemberHistory" datasource="#application.dsn.membercentral.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				DECLARE @orgID int, @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">,
					@historyIDList varchar(max) = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.hIDList#">,
					@runByMemberID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">;

				SELECT @orgID = dbo.fn_getOrgIDFromSiteID(@siteID);
				
				<cfif listLen(arguments.hIDList) GT 1>
					-- create backup
					IF OBJECT_ID('tempdb..##tmpMHEntries') IS NOT NULL 
						DROP TABLE ##tmpMHEntries;
					CREATE TABLE ##tmpMHEntries (historyID int, memberNumber varchar(50), lastName varchar(75), firstName varchar(75), company varchar(200),
						LinkedMemberNumber varchar(50), LinkedMemberLastName varchar(75), LinkedMemberFirstName varchar(75), LinkedMemberCompany varchar(200),
						Category varchar(max), Status varchar(10));

					DECLARE @siteCode varchar(10), @orgCode varchar(10), @documentSectionID int, @documentSRTID int, 
						@siteDocumentsPath varchar(40), @docTitle varchar(100), @fileName varchar(100), @fileExt varchar(3), @documentID int, 
						@documentVersionID int, @documentSiteResourceID int, @destinationFile varchar(400), @s3keyMod varchar(4), 
						@objectKey varchar(400), @s3UploadReadyStatusID int, @historyTypeID int;
					
					select @orgCode = o.orgcode, @siteCode = s.siteCode
					from dbo.sites as s
					inner join dbo.organizations as o on o.orgID = s.orgID
					where s.siteID = @siteID;

					select @documentSectionID = dbo.fn_getRootSectionID(@siteID);
					select @documentSRTID = dbo.fn_getResourceTypeId('ApplicationCreatedDocument');
					select @siteDocumentsPath = siteDocumentsPath from dbo.fn_getServerSettings();

					select @s3UploadReadyStatusID = qs.queueStatusID
					from platformQueue.dbo.tblQueueTypes as qt
					inner join platformQueue.dbo.tblQueueStatuses as qs on qs.queueTypeID = qt.queueTypeID
					where qt.queueType = 's3Upload'
					and qs.queueStatus = 'readyToProcess';

					INSERT INTO ##tmpMHEntries (historyID, memberNumber, lastName, firstName, company, LinkedMemberNumber, LinkedMemberLastName, 
						LinkedMemberFirstName, LinkedMemberCompany, Category, Status)
					SELECT mh.historyID, mActive.memberNumber, mActive.lastName, mActive.firstName, mActive.company, 
						mLinkActive.memberNumber, mLinkActive.lastName, mLinkActive.firstName, mLinkActive.company, 
						CASE WHEN sc.categoryID IS NOT NULL THEN sc.categoryPath ELSE c.categoryPath END,
						'Active'
					FROM dbo.ams_memberHistory AS mh
					INNER JOIN dbo.fn_intListToTable(@historyIDList,',') AS tbl ON tbl.listitem = mh.historyID
					INNER JOIN dbo.cms_categories AS c ON c.categoryID = mh.categoryID
					LEFT OUTER JOIN dbo.cms_categories AS sc ON sc.categoryID = mh.subCategoryID
					INNER JOIN dbo.ams_members AS m on m.orgID = @orgID AND m.memberID = mh.memberID
					INNER JOIN dbo.ams_members AS mActive on mActive.orgID = @orgID AND mActive.memberID = m.activeMemberID
					LEFT OUTER JOIN dbo.ams_members AS mLink on mLink.orgID = @orgID AND mLink.memberID = mh.linkMemberID
					LEFT OUTER JOIN dbo.ams_members AS mLinkActive on mLinkActive.orgID = @orgID AND mLinkActive.memberID = mLink.activeMemberID
					WHERE mh.siteID = @siteID;

					SELECT TOP 1 @historyTypeID = mh.typeID
					FROM dbo.ams_memberHistory AS mh
					INNER JOIN ##tmpMHEntries AS tmp ON tmp.historyID = mh.historyID
					WHERE mh.siteID = @siteID;

					SELECT @docTitle = 'Backup of deleted ' + CASE @historyTypeID WHEN 1 THEN 'Member History' WHEN 2 THEN 'Relationship' WHEN 3 THEN 'Note' END + ' entries', 
						@fileName = 'backup.csv', @fileExt = 'csv';

					EXEC dbo.cms_createDocument @siteID=@siteID, @resourceTypeID=@documentSRTID, @siteResourceStatusID=1,
						@languageID=1, @sectionID=@documentSectionID, @contributorMemberID=@runByMemberID, 
						@recordedByMemberID=@runByMemberID, @isActive=1, @isVisible=1, @docTitle=@docTitle, 
						@docDesc='', @author=null, @publicationDate=null, @fileName=@fileName, @fileExt=@fileExt, 
						@documentID=@documentID OUTPUT, @documentVersionID=@documentVersionID OUTPUT, 
						@documentSiteResourceID=@documentSiteResourceID OUTPUT;

					SET @destinationFile = lower(@siteDocumentsPath + @orgCode + '\' + @siteCode + '\' + cast(@documentVersionID as varchar(10)) + '.' + @fileExt);
					SET @s3keyMod = FORMAT(@documentVersionID % 1000, '0000');
					SET @objectKey = LOWER('sitedocuments/' + @orgCode + '/' + @siteCode + '/' + @s3keyMod + '/' + cast(@documentVersionID as varchar(10)) + '.' + @fileExt);

					DECLARE @selectsql varchar(max) = 'SELECT historyID, memberNumber, lastName, firstName, company, LinkedMemberNumber, LinkedMemberLastName, 
						LinkedMemberFirstName, LinkedMemberCompany, Category, Status, ROW_NUMBER() OVER(order by historyID) as mcCSVorder 
					*FROM* ##tmpMHEntries';
					EXEC dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename=@destinationFile, @returnColumns=0;
					
					INSERT INTO platformQueue.dbo.queue_S3Upload (statusID, s3bucketName, objectKey, filePath, deleteOnSuccess, dateAdded, dateUpdated)
					VALUES (@s3UploadReadyStatusID, 'membercentralcdn', @objectKey, @destinationFile, 1, GETDATE(), GETDATE());
					
					INSERT INTO platformStatsMC.dbo.ams_memberHistoryBackups (siteID, dateEntered, runByMemberID, historyTypeID, action, documentID)
					VALUES (@siteID, GETDATE(), @runByMemberID, @historyTypeID, 'delete', @documentID);

					IF OBJECT_ID('tempdb..##tmpMHEntries') IS NOT NULL 
						DROP TABLE ##tmpMHEntries;
				</cfif>

				EXEC dbo.ams_deleteMemberHistory @siteID=@siteID, @historyIDList=@historyIDList;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
	</cffunction>

	<cffunction name="getSubcategoriesForCategory" access="public" output="false" returntype="string">
		<cfargument name="catID" type="numeric" required="true">
		<cfset var qryCategories = "">
		
		<cftry>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryCategories">
				select c.categoryID, c.categoryName 
				from dbo.cms_categories c
				inner join dbo.cms_categories pc on pc.categoryID = c.parentCategoryID
				where c.parentCategoryID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.catID#">
				and c.isActive = 1
				UNION
				SELECT 0, '--- choose a sub-category ---'		
				order by 2
			</cfquery>
		<cfcatch type="any">		
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset qryCategories = QueryNew("categoryID,categoryName","integer,varchar")>
		</cfcatch>
		</cftry>		
		
		<cfreturn SerializeJSON(qryCategories)>
	</cffunction>

	<cffunction name="getParentCategories" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="siteResourceID" type="numeric" required="true">
		<cfargument name="categoryTreeName" type="string" required="false" default="">
		
		<cfset var qryCategories = "">

		<cfquery name="qryCategories" datasource="#application.dsn.membercentral.dsn#">
			set nocount on

			declare @catTreeID int;
			<cfif arguments.categoryTreeName NEQ "">
				select @catTreeID = dbo.fn_getCategoryTreeIDForSiteResourceIDandTree(<cfqueryparam value="#arguments.siteResourceID#" cfsqltype="CF_SQL_INTEGER">, <cfqueryparam value="#arguments.categoryTreeName#" cfsqltype="CF_SQL_VARCHAR">)
			<cfelse>
				select @catTreeID = dbo.fn_getCategoryTreeIDForSiteResourceID(<cfqueryparam value="#arguments.siteResourceID#" cfsqltype="CF_SQL_INTEGER">)
			</cfif>

			select categoryID, categoryName, categoryDesc, rightsXML
			from (
				select c.categoryID, c.categoryName, c.categoryDesc, dbo.fn_cache_perms_getResourceRightsXML(c.siteResourceID,<cfqueryparam value="#session.cfcuser.memberdata.memberid#" cfsqltype="CF_SQL_INTEGER">,<cfqueryparam value="#arguments.siteID#" cfsqltype="CF_SQL_INTEGER">) as rightsXML
				from dbo.cms_categories as c
				where c.categoryTreeID = @catTreeID
				and c.isActive = 1 
				and c.parentCategoryID is NULL
			)tmp
			where rightsXML.exist('(/rights/right[@allowed="1" and @functionName="ModifyCategory"])[1]') = 1
			order by categoryName
		</cfquery>

		<cfreturn qryCategories>
	</cffunction>

	<cffunction name="getParentChildCategories" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="siteResourceID" type="numeric" required="true">
		<cfargument name="checkPermission" type="string" required="false" default="">
		<cfargument name="categoryTreeName" type="string" required="false" default="">
		
		<cfset var local = structNew()>
		<cfif len(arguments.checkPermission)>
			<cfset local.checkPermMemberHistoryCategoryRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="MemberHistoryCategory", functionName=arguments.checkPermission)>
		</cfif>

		<cfquery name="local.qryCategories" datasource="#application.dsn.membercentral.dsn#">
			set nocount on;

			declare @catTreeID int, @memberID int, @functionID int, @groupPrintID int, @siteID int;
			set @siteID = <cfqueryparam value="#arguments.siteID#" cfsqltype="CF_SQL_INTEGER">;

			<cfif len(arguments.checkPermission)>
				set @memberID = <cfqueryparam value="#session.cfcuser.memberdata.memberid#" cfsqltype="CF_SQL_INTEGER">;
				set @functionID = <cfqueryparam value="#local.checkPermMemberHistoryCategoryRFID#" cfsqltype="CF_SQL_INTEGER">;
				select @groupPrintID = groupPrintID from ams_members where memberID = @memberID;
			</cfif>

			<cfif arguments.categoryTreeName NEQ "">
				select @catTreeID = dbo.fn_getCategoryTreeIDForSiteResourceIDandTree(<cfqueryparam value="#arguments.siteResourceID#" cfsqltype="CF_SQL_INTEGER">, <cfqueryparam value="#arguments.categoryTreeName#" cfsqltype="CF_SQL_VARCHAR">);
			<cfelse>
				select @catTreeID = dbo.fn_getCategoryTreeIDForSiteResourceID(<cfqueryparam value="#arguments.siteResourceID#" cfsqltype="CF_SQL_INTEGER">);
			</cfif>

			;WITH cte AS (
				select c.categoryID, c.categoryName, c.categoryCode, c.categoryDesc, 0 as parentCategoryID,
					dbo.fn_cache_perms_getResourceRightsXML(c.siteResourceID,@memberID,@siteID) as rightsXML,
					'0000.' + RIGHT('0000' + cast(ROW_NUMBER() over (order by c.categoryName) as varchar(max)),4) as theRow
				from dbo.cms_categories c
				<cfif len(arguments.checkPermission)>
					inner join dbo.cache_perms_siteResourceFunctionRightPrints srfrp on srfrp.siteID = @siteID 
						and srfrp.siteResourceID = c.siteResourceID
						and srfrp.functionID = @functionID
					inner join dbo.cache_perms_groupPrintsRightPrints gprp on gprp.siteID = @siteID
						and gprp.rightPrintID = srfrp.rightPrintID
						and gprp.groupPrintID = @groupPrintID
				</cfif>
				where c.categoryTreeID = @catTreeID
				and c.isActive = 1 
				and c.parentCategoryID is NULL
					union all
				select c.categoryID, c.categoryName, c.categoryCode, c.categoryDesc, c.parentCategoryID, pc.rightsXML,
					pc.theRow + '.' + RIGHT('0000' + cast(ROW_NUMBER() over (order by c.categoryName) as varchar(max)),4) as theRow
				from dbo.cms_categories c
				inner join cte as pc on pc.categoryID = c.parentCategoryID
				where c.categoryTreeID = @catTreeID
				and c.isActive = 1 
				and c.parentCategoryID is not NULL
			)
			select categoryID, categoryName, categoryCode, categoryDesc, parentCategoryID, rightsXML, parentChildCategoryID, theRow
			from (
				select categoryID, categoryName, categoryCode, categoryDesc, parentCategoryID, rightsXML, theRow,
					case when parentCategoryID = 0 then cast(categoryID as varchar(10))
						else cast(parentCategoryID as varchar(10)) + '_'  + cast(categoryID as varchar(10)) 
					end as parentChildCategoryID
				from cte
			) as tmp
			order by theRow;
		</cfquery>

		<cfreturn local.qryCategories>
	</cffunction>
	
	<cffunction name="getAllSubCategories" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.MemberHistoryAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='MemberHistoryAdmin',siteID=arguments.siteID)>

		<cfquery name="local.qryCategories" datasource="#application.dsn.membercentral.dsn#">
			set nocount on;

			declare @MemberHistoryAdminSRID int, @CatTreeID int;
			set @MemberHistoryAdminSRID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.MemberHistoryAdminSRID#">;
			select @CatTreeID = dbo.fn_getCategoryTreeIDForSiteResourceID(@MemberHistoryAdminSRID);

			select c.categoryID, c.categoryName, subC.categoryID as subCategoryID, subC.categoryName as subCategoryName
			from dbo.cms_categories as c
			inner join dbo.cms_categories as subC on subC.parentCategoryID = c.categoryID 
			where c.categoryTreeID = @catTreeID
			and c.isActive = 1 
			and subC.isActive = 1 
			and c.parentCategoryID is NULL
			order by c.categoryName, subC.categoryName;
		</cfquery>

		<cfreturn local.qryCategories>
	</cffunction>

	<cffunction name="checkModifyCategory" access="public" output="false" returntype="boolean">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="categoryID" type="numeric" required="true">

		<cfset var qryAllowedCategory = "">

		<cfquery name="qryAllowedCategory" datasource="#application.dsn.membercentral.dsn#">
			select case when c.siteResourceID is null 
				then dbo.fn_cache_perms_getResourceRightsXML(cP.siteResourceID,<cfqueryparam value="#session.cfcuser.memberdata.memberid#" cfsqltype="CF_SQL_INTEGER">,<cfqueryparam value="#arguments.siteID#" cfsqltype="CF_SQL_INTEGER">).exist('(/rights/right[@allowed="1" and @functionName="ModifyCategory"])[1]') 
						when cP.siteResourceID is null 
							then dbo.fn_cache_perms_getResourceRightsXML(c.siteResourceID,<cfqueryparam value="#session.cfcuser.memberdata.memberid#" cfsqltype="CF_SQL_INTEGER">,<cfqueryparam value="#arguments.siteID#" cfsqltype="CF_SQL_INTEGER">).exist('(/rights/right[@allowed="1" and @functionName="ModifyCategory"])[1]') 
						end as allowModifyCategory
			from dbo.cms_categories as c
			left outer join dbo.cms_categories as cP on cP.categoryID = c.parentCategoryID
			where c.categoryID = <cfqueryparam value="#arguments.categoryID#" cfsqltype="CF_SQL_INTEGER">
			and c.isActive = 1;
		</cfquery>

		<cfreturn qryAllowedCategory.allowModifyCategory is 1>
	</cffunction>
	
	<cffunction name="getAnalyzeMemberHistoryTotals" access="public" output="false" returntype="Query">
		<cfargument name="qryHistory" type="query" required="true">
		<cfargument name="categoryName" type="string" required="false" default="">

		<cfset var local = structNew()>

		<cfquery name="local.qryHistoryTotals" dbtype="query">
			select <cfif arguments.categoryName neq ''>categoryName,</cfif> sum(memberCount) as memberCountTotal, sum(quantity) as quantityTotal, sum(dollarAmt) as dollarAmtTotal
			from [arguments].qryHistory
			<cfif arguments.categoryName neq ''>
				where categoryName = <cfqueryparam value="#arguments.categoryName#" cfsqltype="cf_sql_varchar">
				group by categoryName
			</cfif>	
		</cfquery>
		
		<cfreturn local.qryHistoryTotals>
	</cffunction>	
	
	<cffunction name="generateImportTemplate" access="package" output="false" returntype="struct">
		<cfargument name="typeID" type="numeric" required="yes">
		
		<cfset var local = structNew()>
		<cfset local.data = "The sample import file could not be generated. Contact MemberCentral for assistance.">
		
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix='member')>
		<cfset local.reportFileName = "ImportTemplate.csv">

		<cfif arguments.typeID is 1>
			<cfset local.fieldNames = "MemberNumber,Category,Subcategory,StartDate,EndDate,Description,Quantity,Amount,LinkedMemberNumber">
		<cfelseif  listFindNoCase("2,3",arguments.typeID)>
			<cfset local.fieldNames = "MemberNumber,Category,Subcategory,StartDate,EndDate,Description,LinkedMemberNumber">
		</cfif>	
		<cfset local.arrFields = listToArray(local.fieldNames)>

		<cfquery name="local.qryExport" datasource="#Application.dsn.membercentral.dsn#" result="local.qryExportResult">
			set nocount on;

			IF OBJECT_ID('tempdb..##tmpImportTemplate') IS NOT NULL 
				DROP TABLE ##tmpImportTemplate;
			CREATE TABLE ##tmpImportTemplate (
				autoID int
				<cfloop array="#local.arrFields#" index="local.thisCol">
					, #local.thisCol# varchar(30)
				</cfloop>
			);
			
			<cfif arguments.typeID is 1>
				insert into ##tmpImportTemplate (#local.fieldNames#)
				values ('Req', 'Req', '', '', '','','','','');
			<cfelseif  listFindNoCase("2,3",arguments.typeID)>
				insert into ##tmpImportTemplate (#local.fieldNames#)
				values ('Req', 'Req', '', '', '','','');
			</cfif>	
			
			DECLARE @selectsql varchar(max) = '
				SELECT #local.fieldNames#, ROW_NUMBER() OVER(order by MemberNumber) as mcCSVorder 
				*FROM* ##tmpImportTemplate';
			EXEC dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename='#application.objCommon.convertFileSeparator("#local.strFolder.folderPathUNC#/#local.reportFileName#",'\')#', @returnColumns=0;

			IF OBJECT_ID('tempdb..##tmpImportTemplate') IS NOT NULL 
				DROP TABLE ##tmpImportTemplate;
		</cfquery>
				
		<cfset application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#")>
		<cfset application.objDocDownload.doDownloadDocument(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#", displayName=local.reportFileName, deleteSourceFile=1)>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="importHistory" access="package" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfargument name="qryImportColumns" type="query" required="yes">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.errMsg = "">
		
  		<cftry>
  			<cfset local.strSQLPrep = createObject("component","model.admin.common.modules.import.import").prepBCPToTableSQL(event=arguments.event, qryImportColumns=arguments.qryImportColumns, importTableName='##mc_MHImport')>
			<cfif not local.strSQLPrep.success>
				<cfthrow message="There was an error processing final import.">
			</cfif>

			<cfquery name="local.qryImport" datasource="#application.dsn.membercentral.dsn#" result="local.qryImportResult">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					IF OBJECT_ID('tempdb..##mc_MHImport') IS NOT NULL 
						DROP TABLE ##mc_MHImport;

					declare @siteID int, @typeID int, @recordedByMemberID int, @importResult xml;
					set @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteID')#">;
					set @typeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('typeID',1)#">;
					set @recordedByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcUser.memberData.memberID#">;

					-- bcp to table
					BEGIN TRY
						#PreserveSingleQuotes(local.strSQLPrep.sql)#
					END TRY
					BEGIN CATCH
						select @importResult = '<import><errors><error msg="Unable to import the file for processing." /><error msg="' + error_message() + '" /></errors></import>';
						GOTO on_done;
					END CATCH
					
					-- import file
					BEGIN TRY
						set @importResult = null;
						EXEC dbo.mh_importMemberHistory @siteID=@siteID, @typeID=@typeID, @recordedByMemberID=@recordedByMemberID, @importResult=@importResult OUTPUT;
					END TRY
					BEGIN CATCH
						select @importResult = '<import><errors><error msg="Unable to process the import file." /><error msg="' + error_message() + '" /></errors></import>';
						GOTO on_done;
					END CATCH
				
					on_done:
					declare @errCount int;
					select @errCount = @importResult.value('count(/import/errors/error)','int');
					SELECT @importResult as importResult, @errCount as errCount; 

					IF OBJECT_ID('tempdb..##mc_MHImport') IS NOT NULL 
						DROP TABLE ##mc_MHImport;
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
			<cfset local.returnStruct.importResultXML = xmlparse(local.qryImport.importResult)> 
			<cfset local.returnStruct.errCount = local.qryImport.errCount>
			<cfif local.returnStruct.errCount gt 0>
				<cfset local.returnStruct.success = false>
				<cfset local.returnStruct.strImportDetails = getHistoryImportDetails(event=arguments.event, qryImportColumns=arguments.qryImportColumns)>
			</cfif>
			<cfcatch type="Any">
				<cfset local.returnStruct.success = false>
				<cfset local.returnStruct.errMsg = "There was a problem importing the files. Try the upload again or contact us for assistance.">
				<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			 </cfcatch> 
		</cftry>	
		
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getHistoryImportDetails" access="private" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfargument name="qryImportColumns" type="query" required="yes">

		<cfset var local = structNew()>
		
		<cfset local.strFormFields = structNew()>
		<cfset local.arrImportColumnDetails = arrayNew(1)>

		<cfset structInsert(local.strFormFields, 'resourceType', arguments.event.getValue('resourceType',''))>
		<cfset structInsert(local.strFormFields, 'typeID', arguments.event.getValue('typeID',1))>
		<cfset structInsert(local.strFormFields, 'bcpfilename', arguments.event.getValue('bcpfilename',''))>
		<cfset structInsert(local.strFormFields, 'uploadedFileFieldList', arguments.event.getValue('uploadedFileFieldList',''))>

		<cfloop query="arguments.qryImportColumns">
			<cfset local.tmpStr = { columnID=arguments.qryImportColumns.columnID, mappedColValue=arguments.event.getValue('mcimpcol_map_#arguments.qryImportColumns.columnID#',''),
									mappedColOverrideValue='' }>
			<cfif local.tmpStr.mappedColValue EQ '_override_value_'>
				<cfset local.tmpStr.mappedColOverrideValue = arguments.event.getValue('mcimpcol_override_#arguments.qryImportColumns.columnID#','')>
			</cfif>
			<cfset arrayAppend(local.arrImportColumnDetails, local.tmpStr)>
		</cfloop>

		<cfset local.strImportDetails = { strFormFields=local.strFormFields, arrImportColumnDetails=local.arrImportColumnDetails }>

		<cfreturn local.strImportDetails>
	</cffunction>

	<cffunction name="showImportResults" access="package" output="false" returntype="string">
		<cfargument name="strImportResult" type="struct" required="yes">
		<cfargument name="doAgainURL" type="string" required="yes">

		<cfscript>
			var local = structNew();
			local.data = '';
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<cfif NOT arguments.strImportResult.success>
				<div id="divHistoryImportErrorScreen" style="background:##fff6bf url(/assets/common/images/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00;">
					<h4>Import Results</h4>
					<div><b>The import was stopped and requires your attention.</b></div>
					<br/>			

					<cfif structKeyExists(arguments.strImportResult,"importResultXML")>
						<cfset local.arrErrors = XMLSearch(arguments.strImportResult.importResultXML,"/import/errors/error")>
						<div>
						<cfif arrayLen(local.arrErrors) gt 200>
							<b>Only the first 200 errors are shown.</b><br/><br/>
						</cfif>
						<cfset local.thisErrNum = 0>
						<cfloop array="#local.arrErrors#" index="local.thisErr">
							<cfset local.thisErrNum = local.thisErrNum + 1>
							#local.thisErr.xmlAttributes.msg#<br/>
							<cfif local.thisErrNum is 200>
								<cfbreak>
							</cfif>
						</cfloop>
						</div>
					<cfelse>
						<div>#arguments.strImportResult.errMsg#</div>
					</cfif>

					<br/>
					<button class="btn btn-sm btn-secondary" name="btnDoOver" type="button" onClick="self.location.href='#arguments.doAgainURL#';">Try upload again</button>
					<cfif structKeyExists(arguments.strImportResult,"previousMappingScreen")>
						&nbsp;<a href="##" onClick="$('##divHistoryImportErrorScreen').hide();$('##divHistoryImportMappingScreen').show(300);return false;">Return to Column Mapping</a>
					</cfif>
				</div>
				<cfif structKeyExists(arguments.strImportResult,"previousMappingScreen")>
					<div id="divHistoryImportMappingScreen" style="display:none;">
						#arguments.strImportResult.previousMappingScreen#
					</div>
				</cfif>
			<cfelse>
				<h4>Import Results</h4>
				<p><b>Import Has Been Completed</b></p>
				<div>
					The import of the uploaded file has been completed.<br/>
				</div>
				<br/>
				<button class="btn btn-sm btn-secondary" name="btnDoOver" type="button" onClick="self.location.href='#arguments.doAgainURL#';">Upload another file</button>
			</cfif>
			</cfoutput>
		</cfsavecontent>
			
		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="getMemberHistoryFromFilters" access="public" output="false" returntype="query">
		<cfargument name="Event" type="any" required="true">
		<cfargument name="mode" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.typeID = arguments.event.getValue('typeID',1)>
		<cfset local.parentChildCategoryID = arguments.event.getValue('parentChildCategoryID','')>
		<cfset local.hmCategoryID = 0>
		<cfset local.hmSubCategoryID = 0>
		<cfif listLen(local.parentChildCategoryID,'_') eq 2>
			<cfset local.hmCategoryID = val(getToken(local.parentChildCategoryID, 1,'_'))>
			<cfset local.hmSubCategoryID = val(getToken(local.parentChildCategoryID, 2,'_'))>
		<cfelse>
			<cfset local.hmCategoryID = val(local.parentChildCategoryID)>
		</cfif>

		<cfset local.DeleteEntryMemberHistoryCategoryRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="MemberHistoryCategory", functionName="DeleteEntry")>
		<cfset local.EditEntryMemberHistoryCategoryRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="MemberHistoryCategory", functionName="EditEntry")>

		<cfif arguments.mode is 'mhTabGrid'>
			<cfset local.arrCols = arrayNew(1)>
			<cfset arrayAppend(local.arrCols,"memberName #arguments.event.getValue('orderDir')#")>
			<cfset arrayAppend(local.arrCols,"memberName #arguments.event.getValue('orderDir')#")>
			<cfset arrayAppend(local.arrCols,"typeName #arguments.event.getValue('orderDir')#")>
			<cfset arrayAppend(local.arrCols,"dateEntered #arguments.event.getValue('orderDir')#")>
			<cfset local.orderby = local.arrcols[arguments.event.getValue('orderby')+1]>
		<cfelseif arguments.mode is 'mhTabEmailGrid'>
			<cfset local.orderby = "(m.lastname + m.firstname + m.membernumber) #arguments.event.getValue('orderDir')#">
		</cfif>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryMemHistory" result="local.qryMemHistoryResult">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @orgID int, @siteID int, @categoryID int, @subCategoryID int, @userDateFrom date, @userDateTo datetime, @userEndDateFrom date, @userEndDateTo datetime, 
						@dateEnteredFrom date, @dateEnteredTo datetime, @description varchar(max), @quantityFrom int, @quantityTo int, @dollarAmtFrom decimal(14,2), 
						@dollarAmtTo decimal(14,2), @limitToMemberID int, @enteredByMemberID int, @typeID int, @currentMemberID int, @fLinkedMemberID int, 
						@fLinkedGroupID int, @fAssignedMemberID int, @fAssignedGroupID int;

				SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteID')#">;
				SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgID')#">;
				SET @typeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.typeID#">;
				SET @currentMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberid#">;
				<cfif local.hmCategoryID gt 0>
					SET @categoryID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.hmCategoryID#">;
				</cfif>
				<cfif local.hmSubCategoryID gt 0>
					SET @subCategoryID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.hmSubCategoryID#">;
				</cfif>
				<cfif arguments.event.getTrimValue('fDateFrom','') NEQ ''>
					SET @userDateFrom = <cfqueryparam cfsqltype="CF_SQL_DATE" value="#arguments.event.getValue('fDateFrom')#">;
				</cfif>
				<cfif arguments.event.getTrimValue('fDateTo','') NEQ ''>
					SET @userDateTo = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.event.getValue('fDateTo')# 23:59:59.997">;
				</cfif>
				<cfif arguments.event.getTrimValue('fEndDateFrom','') NEQ ''>
					SET @userEndDateFrom = <cfqueryparam cfsqltype="CF_SQL_DATE" value="#arguments.event.getValue('fEndDateFrom')#">;
				</cfif>
				<cfif arguments.event.getTrimValue('fEndDateTo','') NEQ ''>
					SET @userEndDateTo = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.event.getValue('fEndDateTo')# 23:59:59.997">;
				</cfif>
				<cfif arguments.event.getTrimValue('fEntDateFrom','') NEQ ''>
					SET @dateEnteredFrom = <cfqueryparam cfsqltype="CF_SQL_DATE" value="#arguments.event.getValue('fEntDateFrom')#">;
				</cfif>
				<cfif arguments.event.getTrimValue('fEntDateTo','') NEQ ''>
					SET @dateEnteredTo = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.event.getValue('fEntDateTo')# 23:59:59.997">;
				</cfif>
				<cfif arguments.event.getTrimValue('fKeyword','') NEQ ''>
					SET @description = replace(<cfqueryparam value="#arguments.event.getTrimValue('fKeyword')#" cfsqltype="CF_SQL_VARCHAR">,'_','\_');
				</cfif>
				<cfif arguments.event.getTrimValue('fQuantityFrom','') NEQ ''>
					SET @quantityFrom = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getTrimValue('fQuantityFrom')#">;
				</cfif>
				<cfif arguments.event.getTrimValue('fQuantityTo','') NEQ ''>
					SET @quantityTo = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getTrimValue('fQuantityTo')#">;
				</cfif>
				<cfif arguments.event.getTrimValue('fAmtFrom','') NEQ ''>
					SET @dollarAmtFrom = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#abs(rereplace(arguments.event.getTrimValue('fAmtFrom'),'[^\d.]+','','ALL'))#">;
				</cfif>
				<cfif arguments.event.getTrimValue('fAmtTo','') NEQ ''>
					SET @dollarAmtTo = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#abs(rereplace(arguments.event.getTrimValue('fAmtTo'),'[^\d.]+','','ALL'))#">;
				</cfif>
				<cfif arguments.event.getValue('LimitToMemberID',0) gt 0>
					SET @limitToMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('LimitToMemberID')#">;
				</cfif>
				<cfif arguments.event.getValue('fSelectMemberID',0) gt 0>
					SET @enteredByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('fSelectMemberID')#">;
				</cfif>
				<cfif arguments.event.getValue('fLinkedMemberID',0) gt 0>
					SET @fLinkedMemberID = <cfqueryparam value="#arguments.event.getValue('fLinkedMemberID')#" cfsqltype="CF_SQL_INTEGER">;
				</cfif>
				<cfif arguments.event.getValue('fLinkedGroupID',0) gt 0>
					SET @fLinkedGroupID = <cfqueryparam value="#arguments.event.getValue('fLinkedGroupID')#" cfsqltype="CF_SQL_INTEGER">;
				</cfif>
				<cfif arguments.event.getValue('fAssignedMemberID',0) gt 0>
					SET @fAssignedMemberID = <cfqueryparam value="#arguments.event.getValue('fAssignedMemberID')#" cfsqltype="CF_SQL_INTEGER">;
				</cfif>
				<cfif arguments.event.getValue('fAssignedGroupID',0) gt 0>
					SET @fAssignedGroupID = <cfqueryparam value="#arguments.event.getValue('fAssignedGroupID')#" cfsqltype="CF_SQL_INTEGER">;
				</cfif>

				IF OBJECT_ID('tempdb..##tblMemHistorySearch') IS NOT NULL 
					DROP TABLE ##tblMemHistorySearch;
				CREATE TABLE ##tblMemHistorySearch (historyID int PRIMARY KEY);

				INSERT INTO ##tblMemHistorySearch
				select distinct mh.historyID
				from dbo.ams_memberHistory as mh
				inner join dbo.cms_categories as cP on cP.categoryID = mh.categoryID
				inner join dbo.cms_categoryTrees as cPt on cPt.categoryTreeID = cP.categoryTreeID
				inner join dbo.ams_members as m on m.memberID = mh.memberID and m.orgID = @orgID
				inner join dbo.ams_members as mActive on mActive.memberID = m.activeMemberID and mActive.orgID = @orgID
				<cfif arguments.event.getValue('fAssignedMemberID',0) gt 0>
					and mActive.memberID = @fAssignedMemberID
				<cfelseif arguments.event.getValue('fAssignedGroupID',0) gt 0>
					inner join dbo.cache_members_groups as mgAssigned on mgAssigned.memberID = mActive.memberID 
						and mgAssigned.groupid = @fAssignedGroupID
				</cfif>
				inner join dbo.ams_members as mEntered on mEntered.memberID = mh.enteredByMemberID and mEntered.orgID in (@orgID,1)
				inner join dbo.ams_members as mEnteredActive on mEnteredActive.memberID = mEntered.activeMemberID and mEnteredActive.orgID  = mEntered.orgID 
				left outer join dbo.cms_categories cg2 on cg2.categoryID = mh.subCategoryID
				<cfif arguments.event.getValue('fLinkedMemberID',0) gt 0>
					inner join dbo.ams_members as mLink on mLink.memberID = mh.linkMemberID and mLink.orgID = @orgID
					inner join dbo.ams_members as mLinkActive on mLinkActive.memberID = mLink.activeMemberID
						and mLinkActive.memberID = @fLinkedMemberID and mLinkActive.orgID = @orgID
				<cfelseif arguments.event.getValue('fLinkedGroupID',0) gt 0>
					inner join dbo.ams_members as mLink on mLink.memberID = mh.linkMemberID and mLink.orgID = @orgID
					inner join dbo.ams_members as mLinkActive on mLinkActive.memberID = mLink.activeMemberID and mLinkActive.orgID = @orgID
					inner join dbo.cache_members_groups as mgLinked on mgLinked.memberID = mLinkActive.memberID 
						and mgLinked.groupid = @fLinkedGroupID
				<cfelse>
					left outer join dbo.ams_members as mLink 
						inner join dbo.ams_members as mLinkActive on mLinkActive.memberID = mLink.activeMemberID and mLinkActive.orgID = @orgID
						on mLink.memberID = mh.linkMemberID and mLink.orgID = @orgID
				</cfif>
				where cPt.siteID = @siteID
				and mh.typeID = @typeID
				and mh.status = 'A'
				and mh.siteID = @siteID
				<cfif arguments.event.getValue('chkall',0) is 0 and len(arguments.event.getTrimValue('historyIDList','')) and arrayLen(reMatch("[^0-9,]",arguments.event.getTrimValue('historyIDList'))) is 0>
					and mh.historyID in (#arguments.event.getTrimValue('historyIDList')#)
				<cfelse>
					<cfif arguments.event.getValue('chkall',0) is 1 and len(arguments.event.getTrimValue('notHistoryIDList','')) and arrayLen(reMatch("[^0-9,]",arguments.event.getTrimValue('notHistoryIDList'))) is 0>
						and mh.historyID not in (#arguments.event.getTrimValue('notHistoryIDList')#)
					</cfif>
					<cfif local.hmCategoryID gt 0>
						and mh.categoryID = @categoryID
					</cfif>
					<cfif local.hmSubCategoryID gt 0>
						and mh.subCategoryID = @subCategoryID
					</cfif>
					<cfif arguments.event.getTrimValue('fDateFrom','') NEQ ''>
						and mh.userDate >= @userDateFrom
					</cfif>
					<cfif arguments.event.getTrimValue('fDateTo','') NEQ ''>
						and mh.userDate <= @userDateTo
					</cfif>
					<cfif arguments.event.getTrimValue('fEndDateFrom','') NEQ ''>
						and mh.userEndDate >= @userEndDateFrom
					</cfif>
					<cfif arguments.event.getTrimValue('fEndDateTo','') NEQ ''>
						and mh.userEndDate <= @userEndDateTo
					</cfif>
					<cfif arguments.event.getTrimValue('fEntDateFrom','') NEQ ''>
						and mh.dateEntered >= @dateEnteredFrom
					</cfif>
					<cfif arguments.event.getTrimValue('fEntDateTo','') NEQ ''>
						and mh.dateEntered <= @dateEnteredTo
					</cfif>
					<cfif arguments.event.getTrimValue('fKeyword','') NEQ ''>
						AND mh.description like '%'+@description+'%' ESCAPE('\')
					</cfif>
					<cfif arguments.event.getTrimValue('fQuantityFrom','') NEQ ''>
						and mh.quantity >= @quantityFrom
					</cfif>
					<cfif arguments.event.getTrimValue('fQuantityTo','') NEQ ''>
						and mh.quantity <= @quantityTo
					</cfif>
					<cfif arguments.event.getTrimValue('fAmtFrom','') NEQ ''>
						and mh.dollarAmt >= @dollarAmtFrom
					</cfif>
					<cfif arguments.event.getTrimValue('fAmtTo','') NEQ ''>
						and mh.dollarAmt <= @dollarAmtTo
					</cfif>
					<cfif arguments.event.getValue('LimitToMemberID',0) gt 0>
						<cfif arguments.event.getValue('fMHLink','A') eq "HO">
							and mActive.memberid = @limitToMemberID
						<cfelseif arguments.event.getValue('fMHLink','A') eq "LO">
							and mLinkActive.memberid = @limitToMemberID
						<cfelse>
							and (mActive.memberid = @limitToMemberID or mLinkActive.memberid = @limitToMemberID)
						</cfif>
					</cfif>
					<cfif arguments.event.getValue('fSelectMemberID',0) gt 0>
						and mEnteredActive.memberID = @enteredByMemberID
					</cfif>
					<cfif arguments.event.getValue('mhRecipients','') NEQ '' AND arguments.event.getValue('mhRecipients') EQ 'linked'>
						and mLink.memberID is not null 
					</cfif>
				</cfif>;

				<cfif arguments.mode is 'mhTabGrid'>
					DECLARE @posStart int, @posStartAndCount int, @totalCount int;
					SET @posStart = <cfqueryparam value="#arguments.event.getValue('posStart')#" cfsqltype="CF_SQL_INTEGER">;
					SET @posStartAndCount = @posStart + <cfqueryparam value="#arguments.event.getValue('count')#" cfsqltype="CF_SQL_INTEGER">;

					IF OBJECT_ID('tempdb..##tblMemberHistory') IS NOT NULL 
						DROP TABLE ##tblMemberHistory;
					CREATE TABLE ##tblMemberHistory (historyID int, userDate datetime, userEndDate datetime, quantity int, dollarAmt decimal(14,2), description varchar(max), 
						dateEntered datetime, memberID int, memberName varchar(300), memberCompany varchar(200), enteredByMemberName varchar(300), linkMemberID int, 
						linkMemberName varchar(300), linkMemberCompany varchar(200), typeName varchar(200), categoryName varchar(200), catSiteResourceID int, row int);

					INSERT INTO ##tblMemberHistory
					select historyID, userDate, userEndDate, quantity, dollarAmt, description, dateEntered, memberID, memberName, memberCompany, 
						EnteredByMemberName, linkMemberID, linkMemberName, linkMemberCompany, typeName, categoryName, siteResourceID,
						ROW_NUMBER() OVER (ORDER BY #preserveSingleQuotes(local.orderby)#, userDate desc, historyID desc) as row
					from (
						select mh.historyID, mh.userDate, mh.userEndDate, mh.quantity, mh.dollarAmt, mh.description, mh.dateEntered,
							mActive.memberID, mActive.lastName + ', ' + mActive.firstName + ' (' + mActive.memberNumber + ')' as memberName, mActive.company as memberCompany, 
							mEnteredActive.lastName + ', ' + mEnteredActive.firstName + ' (' + mEnteredActive.memberNumber + ')' as EnteredByMemberName, mLinkActive.memberID as linkMemberID, 
							mLinkActive.lastName + ', ' + mLinkActive.firstName + ' (' + mLinkActive.memberNumber + ')' as linkMemberName, mLinkActive.company as linkMemberCompany, 
							cP.categoryName as typeName, cg2.categoryName as categoryName, cP.siteResourceID
						from ##tblMemHistorySearch as tmp
						inner join dbo.ams_memberHistory as mh on mh.historyID = tmp.historyID
							and mh.siteID = @siteID
						inner join dbo.cms_categories as cP on cP.categoryID = mh.categoryID
						inner join dbo.cms_categoryTrees as cPt on cPt.categoryTreeID = cP.categoryTreeID
						inner join dbo.ams_members as m on m.memberID = mh.memberID and m.orgID = @orgID
						inner join dbo.ams_members as mActive on mActive.memberID = m.activeMemberID and mActive.orgID = @orgID
						inner join dbo.ams_members as mEntered on mEntered.memberID = mh.enteredByMemberID and mEntered.orgID in (@orgID,1)
						inner join dbo.ams_members as mEnteredActive on mEnteredActive.memberID = mEntered.activeMemberID and mEnteredActive.orgID = mEntered.orgID
						left outer join dbo.cms_categories cg2 on cg2.categoryID = mh.subCategoryID
						left outer join dbo.ams_members as mLink 
							inner join dbo.ams_members as mLinkActive on mLinkActive.memberID = mLink.activeMemberID and mLinkActive.orgID = @orgID
							on mLink.memberID = mh.linkMemberID and mLink.orgID = @orgID
					) tmpOuter;

					select @totalCount = @@ROWCOUNT;

					select *, dbo.fn_cache_perms_getResourceRightsXML(catSiteResourceID,@currentMemberID,@siteID) as rightsXML, @totalCount as totalCount
					from ##tblMemberHistory
					where row > @posStart
					and row <= @posStartAndCount
					order by row;

					IF OBJECT_ID('tempdb..##tblMemberHistory') IS NOT NULL 
						DROP TABLE ##tblMemberHistory;
				<cfelseif arguments.mode is 'mhTabEmailGrid'>
					DECLARE @posStart int, @posStartAndCount int, @emailTagTypeID int, @totalCount int;
					SET @posStart = <cfqueryparam value="#arguments.event.getValue('posStart')#" cfsqltype="CF_SQL_INTEGER">;
					SET @posStartAndCount = @posStart + <cfqueryparam value="#arguments.event.getValue('count')#" cfsqltype="CF_SQL_INTEGER">;
					SET @emailTagTypeID = <cfqueryparam value="#arguments.event.getValue('emailTagType',0)#" cfsqltype="CF_SQL_INTEGER">;

					IF OBJECT_ID('tempdb..##tblMemberHistory') IS NOT NULL 
						DROP TABLE ##tblMemberHistory;
					CREATE TABLE ##tblMemberHistory (historyID int, memberID int, memberFirstname varchar(75), memberLastname varchar(75), membernumber varchar(50),
						memberCompany varchar(200), memberEmail varchar(400), linkMemberID int, linkMemberFirstname varchar(75), linkMemberLastname varchar(75), 
						linkMembernumber varchar(50), linkMemberCompany varchar(200), linkMemberEmail varchar(400), typeName varchar(200), categoryName varchar(200), row int);

					INSERT INTO ##tblMemberHistory
					select distinct mh.historyID, mActive.memberID, mActive.firstname, mActive.lastname, mActive.membernumber, mActive.company, me.email, 
						mLinkActive.memberID, mLinkActive.firstname, mLinkActive.lastname, mLinkActive.membernumber, mLinkActive.company, linkMemEmail.email, 
						cP.categoryName as typeName, cg2.categoryName as categoryName, ROW_NUMBER() OVER (ORDER BY #local.orderby#) as row
					from ##tblMemHistorySearch as tmp
					inner join dbo.ams_memberHistory as mh on mh.historyID = tmp.historyID
						and mh.siteID = @siteID
					inner join dbo.cms_categories as cP on cP.categoryID = mh.categoryID
					inner join dbo.cms_categoryTrees as cPt on cPt.categoryTreeID = cP.categoryTreeID
					inner join dbo.ams_members as m ON m.memberID = mh.memberID and m.orgID = @orgID
					inner join dbo.ams_members as mActive on mActive.memberID = m.activeMemberID  and mActive.orgID = @orgID
					left outer join dbo.cms_categories cg2 on cg2.categoryID = mh.subCategoryID
					inner join dbo.ams_memberEmails as me on me.orgID = @orgID
						and me.memberID = mActive.memberID
					inner join dbo.ams_memberEmailTags as metag on metag.orgID = @orgID 
						and metag.memberID = me.memberID
						and metag.emailTypeID = me.emailTypeID
					inner join dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = @orgID
						and metagt.emailTagTypeID = metag.emailTagTypeID
						and metagt.emailTagTypeID = @emailTagTypeID
					left outer join dbo.ams_members as mLink 
						inner join dbo.ams_members as mLinkActive on mLinkActive.memberID = mLink.activeMemberID and mLinkActive.orgID = @orgID
						on mLink.memberID = mh.linkMemberID and mLink.orgID = @orgID
					left outer join dbo.ams_memberEmails as linkMemEmail
						inner join dbo.ams_memberEmailTags as linkMemEmailTag on linkMemEmailTag.orgID = @orgID 
							and linkMemEmailTag.memberID = linkMemEmail.memberID
							and linkMemEmailTag.emailTypeID = linkMemEmail.emailTypeID
						inner join dbo.ams_memberEmailTagTypes as linkMemEmailTagT on linkMemEmailTagT.orgID = @orgID
							and linkMemEmailTagT.emailTagTypeID = linkMemEmailTag.emailTagTypeID 
							and linkMemEmailTagT.emailTagTypeID = @emailTagTypeID
						on linkMemEmail.orgID = @orgID
						and linkMemEmail.memberID = mLinkActive.memberID;

					select @totalCount = @@ROWCOUNT;

					select *, @totalCount as totalCount
					from ##tblMemberHistory
					where row > @posStart
					and row <= @posStartAndCount
					order by row;

					IF OBJECT_ID('tempdb..##tblMemberHistory') IS NOT NULL 
						DROP TABLE ##tblMemberHistory;
				<cfelseif arguments.mode is 'mhTabEmail'>
					DECLARE @membersWithEmail int, @linkMembersWithEmail int, @emailTagTypeID int;
					SET @emailTagTypeID = <cfqueryparam value="#arguments.event.getValue('emailTagType',0)#" cfsqltype="CF_SQL_INTEGER">;

					select @membersWithEmail = count(*)
					from ##tblMemHistorySearch as tmp
					inner join dbo.ams_memberHistory as mh on mh.historyID = tmp.historyID
						and mh.siteID = @siteID
					inner join dbo.ams_members as m ON m.memberID = mh.memberID and m.orgID = @orgID
					inner join dbo.ams_members as mActive on mActive.memberID = m.activeMemberID  and mActive.orgID = @orgID
					inner join dbo.ams_memberEmails as me on me.orgID = @orgID
						and me.memberID = mActive.memberID
						and me.email <> ''
					inner join dbo.ams_memberEmailTags as metag on metag.orgID = @orgID 
						and metag.memberID = me.memberID
						and metag.emailTypeID = me.emailTypeID
					inner join dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = @orgID
						and metagt.emailTagTypeID = metag.emailTagTypeID
						and metagt.emailTagTypeID = @emailTagTypeID;

					select @linkMembersWithEmail = count(*)
					from ##tblMemHistorySearch as tmp
					inner join dbo.ams_memberHistory as mh on mh.historyID = tmp.historyID
						and mh.siteID = @siteID
					inner join dbo.ams_members as m ON m.memberID = mh.linkMemberID and m.orgID = @orgID
					inner join dbo.ams_members as mActive on mActive.memberID = m.activeMemberID and mActive.orgID = @orgID
					inner join dbo.ams_memberEmails as me on me.orgID = @orgID
						and me.memberID = mActive.memberID
						and me.email <> ''
					inner join dbo.ams_memberEmailTags as metag on metag.orgID = @orgID
						and metag.memberID = me.memberID
						and metag.emailTypeID = me.emailTypeID
					inner join dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = @orgID
						and metagt.emailTagTypeID = metag.emailTagTypeID
						and metagt.emailTagTypeID = @emailTagTypeID;

					select historyID, @membersWithEmail as membersWithEmail, @linkMembersWithEmail as linkMembersWithEmail
					from ##tblMemHistorySearch;
				<cfelseif arguments.mode is 'deleteMH'>
					DECLARE @functionID int, @groupPrintID int;
					set @functionID = <cfqueryparam value="#local.DeleteEntryMemberHistoryCategoryRFID#" cfsqltype="CF_SQL_INTEGER">;
					select @groupPrintID = groupPrintID from dbo.ams_members where memberID = @currentMemberID;

					select tmp.historyID
					from ##tblMemHistorySearch as tmp
					inner join dbo.ams_memberHistory as mh on mh.historyID = tmp.historyID
						and mh.siteID = @siteID
					inner join dbo.cms_categories as cP on cP.categoryID = mh.categoryID
					inner join dbo.cache_perms_siteResourceFunctionRightPrints as srfrp on srfrp.siteID = @siteID
						and srfrp.siteResourceID = cP.siteResourceID
						and srfrp.functionID = @functionID
					inner join dbo.cache_perms_groupPrintsRightPrints as gprp on gprp.siteID = @siteID
						and gprp.rightPrintID = srfrp.rightPrintID
						and gprp.groupPrintID = @groupPrintID;
				<cfelseif arguments.mode is 'recategorizeMH'>
					DECLARE @functionID int, @groupPrintID int;
					set @functionID = <cfqueryparam value="#local.EditEntryMemberHistoryCategoryRFID#" cfsqltype="CF_SQL_INTEGER">;
					select @groupPrintID = groupPrintID from dbo.ams_members where memberID = @currentMemberID;

					select tmp.historyID
					from ##tblMemHistorySearch as tmp
					inner join dbo.ams_memberHistory as mh on mh.historyID = tmp.historyID
						and mh.siteID = @siteID
					inner join dbo.cms_categories as cP on cP.categoryID = mh.categoryID
					inner join dbo.cache_perms_siteResourceFunctionRightPrints as srfrp on srfrp.siteID = @siteID
						and srfrp.siteResourceID = cP.siteResourceID
						and srfrp.functionID = @functionID
					inner join dbo.cache_perms_groupPrintsRightPrints as gprp on gprp.siteID = @siteID
						and gprp.rightPrintID = srfrp.rightPrintID
						and gprp.groupPrintID = @groupPrintID;
				<cfelseif arguments.mode is 'recategorizeMHEntryCount'>
					<cfset local.parentChildCategoryID = arguments.event.getValue('parentChildReCategorizeCategoryID','')>
					<cfset local.newCategoryID = 0>
					<cfset local.newSubCategoryID = 0>
					<cfif listLen(local.parentChildCategoryID,'_') eq 2>
						<cfset local.newCategoryID = val(getToken(local.parentChildCategoryID, 1,'_'))>
						<cfset local.newSubCategoryID = val(getToken(local.parentChildCategoryID, 2,'_'))>
					<cfelse>
						<cfset local.newCategoryID = val(local.parentChildCategoryID)>
					</cfif>

					DECLARE @functionID int, @groupPrintID int;
					set @functionID = <cfqueryparam value="#local.EditEntryMemberHistoryCategoryRFID#" cfsqltype="CF_SQL_INTEGER">;
					select @groupPrintID = groupPrintID from dbo.ams_members where memberID = @currentMemberID;

					select count(tmp.historyID) as reCatMHEntryCount
					from ##tblMemHistorySearch as tmp
					inner join dbo.ams_memberHistory as mh on mh.historyID = tmp.historyID
						and mh.siteID = @siteID
					inner join dbo.cms_categories as cP on cP.categoryID = mh.categoryID
					inner join dbo.cache_perms_siteResourceFunctionRightPrints as srfrp on srfrp.siteID = @siteID
						and srfrp.siteResourceID = cP.siteResourceID
						and srfrp.functionID = @functionID
					inner join dbo.cache_perms_groupPrintsRightPrints as gprp on gprp.siteID = @siteID
						and gprp.rightPrintID = srfrp.rightPrintID
						and gprp.groupPrintID = @groupPrintID
					where (
						mh.categoryID <> <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.newCategoryID#">
						<cfif local.newSubCategoryID>
							OR ISNULL(mh.subCategoryID,0) <> <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.newSubCategoryID#">
						<cfelse>
							OR mh.subCategoryID IS NOT NULL
						</cfif>
					);
				<cfelseif arguments.mode is 'analyzeMH'>
					select categoryName, subCategoryName, count(*) as memberCount, sum(quantity) as quantity, sum(dollarAmt) as dollarAmt
					from (
						select mh.historyID, mh.quantity, isnull(mh.dollarAmt,0) as dollarAmt, cP.categoryName, cg2.categoryName as subCategoryName
						from ##tblMemHistorySearch as tmp
						inner join dbo.ams_memberHistory as mh on mh.historyID = tmp.historyID
							and mh.siteID = @siteID
						inner join dbo.cms_categories as cP on cP.categoryID = mh.categoryID
						left outer join dbo.cms_categories cg2 on cg2.categoryID = mh.subCategoryID
					)  as x
					group by categoryName, subCategoryName
					order by categoryName, subCategoryName;
				<cfelse>
					select historyID
					from ##tblMemHistorySearch;
				</cfif>

				IF OBJECT_ID('tempdb..##tblMemHistorySearch') IS NOT NULL 
					DROP TABLE ##tblMemHistorySearch;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfreturn local.qryMemHistory>
	</cffunction>

	<cffunction name="getMemHistoryForEmailing" access="public" output="false" returntype="struct">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="historyID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.retStruct = structNew()>

		<cfset local.retStruct.qryData = getMemberHistoryDetails(siteID=arguments.siteID, historyID=arguments.historyID)>
		<cfset local.retStruct.extendedLinkedMergeCode = "linked">
		<cfset local.retStruct.arrResTypeMergeCodes = arrayNew(1)>

		<cfset local.tmpStr = { mergeCode="MHDescription", isDateField=false, isAmountField=false, columnName="description" }>
		<cfset arrayAppend(local.retStruct.arrResTypeMergeCodes,local.tmpStr)>

		<cfset local.tmpStr = { mergeCode="MHQuantity", isDateField=false, isAmountField=false, columnName="quantity" }>
		<cfset arrayAppend(local.retStruct.arrResTypeMergeCodes,local.tmpStr)>

		<cfset local.tmpStr = { mergeCode="MHAmount", isDateField=false, isAmountField=true, columnName="dollarAmt" }>
		<cfset arrayAppend(local.retStruct.arrResTypeMergeCodes,local.tmpStr)>

		<cfset local.tmpStr = { mergeCode="MHStartDate", isDateField=true, isAmountField=false, columnName="userDate" }>
		<cfset arrayAppend(local.retStruct.arrResTypeMergeCodes,local.tmpStr)>

		<cfset local.tmpStr = { mergeCode="MHEndDate", isDateField=true, isAmountField=false, columnName="userEndDate" }>
		<cfset arrayAppend(local.retStruct.arrResTypeMergeCodes,local.tmpStr)>

		<cfset local.tmpStr = { mergeCode="MHCategory", isDateField=false, isAmountField=false, columnName="categoryName" }>
		<cfset arrayAppend(local.retStruct.arrResTypeMergeCodes,local.tmpStr)>

		<cfreturn local.retStruct>
	</cffunction>

	<cffunction name="getMemberHistoryDetails" access="private" output="no" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="historyID" type="numeric" required="yes">

		<cfset var qryMemHistory = "">

		<cfquery name="qryMemHistory" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select mh.historyID, mh.memberID, mh.categoryID, mh.linkMemberID, mh.description, 
				mh.quantity, mh.dollarAmt, mh.userDate, mh.userEndDate,
				case when cg2.categoryName is not null then cg2.categoryName else cP.categoryName end as categoryName
			from dbo.ams_memberHistory as mh 
			inner join dbo.cms_categories as cP on cP.categoryID = mh.categoryID
			left outer join dbo.cms_categories cg2 on cg2.categoryID = mh.subCategoryID
			where mh.historyID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.historyID#">
			and mh.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryMemHistory>
	</cffunction>

	<cffunction name="getFilteredMemHistoryForEmailing" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any" required="true">
		<cfargument name="recipientMode" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.retStruct = { itemIDList='', toolType='MemberHistoryAdmin', catTreeCode='ETHISTORY', extendedLinkedMergeCode='linked', 
									memberIDLabel='historyMemberID', memberNumberLabel='historyMemberNumber', linkedMemberIDLabel='historyLinkMemberID', 
									linkedMemberNumberLabel='historyLinkMemberNumber', extraMergeTagList='', errorCode='' }>

		<cfset local.qryMemHistory = getMemberHistoryFromFilters(event=arguments.event, mode='mhTabEmail')>

		<cfif local.qryMemHistory.recordcount is 0>
			<cfset local.retStruct.errorCode = 'norecipient'>
			<cfreturn local.retStruct>
		<cfelse>
			<cfset local.retStruct.itemIDList = valueList(local.qryMemHistory.historyID)>
		</cfif>

		<!--- no email ids defined --->
		<cfif (arguments.recipientMode eq 'both' and val(local.qryMemHistory.membersWithEmail) + val(local.qryMemHistory.linkMembersWithEmail) is 0) OR 
				(arguments.recipientMode eq 'member' and val(local.qryMemHistory.membersWithEmail) is 0) OR
				(arguments.recipientMode eq 'linked' and val(local.qryMemHistory.linkMembersWithEmail) is 0)>
			<cfset local.retStruct.errorCode = 'noemailrecipient'>
			<cfreturn local.retStruct>
		</cfif>

		<cfreturn local.retStruct>
	</cffunction>

	<cffunction name="getMemberHistoryBackupFromFilters" access="public" output="false" returntype="query">
		<cfargument name="Event" type="any" required="true">
		<cfargument name="mode" type="string" required="true">

		<cfset var local = structNew()>
		
		<cfif arguments.mode is 'mhBackupTable'>
			<cfset local.arrCols = arrayNew(1)>
			<cfset arrayAppend(local.arrCols,"dateEntered #arguments.event.getValue('orderDir')#")>
			<cfset arrayAppend(local.arrCols,"runByMemberName #arguments.event.getValue('orderDir')#")>
			<cfset arrayAppend(local.arrCols,"action #arguments.event.getValue('orderDir')#")>
			<cfset local.orderby = local.arrcols[arguments.event.getValue('orderby')+1]>
		<cfelse>
			<cfset local.orderby = "dateEntered desc">
		</cfif>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryMemHistoryBackups">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				IF OBJECT_ID('tempdb..##tblMemHistoryBackups') IS NOT NULL 
					DROP TABLE ##tblMemHistoryBackups;
				CREATE TABLE ##tblMemHistoryBackups (backupID int PRIMARY KEY, documentID int, dateEntered datetime, runByMemberID int, 
					runByMemberName varchar(300), runByMemberCompany varchar(200), runByMemberOrgID int, action varchar(50), row int);

				DECLARE @orgID int, @siteID int, @typeID int, @dateEnteredFrom date, @dateEnteredTo datetime, @totalCount int;

				SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteID')#">;
				SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgID')#">;
				SET @typeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('typeID',1)#">;
				<cfif arguments.event.getTrimValue('fBackUpDateFrom','') NEQ ''>
					SET @dateEnteredFrom = <cfqueryparam cfsqltype="CF_SQL_DATE" value="#arguments.event.getValue('fBackUpDateFrom')#">;
				</cfif>
				<cfif arguments.event.getTrimValue('fBackUpDateTo','') NEQ ''>
					SET @dateEnteredTo = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.event.getValue('fBackUpDateTo')# 23:59:59.997">;
				</cfif>

				INSERT INTO ##tblMemHistoryBackups (backupID, documentID, dateEntered, runByMemberID, runByMemberName, runByMemberCompany, runByMemberOrgID, action, row)
				select backupID, documentID, dateEntered, runByMemberID, runByMemberName, runByMemberCompany, runByMemberOrgID, action,
					ROW_NUMBER() OVER (ORDER BY #local.orderBy#)
				from (
					select mhb.backupID, mhb.documentID, mhb.dateEntered, mActive.memberID as runByMemberID, mActive.company as runByMemberCompany, mActive.orgID as runByMemberOrgID,
						mActive.lastName + ', ' + mActive.firstName + ' (' + mActive.memberNumber + ')' as runByMemberName, 
						case when mhb.action = 'delete' then 'Mass Delete' when action = 'recat' then 'Recategorization' else '' end as action
					from platformStatsMC.dbo.ams_memberHistoryBackups as mhb
					inner join dbo.ams_members as m on m.memberID = mhb.runByMemberID and m.orgID in (1,@orgID)
					inner join dbo.ams_members as mActive on mActive.memberID = m.activeMemberID and mActive.orgID in (1,@orgID)
					where mhb.siteID = @siteID
					and mhb.historyTypeID = @typeID
					<cfif arguments.event.getTrimValue('fBackUpDateFrom','') NEQ ''>
						and mhb.dateEntered >= @dateEnteredFrom
					</cfif>
					<cfif arguments.event.getTrimValue('fBackUpDateTo','') NEQ ''>
						and mhb.dateEntered <= @dateEnteredTo
					</cfif>
					<cfif arguments.event.getTrimValue('fBackupAct','') NEQ ''>
						and mhb.action = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getTrimValue('fBackupAct')#">
					</cfif>
				) tmp;
				
				SET @totalCount = @@ROWCOUNT;

				<cfif arguments.mode is 'mhBackupTable'>
					DECLARE @posStart int, @posStartAndCount int;
					SET @posStart = <cfqueryparam value="#arguments.event.getValue('posStart')#" cfsqltype="CF_SQL_INTEGER">;
					SET @posStartAndCount = @posStart + <cfqueryparam value="#arguments.event.getValue('count')#" cfsqltype="CF_SQL_INTEGER">;

					select backupID, documentID, dateEntered, runByMemberID, runByMemberName, runByMemberCompany, runByMemberOrgID, action, row, @totalCount as totalCount
					from ##tblMemHistoryBackups
					where row > @posStart
					and row <= @posStartAndCount
					order by row;
				<cfelse>
					select backupID
					from ##tblMemHistoryBackups;
				</cfif>

				IF OBJECT_ID('tempdb..##tblMemHistoryBackups') IS NOT NULL 
					DROP TABLE ##tblMemHistoryBackups;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfreturn local.qryMemHistoryBackups>
	</cffunction>

</cfcomponent>