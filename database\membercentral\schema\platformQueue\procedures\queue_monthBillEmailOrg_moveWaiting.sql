ALTER PROC dbo.queue_monthBillEmailOrg_moveWaiting

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @BillqueueTypeID int, @BillstatusWaiting int, @BillstatusReady int, @SWqueueTypeID int, @SWstatusDone int, 
		@TSRqueueTypeID int, @TSRstatusDone int, @TSAqueueTypeID int, @TSAstatusDone int, @billItemID int, 
		@checkbillingPeriodID int, @checkisOrg bit;
	select @BillqueueTypeID = queueTypeID from dbo.tblQueueTypes where queueType = 'monthBillEmailOrg';
	select @BillstatusWaiting = queuestatusID from dbo.tblQueueStatuses where queueTypeID = @BillqueueTypeID and queueStatus = 'waitingToProcess';
	select @BillstatusReady = queuestatusID from dbo.tblQueueStatuses where queueTypeID = @BillqueueTypeID and queueStatus = 'readyToProcess';
	select @SWqueueTypeID = queueTypeID from dbo.tblQueueTypes where queueType = 'monthBillSW';
	select @SWstatusDone = queuestatusID from dbo.tblQueueStatuses where queueTypeID = @SWqueueTypeID and queueStatus = 'done';
	select @TSRqueueTypeID = queueTypeID from dbo.tblQueueTypes where queueType = 'monthBillTSRoyalty';
	select @TSRstatusDone = queuestatusID from dbo.tblQueueStatuses where queueTypeID = @TSRqueueTypeID and queueStatus = 'done';
	select @TSAqueueTypeID = queueTypeID from dbo.tblQueueTypes where queueType = 'monthBillTSA';
	select @TSAstatusDone = queuestatusID from dbo.tblQueueStatuses where queueTypeID = @TSAqueueTypeID and queueStatus = 'done';

	-- look at anything waitingToProcess to see if any are readyToProcess
	select @checkbillingPeriodID = MIN(billingPeriodID) from dbo.queue_monthBillEmailOrg where statusID = @BillstatusWaiting;
	while @checkbillingPeriodID is not null begin

		IF NOT EXISTS (select top 1 itemID from dbo.queue_monthBillSW where billingPeriodID = @checkbillingPeriodID and statusID <> @SWstatusDone)
		AND NOT EXISTS (select top 1 itemID from dbo.queue_monthBillTSRoyalty where billingPeriodID = @checkbillingPeriodID and statusID <> @TSRstatusDone)
		AND NOT EXISTS (select top 1 itemID from dbo.queue_monthBillTSA where billingPeriodID = @checkbillingPeriodID and isOrg = 1 and statusID <> @TSAstatusDone)
			UPDATE dbo.queue_monthBillEmailOrg
			SET statusID = @BillstatusReady,
				dateUpdated = getdate()
			WHERE billingPeriodID = @checkbillingPeriodID;

		select @checkbillingPeriodID = MIN(billingPeriodID) from dbo.queue_monthBillEmailOrg where statusID = @BillstatusWaiting and billingPeriodID > @checkbillingPeriodID;
	end

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
