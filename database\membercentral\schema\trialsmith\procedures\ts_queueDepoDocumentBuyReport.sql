ALTER PROC dbo.ts_queueDepoDocumentBuyReport 
@itemCount int OUTPUT
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpDepoMembers') IS NOT NULL 
		DROP TABLE #tmpDepoMembers;
	IF OBJECT_ID('tempdb..#tmpDepoCreditMembers') IS NOT NULL 
		DROP TABLE #tmpDepoCreditMembers;
	IF OBJECT_ID('tempdb..#tmpCreditUsedDepoMembers') IS NOT NULL 
		DROP TABLE #tmpCreditUsedDepoMembers;
	CREATE TABLE #tmpDepoMembers (depomemberdataID int PRIMARY KEY);
	CREATE TABLE #tmpDepoCreditMembers (depomemberdataID int PRIMARY KEY);
	CREATE TABLE #tmpCreditUsedDepoMembers (depomemberdataID int);

	DECLARE @queueTypeID int, @statusReady int, @uploadedStatusID int, @nowDate datetime = GETDATE(), 
		@7Daysago datetime = DATEADD(DAY,-7,GETDATE()), @14Daysago datetime = DATEADD(DAY,-14,GETDATE()), @180Daysago datetime = DATEADD(DAY,-180,GETDATE());
	SELECT @queueTypeID = queueTypeID FROM platformQueue.dbo.tblQueueTypes WHERE queueType = 'TSDepoDocumentBuyReport';
	SELECT @statusReady = queuestatusID FROM platformQueue.dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID AND queueStatus = 'readyToProcess';
	SELECT @uploadedStatusID = statusID FROM dbo.depoDocumentStatuses WHERE statusName = 'Uploaded';
	SET @itemCount = 0;

	/*
	This needs to get all depoMembers who purchased depositions in the previous week, 
	where the sum of the amount billed was > $0, 
	and who also did not contribute any documents. 
	and the lastTSDepoDocumentBuyReportSent is older than 180 days (or it is null).
	*/
	INSERT INTO #tmpDepoMembers
	SELECT tmp.depoMemberDataID
	FROM (
		SELECT depoMemberDataID 
		FROM dbo.depoTransactions
		WHERE accountCode = '3000' 
		AND DatePurchased BETWEEN @7Daysago AND @nowDate 
		GROUP BY depoMemberDataID 
		HAVING SUM(AmountBilled) > 0 
			EXCEPT
		SELECT DISTINCT doc.DepomemberdataID 
		FROM dbo.depoDocuments doc 
		INNER JOIN dbo.depoDocumentStatusHistory docSH ON docSH.documentID = doc.DocumentID 
			AND docSH.statusID = @uploadedStatusID
			AND docSH.dateEntered BETWEEN @7Daysago AND @nowDate
	) as tmp
	INNER JOIN dbo.depoMemberData AS d ON d.depomemberdataID = tmp.depomemberdataID 
	WHERE d.Email is not null
	AND d.Email <> ''
	AND d.optOutTSMarketing = 0
	AND (d.lastTSDepoDocumentBuyReportSent is null OR lastTSDepoDocumentBuyReportSent < @180Daysago);

	IF @@ROWCOUNT = 0
		GOTO on_done;

	-- get depoMembers whose credits were used in the previous week
	INSERT INTO #tmpCreditUsedDepoMembers
	SELECT DISTINCT depomemberdataID
	FROM dbo.PurchaseCredits
	WHERE CreditDate BETWEEN @14Daysago AND @nowDate
	AND (PurchaseCreditAmount <= 0 OR AmazonBucksCreditAmount <= 0);

	IF @@ROWCOUNT > 0 BEGIN
		-- DepoCredits
		INSERT INTO #tmpDepoCreditMembers
		SELECT DISTINCT pc.depomemberdataID
		FROM dbo.PurchaseCredits AS pc
		INNER JOIN #tmpCreditUsedDepoMembers AS tmp ON tmp.depomemberdataID = pc.depomemberdataID
		GROUP BY pc.depomemberdataID
		HAVING SUM(pc.purchaseCreditAmount) <= 0
			INTERSECT
		SELECT DISTINCT pc.depomemberdataID
		FROM dbo.PurchaseCredits AS pc
		INNER JOIN #tmpCreditUsedDepoMembers AS tmp ON tmp.depomemberdataID = pc.depomemberdataID
		WHERE pc.purchaseCreditAmount > 0;

		-- AmazonCredits
		INSERT INTO #tmpDepoCreditMembers
		SELECT depomemberdataID
		FROM (
			SELECT DISTINCT pc.depomemberdataID
			FROM dbo.PurchaseCredits AS pc
			INNER JOIN #tmpCreditUsedDepoMembers AS tmp ON tmp.depomemberdataID = pc.depomemberdataID
			GROUP BY pc.depomemberdataID
			HAVING SUM(pc.AmazonBucksCreditAmount) <= 0
				INTERSECT
			SELECT DISTINCT pc.depomemberdataID
			FROM dbo.PurchaseCredits AS pc
			INNER JOIN #tmpCreditUsedDepoMembers AS tmp ON tmp.depomemberdataID = pc.depomemberdataID
			WHERE pc.AmazonBucksCreditAmount > 0
		) tmp
			EXCEPT
		SELECT depomemberdataID FROM #tmpDepoCreditMembers;
	END

    IF NOT EXISTS (
        SELECT TOP 1 tmp.*
        FROM #tmpDepoMembers AS tmp
        LEFT OUTER JOIN #tmpDepoCreditMembers AS tmpc ON tmpc.depomemberdataID = tmp.depomemberdataID
        WHERE tmpc.depomemberdataID IS NULL)
    GOTO on_done;

	-- populate queue
	INSERT INTO platformQueue.dbo.queue_TSDepoDocumentBuyReport (depomemberdataID, FirstName, LastName, Email, statusID, dateAdded, dateUpdated)
	SELECT d.depomemberdataID, d.FirstName, d.LastName, d.Email, @statusReady, @nowDate, @nowDate
	FROM #tmpDepoMembers AS tmp
	INNER JOIN dbo.depoMemberData AS d ON d.depomemberdataID = tmp.depomemberdataID
    LEFT OUTER JOIN #tmpDepoCreditMembers AS tmpc ON tmpc.depomemberdataID = tmp.depomemberdataID
	LEFT OUTER JOIN platformQueue.dbo.queue_TSDepoDocumentBuyReport AS qi ON qi.depomemberdataID = tmp.depomemberdataID
	WHERE tmpc.depomemberdataID IS NULL AND qi.itemID IS NULL;
	SET @itemCount = @@ROWCOUNT;

	IF @itemCount > 0
		EXEC membercentral.dbo.sched_resumeTask @name='TrialSmith Deposition Purchasers Report', @engine='MCLuceeLinux';

	on_done:
	IF OBJECT_ID('tempdb..#tmpDepoMembers') IS NOT NULL 
		DROP TABLE #tmpDepoMembers;
	IF OBJECT_ID('tempdb..#tmpDepoCreditMembers') IS NOT NULL 
		DROP TABLE #tmpDepoCreditMembers;
	IF OBJECT_ID('tempdb..#tmpCreditUsedDepoMembers') IS NOT NULL 
		DROP TABLE #tmpCreditUsedDepoMembers;
	
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
