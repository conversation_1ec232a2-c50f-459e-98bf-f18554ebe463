ALTER PROC dbo.sw_createVideoPreview
@seminarID int,
@baseFileID int,
@timeCodeStart varchar(10),
@timeCodeEnd varchar(10),
@isOnline bit,
@previewID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET @previewID = NULL;

	DECLARE @queueTypeID int, @readyToProcessStatusID int;
	SELECT @queueTypeID = queueTypeID FROM platformQueue.dbo.tblQueueTypes WHERE queueType = 'SWVideoPreview';
	SELECT @readyToProcessStatusID = queueStatusID FROM platformQueue.dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID and queueStatus = 'readyToProcess';

	SELECT @previewID = previewID
	FROM dbo.tblVideoPreviews
	WHERE baseFileID = @baseFileID
	AND ISNULL(seminarID,0) = ISNULL(@seminarID,0);

	IF @previewID IS NULL BEGIN
		BEGIN TRAN;
			INSERT INTO dbo.tblVideoPreviews (seminarID, baseFileID, timeCodeStart, timeCodeEnd, isOnline)
			VALUES (@seminarID, @baseFileID, @timeCodeStart, @timeCodeEnd, @isOnline);
				SELECT @previewID = SCOPE_IDENTITY();

			INSERT INTO platformQueue.dbo.queue_SWVideoPreview (statusID, previewID, seminarID, baseFileID, timeCodeStart, timeCodeEnd, dateAdded, dateUpdated)
			VALUES (@readyToProcessStatusID, @previewID, @seminarID, @baseFileID, @timeCodeStart, @timeCodeEnd, GETDATE(), GETDATE());

			-- resume task
			EXEC memberCentral.dbo.sched_resumeTask @name='SeminarWeb Video Preview', @engine='BERLinux';
		COMMIT TRAN;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
