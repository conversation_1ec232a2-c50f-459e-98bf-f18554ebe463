ALTER PROC dbo.cache_perms_updateGroupPrintsForMembersBulk
@itemGroupUID uniqueidentifier

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @queueTypeID int, @ReadyQueueStatusID int, @ProcessingQueueStatusID int, @itemID int, @orgID int;
	select @queueTypeID = queueTypeID from platformQueue.dbo.tblQueueTypes where queueType = 'MemberGroupPrints';
	select @ReadyQueueStatusID = queuestatusID from platformQueue.dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'readyToProcess';
	select @ProcessingQueueStatusID = queuestatusID from platformQueue.dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'processingItem';

	select top 1 @itemID = itemID, @orgID = orgID from platformQueue.dbo.queue_memberGroupPrints where itemGroupUID = @itemGroupUID and statusID = @ReadyQueueStatusID;
	IF @itemID is null
		GOTO on_done;
	IF dbo.fn_cache_perms_getStatus(@orgID) <> 'enabled' 
		GOTO on_done;

	IF OBJECT_ID('tempdb..#holding_memberGroupPrints') IS NOT NULL
		DROP TABLE #holding_memberGroupPrints;
	IF OBJECT_ID('tempdb..#groupPrintsToProcess') IS NOT NULL
		DROP TABLE #groupPrintsToProcess;
	IF OBJECT_ID('tempdb..#distinctPermissionGroups') IS NOT NULL
		DROP TABLE #distinctPermissionGroups;
	IF OBJECT_ID('tempdb..#tblMembersToUpdateGPMB') IS NOT NULL
		DROP TABLE #tblMembersToUpdateGPMB;
	IF OBJECT_ID('tempdb..#insertedGroupPrints') IS NOT NULL 
		DROP TABLE #insertedGroupPrints;
	CREATE TABLE #holding_memberGroupPrints (autoid int IDENTITY(1,1), orgID int, memberID int PRIMARY KEY, 
		currentGroupPrintID int, newGroupPrintID int, groupList varchar(max), hashGroupList varbinary(16));
	CREATE TABLE #groupPrintsToProcess (autoid int IDENTITY(1,1), orgID int, groupList varchar(max), 
		hashGroupList varbinary(16), groupPrintID int INDEX IX_groupPrintsToProcess_groupPrintID);
	CREATE TABLE #distinctPermissionGroups (groupID int PRIMARY KEY);
	CREATE TABLE #tblMembersToUpdateGPMB (memberID int PRIMARY KEY);
	CREATE TABLE #insertedGroupPrints (groupPrintID int PRIMARY KEY);

	insert into #tblMembersToUpdateGPMB (memberID)
	select memberID
	from platformQueue.dbo.queue_memberGroupPrints
	where itemGroupUID = @itemGroupUID;

	insert into #distinctPermissionGroups (groupID)
	select distinct mg.groupid
	from #tblMembersToUpdateGPMB mu
	inner join dbo.cache_members_groups as mg on mg.orgID = @orgID and mu.memberID = mg.memberID
	inner join cms_siteResourceRightsCache as srrc on mg.groupID = srrc.groupID
	order by mg.groupid;

	insert into #holding_memberGroupPrints (orgID, memberID, currentGroupPrintID, groupList, hashGroupList)
	select m.orgID, m.memberID, m.groupPrintID, dbo.sortedIntList(mg.groupid) as groupList, HASHBYTES('MD5',cast(dbo.sortedIntList(mg.groupid) as varchar(max))) as hashGroupList
	from #tblMembersToUpdateGPMB mu
	inner join dbo.ams_members m on m.memberID = mu.memberID and m.orgID = @orgID
	inner join dbo.cache_members_groups as mg on mu.memberID = mg.memberID and mg.orgID = @orgID
	inner join #distinctPermissionGroups as g on mg.groupID = g.groupID
	group by m.orgID, m.memberID, m.groupPrintID;
	
	update mgp 
	set newGroupPrintID = gp.groupPrintID
	from #holding_memberGroupPrints mgp
	inner join dbo.cache_perms_groupPrints gp on gp.hashGroupList = mgp.hashGroupList;

	insert into #groupPrintsToProcess (orgID, groupList, hashGroupList)
	select distinct orgID, groupList, hashGroupList
	from #holding_memberGroupPrints
	where newGroupPrintID is null;

	-- this proc may be running in snapshot isolation from service broker, so set to read committed for the writes
	IF membercentral.dbo.fn_getCurrentTransactionIsolationLevel() <> 'ReadCommitted'
		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

	UPDATE platformQueue.dbo.queue_memberGroupPrints
	SET statusID = @ProcessingQueueStatusID
	WHERE itemGroupUID = @itemGroupUID;

	insert into dbo.cache_perms_groupPrints (orgID, groupList, hashGroupList, isProcessed)
		OUTPUT inserted.groupPrintID 
		INTO #insertedGroupPrints
	select orgID, groupList, hashGroupList, 0 as isProcessed
	from #groupPrintsToProcess;

	update gpInsert 
	set gpInsert.groupPrintID = igp.groupPrintID
	from #insertedGroupPrints igp
	inner join dbo.cache_perms_groupPrints gp on gp.groupPrintID = igp.groupPrintID
		and gp.orgID = @orgID
	inner join #groupPrintsToProcess gpInsert on gp.hashGroupList = gpInsert.hashGroupList;

	delete from #groupPrintsToProcess
	where groupPrintID is null;

	insert into dbo.cache_perms_groupPrintsAndGroups (groupPrintID, groupID, orgID)
	select groupPrintID, listItem as groupID, @orgID
	from #groupPrintsToProcess
	cross apply dbo.fn_intListToTable(groupList,',');

	update hold
	set newGroupPrintID = gpInsert.groupPrintID
	from #holding_memberGroupPrints hold
	inner join #groupPrintsToProcess gpInsert on hold.hashGroupList = gpInsert.hashGroupList
		and hold.newGroupPrintID is null;

	update m
	set groupPrintID = hold.newGroupPrintID
	from #holding_memberGroupPrints hold
	inner join dbo.ams_members m on m.memberID = hold.memberID and m.orgID = @orgID
	where isnull(hold.currentGroupPrintID,0) <> isnull(hold.newGroupPrintID,0);

	-- process group prints
	if exists (select 1 from #groupPrintsToProcess)
		exec dbo.cache_perms_processGroupPrintsBulk @orgid=@orgID;

	delete from platformQueue.dbo.queue_memberGroupPrints
	where itemGroupUID = @itemGroupUID;

	on_done:
	IF membercentral.dbo.fn_getCurrentTransactionIsolationLevel() <> 'ReadCommitted'
		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	IF OBJECT_ID('tempdb..#holding_memberGroupPrints') IS NOT NULL
		DROP TABLE #holding_memberGroupPrints;
	IF OBJECT_ID('tempdb..#groupPrintsToProcess') IS NOT NULL
		DROP TABLE #groupPrintsToProcess;
	IF OBJECT_ID('tempdb..#distinctPermissionGroups') IS NOT NULL
		DROP TABLE #distinctPermissionGroups;
	IF OBJECT_ID('tempdb..#tblMembersToUpdateGPMB') IS NOT NULL
		DROP TABLE #tblMembersToUpdateGPMB;
	IF OBJECT_ID('tempdb..#insertedGroupPrints') IS NOT NULL 
		DROP TABLE #insertedGroupPrints;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	IF membercentral.dbo.fn_getCurrentTransactionIsolationLevel() <> 'ReadCommitted'
		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
