ALTER PROC dbo.queue_schedReportChangeNotify_grabForProcessing
@batchCount int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @statusReady int, @statusGrabbed int;
	SELECT @queueTypeID = queueTypeID FROM dbo.tblQueueTypes WHERE queueType = 'schedReportChangeNotify';
	SELECT @statusReady = queuestatusID FROM dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID AND queueStatus = 'readyToProcess';
	SELECT @statusGrabbed = queuestatusID FROM dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID AND queueStatus = 'grabbedForProcessing';

	IF OBJECT_ID('tempdb..#tmpQueueItem') IS NOT NULL
		DROP TABLE #tmpQueueItem;
	CREATE TABLE #tmpQueueItem (itemID int);

	-- dequeue in order of dateAdded
	UPDATE qid WITH (UPDLOCK, READPAST)
	SET qid.statusID = @statusGrabbed,
		qid.dateUpdated = GETDATE()
		OUTPUT inserted.itemID
		INTO #tmpQueueItem
	FROM dbo.queue_schedReportChangeNotify AS qid
	INNER JOIN (
		SELECT TOP (@batchCount) itemID
		FROM dbo.queue_schedReportChangeNotify
		WHERE statusID = @statusReady
		ORDER BY dateAdded, itemID
	) AS batch ON batch.itemID = qid.itemID
	WHERE qid.statusID = @statusReady;

	SELECT qid.itemID, qid.reportID, qid.actorMemberID, qid.changeSection, s.siteCode, s.orgID, r.reportName,
		tt.toolType, tt.toolDesc, mActive.firstName AS actorFirstName, mActive.lastName AS actorLastName, me.email AS actorEmail
	FROM #tmpQueueItem AS tmp
	INNER JOIN dbo.queue_schedReportChangeNotify AS qid ON qid.itemID = tmp.itemID
	INNER JOIN membercentral.dbo.rpt_SavedReports AS r ON r.reportID = qid.reportID
	INNER JOIN membercentral.dbo.sites AS s ON s.siteID = r.siteID
	INNER JOIN membercentral.dbo.admin_toolTypes AS tt ON tt.toolTypeID = r.toolTypeID
	INNER JOIN membercentral.dbo.ams_members AS m ON m.memberID = qid.actorMemberID
	INNER JOIN membercentral.dbo.ams_members AS mActive ON mActive.memberID = m.activeMemberID
	INNER JOIN membercentral.dbo.organizations AS o ON o.orgID = mActive.orgID
	INNER JOIN membercentral.dbo.ams_memberEmailTags AS metag ON metag.orgID = o.orgID AND metag.memberID = mActive.memberID
	INNER JOIN membercentral.dbo.ams_memberEmailTagTypes AS metagt ON metagt.orgID = o.orgID 
		AND metagt.emailTagTypeID = metag.emailTagTypeID
		AND metagt.emailTagType = 'Primary'
	INNER JOIN membercentral.dbo.ams_memberEmails AS me ON me.orgID = o.orgID
		AND me.memberID = metag.memberID
		AND me.emailTypeID = metag.emailTypeID;

	IF OBJECT_ID('tempdb..#tmpQueueItem') IS NOT NULL
		DROP TABLE #tmpQueueItem;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
