ALTER PROC dbo.cms_importPages_import
@siteID int,
@runByMemberID int,
@importResult xml OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @orgID int, @itemGroupUID uniqueidentifier, @queueTypeID int, @statusReady int, @xmlMessage xml;

	select @orgID = orgID from dbo.sites where siteID = @siteID;
	select @queueTypeID = queueTypeID from platformQueue.dbo.tblQueueTypes where queueType = 'importPages';
	select @statusReady = queueStatusID from platformQueue.dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'ReadyToProcess';
	select @itemGroupUID = NEWID();

	BEGIN TRY
		BEGIN TRAN;
			INSERT INTO platformQueue.dbo.queue_importPages (itemGroupUID, orgID, siteID, pageName, sectionID, pageTitle, keywords, 
				description, published, quickLink, inheritPlacements, ovModeID, ovTemplateID, applyNoIndex, applyNoFollow, applyNoArchive, 
				runByMemberID, statusID, dateAdded, dateUpdated)
			SELECT @itemGroupUID, @orgID, @siteID, PageName, MCSectionID, PageTitle, Keywords, Description, Published, QuickLink, 
				InheritPlacements, MCModeID, MCTemplateID, ApplyNoIndex, ApplyNoFollow, ApplyNoArchive, @runByMemberID, 
				@statusReady, GETDATE(), GETDATE()
			FROM #mc_pagesImport;

			-- resume task
			EXEC dbo.sched_resumeTask @name='Import Pages Queue', @engine='MCLuceeLinux';
		COMMIT TRAN;

		-- send message to service broker to create all the individual messages
		select @xmlMessage = isnull((
			select 'importPagesLoad' as t, cast(@itemGroupUID as varchar(60)) as u
			FOR XML RAW('mc'), TYPE
		),'<mc/>');
		EXEC platformQueue.dbo.queue_DataImport_sendMessage @xmlMessage=@xmlMessage;
	END TRY
	BEGIN CATCH
		IF @@trancount > 0 ROLLBACK TRANSACTION;
		INSERT INTO #tmpPageImportErrors (msg) VALUES ('Unable to import pages.');
		INSERT INTO #tmpPageImportErrors (msg) VALUES (left(error_message(),600));
	END CATCH

	-- return the xml results
	select @importResult = (
		select getdate() as "@date", 
			isnull((select top 301 dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg"
			from #tmpPageImportErrors
			order by msg
			FOR XML path('error'), root('errors'), type),'<errors/>')
		for xml path('import'), TYPE);
	
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
