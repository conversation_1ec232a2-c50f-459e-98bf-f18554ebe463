CREATE PROC dbo.searchEngineIndex_populateQueue

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpNamesHold') IS NOT NULL
		DROP TABLE #tmpNamesHold;
	IF OBJECT_ID('tempdb..#tmpNames') IS NOT NULL
		DROP TABLE #tmpNames;
	IF OBJECT_ID('tempdb..#tmpNamesToDelete') IS NOT NULL
		DROP TABLE #tmpNamesToDelete;
	CREATE TABLE #tmpNamesHold (firstname VARCHAR(150), lastname VARCHAR(150));
	CREATE TABLE #tmpNames (nameID INT IDENTITY(1,1), firstname VARCHAR(150), lastname VARCHAR(150), baseSlug VARCHAR(200) INDEX IX_baseSlug);
	CREATE TABLE #tmpNamesToDelete (nameID INT PRIMARY KEY);

	INSERT INTO #tmpNamesHold (firstname, lastname)
	SELECT fname, lname
	FROM trialsmith.dbo.depoDocuments d
	WHERE NULLIF(fname, '') IS NOT NULL 
	AND NULLIF(lname, '') IS NOT NULL 
	AND DocumentTypeID = 1
	AND disabled = 'N'
	GROUP BY lname, fname;

	-- strip non A-Z 0-9 dash and space
	UPDATE #tmpNamesHold 
	SET lastname = replace(LTRIM(RTRIM(membercentral.dbo.fn_RegExReplace(lastname,'[^A-Za-z0-9\- ]+',' '))),'  ',' '),
		firstname = replace(LTRIM(RTRIM(membercentral.dbo.fn_RegExReplace(firstname,'[^A-Za-z0-9\- ]+',' '))),'  ',' ');

	DELETE FROM #tmpNamesHold
	WHERE lastname = '' OR firstname = '';

	-- final pool of distinct names
	INSERT INTO #tmpNames (firstname, lastname)
	SELECT firstname, lastname
	FROM #tmpNamesHold
	GROUP BY firstname, lastname;

	-- Create baseSlug
	UPDATE #tmpNames
	SET baseSlug = LOWER(replace(firstname + ' ' + lastname,' ','-'));

	-- if there are still dupe baseSlugs, just keep the first one
	DELETE tmp
	FROM #tmpNames as tmp
	INNER JOIN (
		select nameID, baseSlug, ROW_NUMBER() OVER (PARTITION BY baseSlug ORDER BY nameID) as baseNum
		from #tmpNames
	) as tmp2 on tmp2.nameID = tmp.nameID and tmp2.baseNum > 1;

	-- Insert new names into tblSearchEngineIndex
	DECLARE @dateNextRun datetime = DATEADD(HOUR, 1, GETDATE());

	INSERT INTO dbo.tblSearchEngineIndex (firstname, lastname, baseSlug, slug, dateNextRun)
	SELECT tmp.firstname, tmp.lastname, tmp.baseSlug, tmp.baseSlug, @dateNextRun
	FROM #tmpNames as tmp
	LEFT OUTER JOIN dbo.tblSearchEngineIndex as sei on sei.baseSlug = tmp.baseSlug
	WHERE sei.nameID IS NULL;

	-- names to delete that no longer exist in depoDocuments
	INSERT INTO #tmpNamesToDelete (nameID)
	SELECT sei.nameID
	FROM dbo.tblSearchEngineIndex as sei
	LEFT OUTER JOIN #tmpNames as tmp on tmp.baseSlug = sei.baseSlug
	WHERE tmp.nameID IS NULL;

	DELETE seibc
	FROM dbo.tblSearchEngineIndexBucketCounts as seibc
	INNER JOIN #tmpNamesToDelete as tmp on tmp.nameID = seibc.nameID;

	DELETE sei
	FROM dbo.tblSearchEngineIndex as sei
	INNER JOIN #tmpNamesToDelete as tmp on tmp.nameID = sei.nameID;


	-- populate the queue with all names
	DECLARE @queueTypeID int, @queueStatusID int, @itemCount int = 0;
	SELECT @queueTypeID = queueTypeID FROM platformQueue.dbo.tblQueueTypes WHERE queueType = 'depoCrawlerIndex';
	SELECT @queueStatusID = queueStatusID FROM platformQueue.dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID and queueStatus = 'readyToProcess';

	INSERT INTO platformQueue.dbo.queue_depoCrawlerIndex (nameID, statusID)
	SELECT nameID, @queueStatusID
	FROM dbo.tblSearchEngineIndex
		EXCEPT
	SELECT nameID, @queueStatusID
	FROM platformQueue.dbo.queue_depoCrawlerIndex;
									
	SET @itemCount = @@ROWCOUNT;
									
	IF @itemCount > 0 
		EXEC membercentral.dbo.sched_resumeTask @name='Expert Name Crawler Index', @engine='MCLuceeLinux';

	SELECT @itemCount as itemCount;

	IF OBJECT_ID('tempdb..#tmpNamesHold') IS NOT NULL
		DROP TABLE #tmpNamesHold;
	IF OBJECT_ID('tempdb..#tmpNames') IS NOT NULL
		DROP TABLE #tmpNames;
	IF OBJECT_ID('tempdb..#tmpNamesToDelete') IS NOT NULL
		DROP TABLE #tmpNamesToDelete;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1
END CATCH
GO
