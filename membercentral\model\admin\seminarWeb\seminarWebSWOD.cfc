<cfcomponent output="no">

	<cffunction name="createSWODProgram" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="mcproxy_sitecode" type="string" required="true">
		<cfargument name="seminarName" type="string" required="true">
		<cfargument name="dateOrigPublished" type="date" required="yes">

		<cfset var local = structnew()>

		<cfset local.siteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='SeminarWebAdmin',siteID=arguments.mcproxy_siteID)>
		<cfset local.tmpRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.siteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.mcproxy_siteID)>

		<cftry>
			<cfif not local.tmpRights.addSWODProgram>
				<cfthrow message="invalid request">
			</cfif>

			<cfstoredproc procedure="swod_createSeminar" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.mcproxy_sitecode#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.seminarName#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="true">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="$0.00">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="">
				<cfprocparam type="In" cfsqltype="CF_SQL_DATE" value="#arguments.dateOrigPublished#">
				<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.seminarID">
			</cfstoredproc>

			<cfset local.returnStruct.seminarID = local.seminarID>
			<cfset local.returnStruct.success = true>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="updateSWODProgram" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">
		<cfargument name="mcproxy_orgID" type="string" required="yes">
		<cfargument name="seminarID" type="numeric" required="yes">
		<cfargument name="seminarName" type="string" required="yes">
		<cfargument name="seminarSubTitle" type="string" required="no">
		<cfargument name="programCode" type="string" required="yes">
		<cfargument name="seminarDesc" type="string" required="no">
		<cfargument name="isPublished" type="string" required="no">
		<cfargument name="dateActivated" type="string" required="no">
		<cfargument name="dateOrigPublished" type="string" required="yes">
		<cfargument name="layoutID" type="numeric" required="yes">
		<cfargument name="seminarLength" type="numeric" required="yes">
		<cfargument name="lockSWODProgramSettings" type="boolean" required="no">
		<cfargument name="sendConfirmationEmail" type="boolean" required="no">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { success=false, errmsg='' }>
		<cfparam name="arguments.seminarName" default="">
		<cfparam name="arguments.seminarSubTitle" default="0">
		<cfparam name="arguments.programCode" default="0">
		<cfparam name="arguments.seminarDesc" default="0">
		<cfparam name="arguments.isPublished" default="0">
		<cfparam name="arguments.dateActivated" default="">
		<cfparam name="arguments.dateOrigPublished" default="">
		<cfparam name="arguments.layoutID" default="0">
		<cfparam name="arguments.seminarLength" default="0">
		<cfparam name="arguments.lockSWODProgramSettings" default="0">
		<cfparam name="arguments.sendConfirmationEmail" default="0">
		<cfset local.objSWOD = CreateObject("component","model.seminarweb.SWODSeminars")>
		
		<cftry>
			<cfquery datasource="#application.dsn.tlasites_seminarweb.dsn#" name="local.qrySeminar">
				select p.orgcode as publisherOrgCode, s.lockSettings
				from dbo.tblSeminars as s
				inner join dbo.tblParticipants as p on p.participantID = s.participantID
				inner join dbo.tblSeminarsSWOD as swod on swod.seminarID = s.seminarID
				where s.seminarID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">
				and s.isDeleted = 0
			</cfquery>

			<!--- security --->
			<cfset local.SeminarWebAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='SeminarWebAdmin',siteID=arguments.mcproxy_siteid)>
			<cfset local.tmpRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.SeminarWebAdminSRID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.mcproxy_siteid)>
			<cfset local.hasLockSWProgramRights = local.tmpRights.lockSWProgram is 1>
			<cfset local.hasUpdateSWODProgramRights = (local.tmpRights.editSWODProgramAll is 1 OR local.tmpRights.editSWODProgramPublish is 1) AND local.qrySeminar.publisherOrgCode eq arguments.mcproxy_siteCode>

			<cfif local.hasUpdateSWODProgramRights AND NOT local.qrySeminar.lockSettings>
				<!--- update SWOD Program --->
				<cfstoredproc procedure="swod_updateSeminar" datasource="#application.dsn.tlasites_seminarweb.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.mcproxy_siteCode#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.seminarName#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.seminarSubTitle#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.programCode#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.seminarDesc#">
					<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.isPublished#">
					<cfif arguments.isPublished eq 1 and not len(arguments.dateActivated)>
						<cfprocparam type="In" cfsqltype="CF_SQL_DATE" value="#now()#">
					<cfelse>
						<cfprocparam type="In" cfsqltype="CF_SQL_DATE" value="#arguments.dateActivated#">
					</cfif>
					<cfif len(arguments.dateOrigPublished) and isDate(arguments.dateOrigPublished)>
						<cfprocparam type="In" cfsqltype="CF_SQL_DATE" value="#arguments.dateOrigPublished#">
					<cfelse>
						<cfprocparam type="In" cfsqltype="CF_SQL_DATE" value="#now()#">
					</cfif>				
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.layoutID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarLength#">
					<cfif local.hasLockSWProgramRights>
						<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.lockSWODProgramSettings#">
					<cfelse>
						<cfprocparam type="In" cfsqltype="CF_SQL_BIT" null="true">
					</cfif>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
				</cfstoredproc>

				<cfif arguments.sendConfirmationEmail>
					<cfset local.qrySeminar = local.objSWOD.getSeminarBySeminarID(arguments.seminarID)>
					<cfset local.sendingSiteInfo = application.objSiteInfo.getSiteInfo(local.qrySeminar.publisherOrgCode)>
					<cfset local.siteInfo = application.objSiteInfo.getSiteInfo(local.qrySeminar.publisherOrgCode)>
					<cfset local.programLink = "#local.siteInfo.scheme#://#local.siteInfo.mainhostname##CreateObject('component','model.admin.admin').buildLinkToTool(toolType='SeminarWebAdmin',mca_ta='editSWODProgram')#&pid=#local.qrySeminar.seminarID#">

					<!--- confirmation email to submitter --->
					<cfif len(local.qrySeminar.submittedByEmail)>
						<cfsavecontent variable="local.emailHTMLContent">
							<cfoutput>
								Hi #local.qrySeminar.submitterFirstName#,<br/><br/>
								<b>#local.qrySeminar.seminarName#</b> has been successfully launched as an OnDemand program. To review your program, <a href="#local.programLink#">click here.</a><br/>
								<hr>
								Below is a summary of your program: <br/><br/>
								Program Status: <cfif local.qrySeminar.isPublished>Active <cfelse>Inactive</cfif><br/><br/>
								Program Title: #encodeForHTML(local.qrySeminar.seminarName)#
								<cfif len(local.qrySeminar.seminarSubTitle)>
									<br/><br/>Program SubTitle: #encodeForHTML(local.qrySeminar.seminarSubTitle)#
								</cfif>
								<cfif LEN(local.qrySeminar.dateCatalogStart)>
									<br/><br/>Catalog Sale Dates: #DateFormat(local.qrySeminar.dateCatalogStart,'mm/dd/yyyy')# - #DateFormat(local.qrySeminar.dateCatalogEnd,'mm/dd/yyyy')#
								</cfif>
								<hr><br/>
								<p style="margin-top:2px;margin-bottom:2px;">SeminarWeb</p>
								737-201-2059
							</cfoutput>
						</cfsavecontent>
						<cfset local.emailHTMLContent = trim(replace(replace(replace(local.emailHTMLContent,chr(9),"","ALL"),chr(13),"","ALL"),chr(10),"","ALL"))>
						<cfset local.emailsubject = "OnDemand Program Created -  #local.qrySeminar.seminarName#">

						<cfset application.objEmailWrapper.sendMailESQ(
							emailfrom={ name="SeminarWeb", email="<EMAIL>" },
							emailto=[{ name=local.qrySeminar.submittedByMember, email=local.qrySeminar.submittedByEmail }],
							emailreplyto="",
							emailsubject=local.emailsubject,
							emailtitle="OnDemand Program Created",
							emailhtmlcontent=local.emailHTMLContent,
							siteID=local.qrySeminar.participantSiteID,
							memberID=local.qrySeminar.submittedByMemberID,
							messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="SWODCREATE"),
							sendingSiteResourceID=local.sendingSiteInfo.siteSiteResourceID
						)>
					</cfif>
				</cfif>

				<cfset local.returnStruct.success = true>

			<cfelseif local.hasUpdateSWODProgramRights AND local.hasLockSWProgramRights>
				<cfset CreateObject("seminarWebSWCommon").updateProgramLockSettings(orgID=arguments.mcproxy_orgID,
					siteID=arguments.mcproxy_siteID, programType="SWOD", programID=arguments.seminarID,
					lockSWProgramSettings=arguments.lockSWODProgramSettings)>
				<cfset local.returnStruct.success = true>
			<cfelse>
				<cfset local.returnStruct.success = false>
				<cfset local.returnStruct.errmsg = "You do not have rights to this section.">
			</cfif>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="saveSWODProgramSettings" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">
		<cfargument name="seminarID" type="numeric" required="yes">
		<cfargument name="introMessageText" type="string" required="yes">
		<cfargument name="endofSeminartext" type="string" required="yes">
		<cfargument name="offerQA" type="boolean" required="no">
		<cfargument name="blankOnInactivity" type="boolean" required="no">
		<cfargument name="offerCertificate" type="boolean" required="no">
		<cfargument name="preReq" type="numeric" required="no">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { success=false, errmsg='' }>
		
		<cftry>
			<cfquery datasource="#application.dsn.tlasites_seminarweb.dsn#" name="local.qrySeminar">
				select p.orgcode as publisherOrgCode, s.lockSettings
				from dbo.tblSeminars as s
				inner join dbo.tblParticipants as p on p.participantID = s.participantID
				inner join dbo.tblSeminarsSWOD as swod on swod.seminarID = s.seminarID
				where s.seminarID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">
				and s.isDeleted = 0
			</cfquery>

			<!--- security --->
			<cfset local.SeminarWebAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='SeminarWebAdmin',siteID=arguments.mcproxy_siteID)>
			<cfset local.tmpRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.SeminarWebAdminSRID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.mcproxy_siteID)>
			<cfset local.hasLockSWProgramRights = local.tmpRights.lockSWProgram is 1>
			<cfset local.hasUpdateSWODProgramRights = (local.tmpRights.editSWODProgramAll is 1 OR local.tmpRights.editSWODProgramPublish is 1)>

			<cfif local.hasUpdateSWODProgramRights AND NOT local.qrySeminar.lockSettings>
				<!--- update SWOD Program Settings--->
				<cfstoredproc procedure="swod_updateSeminarSettings" datasource="#application.dsn.tlasites_seminarweb.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.mcproxy_siteCode#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.introMessageText#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.endofSeminartext#">
					<cfif local.qrySeminar.publisherOrgCode eq arguments.mcproxy_siteCode>
						<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="1">
						<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.offerQA#">
						<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.blankOnInactivity#">
						<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.offerCertificate#">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.preReq#" />
					<cfelse>
						<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="0">
						<cfprocparam type="In" cfsqltype="CF_SQL_BIT" null="true">
						<cfprocparam type="In" cfsqltype="CF_SQL_BIT" null="true">
						<cfprocparam type="In" cfsqltype="CF_SQL_BIT" null="true">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="true">
					</cfif>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
				</cfstoredproc>
				
				<cfset local.returnStruct.success = true>

			<cfelse>
				<cfset local.returnStruct.success = false>
				<cfset local.returnStruct.errmsg = "You do not have rights to this section.">
			</cfif>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="saveSWODProgramCatalog" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">
		<cfargument name="seminarID" type="numeric" required="yes">
		<cfargument name="isPriceBasedOnActual" type="boolean" required="yes">
		<cfargument name="revenueGLAccountID" type="numeric" required="no">
		<cfargument name="allowCatalog" type="boolean" required="no">
		<cfargument name="dateCatalogStart" type="string" required="no">
		<cfargument name="dateCatalogEnd" type="string" required="no">
		<cfargument name="freeRateDisplay" type="string" required="yes">
		<cfargument name="isFeatured" type="boolean" required="no">
		<cfargument name="previewID" type="numeric" required="no">
		<cfargument name="sponsorUsageIdsList" type="string" required="no">
		<cfargument name="programType" type="string" required="no">

		<cfset var local = structNew()>
		<cftry>
			<cfif not hasUpdateSeminarRights(siteID=arguments.mcproxy_siteID, seminarID=arguments.seminarID, action="catalog", checkLockSettings=true)>
				<cfthrow message="invalid request">
			</cfif>

			<cfset local.objAdminSWCommon = createObject("component","seminarWebSWCommon")>
			<cfif NOT isNull(arguments.previewID)>
				<cfset local.data = local.objAdminSWCommon.deleteVideoPreview(mcproxy_siteID=arguments.mcproxy_siteID, programType=arguments.programType, programID=arguments.seminarID, previewID=arguments.previewID)>
			</cfif>

			<cfstoredproc procedure="swod_updateSeminarCatalog" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.mcproxy_siteCode#">
				<cfif NOT isNull(arguments.allowCatalog)>
					<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.allowCatalog#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_BIT" null="true">
				</cfif>
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.isPriceBasedOnActual#">
				<cfif NOT isNull(arguments.dateCatalogStart) and arguments.allowCatalog is 1 and len(arguments.dateCatalogStart) and isDate(arguments.dateCatalogStart)>
					<cfprocparam type="In" cfsqltype="CF_SQL_DATE" value="#arguments.dateCatalogStart#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_DATE" null="true">
				</cfif>
				<cfif NOT isNull(arguments.dateCatalogEnd) and arguments.allowCatalog is 1 and len(arguments.dateCatalogEnd) and isDate(arguments.dateCatalogEnd)>
					<cfprocparam type="In" cfsqltype="CF_SQL_DATE" value="#arguments.dateCatalogEnd#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_DATE" null="true">
				</cfif>
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.freeRateDisplay#">
				<cfif NOT isNull(arguments.revenueGLAccountID) and val(arguments.revenueGLAccountID) GT 0>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.revenueGLAccountID#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="true">
				</cfif>
				<cfif NOT isNull(arguments.isFeatured)>
					<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.isFeatured#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_BIT" null="true">
				</cfif>
				<cfif NOT isNull(arguments.sponsorUsageIdsList)>
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.sponsorUsageIdsList#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="true">
				</cfif>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
			</cfstoredproc>

			<cfset local.data["success"] = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data["success"] = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="saveSWODProgramBilling" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="seminarID" type="numeric" required="yes">
		<cfargument name="preventSeminarFees" type="boolean" required="yes">
			
		<cfset var local = structNew()>
		<cftry>
			<cfif not hasUpdateSeminarRights(siteID=arguments.mcproxy_siteID, seminarID=arguments.seminarID, action="billing", checkLockSettings=true)>
				<cfthrow message="invalid request">
			</cfif>
			<cfquery name="local.updateSeminarSWOD" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				SET NOCOUNT ON;

				DECLARE @seminarID INT, @preventSeminarFees bit;
				SET @seminarID = <cfqueryparam value="#arguments.seminarID#" cfsqltype="CF_SQL_INTEGER">;
				SET @preventSeminarFees = <cfqueryparam value="#arguments.preventSeminarFees#" cfsqltype="CF_SQL_BIT">;
				
				IF @preventSeminarFees = 1 AND EXISTS (
					SELECT 1 AS priceCount
					FROM dbo.tblSeminarsAndRates AS r 
					INNER JOIN memberCentral.dbo.cms_siteResources AS sr ON sr.siteResourceID = r.siteResourceID
						AND sr.siteResourceStatusID = 1
					WHERE r.seminarID = @seminarID
				)
					SET @preventSeminarFees = 0;

				UPDATE dbo.tblSeminars
				SET preventSeminarFees = @preventSeminarFees
				WHERE seminarID = @seminarID;
			</cfquery>

			<cfset local.data["success"] = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data["success"] = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="saveSeminarRateOptions" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="seminarID" type="numeric" required="yes">
		<cfargument name="isPriceBasedOnActual" type="boolean" required="yes">
		<cfargument name="revenueGLAccountID" type="numeric" required="yes">
		<cfargument name="allowCatalog" type="boolean" required="yes">
		<cfargument name="dateCatalogStart" type="string" required="yes">
		<cfargument name="dateCatalogEnd" type="string" required="yes">
		<cfargument name="freeRateDisplay" type="string" required="yes">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { success=false, err='' }>

		<cftry>
			<cfif not hasUpdateSeminarRights(siteID=arguments.mcproxy_siteID, seminarID=arguments.seminarID, action="Edit", checkLockSettings=true)>
				<cfthrow message="You do not have rights to this section.">
			</cfif>

			<cfstoredproc procedure="sw_updateSeminarPricing" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.allowCatalog#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.isPriceBasedOnActual#">
				<cfif arguments.allowCatalog is 1 and len(arguments.dateCatalogStart) and isDate(arguments.dateCatalogStart)>
					<cfprocparam type="In" cfsqltype="CF_SQL_DATE" value="#arguments.dateCatalogStart#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="true">
				</cfif>
				<cfif arguments.allowCatalog is 1 and len(arguments.dateCatalogEnd) and isDate(arguments.dateCatalogEnd)>
					<cfprocparam type="In" cfsqltype="CF_SQL_DATE" value="#arguments.dateCatalogEnd#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="true">
				</cfif>
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.freeRateDisplay#">
				<cfif val(arguments.revenueGLAccountID) GT 0>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.revenueGLAccountID#">
				<cfelse>
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="true">
				</cfif>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
			</cfstoredproc>

			<cfset local.returnStruct.success = true>
		<cfcatch type="Any">
			<cfset local.returnStruct.success = false>
			<cfset local.returnStruct.err = cfcatch.message>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getSeminarsByOrgcode" access="public" returntype="query" output="no">
		<cfargument name="orgCode" type="string" required="yes">
		
		<cfset var local = structnew()>

		<cfstoredproc procedure="swod_getSeminarsByOrgcode" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.orgCode#" null="No">
			<cfprocresult name="local.qrySeminars" resultset="1">
		</cfstoredproc>

		<cfreturn local.qrySeminars>
	</cffunction>

	<cffunction name="getLayouts" access="public" returntype="query" output="no">
		<cfset var local = structnew()>

		<cfstoredproc procedure="swod_getLayouts" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocresult name="local.qryLayouts" resultset="1">
		</cfstoredproc>

		<cfreturn local.qryLayouts>
	</cffunction>

	<cffunction name="getPrerequisiteSeminars" access="public" returntype="query" output="no">
		<cfargument name="seminarID" type="numeric" required="yes">
		<cfargument name="orgCode" type="string" required="yes">
		
		<cfset var local = structnew()>

		<cfset local.qrySWODSeminars = getSeminarsByOrgcode(orgCode=arguments.orgCode)>

		<cfquery name="local.qrySWODSeminars" dbtype="query">
			SELECT seminarID, seminarName, seminarSubTitle, isPublished
			FROM [local].qrySWODSeminars
			WHERE seminarID <> <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">
			ORDER BY isPublished, seminarName, seminarSubTitle;
		</cfquery>

		<cfreturn local.qrySWODSeminars>
	</cffunction>

	<cffunction name="saveSWODFilter" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();
			getSWODFilter();
			local.SWODFilter = application.mcCacheManager.sessionGetValue(keyname='SWODFilter',defaultValue={});
			local.SWODFilter_listFilter_hash = hash(serializeJSON(local.SWODFilter.listFilter), "SHA", "UTF-8");

			local.SWODFilter.listFilter.fActivatedDateFrom = arguments.event.getValue('fActivatedDateFrom','');
			local.SWODFilter.listFilter.fActivatedDateTo = arguments.event.getValue('fActivatedDateTo','');
			local.SWODFilter.listFilter.fOrigPublishDateFrom = arguments.event.getValue('fOrigPublishDateFrom','');
			local.SWODFilter.listFilter.fOrigPublishDateTo = arguments.event.getValue('fOrigPublishDateTo','');
			local.SWODFilter.listFilter.fKeyword = arguments.event.getValue('fKeyword','');
			local.SWODFilter.listFilter.fProgramCode = arguments.event.getValue('fProgramCode','');
			local.SWODFilter.listFilter.fPubType = arguments.event.getValue('fPubType','PO');
			local.SWODFilter.listFilter.fStatus = arguments.event.getValue('fStatus',1);
			local.SWODFilter.listFilter.fFeaturedOnly = arguments.event.getValue('fFeaturedOnly',0);
			local.SWODFilter.listFilter.fSyndicatedOnly = arguments.event.getValue('fSyndicatedOnly',0);

			if (local.SWODFilter_listFilter_hash NEQ hash(serializeJSON(local.SWODFilter.listFilter), "SHA", "UTF-8")) 
				application.mcCacheManager.sessionSetValue(keyname='SWODFilter', value=local.SWODFilter);
			
			local.data.success = true;
		</cfscript>
		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="getSWODFilter" access="public" output="false" returntype="struct">
		<cfscript>
			var local = structNew();
			local.tmpStr = { fActivatedDateFrom='', fActivatedDateTo='', fKeyword='',fOrigPublishDateFrom='', fOrigPublishDateTo='', fProgramCode='', fPubType='PO', fStatus=1, fFeaturedOnly=0, fSyndicatedOnly=0};
			local.SWODFilter = application.mcCacheManager.sessionGetValue(keyname='SWODFilter',defaultValue={});
			local.SWODFilter_hash = hash(serializeJSON(local.SWODFilter), "SHA", "UTF-8");

			if (NOT structKeyExists(local.SWODFilter,"listFilter"))
				local.SWODFilter.listFilter = duplicate(local.tmpStr);
				
			for (local.thiskey in local.tmpStr) {
				if (not structKeyExists(local.SWODFilter.listFilter, local.thiskey))
					structInsert(local.SWODFilter.listFilter, local.thiskey, local.tmpStr[local.thiskey], true);
			}
			if (local.SWODFilter_hash NEQ hash(serializeJSON(local.SWODFilter), "SHA", "UTF-8")) 
				application.mcCacheManager.sessionSetValue(keyname='SWODFilter', value=local.SWODFilter);
		</cfscript>

		<cfreturn local.SWODFilter>
	</cffunction>

	<cffunction name="getPrograms" access="public" output="false" returntype="Any">
		<cfargument name="sitecode" type="string" required="true">
		<cfargument name="mode" type="string" required="true">
		<cfargument name="keyword" type="string" required="true">
		<cfargument name="programCode" type="string" required="true">
		<cfargument name="publisherType" type="string" required="true">
		<cfargument name="hideInactive" type="boolean" required="true">
		<cfargument name="featuredOnly" type="boolean" required="false" default="0">
		<cfargument name="createdDateFrom" type="string" required="false" default="">
		<cfargument name="createdDateTo" type="string" required="false" default="">
		<cfargument name="activatedDateFrom" type="string" required="false" default="">
		<cfargument name="activatedDateTo" type="string" required="false" default="">
		<cfargument name="origPublishDateFrom" type="string" required="false" default="">
		<cfargument name="origPublishDateTo" type="string" required="false" default="">
		<cfargument name="orderby" type="numeric" required="false" default="0">
		<cfargument name="direct" type="string" required="false" default="asc">
		<cfargument name="posstart" type="numeric" required="false" default="0">
		<cfargument name="count" type="numeric" required="false" default="50">
		<cfargument name="sidList" type="string" required="false" default="">
		<cfargument name="xsidList" type="string" required="false" default="">
		<cfargument name="folderPathUNC" type="string" required="false" default="">
		<cfargument name="reportFileName" type="string" required="false" default="">
		<cfargument name="pid" type="numeric" required="false" default="0">
		<cfargument name="fPublisher" type="string" required="false" default="">
		<cfargument name="fSeminarID" type="string" required="false" default="">
		<cfargument name="publisherIDList" type="string" required="false" default="">
		<cfargument name="syndicatedOnly" type="boolean" required="false" default="0">
		
		<cfset var local = structNew()>
		
		<cfset local.arrCols = arrayNew(1)>		
		<cfset arrayAppend(local.arrCols,"tmp.seminarName")>
		<cfif arguments.mode eq 'copyRatesGrid'>
			<cfset arrayAppend(local.arrCols,"tmp.seminarName")>
		</cfif>
		<cfset arrayAppend(local.arrCols,"tmp.dateActivated")>
		<cfset arrayAppend(local.arrCols,"tmp.dateOrigPublished")>
		<cfif listFindNoCase("swSearchGrid,exportswSearchGrid", arguments.mode)>
			<cfset arrayAppend(local.arrCols,"tmp.publisherOrgCode")>
		</cfif>
		<cfif arguments.direct eq "DES"><cfset arguments.direct = 'DESC'></cfif>
		<cfset local.orderby = local.arrcols[arguments.orderby+1]>

		<cfquery name="local.qrySeminars" datasource="#application.dsn.tlasites_seminarweb.dsn#" result="local.qrySeminarsResult">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				-- create temp table to store all seminars in catalog
				DECLARE @tmpTable TABLE (autoID int IDENTITY(1,1), seminarID int, programCode varchar(15), seminarName varchar(250), seminarSubTitle varchar(250), 
					publisherOrgCode varchar(10), isFeatured bit, publisherParticipantID int, isPublished BIT, dateCreated datetime, dateOrigPublished date, 
					dateActivated date, seminarDesc varchar(max), introMessageText varchar(max), endOfSeminarText varchar(max), layoutID int, offerQA bit, blankOnInactivity bit, seminarLength int,
					offerCertificate bit, dateCatalogStart date , dateCatalogEnd date, allowSyndication bit, lockSettings bit, row int);

				declare @participantID int, @pn varchar(200), @rc varchar(15), @maxrows int, @startRow int, 
					@siteCode varchar(10), @totalCount int, @posStart int, @posStartAndCount int, @csd datetime, @ced datetime, 
					@asd date, @aed date, @opsd date, @oped date;
				SET @siteCode = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.sitecode#">;
				SET @posStart = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.posStart#">;
				SET @posStartAndCount = @posStart + <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.count#">;
				select @participantID = dbo.fn_getParticipantIDFromOrgcode(@siteCode);
				SET @pn = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.keyword#">;
				SET @rc = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.programCode#">;
				<cfif len(arguments.createdDateFrom)>
					SET @csd = <cfqueryparam cfsqltype="cf_sql_timestamp" value="#arguments.createdDateFrom#">;
				</cfif>
				<cfif len(arguments.createdDateTo)>
					SET @ced = <cfqueryparam cfsqltype="cf_sql_timestamp" value="#arguments.createdDateTo# 23:59:59.997">;
				</cfif>
				<cfif len(arguments.activatedDateFrom)>
					SET @asd = <cfqueryparam cfsqltype="cf_sql_date" value="#arguments.activatedDateFrom#">;
				</cfif>
				<cfif len(arguments.activatedDateTo)>
					SET @aed = <cfqueryparam cfsqltype="cf_sql_date" value="#arguments.activatedDateTo#">;
				</cfif>
				<cfif len(arguments.origPublishDateFrom)>
					SET @opsd = <cfqueryparam cfsqltype="cf_sql_date" value="#arguments.origPublishDateFrom#">;
				</cfif>
				<cfif len(arguments.origPublishDateTo)>
					SET @oped = <cfqueryparam cfsqltype="cf_sql_date" value="#arguments.origPublishDateTo#">;
				</cfif>

				INSERT INTO @tmpTable (seminarID, programCode, seminarName, seminarSubTitle, publisherOrgCode, publisherParticipantID, isPublished, 
					dateCreated, dateOrigPublished, dateActivated, isFeatured, seminarDesc, introMessageText, endOfSeminarText,layoutID, offerQA, 
					blankOnInactivity, seminarLength, offerCertificate, dateCatalogStart, dateCatalogEnd, allowSyndication, lockSettings, row)
				select tmp.seminarID, tmp.programCode, tmp.seminarName, tmp.seminarSubTitle, tmp.publisherOrgCode, tmp.publisherParticipantID, 
					tmp.isPublished, tmp.dateCreated, tmp.dateOrigPublished, tmp.dateActivated, case when fp.featuredID is not null then 1 else 0 end as isFeatured,
					tmp.seminarDesc,tmp.introMessageText, tmp.endOfSeminarText, tmp.layoutID, tmp.offerQA, tmp.blankOnInactivity, tmp.seminarLength, 
					tmp.offerCertificate, tmp.dateCatalogStart, tmp.dateCatalogEnd, tmp.allowSyndication, tmp.lockSettings,
					ROW_NUMBER() OVER (ORDER BY #preserveSingleQuotes(local.orderby)# #arguments.direct#) as row
				from (
					<cfif listFindNoCase("swSearchGrid,exportswSearchGrid", arguments.mode)>
						select s.seminarID, s.programCode, s.seminarName, s.seminarSubTitle, p.participantID, p.orgcode as publisherOrgCode, 
							p.participantID as publisherParticipantID, s.isPublished, swod.dateCreated, swod.dateOrigPublished, swod.dateActivated,
							s.seminarDesc, swod.introMessageText, swod.endOfSeminarText, swod.layoutID, swod.offerQA, swod.blankOnInactivity, 
							swod.seminarLength, s.offerCertificate, s.dateCatalogStart, s.dateCatalogEnd, swod.allowSyndication, s.lockSettings
						FROM dbo.tblSeminars as s
						INNER JOIN dbo.tblSeminarsSWOD as swod on swod.seminarID = s.seminarID
						INNER JOIN dbo.tblParticipants as p on p.participantID = s.participantID
						and s.isDeleted = 0
						<cfif arguments.hideInactive is 1>
							and s.isPublished = 1
						</cfif>
					<cfelse>
						select s.seminarID, s.programCode, s.seminarName, s.seminarSubTitle, p.participantID, p.orgcode as publisherOrgCode, p.participantID as publisherParticipantID, 
							s.isPublished, swod.dateCreated, swod.dateOrigPublished, swod.dateActivated, s.seminarDesc, swod.introMessageText, swod.endOfSeminarText, swod.layoutID, 
							swod.offerQA, swod.blankOnInactivity, swod.seminarLength, s.offerCertificate, s.dateCatalogStart, s.dateCatalogEnd, swod.allowSyndication, s.lockSettings
						FROM dbo.tblSeminars as s
						INNER JOIN dbo.tblSeminarsSWOD as swod on swod.seminarID = s.seminarID
						INNER JOIN dbo.tblParticipants as p on p.participantID = s.participantID
						WHERE p.participantID = @participantID
						and s.isDeleted = 0
						<cfif arguments.hideInactive is 1>
							and s.isPublished = 1
						</cfif>
							union
						SELECT s.seminarID, s.programCode, s.seminarName, s.seminarSubTitle, p2.participantID, p2.orgcode as publisherOrgCode, p.participantID as publisherParticipantID, 
							s.isPublished, swod.dateCreated, swod.dateOrigPublished, swod.dateActivated, s.seminarDesc, swod.introMessageText, swod.endOfSeminarText, swod.layoutID, 
							swod.offerQA, swod.blankOnInactivity, swod.seminarLength, s.offerCertificate, s.dateCatalogStart, s.dateCatalogEnd, swod.allowSyndication, s.lockSettings
						FROM dbo.tblParticipants AS p 
						INNER JOIN dbo.tblSeminarsOptIn AS soi ON p.participantID = soi.participantID AND soi.IsActive = 1
						INNER JOIN dbo.tblSeminars AS s ON soi.seminarID = s.seminarID 
						INNER JOIN dbo.tblSeminarsSWOD AS swod ON s.seminarID = swod.seminarID
						INNER JOIN dbo.tblParticipants AS p2 ON p2.participantID = s.participantID
						WHERE p.participantID = @participantID
						and s.isDeleted = 0
						<cfif arguments.hideInactive is 1>
							and s.isPublished = 1
						</cfif>
					</cfif>
				) as tmp
				<cfif listLen(arguments.publisherIDList)>
					inner join memberCentral.dbo.fn_intListToTableInline(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.publisherIDList#">,',') as tmpP on tmpP.listitem = tmp.participantID
				</cfif>
				<cfif listLen(arguments.sidList)>
					inner join memberCentral.dbo.fn_intListToTableInline('0#arguments.sidList#',',') as tbl on tbl.listitem = tmp.seminarID
				</cfif>
				<cfif listLen(arguments.xsidList)>
					left outer join memberCentral.dbo.fn_intListToTableInline('0#arguments.xsidList#',',') as tblExc on tblExc.listitem = tmp.seminarID
				</cfif>
				left outer join dbo.tblFeaturedPrograms as fp on fp.participantID = @participantID
					and fp.seminarID = tmp.seminarID
				where 1 = 1
				<cfif len(arguments.keyword)>
					and tmp.seminarName + ISNULL(' ' + tmp.seminarSubTitle,'') LIKE '%' + @pn + '%'
				</cfif>
				<cfif len(arguments.programCode)>
					and tmp.programCode = @rc
				</cfif>
				<cfif len(arguments.createdDateFrom) and len(arguments.createdDateTo)>
					and tmp.dateCreated between @csd and @ced 
				<cfelseif len(arguments.createdDateFrom)>
					and tmp.dateCreated >= @csd
				<cfelseif len(arguments.createdDateTo)>
					and tmp.dateCreated <= @ced
				</cfif>
				<cfif len(arguments.activatedDateFrom) and len(arguments.activatedDateTo)>
					and tmp.dateActivated between @asd and @aed 
				<cfelseif len(arguments.activatedDateFrom)>
					and tmp.dateActivated >= @asd
				<cfelseif len(arguments.activatedDateTo)>
					and tmp.dateActivated <= @aed
				</cfif>
				<cfif len(arguments.origPublishDateFrom) and len(arguments.origPublishDateTo)>
					and tmp.dateOrigPublished between @opsd and @oped 
				<cfelseif len(arguments.origPublishDateFrom)>
					and tmp.dateOrigPublished >= @opsd
				<cfelseif len(arguments.origPublishDateTo)>
					and tmp.dateOrigPublished <= @oped
				</cfif>
				<cfif arguments.publisherType EQ 'P'>
					and tmp.publisherOrgCode = @siteCode
				<cfelseif arguments.publisherType EQ 'O'>
					and tmp.publisherOrgCode <> @siteCode
				</cfif>
				<cfif arguments.featuredOnly eq 1>
					and fp.featuredID is not null
				</cfif>
				<cfif listLen(arguments.xsidList)>
					and tblExc.listitem is null
				</cfif>
				<cfif arguments.mode EQ "copyRatesGrid">
					and tmp.seminarID <> <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.pid#">
				</cfif>
				<cfif listFindNoCase("swSearchGrid,exportswSearchGrid", arguments.mode)>
					<cfif len(arguments.fSeminarID)>
						and tmp.seminarID = <cfqueryparam cfsqltype="cf_sql_integer" value="#int(val(arguments.fSeminarID))#">
					</cfif>
					<cfif len(arguments.fPublisher)>
						and tmp.publisherParticipantID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.fPublisher#">
					</cfif>
				</cfif>
				<cfif arguments.syndicatedOnly eq 1>
					and tmp.allowSyndication = 1
					and tmp.publisherOrgCode = @siteCode
				</cfif>;

				SET @totalCount = @@ROWCOUNT;
				<cfif arguments.mode EQ "export">
					IF OBJECT_ID('tempdb..##tmpSWOD') IS NOT NULL
		      			DROP TABLE ##tmpSWOD;
					
					SELECT 'SWOD-' + cast(tmp.seminarId as varchar(10)) as [SeminarID], tmp.ProgramCode, tmp.seminarName as [SeminarTitle], 
						tmp.seminarSubTitle as [SeminarSubtitle],
						convert(varchar, tmp.dateOrigPublished, 101) as [OrigPublished],
						convert(varchar, tmp.dateActivated, 101) as [Activated Date],
						case when tmp.publisherOrgCode = @siteCode and tmp.allowSyndication = 1 then 'Publisher/Syndicated' 
						when tmp.publisherOrgCode = @siteCode then 'Publisher' 
						else 'Opted-In' end as [Opt-In],
						tmp.publisherOrgCode as Publisher,
						case when tmp.isPublished = 1 then 'Active' else 'Inactive' end as [Status],
						(
						SELECT count(e.enrollmentID)
						FROM dbo.tblEnrollments AS e 
						INNER JOIN dbo.tblSeminars as s on s.seminarid = e.seminarID
						INNER JOIN dbo.tblUsers AS u ON e.userID = u.userID 
						INNER JOIN trialsmith.dbo.depomemberdata AS d ON d.depomemberdataID = u.depoMemberDataID 
						WHERE e.seminarID = tmp.seminarID
						AND e.isActive = 1
						AND (s.participantID = @participantID OR e.participantID = @participantID)
						AND (d.adminflag2 is null or d.adminflag2 <> 'Y')
						) as Registrants,
						tmp.seminarDesc as [Description],
						tmp.introMessageText as [IntroductoryText],
						tmp.endOfSeminarText as [CompletionText],
						(SELECT layout FROM tblSeminarsSWODLayouts WHERE layoutID = tmp.layoutID) as [PlayerMode], 
						(CASE WHEN tmp.offerQA = 1 THEN 'Yes' ELSE 'No' END ) as [PlayerQATab],
						(CASE WHEN tmp.blankOnInactivity = 1 THEN 'Yes' ELSE 'No' END ) as [BlankPlayer],
						tmp.seminarLength as [CompleteTime], 
						(CASE WHEN tmp.offerCertificate = 1 THEN 'Yes' ELSE 'No' END ) as [Certificate], 
						(CASE WHEN tmp.dateCatalogStart IS NOT NULL THEN 'Yes' ELSE 'No' END ) as [SellInCatalog],
						convert(varchar, tmp.dateCatalogStart, 101) as [StartSale], convert(varchar, tmp.dateCatalogEnd, 101)  as [EndSale],
						(SELECT
							STUFF((SELECT '|' + CAST(c.categoryName AS VARCHAR(100))  
							FROM tblSeminarsAndCategories tsc
							INNER JOIN tblCategories c ON tsc.categoryID= c.categoryID
							WHERE tsc.seminarID=tmp.seminarID AND participantID=participantID
							FOR XML PATH ('')),1,1,'')) AS [Subjects],
						(SELECT
							STUFF((SELECT '|' + CAST(f.formtitle AS VARCHAR(100))  
							FROM dbo.tblSeminarsAndForms as saf
							INNER JOIN formbuilder.dbo.tblForms as f on f.formid = saf.formid and f.isDeleted = 0
							WHERE saf.seminarID = tmp.seminarID
							FOR XML PATH ('')),1,1,'')) AS [Evaluation]	
					INTO ##tmpSWOD
					FROM @tmpTable as tmp
					ORDER BY tmp.row;
					
					DECLARE @selectsql varchar(max) = 'SELECT [SeminarID], [ProgramCode], [Status], [SeminarTitle], [SeminarSubtitle], [Description], 
						[IntroductoryText],[CompletionText], [OrigPublished], [Activated Date], [Opt-In], [Publisher], [PlayerMode], [PlayerQATab], [BlankPlayer], 
						[CompleteTime], [Certificate], [SellInCatalog], [StartSale], [EndSale], [Subjects], [Registrants], [Evaluation],
						 ROW_NUMBER() OVER(order by SeminarTitle) as mcCSVorder 
						*FROM* ##tmpSWOD';
					EXEC membercentral.dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename='#arguments.folderPathUNC#\#arguments.reportFileName#', @returnColumns=0;
					
					IF OBJECT_ID('tempdb..##tmpSWOD') IS NOT NULL
						DROP TABLE ##tmpSWOD;
				<cfelseif arguments.mode EQ "exportswSearchGrid">
					IF OBJECT_ID('tempdb..##tmpSWOD') IS NOT NULL
		      			DROP TABLE ##tmpSWOD;
					
					SELECT 'SWOD-' + cast(tmp.seminarId as varchar(10)) as [Program ID], tmp.ProgramCode, tmp.seminarName as [Program], 
						tmp.seminarSubTitle as [ProgramSubTitle],
						tmp.dateOrigPublished as [Date Originally Published],
						tmp.dateActivated as [Activated Date],
						tmp.dateCreated as [Created Date],						
						tmp.publisherOrgCode as [Opt-In], 						
						case when tmp.isPublished = 1 then 'Active' else 'Inactive' end as [Status]
					INTO ##tmpSWOD
					FROM @tmpTable as tmp
					ORDER BY tmp.row;
					
					DECLARE @selectsql varchar(max) = 'SELECT [Program ID], [ProgramCode], [Program], [ProgramSubTitle], [Date Originally Published], 
						[Activated Date], [Created Date], [Opt-In], [Status], ROW_NUMBER() OVER(order by Program) as mcCSVorder 
						*FROM* ##tmpSWOD';
					EXEC membercentral.dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename='#arguments.folderPathUNC#\#arguments.reportFileName#', @returnColumns=0;
					
					IF OBJECT_ID('tempdb..##tmpSWOD') IS NOT NULL
						DROP TABLE ##tmpSWOD;
				<cfelseif listFindNoCase("grid,copyRatesGrid,swSearchGrid", arguments.mode)>
					SELECT tmp.seminarID, tmp.seminarName, tmp.seminarSubTitle, tmp.publisherOrgCode, tmp.isPublished, tmp.dateOrigPublished, tmp.dateActivated, isFeatured, @totalCount as totalCount, (
						SELECT count(e.enrollmentID)
						FROM dbo.tblEnrollments AS e 
						INNER JOIN dbo.tblSeminars as s on s.seminarid = e.seminarID
						INNER JOIN dbo.tblUsers AS u ON e.userID = u.userID 
						INNER JOIN trialsmith.dbo.depomemberdata AS d ON d.depomemberdataID = u.depoMemberDataID 
						WHERE e.seminarID = tmp.seminarID
						AND e.isActive = 1
						<cfif  arguments.mode neq "swSearchGrid">
							AND (s.participantID = @participantID OR e.participantID = @participantID)
						</cfif>
						AND (d.adminflag2 is null or d.adminflag2 <> 'Y')
						) as enrolledCount, 
						tmp.allowSyndication, tmp.lockSettings
					FROM @tmpTable as tmp
					WHERE tmp.row > @posStart AND tmp.row <= @posStartAndCount
					ORDER BY tmp.row;
				</cfif>

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfif listFindNoCase("grid,copyRatesGrid,swSearchGrid", arguments.mode)>
			<cfreturn local.qrySeminars>
		</cfif>
	</cffunction>

	<cffunction name="getRegistrants" access="public" output="false" returntype="Any">
		<cfargument name="sitecode" type="string" required="yes">
		<cfargument name="mode" type="string" required="true">
		<cfargument name="seminarID" type="numeric" required="yes">
		<cfargument name="rdateFrom" type="string" required="true">
		<cfargument name="rdateTo" type="string" required="true">
		<cfargument name="completed" type="string" required="true">
		<cfargument name="rHideDeleted" type="boolean" required="true">
		<cfargument name="cdateFrom" type="string" required="true">
		<cfargument name="cdateTo" type="string" required="true">
		<cfargument name="memberID" type="numeric" required="yes">
		<cfargument name="pKeyword" type="string" required="false" default="">
		<cfargument name="pProgramCode" type="string" required="false" default="">
		<cfargument name="pPublisherType" type="string" required="false" default="">
		<cfargument name="pHideInactive" type="boolean" required="false" default="0">
		<cfargument name="emailTagTypeID" type="numeric" required="false" default="0">
		<cfargument name="orderby" type="numeric" required="false" default="0">
		<cfargument name="direct" type="string" required="false" default="asc">
		<cfargument name="posstart" type="numeric" required="false" default="0">
		<cfargument name="count" type="numeric" required="false" default="50">
		<cfargument name="folderPathUNC" type="string" required="false" default="">
		<cfargument name="reportFileName" type="string" required="false" default="">
		<cfargument name="fbExportType" type="string" required="false" default="">
		<cfargument name="fbFormID" type="numeric" required="false" default="0">
		<cfargument name="fieldSetID" type="numeric" required="false" default="0">
		<cfargument name="exportFieldIDList" type="string" required="false" default="">
		
		<cfset var local = StructNew()>

		<cfset local.arrCols = arrayNew(1)>		
		<cfif listFindNoCase("reggrid", arguments.mode)>
			<cfset arrayAppend(local.arrCols,"tmp.seminarName")>
		<cfelse>		
			<cfset arrayAppend(local.arrCols,"m.LastName + ', ' + m.FirstName")>
		</cfif>
		<cfset arrayAppend(local.arrCols,"tmp.dateEnrolled")>
		<cfif arguments.direct eq "DES"><cfset arguments.direct = 'DESC'></cfif>
		<cfset local.orderby = local.arrcols[arguments.orderby+1]>

		<cfif listFindNoCase("export,exportregsearch",arguments.mode)>
			<cfset local.tmpSuffix = replace(createUUID(),'-','','ALL')>
		</cfif>
		
		<cfquery name="local.qryEnrollments" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @participantID int, @sd date, @ed date, @pn varchar(200), @maxrows int, @startRow int, @siteCode varchar(10),
					@seminarID int, @totalCount int, @rDateFrom smalldatetime, @rDateTo smalldatetime, @cDateFrom smalldatetime, 
					@cDateTo smalldatetime, @offerCertificate bit, @memberID int, @siteID int, @orgID int, @swodFieldList varchar(max) = '', 
					@fullsql varchar(max);

				IF OBJECT_ID('tempdb..##tmpEnrollments') IS NOT NULL 
					DROP TABLE ##tmpEnrollments;
				IF OBJECT_ID('tempdb..##tmpEnrollments2') IS NOT NULL 
					DROP TABLE ##tmpEnrollments2;
				CREATE TABLE ##tmpEnrollments (userid int, enrollmentID int PRIMARY KEY, MCMemberID int, depoMemberDataID int INDEX IX_tmpEnrollments_depomemberdataID, 
					orgcode varchar(10), dateEnrolled smalldatetime, dateCompleted smalldatetime, passed bit, calcTimeSpent int, offerCertificate bit,
					seminarID int, programCode varchar(15), seminarName varchar(250), seminarSubTitle varchar(250), publisherOrgCode varchar(10), bundleOrderID int, 
					handlesOwnPayment bit, isActive bit, isFeeExempt bit);
				CREATE TABLE ##tmpEnrollments2 (autoID int IDENTITY(1,1), enrollmentID int PRIMARY KEY, MCMemberID int, depomemberdataID int, firstName varchar(100), 
					lastName varchar(100), memberNumber varchar(50), company varchar(200), dateEnrolled datetime, dateCompleted datetime,
					CreditCount int, EmailCount int, passed bit, progress int, showCertButton bit, signUpOrgCode varchar(10),
					seminarID int, programCode varchar(15), seminarName varchar(250), seminarSubTitle varchar(250), publisherOrgCode varchar(10), 
					bundleOrderID int, handlesOwnPayment bit, amountBilled decimal(14,2), amountDue decimal(14,2), isActive bit, isFeeExempt bit, row int);

				SET @siteCode = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.sitecode#">;
				SELECT @siteID = siteID, @orgID = orgID from membercentral.dbo.sites where siteCode = @siteCode;
				SET @participantID = dbo.fn_getParticipantIDFromOrgcode(@siteCode);
				<cfif arguments.rdateFrom NEQ ''>	
					SET @rDateFrom = <cfqueryparam cfsqltype="cf_sql_date" value="#arguments.rdateFrom#">;	
				</cfif>
				<cfif arguments.rdateTo NEQ ''>	
					SET @rDateTo = <cfqueryparam cfsqltype="cf_sql_timestamp" value="#arguments.rdateTo# 23:59">; <!--- smalldatetime has no seconds --->
				</cfif>
				<cfif arguments.cdateFrom NEQ ''>	
					SET @cDateFrom = <cfqueryparam cfsqltype="cf_sql_date" value="#arguments.cdateFrom#">;	
				</cfif>
				<cfif arguments.cdateTo NEQ ''>	
					SET @cDateTo = <cfqueryparam cfsqltype="cf_sql_timestamp" value="#arguments.cdateTo# 23:59">; <!--- smalldatetime has no seconds --->
				</cfif>
				<cfif listFindNoCase("reggrid", arguments.mode)>
					SET @memberID = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.memberID#">;
				<cfelse>
					SET @seminarID = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.seminarID#">;
				</cfif>

				INSERT INTO ##tmpEnrollments (userid, enrollmentID, MCMemberID, depoMemberDataID, orgcode, dateEnrolled, dateCompleted, passed, 
					calcTimeSpent, offerCertificate, seminarID, programCode, seminarName, seminarSubTitle, publisherOrgCode, bundleOrderID, 
					handlesOwnPayment, isActive, isFeeExempt)
				SELECT e.userid, e.enrollmentID, m.activeMemberID, d.depoMemberDataID, p.orgcode, e.dateEnrolled, e.dateCompleted, e.passed, eswod.calcTimeSpent, 
					s.offerCertificate, s.seminarID, s.programCode, s.seminarName, s.seminarSubTitle, p2.orgcode as publisherOrgCode, e.bundleOrderID, 
					e.handlesOwnPayment, e.isActive, e.isFeeExempt
				FROM dbo.tblEnrollments e
				INNER JOIN dbo.tblEnrollmentsSWOD eswod ON e.enrollmentID = eswod.enrollmentID 
				INNER JOIN dbo.tblSeminars s ON s.seminarID = e.seminarID 
					AND s.isDeleted = 0
				INNER JOIN dbo.tblParticipants p ON e.participantID = p.participantID 
				INNER JOIN dbo.tblUsers u ON e.userID = u.userID
				INNER JOIN trialsmith.dbo.depomemberdata AS d ON d.depomemberdataID = u.depoMemberDataID 
				INNER JOIN dbo.tblParticipants as p2 on p2.participantID = s.participantID
				INNER JOIN membercentral.dbo.ams_members as m on m.memberID = e.MCMemberID
				WHERE 1=1
				<cfif arguments.rHideDeleted eq 1>
					AND e.isActive = 1
				</cfif>
				<cfif listFindNoCase("reggrid", arguments.mode)>
					AND m.activeMemberID = @memberID
					AND e.participantID = @participantID
				<cfelseif listFindNoCase("massEmailRegGrid,massEmailReg,massEmailCert",arguments.mode)>
					AND e.seminarID = @seminarID
					AND e.participantID = @participantID
				<cfelseif listFindNoCase("grid,export,formResponses",arguments.mode)>
					AND e.seminarID = @seminarID
				</cfif>
				<cfif arguments.rdateFrom NEQ '' and arguments.rdateTo NEQ ''>
					AND e.dateEnrolled BETWEEN @rDateFrom AND @rDateTo
				<cfelseif arguments.rdateFrom NEQ ''>
					AND e.dateEnrolled >= @rDateFrom
				<cfelseif arguments.rdateTo NEQ ''>
					AND e.dateEnrolled <= @rDateTo
				</cfif>
				<cfif arguments.cdateFrom NEQ '' and arguments.cdateTo NEQ ''>
					AND e.dateCompleted BETWEEN @cDateFrom AND @cDateTo
				<cfelseif arguments.cdateFrom NEQ ''>
					AND e.dateCompleted >= @cDateFrom
				<cfelseif arguments.cdateTo NEQ ''>
					AND e.dateCompleted <= @cDateTo
				<cfelseif arguments.completed eq "C">
					AND e.dateCompleted is not null
				<cfelseif arguments.completed eq "I">
					AND e.dateCompleted is null
				</cfif>
				<cfif arguments.pPublisherType EQ 'P'>
					AND s.participantID = @participantID 
				<cfelseif arguments.pPublisherType EQ 'O'>
					AND s.participantID <> @participantID
					AND e.participantID = @participantID
				<cfelse>
					AND (s.participantID = @participantID OR e.participantID = @participantID)
				</cfif>
				<cfif len(arguments.pKeyword)>
					AND s.seminarName + ISNULL(' ' + s.seminarSubTitle,'') LIKE <cfqueryparam cfsqltype="cf_sql_varchar" value="%#arguments.pKeyword#%">
				</cfif>
				<cfif len(arguments.pProgramCode)>
					and s.programCode = <cfqueryparam value="#arguments.pProgramCode#" cfsqltype="cf_sql_varchar">
				</cfif>
				<cfif arguments.pHideInactive is 1>
					AND s.isPublished = 1
				</cfif>
				AND (d.adminflag2 is null or d.adminflag2 <> 'Y');

				<cfif listFindNoCase("reggrid,regsearchgrid,grid",arguments.mode)>
					IF OBJECT_ID('tempdb..##tblEnrollmentInvoices') IS NOT NULL 
						DROP TABLE ##tblEnrollmentInvoices;
					CREATE TABLE ##tblEnrollmentInvoices (enrollmentID int, invoiceID int);

					INSERT INTO ##tblEnrollmentInvoices (enrollmentID, invoiceID)
					select tmp.enrollmentID, it.invoiceID
					from ##tmpEnrollments as tmpOuter
					inner join ##tmpEnrollments as tmp on tmp.enrollmentID = tmpOuter.enrollmentID and tmp.handlesOwnPayment = 1
					cross apply memberCentral.dbo.fn_sw_enrollmentTransactions(tmp.enrollmentID,'SWOD') as et
					inner join memberCentral.dbo.tr_invoiceTransactions as it on it.orgID = et.ownedByOrgID and it.transactionID = et.transactionID
					inner join memberCentral.dbo.tr_invoices as i on i.invoiceID = it.invoiceID
					inner join memberCentral.dbo.tr_invoiceStatuses as invs on invs.statusID = i.statusID
					where invs.status in ('Open','Closed','Delinquent')
					group by tmp.enrollmentID, it.invoiceID
					having sum(it.cache_invoiceAmountAfterAdjustment - it.cache_activePaymentAllocatedAmount - it.cache_pendingPaymentAllocatedAmount) > 0;
				</cfif>

				<cfif NOT listFindNoCase("reggrid,massEmailRegGrid,massEmailReg,massEmailCert", arguments.mode)>
					<cfif listFindNoCase("export,grid",arguments.mode)>
						-- get seminar fees
						IF OBJECT_ID('tempdb..##tmpSeminarsForFee') IS NOT NULL 
							DROP TABLE ##tmpSeminarsForFee; 
						IF OBJECT_ID('tempdb..##tmpSeminarsForFeeResult') IS NOT NULL 
							DROP TABLE ##tmpSeminarsForFeeResult; 
						IF OBJECT_ID('tempdb..##tblEnrollmentFees') IS NOT NULL 
							DROP TABLE ##tblEnrollmentFees;
						create table ##tmpSeminarsForFee (programID int PRIMARY KEY);
						create table ##tmpSeminarsForFeeResult (programID int, enrollmentID int, depoMemberDataID int, transactionID int, TransactionIDForRateAdjustment int);
						CREATE TABLE ##tblEnrollmentFees (enrollmentID int PRIMARY KEY, TransactionIDForRateAdjustment int, totalRegFee decimal(18,2), regFeePaid decimal(18,2));

						INSERT INTO ##tmpSeminarsForFee (programID)
						VALUES (@seminarID);

						EXEC memberCentral.dbo.sw_enrollmentTransactionsBySeminarBulk @swType='SWOD';

						INSERT INTO ##tblEnrollmentFees (enrollmentID, TransactionIDForRateAdjustment, totalRegFee, regFeePaid)
						select fees.enrollmentID, max(fees.TransactionIDForRateAdjustment), sum(ts.cache_amountAfterAdjustment), sum(ts.cache_activePaymentAllocatedAmount)
						from ##tmpSeminarsForFeeResult as fees
						inner join memberCentral.dbo.tr_transactionSales as ts on ts.orgID = @orgID and ts.transactionID = fees.transactionid
						group by fees.enrollmentID;
					</cfif>

					INSERT INTO ##tmpEnrollments2 (enrollmentID, MCMemberID, depomemberdataID, firstName, lastName, memberNumber, company, dateEnrolled, 
						dateCompleted, CreditCount, EmailCount, passed, Progress, showCertButton, signUpOrgCode, 
						seminarID, programCode, publisherOrgCode, <cfif listFindNoCase("regsearchgrid,exportregsearch",arguments.mode)>seminarName, seminarSubTitle,</cfif> 
						bundleOrderID, handlesOwnPayment, amountBilled, amountDue, isActive, isFeeExempt, row)
					SELECT tmp.enrollmentID, tmp.MCMemberID, tmp.depomemberdataid, m.FirstName, m.LastName, m.membernumber, m.Company, tmp.dateEnrolled,
						tmp.dateCompleted, 
						(SELECT COUNT(*) FROM dbo.tblEnrollmentsAndCredit WHERE enrollmentID = tmp.enrollmentID) AS CreditCount,
						(SELECT COUNT(*) FROM dbo.tblLogSWOD WHERE enrollmentID = tmp.enrollmentID AND seminarID = tmp.seminarID AND contact LIKE '%@%') AS EmailCount, 
						tmp.passed, 
						CASE 
							WHEN tmp.passed = 1 and len(tmp.datecompleted) > 0 THEN 1 
							WHEN tmp.passed = 0 and len(tmp.datecompleted) > 0 THEN 2
							ELSE 3
							END as Progress,
						CASE WHEN tmp.offerCertificate = 1 and len(tmp.dateCompleted) > 0 THEN 1 ELSE 0 END as showCertButton, 
						tmp.orgCode,
						tmp.seminarID, tmp.programCode, tmp.publisherOrgCode, 
						<cfif listFindNoCase("regsearchgrid,exportregsearch",arguments.mode)>
							tmp.seminarName, tmp.seminarSubTitle, 
						</cfif>
						tmp.bundleOrderID, tmp.handlesOwnPayment, 
						CASE WHEN tmp.handlesOwnPayment = 0 
							THEN ISNULL((SELECT SUM(dt.amountBilled + dt.salesTaxAmount) 
										FROM trialsmith.dbo.depoTransactionsApplications as dta 
										INNER JOIN trialsmith.dbo.depoTransactions as dt on dt.TransactionID = dta.transactionID
										WHERE dta.itemID = tmp.enrollmentID
										AND dta.itemType = 'SWE'),0)
							ELSE regFee.totalRegFee END as amountBilled,
						CASE WHEN tmp.handlesOwnPayment = 1 THEN regFee.totalRegFee-regFee.regFeePaid ELSE 0 END as amountDue, tmp.isActive, tmp.isFeeExempt,
						ROW_NUMBER() OVER (ORDER BY #preserveSingleQuotes(local.orderby)# #arguments.direct#) AS row
					FROM ##tmpEnrollments AS tmp
					INNER JOIN memberCentral.dbo.ams_members as m on m.memberID = tmp.MCMemberID
					<cfif listFindNoCase("export,grid",arguments.mode)>
						LEFT OUTER JOIN ##tblEnrollmentFees as regFee on regFee.enrollmentID = tmp.enrollmentID
					<cfelse>
						OUTER APPLY memberCentral.dbo.fn_sw_totalRegFeeAndPaid(tmp.enrollmentID,'SWOD') as regFee
					</cfif>;

					SET @totalCount = @@ROWCOUNT;
					
					<cfif listFindNoCase("export,exportregsearch",arguments.mode)>
						IF OBJECT_ID('tempdb..####tmpSWExport#local.tmpSuffix#') IS NOT NULL
			      			DROP TABLE ####tmpSWExport#local.tmpSuffix#;
						IF OBJECT_ID('tempdb..##tmpMembers') IS NOT NULL 
							DROP TABLE ##tmpMembers;
						IF OBJECT_ID('tempdb..##tmp_membersForFS') IS NOT NULL
							DROP TABLE ##tmp_membersForFS;
						IF OBJECT_ID('tempdb..##tmp_CF_ItemIDs') IS NOT NULL
							DROP TABLE ##tmp_CF_ItemIDs;
						IF OBJECT_ID('tempdb..##tmp_CF_FieldData') IS NOT NULL
							DROP TABLE ##tmp_CF_FieldData;
						IF OBJECT_ID('tempdb..##tmpSWODRegFieldData') IS NOT NULL
							DROP TABLE ##tmpSWODRegFieldData;
						IF OBJECT_ID('tempdb..####tmpSWODRegFieldData#local.tmpSuffix#') IS NOT NULL
							DROP TABLE ####tmpSWODRegFieldData#local.tmpSuffix#;
						CREATE TABLE ##tmpMembers (MFSAutoID int IDENTITY(1,1) not null);
						CREATE TABLE ##tmp_membersForFS (memberID int PRIMARY KEY, FirstName varchar(75), LastName varchar(75));
						CREATE TABLE ##tmp_CF_ItemIDs (itemID int, itemType varchar(20));
						CREATE TABLE ##tmp_CF_FieldData (fieldID int, fieldValue varchar(max), amount decimal(14,2), itemID int);

						DECLARE @fieldsetID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.fieldSetID#">,
							@outputFieldsXML xml;

						-- get fieldset data
						INSERT INTO ##tmp_membersForFS (memberID, FirstName, LastName)
						SELECT DISTINCT MCMemberID, firstName, lastName
						FROM ##tmpEnrollments2
						WHERE MCMemberID IS NOT NULL;

						EXEC memberCentral.dbo.ams_getMemberDataByFieldSets @orgID=@orgID, @fieldsetIDList=@fieldsetID, @existingFields='',
							@ovNameFormat=NULL, @ovMaskEmails=NULL, @membersTableName='##tmp_membersForFS', @membersResultTableName='##tmpMembers',
							@linkedMembers=0, @mode='export', @outputFieldsXML=@outputFieldsXML OUTPUT;

						<cfif arguments.fbFormID>
							DECLARE @SWFormID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.fbFormID#">,
								@finalResponseTable varchar(60) = '####SWFBFinalResp' + replace(NEWID(),'-','');

							EXEC dbo.sw_getRegistrantFormResponses @formID=@SWFormID, @onlyFormData=1, @finalResponseTable=@finalResponseTable;
						</cfif>

						-- swod program custom fields
						INSERT INTO ##tmp_CF_ItemIDs (itemID, itemType)
						SELECT DISTINCT enrollmentID, 'SWODRegCustom'
						FROM ##tmpEnrollments2;

						EXEC memberCentral.dbo.cf_getFieldData;

						SELECT fd.itemID AS enrollmentID, replace(f.fieldReference,',','') as titleOnInvoice, fd.fieldValue as answer
						INTO ##tmpSWODRegFieldData
						FROM ##tmp_CF_FieldData AS fd
						INNER JOIN memberCentral.dbo.cf_fields as f on f.fieldID = fd.fieldID
						<cfif listLen(arguments.exportFieldIDList)>
							WHERE f.fieldID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="yes" value="#arguments.exportFieldIDList#">)
						</cfif>;

						-- swod program custom fields pivoted
						select @swodFieldList = COALESCE(@swodFieldList + ',', '') + quoteName(titleonInvoice) from ##tmpSWODRegFieldData group by titleOnInvoice;
						IF left(@swodFieldList,1) = ','
							select @swodFieldList = right(@swodFieldList,len(@swodFieldList)-1);
						IF len(@swodFieldList) > 0 BEGIN
							set @fullsql = '';
							select @fullsql = @fullsql + '
								select * 
								into ####tmpSWODRegFieldData#local.tmpSuffix#
								from (
									select enrollmentID, titleOnInvoice, answer
									from ##tmpSWODRegFieldData
								) as reg
								PIVOT (min(answer) for titleonInvoice in (' + @swodFieldList + ')) as p ';
							EXEC(@fullsql);
						END
						ELSE
							SELECT enrollmentID 
							INTO ####tmpSWODRegFieldData#local.tmpSuffix# 
							FROM ##tmpEnrollments2 
							WHERE 0=1;
							
						SET @fullsql = 'SELECT tmp.dateEnrolled as Registered, tmp.FirstName, tmp.LastName,
							<cfif arguments.mode EQ "exportregsearch">
								tmp.ProgramCode, tmp.SeminarName as ProgramName, tmp.seminarSubTitle as ProgramSubTitle,
							</cfif>
							m.*, tmp.CreditCount as [NumberOfCredits], tmp.EmailCount as [NumberOfCommunications], 
							case when tmp.passed = 1 then ''Yes'' else ''No'' end as [Passed], tmp.DateCompleted, 
							tla.Description as EnrolledOnSite, amountBilled as [AmountBilled], amountDue as [AmountDue],
							case when tmp.isActive = 1 then ''Yes'' else ''No'' end as [isActive],
							case when tmp.isFeeExempt = 1 then ''Yes'' else ''No'' end as [isFeeExempt]';
						<cfif arguments.fbFormID>
							SET @fullsql = @fullsql + ', SWFR.*';
						</cfif>
						SET @fullsql = @fullsql + case when len(@swodFieldList)>0 then ', swfd.' + replace(@swodFieldList,',',',swfd.') else '' end;
						SET @fullsql = @fullsql + ' 
							INTO ####tmpSWExport#local.tmpSuffix#
							FROM ##tmpEnrollments2 as tmp
							LEFT OUTER JOIN trialsmith.dbo.depoTLA as tla on tla.[state] = tmp.SignUpOrgCode
							LEFT OUTER JOIN ##tmpMembers AS m ON m.memberID = tmp.MCMemberID
							LEFT OUTER JOIN ####tmpSWODRegFieldData#local.tmpSuffix# as swfd on swfd.enrollmentID = tmp.enrollmentID
							<cfif arguments.fbFormID>
								LEFT OUTER JOIN ' + @finalResponseTable + ' AS SWFR ON SWFR.SWFREnrollmentID = tmp.enrollmentID
							</cfif>
							ORDER BY tmp.row;';
						
						EXEC(@fullsql);

						ALTER TABLE ####tmpSWExport#local.tmpSuffix# DROP COLUMN memberID;
						<cfif arguments.fbFormID>
							IF EXISTS(SELECT TOP 1 column_id FROM tempdb.sys.columns where [name] = 'SWFREnrollmentID' and object_id = object_id('tempdb..####tmpSWExport#local.tmpSuffix#')) 
								ALTER TABLE ####tmpSWExport#local.tmpSuffix# DROP COLUMN SWFREnrollmentID;
						</cfif>
						
						DECLARE @selectsql varchar(max) = 'SELECT *, ROW_NUMBER() OVER(order by Registered desc, LastName, FirstName) as mcCSVorder *FROM* ####tmpSWExport#local.tmpSuffix#';
						EXEC membercentral.dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename='#application.objCommon.convertFileSeparator("#arguments.folderPathUNC#/#arguments.reportFileName#",'\')#', @returnColumns=0;
							
						IF OBJECT_ID('tempdb..####tmpSWExport#local.tmpSuffix#') IS NOT NULL
						DROP TABLE ####tmpSWExport#local.tmpSuffix#;
						IF OBJECT_ID('tempdb..##tmpMembers') IS NOT NULL 
							DROP TABLE ##tmpMembers;
						IF OBJECT_ID('tempdb..##tmp_membersForFS') IS NOT NULL
							DROP TABLE ##tmp_membersForFS;
						IF OBJECT_ID('tempdb..##tmp_CF_ItemIDs') IS NOT NULL
							DROP TABLE ##tmp_CF_ItemIDs;
						IF OBJECT_ID('tempdb..##tmp_CF_FieldData') IS NOT NULL
							DROP TABLE ##tmp_CF_FieldData;
						IF OBJECT_ID('tempdb..##tmpSWODRegFieldData') IS NOT NULL
							DROP TABLE ##tmpSWODRegFieldData;
						IF OBJECT_ID('tempdb..####tmpSWODRegFieldData#local.tmpSuffix#') IS NOT NULL
							DROP TABLE ####tmpSWODRegFieldData#local.tmpSuffix#;
						<cfif arguments.fbFormID>
							IF OBJECT_ID('tempdb..' + @finalResponseTable) IS NOT NULL
								EXEC('DROP TABLE ' + @finalResponseTable);
						</cfif>

					<cfelseif listFindNoCase("grid,regsearchgrid",arguments.mode)>
						SELECT 
							tmp.seminarName, tmp.seminarSubTitle,
							tmp.enrollmentID, tmp.seminarID, tmp.depomemberdataID, tmp.firstName, tmp.lastName, tmp.memberNumber, tmp.company, 
							tmp.dateEnrolled, tmp.CreditCount, tmp.EmailCount, tmp.passed, tmp.Progress, tmp.showCertButton, tmp.signUpOrgCode, 
							tmp.bundleOrderID, tmp.handlesOwnPayment, tmp.amountBilled, tmp.amountDue, @totalCount as totalCount, 
							tla.Description as signUpOrgName, tmp.publisherOrgCode, tmp.isActive, tmp.isFeeExempt,
							case when tmp.handlesOwnPayment = 1 then case when tmp.amountDue > 0 
																			then (select substring((
																				  select ','+ cast(invoiceID as varchar(10)) AS [text()]
																					from ##tblEnrollmentInvoices
																					where enrollmentID = tmp.enrollmentID
																					For XML PATH ('')
																					), 2, 2000)) 
																			else '' end
								else null end as invoicesDue, m.memberID as memberID
						FROM ##tmpEnrollments2 as tmp
						INNER JOIN trialsmith.dbo.depoTLA as tla on tla.[state] = tmp.SignUpOrgCode
						LEFT OUTER JOIN memberCentral.dbo.ams_members as m on m.orgID = @orgID AND m.memberID = tmp.MCMemberID
						WHERE tmp.row > #arguments.posStart# AND tmp.row <= #arguments.posStart + arguments.count#
						ORDER BY tmp.row;
					<cfelseif arguments.mode eq 'formResponses'>
						DECLARE @formID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.fbFormID#">;
						<cfif arguments.fbExportType eq "csv">
							EXEC dbo.sw_exportRegistrantFormResponses @formID=@formID, @filename='#arguments.folderPathUNC#\#arguments.reportFileName#';
							SELECT 1 AS "success";
						<cfelseif arguments.fbExportType eq "pdf">
							DECLARE @xmlResult xml;
							EXEC dbo.sw_getRegistrantsFormResponseSummaryXML @formID=@formID, @xmlResult=@xmlResult OUTPUT;
							SELECT @xmlResult AS xmlResult;
						</cfif>
					</cfif>
				<cfelseif listFindNoCase("massEmailReg,massEmailRegGrid,massEmailCert",arguments.mode)>
					DECLARE @emailTagTypeID int;
					SET @emailTagTypeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.emailTagTypeID#">;

					<cfif arguments.mode eq 'massEmailRegGrid'>
						DECLARE @posStart int, @posStartAndCount int;
						SET @posStart = <cfqueryparam value="#arguments.posStart#" cfsqltype="CF_SQL_INTEGER">;
						SET @posStartAndCount = @posStart + <cfqueryparam value="#arguments.count#" cfsqltype="CF_SQL_INTEGER">;

						IF OBJECT_ID('tempdb..##tblRegistrants') IS NOT NULL 
							DROP TABLE ##tblRegistrants;
						CREATE TABLE ##tblRegistrants (enrollmentID int, depomemberdataID int, firstname varchar(75), lastname varchar(75), membernumber varchar(50),
							company varchar(200), email varchar(400), row int);

						INSERT INTO ##tblRegistrants
						SELECT e.enrollmentID, e.depomemberdataID, m.firstname, m.lastName, m.memberNumber, m.company, me.email,
							ROW_NUMBER() OVER (order by (m.lastName + m.firstname + m.memberNumber) #arguments.direct#) as row
						FROM ##tmpEnrollments as e
						INNER JOIN memberCentral.dbo.ams_members as m on m.memberID = e.MCMemberID
						INNER JOIN memberCentral.dbo.ams_memberEmails as me ON me.orgID = @orgID
							AND me.memberID = m.memberID
						INNER JOIN memberCentral.dbo.ams_memberEmailTags as metag on metag.orgID = @orgID 
							AND metag.memberID = me.memberID
							AND metag.emailTypeID = me.emailTypeID
						INNER JOIN memberCentral.dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = @orgID
							AND metagt.emailTagTypeID = metag.emailTagTypeID
							AND metagt.emailTagTypeID = @emailTagTypeID;

						SELECT @totalCount = @@ROWCOUNT;

						SELECT enrollmentID, depomemberdataID, firstname, lastname, membernumber, company, email, @totalCount as totalCount
						FROM ##tblRegistrants
						WHERE row > @posStart
						AND row <= @posStartAndCount
						ORDER by row;

						IF OBJECT_ID('tempdb..##tblRegistrants') IS NOT NULL 
							DROP TABLE ##tblRegistrants;
					<cfelse>
						DECLARE @membersWithEmail int;

						SELECT @membersWithEmail = count(*)
						FROM ##tmpEnrollments as e
						INNER JOIN memberCentral.dbo.ams_members as m on m.memberID = e.MCMemberID
						INNER JOIN memberCentral.dbo.ams_memberEmails as me on me.orgID = @orgID
							AND me.memberID = m.memberID
							AND me.email <> ''
						INNER JOIN memberCentral.dbo.ams_memberEmailTags as metag on metag.orgID = @orgID 
							and metag.memberID = me.memberID
							AND metag.emailTypeID = me.emailTypeID
						INNER JOIN memberCentral.dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = @orgID
							AND metagt.emailTagTypeID = metag.emailTagTypeID
							AND metagt.emailTagTypeID = @emailTagTypeID;

						SELECT distinct e.enrollmentID, e.depomemberdataID, m.membernumber, @membersWithEmail as membersWithEmail
						FROM ##tmpEnrollments as e
						INNER JOIN memberCentral.dbo.ams_members as m on m.memberID = e.MCMemberID
						ORDER BY e.enrollmentID;
					</cfif>
				<cfelseif arguments.mode eq 'reggrid'>
					INSERT INTO ##tmpEnrollments2 (enrollmentID, depomemberdataID, dateEnrolled, dateCompleted, CreditCount, EmailCount, passed, Progress, 
						showCertButton, signUpOrgCode, seminarID, seminarName, seminarSubTitle, publisherOrgCode, bundleOrderID, handlesOwnPayment, 
						amountBilled, amountDue, isActive, isFeeExempt, row)
					SELECT tmp.enrollmentID, tmp.depomemberdataID, tmp.dateEnrolled, tmp.dateCompleted, 
						(SELECT COUNT(*) FROM dbo.tblEnrollmentsAndCredit WHERE enrollmentID = tmp.enrollmentID) AS CreditCount,
						(SELECT COUNT(*) FROM dbo.tblLogSWOD WHERE enrollmentID = tmp.enrollmentID AND seminarID = tmp.seminarID AND contact LIKE '%@%') AS EmailCount, 
						tmp.passed, 
						CASE 
							WHEN tmp.passed = 1 and len(tmp.datecompleted) > 0 THEN 1 
							WHEN tmp.passed = 0 and len(tmp.datecompleted) > 0 THEN 2
							ELSE 3
							END as Progress,
						CASE WHEN tmp.offerCertificate = 1 and len(tmp.dateCompleted) > 0 THEN 1 ELSE 0 END as showCertButton, 
						tmp.orgCode, tmp.seminarID, tmp.seminarName, tmp.seminarSubTitle, tmp.publisherOrgCode, 
						tmp.bundleOrderID, tmp.handlesOwnPayment, 
						CASE WHEN tmp.handlesOwnPayment = 0 
							THEN ISNULL((SELECT SUM(dt.amountBilled + dt.salesTaxAmount) 
										FROM trialsmith.dbo.depoTransactionsApplications as dta 
										INNER JOIN trialsmith.dbo.depoTransactions as dt on dt.TransactionID = dta.transactionID
										WHERE dta.itemID = tmp.enrollmentID
										AND dta.itemType = 'SWE'),0)
							ELSE regFee.totalRegFee END as amountBilled, 
						CASE WHEN tmp.handlesOwnPayment = 1 THEN regFee.totalRegFee-regFee.regFeePaid ELSE 0 END as amountDue, tmp.isActive, tmp.isFeeExempt,
						ROW_NUMBER() OVER (ORDER BY #preserveSingleQuotes(local.orderby)# #arguments.direct#) AS row
					FROM ##tmpEnrollments AS tmp
					OUTER APPLY memberCentral.dbo.fn_sw_totalRegFeeAndPaid(tmp.enrollmentID,'SWOD') as regFee;

					SET @totalCount = @@ROWCOUNT;

					SELECT tmp.enrollmentID, tmp.depomemberdataID, tmp.seminarID, tmp.seminarName, tmp.seminarSubTitle, tmp.dateEnrolled, tmp.CreditCount, tmp.EmailCount, 
						tmp.passed, tmp.Progress, tmp.showCertButton, tmp.signUpOrgCode, tmp.bundleOrderID, tmp.handlesOwnPayment, tmp.amountBilled, tmp.amountDue, 
						case when tmp.handlesOwnPayment = 1 then case when tmp.amountDue > 0 
																		then (select substring((
																			  select ','+ cast(invoiceID as varchar(10)) AS [text()]
																				from ##tblEnrollmentInvoices
																				where enrollmentID = tmp.enrollmentID
																				For XML PATH ('')
																				), 2, 2000)) 
																		else '' end
							else null end as invoicesDue, 
						tla.Description as signUpOrgName, tmp.publisherOrgCode, tmp.isActive, tmp.isFeeExempt, @totalCount as totalCount
					FROM ##tmpEnrollments2 as tmp
					INNER JOIN trialsmith.dbo.depoTLA as tla on tla.[state] = tmp.SignUpOrgCode
					WHERE tmp.row > #arguments.posStart# AND tmp.row <= #arguments.posStart + arguments.count#
					ORDER BY tmp.row;
				<cfelse>
					SELECT enrollmentID 
					FROM ##tmpEnrollments;
				</cfif>
				
				IF OBJECT_ID('tempdb..##tmpEnrollments') IS NOT NULL 
					DROP TABLE ##tmpEnrollments;
				IF OBJECT_ID('tempdb..##tmpEnrollments2') IS NOT NULL 
					DROP TABLE ##tmpEnrollments2;
				IF OBJECT_ID('tempdb..##tmpRegistrantsDataMC') IS NOT NULL 
					DROP TABLE ##tmpRegistrantsDataMC;
				<cfif listFindNoCase("reggrid,regsearchgrid,grid",arguments.mode)>
					IF OBJECT_ID('tempdb..##tblEnrollmentInvoices') IS NOT NULL 
						DROP TABLE ##tblEnrollmentInvoices;
				</cfif>

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfif listFindNoCase("grid,reggrid,regsearchgrid,massEmailRegGrid,massEmailReg,formResponses,massEmailCert", arguments.mode)>
			<cfreturn local.qryEnrollments>
		</cfif>
	</cffunction>

	<cffunction name="sendTestInvitationEmail" access="public" returntype="void" output="no">
		<cfargument name="seminarIDlist" type="string" required="yes">
		<cfargument name="testEmail" type="string" required="yes">
		<cfargument name="performedBy" type="numeric" required="yes">
		<cfargument name="outgoingType" type="string" required="yes">
		<cfargument name="orgcode" type="string" required="yes">
		<cfargument name="subject" type="string" required="yes">
		<cfargument name="preheaderText" type="string" required="true">
		<cfargument name="template" type="string" required="true">

		<cfset var local = structnew()>
		<cfset local.objSWOD = CreateObject("component","model.seminarweb.SWODSeminars")>
		
		<cfif len(arguments.testEmail) is 0 or NOT IsValid("regex",arguments.testEmail,application.regEx.email)>
			<cfreturn>
		</cfif>

		<cfif ListLen(arguments.seminarIDlist) EQ 1>
			<cfset local.utm_campaign = local.objSWOD.getSeminarBySeminarID(seminarID=val(arguments.seminarIDlist)).seminarName>
		<cfelse>
			<cfset local.utm_campaign = 'Multiple Programs'>
		</cfif>

		<cfset local.qrySend = generateInvitationTable(seminarIDlist=arguments.seminarIDlist,orgcode=arguments.orgcode)>
		<cfoutput query="local.qrySend" group="orgcode">
			<cfquery name="local.qrySendIndiv" dbtype="query">
				select seminarID
				from [local].qrySend
				where orgcode = '#local.qrySend.orgcode#'
			</cfquery>
			
			<cfif arguments.template eq '2021'>
				<cfset local.strEmailContent = generateInvitationEmailFor2021(seminarIDlist=valuelist(local.qrySendIndiv.seminarID), orgcode=local.qrySend.orgcode, subject=arguments.subject, preheaderText=arguments.preheaderText)>
			<cfelseif arguments.template eq '2020'>
				<cfset local.strEmailContent = generateInvitationEmailFor2020(seminarIDlist=valuelist(local.qrySendIndiv.seminarID), orgcode=local.qrySend.orgcode, subject=arguments.subject)>
			<cfelseif arguments.template eq '2017NONSAE'>
				<cfset local.strEmailContent = generateInvitationEmailFor2017NONSAE(seminarIDlist=valuelist(local.qrySendIndiv.seminarID), orgcode=local.qrySend.orgcode, subject=arguments.subject)>
			</cfif>

			<cfset local.strEmailContent.html = application.objEmailWrapper.appendUTMCodesToLinks(htmlcontent=local.strEmailContent.html, utm_campaign=local.utm_campaign,
				utm_source="SWOD Marketing", utm_medium="email", utm_content="#DateFormat(Now(),'YYYY-MM-DD')#-#local.strEmailContent.subject#")>

			<cfif application.MCEnvironment neq "production">
				<cfset arguments.testEmail = "<EMAIL>">
			</cfif>

			<!--- send email --->
			<cfmail to="#arguments.testEmail#" from="<EMAIL> (""#local.strEmailContent.fromName#"")" subject="**TEST** #local.strEmailContent.subject#" mailerid="SeminarWeb" type="html" charset="utf-8">
				#local.strEmailContent.html#
			</cfmail>
			
			<cfoutput>
			<cfset local.objSWOD.logAction(local.qrySend.seminarID,arguments.outgoingType,arguments.performedBy,arguments.testEmail,0,0)>
			</cfoutput>
		</cfoutput>
	</cffunction>

	<cffunction name="generateInvitationTable" access="private" returntype="query" output="no">
		<cfargument name="seminarIDlist" type="string" required="yes">
		<cfargument name="orgcode" type="string" required="yes">

		<cfset var local = structnew()>
		<cfset local.objSWPAdmin = CreateObject("component","model.admin.seminarweb.seminarWebParticipants")>

		<cfset local.tmpOptedIn = ArrayNew(1)>
		<cfset local.tmpArr = ListToArray(arguments.seminarIDlist)>
		<cfset ArraySet(local.tmpOptedIn,1,ArrayMax(local.tmpArr),'')>
		<cfloop list="#arguments.seminarIDlist#" index="local.thisSID">
			<cfset local.tmpOptedIn[local.thisSID] = local.objSWPAdmin.getParticipantsOptedIntoSeminar(seminarID=local.thisSID)>
		</cfloop>
		<cfset local.qrySend = QueryNew("orgcode,seminarID","varchar,integer")>

		<cfif len(arguments.orgcode)>
			<cfloop list="#arguments.seminarIDlist#" index="local.thisSID">
				<cfset local.tmpqry = local.tmpOptedIn[local.thisSID]>
				<cfif listFindNoCase(valuelist(local.tmpqry.orgcode),arguments.orgcode)>
					<cfset QueryAddRow(local.qrySend)>
					<cfset QuerySetCell(local.qrySend,"orgcode",UCASE(arguments.orgcode))>
					<cfset QuerySetCell(local.qrySend,"seminarID",local.thisSID)>
				</cfif>
			</cfloop>
		<cfelse>
			<cfset local.qryAssociations = local.objSWPAdmin.getParticipantsByFormat('SWOD')>
			<cfloop query="local.qryAssociations">
				<cfset local.tmpOrgcode = UCASE(local.qryAssociations.orgcode)>
				<cfloop list="#arguments.seminarIDlist#" index="local.thisSID">
					<cfset local.tmpqry = local.tmpOptedIn[local.thisSID]>
					<cfif listFindNoCase(valuelist(local.tmpqry.orgcode),local.tmpOrgcode)>
						<cfset QueryAddRow(local.qrySend)>
						<cfset QuerySetCell(local.qrySend,"orgcode",local.tmpOrgcode)>
						<cfset QuerySetCell(local.qrySend,"seminarID",local.thisSID)>
					</cfif>
				</cfloop>
			</cfloop>
		</cfif>

		<cfreturn local.qrySend>
	</cffunction>

	<cffunction name="generateInvitationEmailFor2021" access="public" returntype="struct" output="no">
		<cfargument name="seminarIDlist" type="string" required="yes">
		<cfargument name="orgcode" type="string" required="yes">
		<cfargument name="subject" type="string" required="yes">
		<cfargument name="preheaderText" type="string" required="true">

		<cfset var local = structnew()>
		<cfset local.strEmailContent = StructNew()>
		<cfset local.objSWP = CreateObject("component","model.seminarweb.SWParticipants")>
		<cfset local.objSWOD = CreateObject("component","model.seminarweb.SWODSeminars")>
		<cfset local.objAuthor = CreateObject("component","model.seminarweb.SWAuthors")>
		<cfset local.objWebsite = CreateObject("component","model.admin.website.website")>
		<cfset local.objSWCommon = CreateObject("component","seminarWebSWCommon")>
		<cfset local.objResourceTemplate = CreateObject("component","model.admin.common.modules.resourceTemplates.resourceTemplate")>
		<cfset local.strAssociation = local.objSWP.getAssociationDetails(arguments.orgCode)>
		<cfset local.qryProgramCredits = local.objSWCommon.getSWProgramCredits(seminarIDList=arguments.seminarIDlist)>
		<cfset local.qryOrgIdentity = application.objOrgInfo.getOrgIdentity(orgIdentityID=local.strAssociation.qryAssociation.orgIdentityID)>
		<cfset local.qrySWHostName = local.objSWP.getSWHostName()>
		<cfset local.qryPlatformFeaturedImageSetup = createObject("component","model.admin.common.modules.featuredImages.featuredImages").getPlatformFeaturedImagesSetup()>
		<cfset local.qryParticipantFeaturedImageSetup = local.strAssociation.qryParticipantFeaturedImageSetup>

		<cfset local.programsArr = ArrayNew(1)>
		<cfset local.programLevelFeaturedImageCount = 0>
		<cfloop list="#arguments.seminarIDlist#" index="local.thisSeminarID">
			<cfset local.tmpStr = StructNew()>
			<cfset local.tmpStr.seminarID = local.thisSeminarID>
			<cfset local.tmpStr.strSeminar = local.objSWOD.getSeminarForCatalog(seminarID=local.thisSeminarID, catalogOrgCode=arguments.orgCode, billingState='', billingZip='', depoMemberDataID=0, memberID=0)>
			<cfif local.tmpStr.strSeminar.qrySeminar.recordCount>
				<cfset local.tmpStr.trimmedProgramDesc = local.objSWCommon.trimByWordsCount(inputString=local.tmpStr.strSeminar.qrySeminar.SeminarDesc, count=100)>

				<cfset local.creditDisplayInfoStr = local.objSWCommon.getSWCreditDisplayInfoForInvitationEmail(seminarID=local.thisSeminarID, qryProgramCredits=local.qryProgramCredits)>
				<cfset local.tmpStr.creditAuthorityCount = local.creditDisplayInfoStr.creditAuthorityCount>
				<cfset local.tmpStr.dspCredits = local.creditDisplayInfoStr.dspCredits>

				<!--- featured image paths --->
				<cfif local.strAssociation.qryAssociation.hasSWProgramImageConfiguration>
					<cfset local.programFeaturedImagesStr = local.objSWCommon.getProgramFeaturedImagePathsForInvitationEmail(publisherOrgCode=local.tmpStr.strSeminar.qrySeminar.publisherOrgCode, programType="SWOD",
						programFeatureImageID=val(local.tmpStr.strSeminar.qrySeminar.featureImageID), programFeatureImageSizeID=val(local.tmpStr.strSeminar.qrySeminar.featureImageSizeID),
						programFeatureImageFileExtension=local.tmpStr.strSeminar.qrySeminar.featureImageFileExtension, qryPlatformFeaturedImageSetup=local.qryPlatformFeaturedImageSetup,
						qryParticipantFeaturedImageSetup=local.qryParticipantFeaturedImageSetup, qrySWHostName=local.qrySWHostName)>
					
					<cfset local.tmpStr.programLevelFeaturedImagePath = local.programFeaturedImagesStr.programLevelFeaturedImagePath>
					<cfif len(local.tmpStr.programLevelFeaturedImagePath)>
						<cfset local.programLevelFeaturedImageCount += 1>
					</cfif>
					<cfset local.tmpStr.featuredImagePath = local.programFeaturedImagesStr.featuredImagePath>
				<cfelse>
					<cfset local.tmpStr.programLevelFeaturedImagePath = "">
					<cfset local.tmpStr.featuredImagePath = "">
				</cfif>
				
				<cfset local.publisherSiteInfo = application.objSiteInfo.getSiteInfo(local.tmpStr.strSeminar.qrySeminar.publisherOrgCode)>
				<cfset local.publisherHostName = local.objWebsite.getMainHost(val(local.publisherSiteInfo.siteID)).mainHostname>
				<cfset local.tmpStr.browsePageLink = "#local.strAssociation.qryAssociation.CatalogURL#/?panel=browse&_swft=swod">
				<cfset local.tmpStr.programDetailPageLink = "#local.strAssociation.qryAssociation.CatalogURL#/?d=SWOD-#local.tmpStr.seminarID#">

				<cfset ArrayAppend(local.programsArr,local.tmpStr)>
			</cfif>
		</cfloop>
			
		<!--- set from/subject --->
		<cfif len(local.strAssociation.qryAssociation.emailFrom)>
			<cfset local.strEmailContent.fromName = local.strAssociation.qryAssociation.emailFrom>
		<cfelse>
			<cfset local.strEmailContent.fromName = "SeminarWeb">
		</cfif>
		<cfset local.strEmailContent.subject = arguments.subject>
		
		<cfsavecontent variable="local.strEmailContent.html">
			<cfoutput>
			<cfif arrayLen(local.programsArr) eq 1>
				<cfset local.thisProgram = local.programsArr[1]>
				<cfset local.strSeminar = local.thisProgram.strSeminar>
				<cfset local.upcomingProgramsArr = local.objSWCommon.getUpcomingProgramsForInvitationEmail(siteCode=arguments.orgCode, programID=local.thisProgram.seminarID, programType="SWOD")>

				<cfset local.qryAuthorsAll = local.objAuthor.getAuthorsBySeminarID(seminarID=local.thisProgram.seminarID, authorType='Speaker')>
				<cfquery name="local.qrySpeakers" dbtype="query">
					select * from [local].qryAuthorsAll where displayOnWebsite = 1;
				</cfquery>

				<cfif local.qrySpeakers.recordcount GT 1>
					<cfset local.bioCount = 250>
				<cfelse>
					<cfset local.bioCount = 500>
				</cfif>
				<cfset local.speakerFeaturedImagesList = valueList(local.qrySpeakers.featureImageID)>
				<cfset local.speakersWithPhotosCount = listLen(local.speakerFeaturedImagesList.listFilter(function(id) { return arguments.id gt 0; } ))>

				<cfinclude template="dsp_programInvitationTemplate_swod_single.cfm">
			<cfelseif arrayLen(local.programsArr) gt 1>
				<cfinclude template="dsp_programInvitationTemplate_swod_multiple.cfm">
			</cfif>
			</cfoutput>
		</cfsavecontent>

		<cfset local.strRenderedTemplate = local.objResourceTemplate.doRenderResourceTemplate(template=local.strEmailContent.html, model={}, templateFormat="MJML")>

		<!--- dont replace 13 and 10 with space -- it leads to screwed up messages in lyris all being on one line --->
		<cfset local.strEmailContent.html = trim(replace(local.strRenderedTemplate.content,chr(9),"","ALL"))>

		<!--- replacing chr(7) with CRLF to ensure that Lyris merge code is processed correctly. --->
		<cfset local.strEmailContent.html = replace(local.strEmailContent.html,chr(7),chr(13) & chr(10),"all")>

		<cfreturn local.strEmailContent>
	</cffunction>

	<cffunction name="generateInvitationEmailFor2020" access="public" returntype="struct" output="no">
		<cfargument name="seminarIDlist" type="string" required="yes">
		<cfargument name="orgcode" type="string" required="yes">
		<cfargument name="subject" type="string" required="yes">

		<cfset var local = structnew()>
		<cfset local.strEmailContent = StructNew()>
		<cfset local.objSWP = CreateObject("component","model.seminarweb.SWParticipants")>
		<cfset local.qryAssociationDetails = local.objSWP.getAssociationDetails(arguments.orgcode).qryAssociation>
		<cfset local.qrySWHostName = local.objSWP.getSWHostName()>
		<cfset local.qryCredit = CreateObject("component","model.seminarweb.SWCredits").getCreditsforSeminarList(arguments.seminarIDlist)>
		<cfset local.objAuthor = CreateObject("component","model.seminarweb.SWAuthors")>
		<cfset local.objSWOD = CreateObject("component","model.seminarweb.SWODSeminars")>

		<cfset local.seminarIDs = ArrayNew(1)>
		<cfloop list="#arguments.seminarIDlist#" index="local.thisSID">
			<cfquery name="local.qryCreditDistinct" dbtype="query">
				select distinct Code
				from [local].qryCredit
				where seminarid = #local.thisSID#
				order by Code
			</cfquery>
			
			<cfset local.tmpStr = StructNew()>
			<cfset local.tmpStr.seminarID = local.thisSID>
			<cfset local.tmpStr.strSeminar = getSeminarForRegistrationByAdmin(seminarID=local.thisSID, catalogOrgCode=arguments.orgcode, billingState="", billingZip="", depoMemberDataID=0, memberID=0)>

			<cfset local.tmpStr.creditList = valueList(local.qryCreditDistinct.code)>
			<cfset local.tmpStr.qrySpeakers = local.objAuthor.getAuthorsBySeminarID(seminarID=local.thisSID, authorType='Speaker')>
			<cfset ArrayAppend(local.seminarIDs,local.tmpStr)>
		</cfloop>

		<!--- set from/subject --->
		<cfif len(local.qryAssociationDetails.emailFrom)>
			<cfset local.strEmailContent.fromName = local.qryAssociationDetails.emailFrom>
		<cfelse>
			<cfset local.strEmailContent.fromName = "SeminarWeb">
		</cfif>
		<cfset local.strEmailContent.subject = arguments.subject>

		<cfsavecontent variable="local.strEmailContent.html">
			<cfoutput>
			<html>
			<head></head>
			<body topmargin="0" leftmargin="0" marginwidth="0" marginheight="0" bgcolor="##ffffff" text="##000000" link="##114189">
			<br/>
			<div style="font-family:Verdana,Arial,Helvetica,sans-serif;font-size:10pt;color:##666;">
				#local.qryAssociationDetails.description# invites you to register for the following online seminar<cfif arraylen(local.seminarIDs) gt 1>s</cfif>:
			</div>			
			<cfif arraylen(local.seminarIDs) gt 1>
				<div align="left" style="font-family:Verdana,Arial,Helvetica,sans-serif;font-size:12pt;color:##666;">
				<ol>
				<cfloop from="1" to="#arraylen(local.seminarIDs)#" index="local.thisID">
					<li><a href="###local.seminarIDs[local.thisID].seminarID#">#encodeForHTML(local.seminarIDs[local.thisID].strSeminar.qrySeminar.seminarName)#</a></li>
				</cfloop>
				</ol>
				</div>
			</cfif>
			<br/>
			<cfloop from="1" to="#arraylen(local.seminarIDs)#" index="local.thisID">
				<a name="#local.seminarIDs[local.thisID].seminarID#"></a>
				<table width="95%" border="0" cellpadding="4" cellspacing="0" style="border:1px solid ##ccc;">
				<tr>
					<td colspan="3" style="font-family:Verdana,Arial,Helvetica,sans-serif;color:##0E568D;font-weight:bold;font-size:12pt;margin-bottom:4px;">
						#encodeForHTML(local.seminarIDs[local.thisID].strSeminar.qrySeminar.seminarName)#
					</td>
				</tr>
				<tr valign="top">
					<td style="font-family:Verdana,Arial,Helvetica,sans-serif;font-size:8pt;color:##666;">
						<b style="color:##0E568D;">Summary:</b><br/>
						<div style="margin:2px 20px;line-height:1.2em;">
							#left(local.seminarIDs[local.thisID].strSeminar.qrySeminar.seminarDesc,500)#
							<cfif len(local.seminarIDs[local.thisID].strSeminar.qrySeminar.seminarDesc) gt 500>... [<a href="#local.qryAssociationDetails.CatalogURL#/?d=SWOD-#local.seminarIDs[local.thisID].seminarID#">more</a>]</cfif>
						</div>
						<br/>
						<cfset local.qryAuthors = local.seminarIDs[local.thisID].qrySpeakers>
						<cfif local.qryAuthors.recordcount>
							<b style="color:##0E568D;">Presenter<cfif local.qryAuthors.recordcount GT 1>s</cfif>:</b><br/>
							<div style="margin:2px 20px;">
								<table cellpadding="3">
								<cfloop query="local.qryAuthors">
									<tr valign="top">
									<cfif val(local.qryAuthors.featureImageID) gt 0>
										<td><img src="#local.qrySWHostName.scheme#://#local.qrySWHostName.mainHostName#/userassets/#LCASE(local.qryAuthors.featureImageOrgCode)#/#LCASE(local.qryAuthors.featureImageSiteCode)#/featuredimages/thumbnails/#local.qryAuthors.featureImageID#-#local.qryAuthors.featureImageSizeID#.#local.qryAuthors.fileExtension#" alt="#local.qryAuthors.firstname# #local.qryAuthors.middlename# #local.qryAuthors.lastname#" /></td>
									<cfelse>
										<td></td>
									</cfif>
									<td style="font-family:Verdana,Arial,Helvetica,sans-serif;font-size:8pt;color:##666;line-height:1.2em;">
										<b>#prefix# #firstname# #middlename# #lastname#<cfif len(suffix)>, #suffix#</cfif></b><br/>
										#left(biography,250)#
										<cfif len(biography) gt 250>... [<a href="#local.qryAssociationDetails.CatalogURL#/?d=SWOD-#local.seminarIDs[local.thisID].seminarID#">more</a>]</cfif>
									</td>
									</tr>
								</cfloop>
								</table>
							</div>
							<br/>
						</cfif>
						<b style="color:##0E568D;">Credit:</b><br/>
						<div style="margin:2px 20px;line-height:1.4em;">
							<cfif listLen(local.seminarIDs[local.thisID].creditlist)>
								#replace(local.seminarIDs[local.thisID].creditlist,",",", ","ALL")#<br/><br/>
								Detailed credit information is available on the <a href="#local.qryAssociationDetails.CatalogURL#/?d=SWOD-#local.seminarIDs[local.thisID].seminarID#">registration page</a>.
							</cfif>
							<br/><br/>
						</div>
						<b style="color:##0E568D;">Originally Published:</b><br/>
						<div style="margin:2px 20px;line-height:1.4em;">#DateFormat(local.seminarIDs[local.thisID].strSeminar.qrySeminar.dateOrigPublished,"mmmm d, yyyy")#</div>
						<br/>
						<cfset local.qryPreReqs = local.objSWOD.getSeminarPreReqs(seminarID=local.seminarIDs[local.thisID].strSeminar.qrySeminar.seminarID, depoMemberDataID=0)>
						<cfif local.qryPreReqs.recordcount>
							<b style="color:##0E568D;">Program Prerequisites:</b><br/>
							<div style="margin:2px 20px;line-height:1.4em;">
								You must successfully complete the following prerequisites prior to beginning this program:<br/>
								<ul>
								<cfloop query="local.qryPreReqs">
									<li><a href="#local.qryAssociationDetails.CatalogURL#/?d=SWOD-#local.qryPreReqs.seminarID#">#encodeForHTML(local.qryPreReqs.seminarName)#</a></li>
								</cfloop>
								</ul>
							</div>
						</cfif>
						<b style="color:##0E568D;">How to Attend:</b><br/>
						<div style="margin:2px 20px;line-height:1.4em;">
							Join the self-paced program from your office, home, or hotel room using a computer and high speed 
							internet connection. You may start and stop the program at your convenience, continue where you left off, 
							and review supporting materials as often as you like. 
						</div>
				</td>
				<td width="10">&nbsp; &nbsp;</td>
				<td width="190" style="font-family:Verdana,Arial,Helvetica,sans-serif;font-size:8pt;color:##666;">
					<div style="border:1px solid ##DEDEDE;">
						<div style="padding:4px;background-color:##DEDEDE;font-weight:bold;">Registration</div>
						<div style="padding:4px;">
							Register now for immediate access to this program.<br/><br/>
							
							<cfset local.tmpPrices = local.seminarIDs[local.thisID].strSeminar.qrySeminarPrices>
							<cfloop query="local.tmpPrices">
								<cfif local.tmpPrices.price gte 0>
									<cfif local.tmpPrices.price is 0>
										#replace(local.seminarIDs[local.thisID].strSeminar.qrySeminar.freeRateDisplay,".00","")#
									<cfelseif local.tmpPrices.price gt 0>
										#replace(dollarformat(local.tmpPrices.price),".00","")#<cfif local.seminarIDs[local.thisID].strSeminar.qrySeminar.showUSD> USD</cfif>
									</cfif>
									<cfif len(local.tmpPrices.description)> <cfif local.tmpPrices.price gt 0 or len(local.seminarIDs[local.thisID].strSeminar.qrySeminar.freeRateDisplay)>for </cfif>#local.tmpPrices.description#</cfif><br/>
								</cfif>
							</cfloop>
							<br/>
							<div align="center">
								<a href="#local.qryAssociationDetails.CatalogURL#/?d=SWOD-#local.seminarIDs[local.thisID].seminarID#" target="_blank"><b><span style="font-family:Verdana,Arial,sans-serif;font-size:12pt;">Register Now</span></b></a>
							</div>
						</div>
					</div>	
					<br/>
					<div style="border:1px solid ##DEDEDE;">
						<div style="padding:4px;background-color:##DEDEDE;font-weight:bold;">Tell a Colleague!</div>
							<span class="st_facebook_large">
								<span style="padding:5px;text-decoration:none;color:##000000;display:inline-block;cursor:pointer;border=0">
									<a target="_blank" title="Share this on Facebook" href="https://www.facebook.com/sharer/sharer.php?u=#local.qryAssociationDetails.CatalogURL#/?d=SWOD-#local.seminarIDs[local.thisID].seminarID#" target="_blank">
										<img width="32" height="32" alt="Share this on Facebook" src="http://ws.sharethis.com/images/facebook_32.png" border=0>
									</a>
								</span>
							</span>
							<span class="st_linkedin_large">
								<span style="padding:5px;text-decoration:none;color:##000000;display:inline-block;cursor:pointer;border=0">
									<a title="Share this on Linked In" href="http://www.linkedin.com/shareArticle?mini=true&url=#local.qryAssociationDetails.CatalogURL#/?d=SWOD-#local.seminarIDs[local.thisID].seminarID#" target="_blank">
										<img src="http://ws.sharethis.com/images/linkedin_32.png" height="32" width="32" border=0>
									</a>
								</span>
							</span>
							<span class="st_email_large">
								<span style="padding:5px;text-decoration:none;color:##000000;display:inline-block;cursor:pointer;border=0">
									<a title="Email this to a colleague" href="mailto:?Subject=#URLEncodedFormat(local.seminarIDs[local.thisID].strSeminar.qrySeminar.seminarName)#&Body=Hello - %0A%0AI thought you would be interested in '#URLEncodedFormat(local.seminarIDs[local.thisID].strSeminar.qrySeminar.seminarName)#.' You can register for the seminar at #local.qryAssociationDetails.CatalogURL#/?d=SWOD-#local.seminarIDs[local.thisID].seminarID#." target="_blank">
										<img src="http://ws.sharethis.com/images/email_32.png" height="32" width="32" border=0>
									</a>
								</span>
							</span>
							<span class="st_twitter_large">
								<span style="padding:5px;text-decoration:none;color:##000000;display:inline-block;cursor:pointer;border=0">
									<a title="Share this on Twitter" href="http://twitter.com/intent/tweet?source=webclient&text=#URLEncodedFormat(local.seminarIDs[local.thisID].strSeminar.qrySeminar.seminarName)#%20#local.qryAssociationDetails.CatalogURL#/?d=SWOD-#local.seminarIDs[local.thisID].seminarID#" target="_blank">
										<img src="http://ws.sharethis.com/images/twitter_32.png" height="32" width="32" border=0>
									</a>
								</span>
							</span>
					</div>
					<br/>
					<div style="border:1px solid ##DEDEDE;">
						<div style="padding:4px;background-color:##DEDEDE;font-weight:bold;">Questions?</div>
						<div style="padding:4px;">
							For immediate help please consult our 
							<a href="#local.qryAssociationDetails.CatalogURL#/?d=FAQ-SWOD" target="_blank"><b>FAQ page</b></a>.
							<br/><br/>
							If you're unable to find the answer you need, please call #local.qryAssociationDetails.supportPhone# (#local.qryAssociationDetails.supportHours#) or e-mail <a href="mailto:#local.qryAssociationDetails.supportEmail#" target="_blank">customer service</a>.
						</div>
					</div>
					</div>
				</td>
			</tr>
			</table>
			<br/>
			</cfloop>
				<p style="font-family:Verdana,Arial,Helvetica,sans-serif;font-size:7pt;color:##666;text-align:center;">
					<!--- chr(7) inserted before unsub address done on purpose to ensure that Lyris merge code is processed correctly. it is replaced with a CRLF after HTML is compacted --->
					If you wish to cease further e-mails regarding #local.qryAssociationDetails.shortname#'s #local.qryAssociationDetails.brandSWODTab# seminars, click #chr(7)# <a href="mailto:$subst('Email.UnSub')" style="color:##000;">here</a>.
				</p>
				</td>
			</tr>
			</table>
			</body>
			</html>
			</cfoutput>
		</cfsavecontent>
		
		<!--- dont replace 13 and 10 with space -- it leads to screwed up messages in lyris all being on one line --->
		<cfset local.strEmailContent.html = trim(replace(local.strEmailContent.html,chr(9),"","ALL"))>

		<!--- replacing chr(7) with CRLF to ensure that Lyris merge code is processed correctly. --->
		<cfset local.strEmailContent.html = replace(local.strEmailContent.html,chr(7),chr(13) & chr(10),"all")>

		<cfreturn local.strEmailContent>
	</cffunction>

	<cffunction name="generateInvitationEmailFor2017NONSAE" access="public" returntype="struct" output="no">
		<cfargument name="seminarIDlist" type="string" required="yes">
		<cfargument name="orgcode" type="string" required="yes">
		<cfargument name="subject" type="string" required="yes">

		<cfset var local = structnew()>
		<cfset local.strEmailContent = StructNew()>
		<cfset local.objSWP = CreateObject("component","model.seminarweb.SWParticipants")>
		<cfset local.qryAssociationDetails = local.objSWP.getAssociationDetails(arguments.orgcode).qryAssociation>
		<cfset local.qrySWHostName = local.objSWP.getSWHostName()>
		<cfset local.qryCredit = CreateObject("component","model.seminarweb.SWCredits").getCreditsforSeminarList(arguments.seminarIDlist)>
		<cfset local.objAuthor = CreateObject("component","model.seminarweb.SWAuthors")>
		<cfset local.objSWOD = CreateObject("component","model.seminarweb.SWODSeminars")>

		<cfset local.seminarIDs = ArrayNew(1)>
		<cfloop list="#arguments.seminarIDlist#" index="local.thisSID">
			<cfquery name="local.qryCreditDistinct" dbtype="query">
				select distinct Code
				from [local].qryCredit
				where seminarid = #local.thisSID#
				order by Code
			</cfquery>
			
			<cfset local.tmpStr = StructNew()>
			<cfset local.tmpStr.seminarID = local.thisSID>
			<cfset local.tmpStr.strSeminar = getSeminarForRegistrationByAdmin(seminarID=local.thisSID, catalogOrgCode=arguments.orgcode, billingState="", billingZip="", depoMemberDataID=0, memberID=0)>

			<cfset local.tmpStr.creditList = valueList(local.qryCreditDistinct.code)>
			<cfset local.tmpStr.qrySpeakers = local.objAuthor.getAuthorsBySeminarID(seminarID=local.thisSID, authorType='Speaker')>
			<cfset ArrayAppend(local.seminarIDs,local.tmpStr)>
		</cfloop>

		<!--- set from/subject --->
		<cfif len(local.qryAssociationDetails.emailFrom)>
			<cfset local.strEmailContent.fromName = local.qryAssociationDetails.emailFrom>
		<cfelse>
			<cfset local.strEmailContent.fromName = "SeminarWeb">
		</cfif>
		<cfset local.strEmailContent.subject = arguments.subject>

		<cfsavecontent variable="local.strEmailContent.html">
			<cfoutput>
			<html>
			<head></head>
			<body topmargin="0" leftmargin="0" marginwidth="0" marginheight="0" bgcolor="##ffffff" text="##000000" link="##114189">
			<br/>
			<div style="font-family:Verdana,Arial,Helvetica,sans-serif;font-size:10pt;color:##666;">
				#local.qryAssociationDetails.description# invites you to register for the following online seminar<cfif arraylen(local.seminarIDs) gt 1>s</cfif>:
			</div>			
			<cfif arraylen(local.seminarIDs) gt 1>
				<div align="left" style="font-family:Verdana,Arial,Helvetica,sans-serif;font-size:12pt;color:##666;">
				<ol>
				<cfloop from="1" to="#arraylen(local.seminarIDs)#" index="local.thisID">
					<li><a href="###local.seminarIDs[local.thisID].seminarID#">#encodeForHTML(local.seminarIDs[local.thisID].strSeminar.qrySeminar.seminarName)#</a></li>
				</cfloop>
				</ol>
				</div>
			</cfif>
			<br/>
			<cfloop from="1" to="#arraylen(local.seminarIDs)#" index="local.thisID">
				<a name="#local.seminarIDs[local.thisID].seminarID#"></a>
				<table width="95%" border="0" cellpadding="4" cellspacing="0" style="border:1px solid ##ccc;">
				<tr>
					<td colspan="3" style="font-family:Verdana,Arial,Helvetica,sans-serif;color:##0E568D;font-weight:bold;font-size:12pt;margin-bottom:4px;">
						#encodeForHTML(local.seminarIDs[local.thisID].strSeminar.qrySeminar.seminarName)#
					</td>
				</tr>
				<tr valign="top">
					<td style="font-family:Verdana,Arial,Helvetica,sans-serif;font-size:8pt;color:##666;">
						<b style="color:##0E568D;">Summary:</b><br/>
						<div style="margin:2px 20px;line-height:1.2em;">
							#left(local.seminarIDs[local.thisID].strSeminar.qrySeminar.seminarDesc,500)#
							<cfif len(local.seminarIDs[local.thisID].strSeminar.qrySeminar.seminarDesc) gt 500>... [<a href="#local.qryAssociationDetails.CatalogURL#/?d=SWOD-#local.seminarIDs[local.thisID].seminarID#">more</a>]</cfif>
						</div>
						<br/>
						<cfset local.qryAuthors = local.seminarIDs[local.thisID].qrySpeakers>
						<cfif local.qryAuthors.recordcount>
							<b style="color:##0E568D;">Presenter<cfif local.qryAuthors.recordcount GT 1>s</cfif>:</b><br/>
							<div style="margin:2px 20px;">
								<table cellpadding="3">
								<cfloop query="local.qryAuthors">
									<tr valign="top">
									<cfif val(local.qryAuthors.featureImageID) gt 0>
										<td><img src="#local.qrySWHostName.scheme#://#local.qrySWHostName.mainHostName#/userassets/#LCASE(local.qryAuthors.featureImageOrgCode)#/#LCASE(local.qryAuthors.featureImageSiteCode)#/featuredimages/thumbnails/#local.qryAuthors.featureImageID#-#local.qryAuthors.featureImageSizeID#.#local.qryAuthors.fileExtension#" alt="#local.qryAuthors.firstname# #local.qryAuthors.middlename# #local.qryAuthors.lastname#" /></td>
									<cfelse>
										<td></td>
									</cfif>
									<td style="font-family:Verdana,Arial,Helvetica,sans-serif;font-size:8pt;color:##666;line-height:1.2em;">
										<b>#prefix# #firstname# #middlename# #lastname#<cfif len(suffix)>, #suffix#</cfif></b><br/>
										#left(biography,250)#
										<cfif len(biography) gt 250>... [<a href="#local.qryAssociationDetails.CatalogURL#/?d=SWOD-#local.seminarIDs[local.thisID].seminarID#">more</a>]</cfif>
									</td>
									</tr>
								</cfloop>
								</table>
							</div>
							<br/>
						</cfif>
						<b style="color:##0E568D;">Credit:</b><br/>
						<div style="margin:2px 20px;line-height:1.4em;">
							<cfif listLen(local.seminarIDs[local.thisID].creditlist)>
								#replace(local.seminarIDs[local.thisID].creditlist,",",", ","ALL")#<br/><br/>
								Detailed credit information is available on the <a href="#local.qryAssociationDetails.CatalogURL#/?d=SWOD-#local.seminarIDs[local.thisID].seminarID#">registration page</a>.
							</cfif>
							<br/><br/>
						</div>
						<b style="color:##0E568D;">Originally Published:</b><br/>
						<div style="margin:2px 20px;line-height:1.4em;">#DateFormat(local.seminarIDs[local.thisID].strSeminar.qrySeminar.dateOrigPublished,"mmmm d, yyyy")#</div>
						<br/>
						<cfset local.qryPreReqs = local.objSWOD.getSeminarPreReqs(seminarID=local.seminarIDs[local.thisID].strSeminar.qrySeminar.seminarID, depoMemberDataID=0)>
						<cfif local.qryPreReqs.recordcount>
							<b style="color:##0E568D;">Program Prerequisites:</b><br/>
							<div style="margin:2px 20px;line-height:1.4em;">
								You must successfully complete the following prerequisites prior to beginning this program:<br/>
								<ul>
								<cfloop query="local.qryPreReqs">
									<li><a href="#local.qryAssociationDetails.CatalogURL#/?d=SWOD-#local.qryPreReqs.seminarID#">#encodeForHTML(local.qryPreReqs.seminarName)#</a></li>
								</cfloop>
								</ul>
							</div>
						</cfif>
						<b style="color:##0E568D;">How to Attend:</b><br/>
						<div style="margin:2px 20px;line-height:1.4em;">
							Join the self-paced program from your office, home, or hotel room using a computer and high speed 
							internet connection. You may start and stop the program at your convenience, continue where you left off, 
							and review supporting materials as often as you like. 
						</div>
				</td>
				<td width="10">&nbsp; &nbsp;</td>
				<td width="190" style="font-family:Verdana,Arial,Helvetica,sans-serif;font-size:8pt;color:##666;">
					<div style="border:1px solid ##DEDEDE;">
						<div style="padding:4px;background-color:##DEDEDE;font-weight:bold;">Registration</div>
						<div style="padding:4px;">
							Register now for immediate access to this program.<br/><br/>
							
							<cfset local.tmpPrices = local.seminarIDs[local.thisID].strSeminar.qrySeminarPrices>
							<cfloop query="local.tmpPrices">
								<cfif local.tmpPrices.price gte 0>
									<cfif local.tmpPrices.price is 0>
										#replace(local.seminarIDs[local.thisID].strSeminar.qrySeminar.freeRateDisplay,".00","")#
									<cfelseif local.tmpPrices.price gt 0>
										#replace(dollarformat(local.tmpPrices.price),".00","")#<cfif local.seminarIDs[local.thisID].strSeminar.qrySeminar.showUSD> USD</cfif>
									</cfif>
									<cfif len(local.tmpPrices.description)> <cfif local.tmpPrices.price gt 0 or len(local.seminarIDs[local.thisID].strSeminar.qrySeminar.freeRateDisplay)>for </cfif>#local.tmpPrices.description#</cfif><br/>
								</cfif>
							</cfloop>
							<br/>
							<div align="center">
								<a href="#local.qryAssociationDetails.CatalogURL#/?d=SWOD-#local.seminarIDs[local.thisID].seminarID#" target="_blank"><b><span style="font-family:Verdana,Arial,sans-serif;font-size:12pt;">Register Now</span></b></a>
							</div>
						</div>
					</div>	
					<br/>
					<div style="border:1px solid ##DEDEDE;">
						<div style="padding:4px;background-color:##DEDEDE;font-weight:bold;">Tell a Colleague!</div>
							<span class="st_facebook_large">
								<span style="padding:5px;text-decoration:none;color:##000000;display:inline-block;cursor:pointer;border=0">
									<a target="_blank" title="Share this on Facebook" href="https://www.facebook.com/sharer/sharer.php?u=#local.qryAssociationDetails.CatalogURL#/?d=SWOD-#local.seminarIDs[local.thisID].seminarID#" target="_blank">
										<img width="32" height="32" alt="Share this on Facebook" src="http://ws.sharethis.com/images/facebook_32.png" border=0>
									</a>
								</span>
							</span>
							<span class="st_linkedin_large">
								<span style="padding:5px;text-decoration:none;color:##000000;display:inline-block;cursor:pointer;border=0">
									<a title="Share this on Linked In" href="http://www.linkedin.com/shareArticle?mini=true&url=#local.qryAssociationDetails.CatalogURL#/?d=SWOD-#local.seminarIDs[local.thisID].seminarID#" target="_blank">
										<img src="http://ws.sharethis.com/images/linkedin_32.png" height="32" width="32" border=0>
									</a>
								</span>
							</span>
							<span class="st_email_large">
								<span style="padding:5px;text-decoration:none;color:##000000;display:inline-block;cursor:pointer;border=0">
									<a title="Email this to a colleague" href="mailto:?Subject=#URLEncodedFormat(local.seminarIDs[local.thisID].strSeminar.qrySeminar.seminarName)#&Body=Hello - %0A%0AI thought you would be interested in '#URLEncodedFormat(local.seminarIDs[local.thisID].strSeminar.qrySeminar.seminarName)#.' You can register for the seminar at #local.qryAssociationDetails.CatalogURL#/?d=SWOD-#local.seminarIDs[local.thisID].seminarID#." target="_blank">
										<img src="http://ws.sharethis.com/images/email_32.png" height="32" width="32" border=0>
									</a>
								</span>
							</span>
							<span class="st_twitter_large">
								<span style="padding:5px;text-decoration:none;color:##000000;display:inline-block;cursor:pointer;border=0">
									<a title="Share this on Twitter" href="http://twitter.com/intent/tweet?source=webclient&text=#URLEncodedFormat(local.seminarIDs[local.thisID].strSeminar.qrySeminar.seminarName)#%20#local.qryAssociationDetails.CatalogURL#/?d=SWOD-#local.seminarIDs[local.thisID].seminarID#" target="_blank">
										<img src="http://ws.sharethis.com/images/twitter_32.png" height="32" width="32" border=0>
									</a>
								</span>
							</span>
					</div>
					<br/>
					<div style="border:1px solid ##DEDEDE;">
						<div style="padding:4px;background-color:##DEDEDE;font-weight:bold;">Questions?</div>
						<div style="padding:4px;">
							For immediate help please consult our 
							<a href="#local.qryAssociationDetails.CatalogURL#/?d=FAQ-SWOD" target="_blank"><b>FAQ page</b></a>.
							<br/><br/>
							If you're unable to find the answer you need, please call #local.qryAssociationDetails.supportPhone# (#local.qryAssociationDetails.supportHours#) or e-mail <a href="mailto:#local.qryAssociationDetails.supportEmail#" target="_blank">customer service</a>.
						</div>
					</div>
					</div>
				</td>
			</tr>
			</table>
			<br/>
			</cfloop>
				<p style="font-family:Verdana,Arial,Helvetica,sans-serif;font-size:7pt;color:##666;text-align:center;">
					<!--- chr(7) inserted before unsub address done on purpose to ensure that Lyris merge code is processed correctly. it is replaced with a CRLF after HTML is compacted --->
					If you wish to cease further e-mails regarding #local.qryAssociationDetails.shortname#'s #local.qryAssociationDetails.brandSWODTab# seminars, click #chr(7)# <a href="mailto:$subst('Email.UnSub')" style="color:##000;">here</a>.
				</p>
				</td>
			</tr>
			</table>
			</body>
			</html>
			</cfoutput>
		</cfsavecontent>
		
		<!--- dont replace 13 and 10 with space -- it leads to screwed up messages in lyris all being on one line --->
		<cfset local.strEmailContent.html = trim(replace(local.strEmailContent.html,chr(9),"","ALL"))>

		<!--- replacing chr(7) with CRLF to ensure that Lyris merge code is processed correctly. --->
		<cfset local.strEmailContent.html = replace(local.strEmailContent.html,chr(7),chr(13) & chr(10),"all")>

		<cfreturn local.strEmailContent>
	</cffunction>

	<cffunction name="sendInvitationEmail" access="public" returntype="void" output="no">
		<cfargument name="seminarIDlist" type="string" required="yes">
		<cfargument name="performedBy" type="numeric" required="yes">
		<cfargument name="outgoingType" type="string" required="yes">
		<cfargument name="orgcode" type="string" required="yes">
		<cfargument name="subject" type="string" required="yes">
		<cfargument name="preheaderText" type="string" required="true">
		<cfargument name="listchoice" type="string" required="yes">
		<cfargument name="template" type="string" required="true">

		<cfset var local = structnew()>
		<cfset local.objSWOD = CreateObject("component","model.seminarweb.SWODSeminars")>

		<cfset local.qrySend = generateInvitationTable(seminarIDlist=arguments.seminarIDlist,orgcode=arguments.orgcode)>
						
		<cfif ListLen(arguments.seminarIDlist) EQ 1>
			<cfset local.utm_campaign = local.objSWOD.getSeminarBySeminarID(seminarID=val(arguments.seminarIDlist)).seminarName>
		<cfelse>
			<cfset local.utm_campaign ='Multiple Programs'>
		</cfif>

		<cfoutput query="local.qrySend" group="orgcode">
			<cfoutput>
			<cfset local.objSWOD.logAction(local.qrySend.seminarID,"logInvitationToLyris",arguments.performedBy,'',0,0,2)>
			</cfoutput>
			
			<cfset local.thisOrgCode = UCASE(local.qrySend.orgcode)>

			<!--- get programs for orgcode --->
			<cfquery name="local.qrySendIndiv" dbtype="query">
				select seminarID
				from [local].qrySend
				where UPPER(orgcode) = '#local.thisOrgCode#'
			</cfquery>

			<cfif arguments.template eq '2021'>
				<cfset local.strEmailContent = generateInvitationEmailFor2021(seminarIDlist=valuelist(local.qrySendIndiv.seminarID), orgcode=local.thisOrgCode, subject=arguments.subject, preheaderText=arguments.preheaderText)>
			<cfelseif arguments.template eq '2020'>
				<cfset local.strEmailContent = generateInvitationEmailFor2020(seminarIDlist=valuelist(local.qrySendIndiv.seminarID), orgcode=local.thisOrgCode, subject=arguments.subject)>
			<cfelseif arguments.template eq '2017NONSAE'>
				<cfset local.strEmailContent = generateInvitationEmailFor2017NONSAE(seminarIDlist=valuelist(local.qrySendIndiv.seminarID), orgcode=local.thisOrgCode, subject=arguments.subject)>
			</cfif>

			<cfset local.strEmailContent.html = application.objEmailWrapper.appendUTMCodesToLinks(htmlcontent=local.strEmailContent.html, utm_campaign=local.utm_campaign,
				utm_source="SWOD Marketing", utm_medium="email", utm_content="#DateFormat(Now(),'YYYY-MM-DD')#-#local.strEmailContent.subject#")>

			<!--- set list email address --->
			<cfswitch expression="#arguments.listChoice#">
				<cfcase value="SAE">
					<cfset local.emailList = "seminarweb_sae.#local.thisOrgCode#@lists.trialsmith.com">
				</cfcase>
				<cfcase value="NATLE">
					<cfset local.emailList = "seminarweblive.#local.thisOrgCode#@lists.trialsmith.com">
				</cfcase>
				<cfcase value="RX">
					<cfset local.emailList = "seminarweb_rx.#local.thisOrgCode#@lists.trialsmith.com">
				</cfcase>
				<cfcase value="PT">
					<cfset local.emailList = "seminarweb_pt.#local.thisOrgCode#@lists.trialsmith.com">
				</cfcase>
				<cfdefaultcase> 
					<cfset local.emailList = "seminarweb_#arguments.listChoice#.#arguments.listChoice#@lists.trialsmith.com">
			    </cfdefaultcase> 
			</cfswitch>

			<cfif application.MCEnvironment neq "production">
				<cfset local.emailList = "<EMAIL>">
			</cfif>

			<cfmail to="#local.emailList#" from="<EMAIL> (""#local.strEmailContent.fromName#"")" subject="#local.strEmailContent.subject#" mailerid="SeminarWeb" type="html" charset="utf-8" server="#application.mailservers.lyris.server#" port="#application.mailservers.lyris.port#" username="#application.mailservers.lyris.username#" password="#application.mailservers.lyris.password#">
				#local.strEmailContent.html#
			</cfmail>
						
			<cfoutput>
			<cfset local.objSWOD.logAction(local.qrySend.seminarID,arguments.outgoingType,arguments.performedBy,local.emailList,0,0,0)>
			<cfset local.objSWOD.logAction(local.qrySend.seminarID,"logInvitationToLyris",arguments.performedBy,'',0,0,1)>
			</cfoutput>
		</cfoutput>
	</cffunction>

	<cffunction name="getSWODProgramOptInAndOuts" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="seminarID" type="numeric" required="yes">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		
		<cfif hasUpdateSeminarRights(siteID=arguments.mcproxy_siteID, seminarID=arguments.seminarID, action="manageSWOptIns", checkLockSettings=true)>
			<cfset local.objSWParticipants = createObject("component","seminarwebParticipants")>

			<cfset local.qryOptInParticipants = local.objSWParticipants.getParticipantsOptedIntoSeminar(seminarID=arguments.seminarID)>
			<cfset local.qryOptOutParticipants = local.objSWParticipants.getParticipantsOptedOutSeminar(seminarID=arguments.seminarID)>

			<cfset local.returnStruct.arrOptins = arrayNew(1)>
			<cfloop query="local.qryOptInParticipants">
				<cfset local.tmpStr = structNew()>
				<cfset local.tmpStr['orgcode'] = local.qryOptInParticipants.orgcode>
				<cfset local.tmpStr['description'] = local.qryOptInParticipants.description>
				<cfset arrayAppend(local.returnStruct.arrOptins,local.tmpStr)>
			</cfloop>

			<cfset local.returnStruct.arrOptOuts = arrayNew(1)>
			<cfloop query="local.qryOptOutParticipants">
				<cfif local.qryOptOutParticipants.isSWOD>
					<cfset local.tmpStr = structNew()>
					<cfset local.tmpStr['orgcode'] = local.qryOptOutParticipants.orgcode>
					<cfset local.tmpStr['description'] = local.qryOptOutParticipants.description>
					<cfset arrayAppend(local.returnStruct.arrOptOuts,local.tmpStr)>
				</cfif>
			</cfloop>

			<cfset local.returnStruct.success = true>
		<cfelse>
			<cfset local.returnStruct.success = false>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="removeEnrollment" access="public" returntype="struct" output="no">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="enrollmentID" type="numeric" required="yes">
		<cfargument name="AROption" type="string" required="yes">
		<cfargument name="emailRegistrant" type="boolean" required="yes">

		<cfset var local = structnew()>
		<cfset local.data.success = false>
		<cfset local.data.errmsg = "">

		<cfset local.qryEnrollment = CreateObject("component","model.seminarweb.SWODSeminars").getEnrollmentByEnrollmentID(enrollmentID=arguments.enrollmentID)>
		<cfif local.qryEnrollment.recordcount is 0>
			<cfset local.data.errmsg = "Invalid enrollmentID.">
			<cfreturn local.data>
		</cfif>
		
		<cfif not hasUpdateSeminarRights(siteID=arguments.mcproxy_siteID, action="removeEnrollment", enrollmentID=arguments.enrollmentID)>
			<cfset local.data.errmsg = "You do not have rights to perform this operation.">
			<cfreturn local.data>
		</cfif>

		<cfif NOT local.qryEnrollment.handlesOwnPayment>
			<cfset local.qryEnrollmentFees = CreateObject("component","seminarWebSWCommon").getEnrollmentFees(referenceID=arguments.enrollmentID, programType="SWOD")>
		</cfif>

		<cfstoredproc procedure="swod_removeEnrollment" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.enrollmentID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(session.cfcuser.statsSessionID)#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.AROption#">
		</cfstoredproc>

		<cfset local.qryAssociation = CreateObject("component","model.seminarweb.SWParticipants").getAssociationDetails(orgcode=local.qryEnrollment.orgcode).qryAssociation>
		<cfif NOT local.qryEnrollment.handlesOwnPayment>
			<cfset local.hasSWRegRefund = arguments.AROption EQ 'A' AND val(local.qryEnrollmentFees.total) GT 0>
		<cfelse>
			<cfset local.hasSWRegRefund = false>
		</cfif>
		
		<cfif arguments.emailRegistrant>
			<cfset local.arrEmailTo = []>
			<cfif len(local.qryEnrollment.email)>
				<cfset local.arrEmailTo.append({ name="#local.qryEnrollment.firstName# #local.qryEnrollment.lastName#", email=local.qryEnrollment.email })>
			</cfif>
			<cfif len(local.qryEnrollment.overrideEmail) AND local.qryEnrollment.email NEQ local.qryEnrollment.overrideEmail>
				<cfset local.arrEmailTo.append({ name="#local.qryEnrollment.firstName# #local.qryEnrollment.lastName#", email=local.qryEnrollment.overrideEmail })>
			</cfif>

			<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(local.qryEnrollment.orgcode)>

			<cfif arrayLen(local.arrEmailTo)>
				<cfsavecontent variable="local.emailContent">
					<cfoutput>
						#local.qryEnrollment.firstName# #local.qryEnrollment.lastName#:<br/><br/>
						This email confirms that your registration for #encodeForHTML(local.qryEnrollment.seminarName)# has been cancelled and deactivated.<cfif local.hasSWRegRefund> A refund request for #dollarformat(val(local.qryEnrollmentFees.total)+val(local.qryEnrollmentFees.tax))# has been sent to our accounting department. Please allow 3 business days for your refund to be processed.</cfif><br/><br/>
						If you have any questions about this cancellation, please contact us at #local.mc_siteInfo.supportProviderEmail#.<br/><br/>
						#local.qryAssociation.emailFrom#<br/>
						#local.qryAssociation.supportPhone#
					</cfoutput>
				</cfsavecontent>
				<cfset local.emailContent = trim(replace(replace(replace(local.emailContent,chr(9),"","ALL"),chr(13),"","ALL"),chr(10),"","ALL"))>

				<cfset local.memberID = CreateObject("component","model.seminarWeb.SWCommon").getMemberIDByDepoMemberDataID(siteCode=local.qryEnrollment.signUpOrgCode, depoMemberDataID=local.qryEnrollment.depomemberdataID)>

				<cfset local.SeminarWebAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='SeminarWebAdmin',siteID=local.mc_siteInfo.siteID)>

				<cfset local.strResult = application.objEmailWrapper.sendMailESQ(
						emailfrom={ name=local.qryAssociation.emailFrom, email=local.mc_siteInfo.networkEmailFrom },
						emailto=local.arrEmailTo,
						emailreplyto=local.mc_siteInfo.supportProviderEmail,
						emailsubject="Registration Cancelled: #encodeForHTML(local.qryEnrollment.seminarName)#",
						emailtitle="Registration Cancelled: #encodeForHTML(local.qryEnrollment.seminarName)#",
						emailhtmlcontent=local.emailContent,
						siteID=local.mc_siteInfo.siteID,
						memberID=local.memberID,
						messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="SEMWEBREGCANCEL"),
						sendingSiteResourceID=local.SeminarWebAdminSRID
				)>
			</cfif>
		</cfif>
		
		<!--- email SW Support for Refunds --->
		<cfif local.hasSWRegRefund>
			<cfsavecontent variable="local.refundEmailContent">
				<cfoutput>
					This registration has been cancelled and a refund may be applicable.<br/><br/>
					<b>Registration Details</b><br/><hr>
					Registrant Name: #local.qryEnrollment.firstName# #local.qryEnrollment.lastName#<br/><br/>
					Program: #encodeForHTML(local.qryEnrollment.seminarName)# (SWOD-#local.qryEnrollment.seminarID#)<br/><br/>
					DepoMemberDataID: <a href="https://admin.trialsmith.com/TransactionView.cfm?depoMemberDataID=#local.qryEnrollment.depoMemberDataID#">#local.qryEnrollment.depoMemberDataID#</a><br/><br/>
					Refund Amount: #dollarformat(val(local.qryEnrollmentFees.total)+val(local.qryEnrollmentFees.tax))#
				</cfoutput>
			</cfsavecontent>
			<cfset local.refundEmailContent = trim(replace(replace(replace(local.refundEmailContent,chr(9),"","ALL"),chr(13),"","ALL"),chr(10),"","ALL"))>

			<cfset local.SWSiteInfo = application.objSiteInfo.getSiteInfo('SW')>
			<cfset local.emailtitle = "Refund Request for #local.qryEnrollment.firstName# #local.qryEnrollment.lastName# on SWOD-#local.qryEnrollment.seminarID#">

			<cfset application.objEmailWrapper.sendMailESQ(
				emailfrom={ name='SeminarWeb', email='<EMAIL>' },
				emailto=[{ name="", email="<EMAIL>" }],
				emailreplyto="",
				emailsubject=local.emailtitle,
				emailtitle=local.emailtitle,
				emailhtmlcontent=local.refundEmailContent,
				siteID=local.SWSiteInfo.siteID,
				memberID=local.SWSiteInfo.sysMemberID,
				messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="SEMWEBREFUNDREQ"),
				sendingSiteResourceID=local.SWSiteInfo.siteSiteResourceID
			)>
		</cfif>

		<cfset local.data.success = true>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getSeminarFormDetail" access="public" output="false" returntype="query">
		<cfargument name="seminarformID" type="numeric" required="yes">
		<cfargument name="depoMemberDataID" type="numeric" required="yes">
		<cfargument name="isActive" type="numeric" required="false" default='0'>

		<cfset var qryFormDetails = "">

		<cfquery name="qryFormDetails" datasource="#application.dsn.tlasites_formbuilder.dsn#">
			set nocount on

			-- get form type
			DECLARE @formtypeID int, @seminarformID int, @depoMemberDataID int;
			SET @seminarformID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarformID#">;
			SET @depoMemberDataID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.depoMemberDataID#">;

			SELECT top 1 @formtypeID = f.formtypeid
				from seminarweb.dbo.tblSeminarsAndForms as saf
				inner join dbo.tblForms as f on f.formid = saf.formid and f.isDeleted = 0
				where saf.seminarFormID = @seminarformID;
			
			-- put responses into table
			DECLARE @resp TABLE (responseID int, dateDelivered smalldatetime, dateCompleted smalldatetime, passingPct int, questionID int, isCorrect bit, isResponseActive bit)
			INSERT INTO @resp (responseID, dateDelivered, dateCompleted, passingPct, questionID, isCorrect, isResponseActive)	
			SELECT r.responseID, r.dateDelivered, r.dateCompleted, r.passingPct, rd.questionID, rd.isCorrect, r.isActive
			FROM seminarweb.dbo.tblSeminarsAndFormResponses as safr
			INNER JOIN dbo.tblResponses as r on r.responseID = safr.responseID
				and r.depomemberdataid = @depoMemberDataID
			INNER JOIN dbo.tblResponseDetails as rd on rd.responseid = r.responseid
			WHERE safr.seminarFormID = @seminarformID;

			-- if exam, get passingpct, numquestions, numcorrect
			if @formtypeID = 2
				select r.responseid, r.isResponseActive, r.dateDelivered, r.dateCompleted, @formtypeID as formTypeID, r.passingPct, 
					count(r.questionID) as questionCount,
					(select count(questionID) from @resp where isCorrect = 1 and responseid = r.responseid) as correctCount
				from @resp as r
				<cfif arguments.isActive eq 1>
					where r.isResponseActive = 1
				</cfif>
				group by r.responseid, r.isResponseActive, r.dateDelivered, r.dateCompleted, r.passingPct;
			else
				select distinct r.responseid, r.isResponseActive, r.dateDelivered, r.dateCompleted, @formtypeID as formTypeID, r.passingPct,
					0 as questionCount, 0 as correctCount
				from @resp as r
				<cfif arguments.isActive eq 1>
					where r.isResponseActive = 1
				</cfif>;
		</cfquery>
		
		<cfreturn qryFormDetails>
	</cffunction>

	<cffunction name="uncompleteSeminar" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="enrollmentID" type="numeric" required="yes">
		
		<cfset var local = structNew()>

		<cftry>
			<cfif not hasUpdateSeminarRights(siteID=arguments.mcproxy_siteID, action="manageRegProgress", enrollmentID=arguments.enrollmentID)>
				<cfthrow message="invalid request">
			</cfif>

			<cfstoredproc procedure="swod_uncompleteSeminar" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.enrollmentID#" null="No">
				<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
			</cfstoredproc>

			<cfset local.data.success = true>
		<cfcatch type="any">
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="markCompleteSeminar" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="enrollmentID" type="numeric" required="yes">

		<cfset var local = structNew()>

		<cftry>
			<cfif not hasUpdateSeminarRights(siteID=arguments.mcproxy_siteID, action="manageRegProgress", enrollmentID=arguments.enrollmentID)>
				<cfthrow message="invalid request">
			</cfif>

			<cfstoredproc procedure="swod_getSeminarProgress" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.enrollmentID#" null="No">
				<cfprocresult name="local.qryTimeSpent" resultset="5">
			</cfstoredproc>

			<!--- convert totalTimeSpent from seconds to minutes --->
			<cfset local.realTimeSpent = round(val(local.qryTimeSpent.totalTimeSpent) / 60)>

			<!--- check for incomplete exams and complete them for this enrollmentID --->
			<cfset CreateObject("component","model.seminarweb.SWODSeminars").completeIncompleteExams(enrollmentid=arguments.enrollmentID,loadPoint='postTest',recordedByMemberID=session.cfcuser.memberdata.memberID)>

			<cfstoredproc procedure="swod_recordCompletion" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.enrollmentID#" null="No">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.realTimeSpent#" null="No">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.realTimeSpent#" null="No">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#" null="No">
			</cfstoredproc>

			<cfset local.data.success = true>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getActivityLogSessions" access="public" output="false" returntype="query">
		<cfargument name="enrollmentid" type="numeric" required="yes">
		<cfargument name="logaccessid" type="numeric" required="yes">
		
		<cfset var qrySessions = "">
		
		<cfquery name="qrySessions" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			select logaccessid, activityLog
			from dbo.tblLogAccessSWOD
			where enrollmentID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.enrollmentid#">
			<cfif arguments.logaccessid gt 0>
				and logaccessid = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.logaccessid#">
			</cfif>
			order by dateEntered;
		</cfquery>

		<cfreturn qrySessions>
	</cffunction>

	<cffunction name="getDebugLogSessions" access="public" output="false" returntype="query">
		<cfargument name="enrollmentid" type="numeric" required="yes">
		<cfargument name="logaccessid" type="numeric" required="yes">
		
		<cfset var qrySessions = "">
		
		<cfquery name="qrySessions" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			select logaccessid, debugLog
			from dbo.tblLogAccessSWOD
			where enrollmentID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.enrollmentid#">
			<cfif arguments.logaccessid gt 0>
				and logaccessid = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.logaccessid#">
			</cfif>
			order by dateEntered;
		</cfquery>

		<cfreturn qrySessions>
	</cffunction>

	<cffunction name="manuallyCompleteFile" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="enrollmentID" type="numeric" required="yes">
		<cfargument name="fileID" type="numeric" required="yes">

		<cftry>
			<cfif not hasUpdateSeminarRights(siteID=arguments.mcproxy_siteID, action="manageRegProgress", enrollmentID=arguments.enrollmentID)>
				<cfthrow message="invalid request">
			</cfif>

			<cfstoredproc procedure="swod_manuallyCompleteFile" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.enrollmentID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.fileID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
			</cfstoredproc>

			<cfset local.data.success = true>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getCreditSelections" access="public" output="false" returntype="query">
		<cfargument name="enrollmentID" type="numeric" required="yes">
		
		<cfset var qryCreditSelections = "">
		
		<cfquery name="qryCreditSelections" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			select eac.enrollCreditID, eac.lastDateToComplete, eac.earnedCertificate, eac.finalTimeSpent, cs.sponsorName, ca.authorityName
			from dbo.tblEnrollmentsAndCredit as eac
			inner join dbo.tblSeminarsAndCredit as sac on sac.seminarCreditID = eac.seminarCreditID
			inner join dbo.tblCreditSponsorsAndAuthorities as csa on csa.csaLinkID = sac.CSALinkID
			inner join dbo.tblCreditSponsors as cs on cs.sponsorID = csa.sponsorID
			inner join dbo.tblCreditAuthorities as ca on ca.authorityID = csa.authorityID
			where eac.enrollmentID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.enrollmentID#">
			order by ca.authorityName, cs.sponsorName, eac.enrollCreditID
		</cfquery>

		<cfreturn qryCreditSelections>
	</cffunction>

	<cffunction name="updateEnrolleeDatesAndCredits" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">
		<cfargument name="enrollmentID" type="numeric" required="yes">

		<cfset var local = structNew()>

		<cfset local.strProgress = CreateObject("component","model.seminarweb.SWODSeminars").getProgressByEnrollmentID(enrollmentID=arguments.enrollmentID)>

		<cfquery name="local.qryUpdateChanges" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
			
				DECLARE @enrollmentID int, @str varchar(1000), @orgID INT, @siteID INT, @calcTimeSpent INT, @dateCompleted SMALLDATETIME, @passed BIT,
					@seminarID INT, @registrantName VARCHAR(300), @recordedByMemberID int, @crlf VARCHAR(10), @msgjson VARCHAR(MAX),
					@siteResourceID int, @dataXML xml, @depomemberdataID int;
				DECLARE @tblHookListeners TABLE (executionType varchar(13), objectPath varchar(200));

				SET @crlf = CHAR(13) + CHAR(10);
				SET @enrollmentID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.enrollmentID#">;
				SET @str = 'description' + char(9) + 'timestamp' + char(13) + char(10);
				SET @str = @str + 'Changed enrollment/progress information.';
				SET @str = @str + char(9) + convert(varchar(30),getdate());
				SET @recordedByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcUser.memberData.memberID#">;

				IF OBJECT_ID('tempdb..##tblExistingEnrollmentInfo') IS NOT NULL
					DROP TABLE ##tblExistingEnrollmentInfo;
				IF OBJECT_ID('tempdb..##tblExistingCreditInfo') IS NOT NULL
					DROP TABLE ##tblExistingCreditInfo;
				IF OBJECT_ID('tempdb..##tmpLogMessages') IS NOT NULL
					DROP TABLE ##tmpLogMessages;
				
				CREATE TABLE ##tblExistingEnrollmentInfo(enrollmentID INT, calcTimeSpent INT, dateCompleted SMALLDATETIME, passed BIT);
				CREATE TABLE ##tblExistingCreditInfo(enrollCreditID INT, earnedCertificate BIT, finalTimeSpent INT, lastDateToComplete DATE, authorityAndSponsor VARCHAR(500));
				CREATE TABLE ##tmpLogMessages (rowID INT IDENTITY(1,1), settingsType CHAR(1), msg VARCHAR(MAX));

				SELECT @orgID = mcs.orgID, @siteID = mcs.siteID, @seminarID = s.seminarID, @depomemberdataID = u.depomemberdataID
				FROM dbo.tblEnrollments AS e 
				INNER JOIN dbo.tblSeminars AS s ON s.seminarID = e.seminarID
				INNER JOIN dbo.tblParticipants AS p ON p.participantID = s.participantID
				INNER JOIN membercentral.dbo.sites AS mcs ON mcs.siteCode = p.orgCode
				INNER JOIN dbo.tblUsers as u on u.userID = e.userID
				WHERE e.enrollmentID = @enrollmentID;

				SELECT @siteResourceID = siteResourceID from membercentral.dbo.sites where siteID = @siteID;

				INSERT INTO ##tblExistingEnrollmentInfo(enrollmentID, calcTimeSpent, dateCompleted, passed)
				SELECT e.enrollmentID, eswod.calcTimeSpent, e.dateCompleted, e.passed
				FROM dbo.tblEnrollmentsSWOD AS eswod
				INNER JOIN dbo.tblEnrollments AS e ON e.enrollmentID = eswod.enrollmentID
				WHERE e.enrollmentID = @enrollmentID;

				INSERT INTO ##tblExistingCreditInfo(enrollCreditID, earnedCertificate, finalTimeSpent, lastDateToComplete, authorityAndSponsor)
				SELECT eac.enrollCreditID, eac.earnedCertificate, eac.finalTimeSpent, eac.lastDateToComplete, ca.authorityName + ' - ' + cs.sponsorName
				FROM dbo.tblEnrollmentsAndCredit eac
				INNER JOIN dbo.tblSeminarsAndCredit AS sac ON sac.seminarCreditID = eac.seminarCreditID
				INNER JOIN dbo.tblCreditSponsorsAndAuthorities AS csa ON csa.csaLinkID = sac.CSALinkID
				INNER JOIN dbo.tblCreditSponsors AS cs ON cs.sponsorID = csa.sponsorID
				INNER JOIN dbo.tblCreditAuthorities AS ca ON ca.authorityID = csa.authorityID
				WHERE eac.enrollCreditID IN (<cfqueryparam cfsqltype="cf_sql_integer" value="0#arguments.event.getValue('credits','')#" list="yes">);
				
				BEGIN TRAN;
					INSERT INTO dbo.tblLogAccessSWOD (enrollmentID, dateEntered, dateLastModified, timeSpent, activityLog)
					VALUES (@enrollmentID, getdate(), getdate(), 0, @str);
					
					<cfif len(local.strProgress.qryProgress.datecompleted)>
						SET @calcTimeSpent = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getTrimValue('timeSpent',0)#">;
						SET @dateCompleted = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#ParseDateTime("#replace(arguments.event.getValue('dateCompleted'),' - ',' ')#")#">;
						SET @passed = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.event.getTrimValue('isPassed',0)#">;

						UPDATE dbo.tblEnrollmentsSWOD 
						SET calcTimeSpent = @calcTimeSpent
						WHERE enrollmentID = @enrollmentID;

						UPDATE dbo.tblEnrollments
						SET dateCompleted = @dateCompleted, passed = @passed
						WHERE enrollmentID = @enrollmentID;

						INSERT INTO ##tmpLogMessages(settingsType, msg)
						SELECT 'E', 'Real Time Spent in Seminar changed from ' + CAST(ISNULL(calcTimeSpent,0) AS VARCHAR(15)) + ' Minutes to ' + CAST(ISNULL(@calcTimeSpent,0) AS VARCHAR(15)) + ' Minutes.'
						FROM ##tblExistingEnrollmentInfo
						WHERE enrollmentID = @enrollmentID
						AND ISNULL(calcTimeSpent,0) <> ISNULL(@calcTimeSpent,0);

						INSERT INTO ##tmpLogMessages(settingsType, msg)
						SELECT 'E', 'Date Completed changed from ' + CASE WHEN dateCompleted IS NULL THEN '[blank]' ELSE CONVERT(VARCHAR(20),dateCompleted,120) END +
							' to ' + CASE WHEN @dateCompleted IS NULL THEN '[blank]' ELSE CONVERT(VARCHAR(20),@dateCompleted,120) END + '.'
						FROM ##tblExistingEnrollmentInfo
						WHERE enrollmentID = @enrollmentID
						AND ISNULL(dateCompleted,GETDATE()) <> ISNULL(@dateCompleted,GETDATE());

						INSERT INTO ##tmpLogMessages(settingsType, msg)
						SELECT 'E', 'Enrollment Pass/Fail changed from ' + CASE WHEN passed = 1 THEN 'success' ELSE 'failed' END + ' to ' + CASE WHEN @passed = 1 THEN 'success' ELSE 'failed' END + '.'
						FROM ##tblExistingEnrollmentInfo
						WHERE enrollmentID = @enrollmentID
						AND passed <> @passed;
					</cfif>

					<cfif listLen(arguments.event.getValue('credits',''))>
						DECLARE	@enrollCreditID INT, @earnedCertificate BIT, @finalTimeSpent INT, @lastDateToComplete DATE;

						<cfloop list="#arguments.event.getValue('credits','')#" index="local.thisCredit">
							SET @enrollCreditID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisCredit#">;
							SET @lastDateToComplete = <cfqueryparam cfsqltype="CF_SQL_DATE" value="#arguments.event.getValue('ec_#local.thisCredit#_dateby')#">;
							SET @earnedCertificate = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('ec_#local.thisCredit#_earned',0)#">;
							SET @finalTimeSpent = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('ec_#local.thisCredit#_spent',0)#">;

							UPDATE dbo.tblEnrollmentsAndCredit
							SET lastDateToComplete = @lastDateToComplete
							<cfif len(local.strProgress.qryProgress.datecompleted)>
								, earnedCertificate = @earnedCertificate
								, finalTimeSpent = @finalTimeSpent
							</cfif>
							WHERE enrollCreditID = @enrollCreditID;

							INSERT INTO ##tmpLogMessages(settingsType, msg)
							SELECT 'C', 'For Credit Authority/Sponsor [' + authorityAndSponsor + '], Earned Certificate changed from ' + CASE WHEN earnedCertificate = 1 THEN 'Yes' ELSE 'No' END + ' to ' + CASE WHEN @earnedCertificate = 1 THEN 'Yes' ELSE 'No' END + '.'
							FROM ##tblExistingCreditInfo
							WHERE enrollCreditID = @enrollCreditID
							AND earnedCertificate <> @earnedCertificate;

							INSERT INTO ##tmpLogMessages(settingsType, msg)
							SELECT 'C', 'For Credit Authority/Sponsor [' + authorityAndSponsor + '], Reported Time changed from ' + CAST(ISNULL(finalTimeSpent,0) AS VARCHAR(15)) + ' Minutes to ' + CAST(ISNULL(@finalTimeSpent,0) AS VARCHAR(15)) + ' Minutes.'
							FROM ##tblExistingCreditInfo
							WHERE enrollCreditID = @enrollCreditID
							AND ISNULL(finalTimeSpent,0) <> ISNULL(@finalTimeSpent,0);

							INSERT INTO ##tmpLogMessages(settingsType, msg)
							SELECT 'C', 'For Credit Authority/Sponsor [' + authorityAndSponsor + '], Completed By Date changed from ' + CONVERT(VARCHAR(20),CAST(lastDateToComplete AS DATE),120) + ' to ' + CONVERT(VARCHAR(20),@lastDateToComplete,120) + '.'
							FROM ##tblExistingCreditInfo
							WHERE enrollCreditID = @enrollCreditID
							AND lastDateToComplete <> @lastDateToComplete;
						</cfloop>
					</cfif>
				COMMIT TRAN;

				IF EXISTS(SELECT 1 FROM ##tmpLogMessages) BEGIN
					IF EXISTS (SELECT TOP 1 enrollCreditID FROM dbo.tblEnrollmentsAndCredit WHERE enrollmentID = @enrollmentID AND earnedCertificate = 1) BEGIN
						SELECT @dataXML = 
							ISNULL((
								SELECT 'semweb' AS itemtype, @enrollmentID AS itemid for xml path ('data')
							),'<data/>');

						INSERT INTO @tblHookListeners (executionType, objectPath)
						EXEC memberCentral.dbo.hooks_runHook @event='creditAwarded', @siteResourceID=@siteResourceID, @dataXML=@dataXML;
					END

					SELECT @registrantName = '[' + ISNULL(m2.firstname,d.firstName) + ' ' + ISNULL(m2.lastname,d.lastname) + ']' + ISNULL(' (' + m2.membernumber + ')','')
					FROM dbo.tblEnrollments AS e
					INNER JOIN dbo.tblUsers AS u ON e.userID = u.userID
					INNER JOIN trialsmith.dbo.depomemberdata AS d ON d.depomemberdataID = u.depoMemberDataID
					LEFT OUTER JOIN membercentral.dbo.ams_members AS m
						INNER JOIN membercentral.dbo.ams_members as m2 on m2.orgID = m.orgID and m2.memberID = m.activeMemberID
					ON m.memberID = e.MCMemberID
					WHERE e.enrollmentID = @enrollmentID;

					SET @msgjson = 'Enrollment/Progress information has been updated for registrant ' + @registrantName + ' on SWOD-' + CAST(@seminarID AS VARCHAR(10)) + '.';

					IF EXISTS(SELECT 1 FROM ##tmpLogMessages WHERE settingsType = 'E') BEGIN
						SET @msgjson = @msgjson + @crlf + 'The following Enrollment Settings changes have been made:';

						SELECT @msgjson = COALESCE(@msgjson + @crlf, '') + memberCentral.dbo.fn_cleanInvalidXMLChars(msg)
						FROM ##tmpLogMessages
						WHERE msg IS NOT NULL
						AND settingsType = 'E';
					END

					IF EXISTS(SELECT 1 FROM ##tmpLogMessages WHERE settingsType = 'C') BEGIN
						SET @msgjson = @msgjson + @crlf + 'The following Credit Authority Settings changes have been made:';

						SELECT @msgjson = COALESCE(@msgjson + @crlf, '') + memberCentral.dbo.fn_cleanInvalidXMLChars(msg)
						FROM ##tmpLogMessages
						WHERE msg IS NOT NULL
						AND settingsType = 'C';
					END

					INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
					VALUES ('{ "c":"auditLog", "d": {
						"AUDITCODE":"SW",
						"ORGID":' + CAST(@orgID AS VARCHAR(10)) + ',
						"SITEID":' + CAST(@siteID AS VARCHAR(10)) + ',
						"ACTORMEMBERID":' + CAST(@recordedByMemberID AS VARCHAR(20)) + ',
						"ACTIONDATE":"' + CONVERT(VARCHAR(20),GETDATE(),120) + '",
						"MESSAGE":"' + REPLACE(@msgjson,'"','\"') + '" } }');
				END

				-- rerun conditions since there may have been changes
				EXEC membercentral.dbo.ams_processSeminarWebConditionsByDepoMemberDataID @depoMemberDataID=@depoMemberDataID; 

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
	</cffunction>

	<cffunction name="getSeminarForRegistrationByAdmin" access="public" returntype="struct" output="no">
		<cfargument name="seminarID" type="numeric" required="yes">
		<cfargument name="catalogOrgCode" type="string" required="yes">
		<cfargument name="billingState" type="string" required="yes">
		<cfargument name="billingZip" type="string" required="yes">
		<cfargument name="depoMemberDataID" type="numeric" required="yes">
		<cfargument name="memberID" type="numeric" required="yes">

		<cfset var local = structnew()>
		
		<cfstoredproc procedure="swod_getSeminarForRegistrationByAdmin" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.catalogOrgCode#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.billingState#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.billingZip#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.depoMemberDataID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">
			<cfprocresult name="local.qrySeminar" resultset="1">
			<cfprocresult name="local.qrySeminarPrices" resultset="2">
		</cfstoredproc>
		
		<!--- Add timezone specific display to query --->
		<cfset local.qrySeminar = CreateObject("component","model.seminarweb.SWODSeminars").addColumnsToSeminars(local.qrySeminar,arguments.catalogOrgCode)>

		<cfreturn local>
	</cffunction>

	<cffunction name="getEnrollmentDataForEmailing" access="public" output="false" returntype="struct">
		<cfargument name="enrollmentID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.retStruct = structNew()>

		<cfquery name="local.retStruct.qryData" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SELECT m.activeMemberID AS memberID, d.depomemberdataID
			FROM dbo.tblEnrollments AS e
			INNER JOIN dbo.tblEnrollmentsSWOD AS eswod ON eswod.enrollmentID = e.enrollmentID
			INNER JOIN dbo.tblUsers u ON u.userID = e.userID
			INNER JOIN trialsmith.dbo.depomemberdata AS d ON d.depomemberdataID = u.depoMemberDataID
			INNER JOIN membercentral.dbo.ams_members AS m ON m.memberID = e.MCMemberID
			WHERE e.enrollmentID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.enrollmentID#">
			AND e.isActive = 1
		</cfquery>

		<cfset local.retStruct.extendedLinkedMergeCode = "">
		<cfset local.retStruct.arrResTypeMergeCodes = arrayNew(1)>

		<cfreturn local.retStruct>
	</cffunction>

	<cffunction name="getFilteredRegistrantsForEmailing" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any" required="true">

		<cfset var local = structNew()>
		<cfset local.retStruct = { itemIDList='', toolType='SeminarWebAdmin', catTreeCode='ETSEMWEB', extendedLinkedMergeCode='', extraMergeTagList='', errorCode='' }>

		<cfset local.qryRegistrants = getRegistrants(sitecode=arguments.event.getValue('mc_siteInfo.siteCode'), mode="massEmailReg", seminarID=arguments.event.getValue('pid',0), 
			rdateFrom=arguments.event.getValue('rDateFrom',''), rdateTo=arguments.event.getValue('rDateTo',''), completed=arguments.event.getValue('fCompleted',''),
			rHideDeleted=arguments.event.getValue('rHideDeleted',1), cdateFrom=arguments.event.getValue('cDateFrom',''), cdateTo=arguments.event.getValue('cDateTo',''), memberID=0,
			emailTagTypeID=arguments.event.getValue('emailTagType',0))>
					
		<cfif local.qryRegistrants.recordcount is 0>
			<cfset local.retStruct.errorCode = 'norecipient'>
			<cfreturn local.retStruct>
		
		<!--- no email ids defined --->
		<cfelseif val(local.qryRegistrants.membersWithEmail) eq 0>
			<cfset local.retStruct.errorCode = 'noemailrecipient'>
			<cfreturn local.retStruct>
		</cfif>

		<cfset local.retStruct.itemIDList = valueList(local.qryRegistrants.enrollmentID)>

		<cfreturn local.retStruct>
	</cffunction>

	<cffunction name="getCreditSponsorAndAuthorityInfo" access="public" output="false" returntype="struct">
		<cfargument name="CSALinkID" type="numeric" required="yes">

		<cfset var local = structnew()>
		<cfset local.returnStruct = structNew()>

		<cfquery name="local.qryCreditsGrid" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SELECT csa.CSALinkID, cs.orgCode AS creditSponsorOrgCode, ca.code AS authorityCode, 
				ca.authorityName, ca.wddxCreditTypes, cs.sponsorName
			FROM dbo.tblCreditSponsorsAndAuthorities AS csa
			INNER JOIN dbo.tblCreditAuthorities AS ca ON csa.authorityID = ca.authorityID 
			INNER JOIN dbo.tblCreditSponsors AS cs ON csa.sponsorID = cs.sponsorID
			WHERE csa.CSALinkID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.CSALinkID#">;
		</cfquery>

		<cfif len(local.qryCreditsGrid.wddxCreditTypes)>
			<cfwddx action="WDDX2CFML" input="#local.qryCreditsGrid.wddxCreditTypes#" output="local.returnStruct.arrcredittypes">
		<cfelse>
			<cfset local.returnStruct.arrcredittypes = ArrayNew(1)>
		</cfif>

		<cfset local.returnStruct.authoritycode = local.qryCreditsGrid.authorityCode>
		<cfset local.returnStruct.authorityname = local.qryCreditsGrid.authorityName>
		<cfset local.returnStruct.sponsorname = local.qryCreditsGrid.sponsorName>
		<cfset local.returnStruct.arrcredittypes.map(function(thisRow){
			if(len(arguments.thisRow['displayname']) gt 95)
				arguments.thisRow['displayname'] = "#left(arguments.thisRow['displayname'],91)#...";
		})>

		<cfset local.returnStruct.success = true>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getSubmissionDataFromGrids" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structnew()>
		<cfset local.returnStruct = { "arrObjectives": [], "arrLinks": [], "arrRates": [], "arrCredits": []	}>
		<cfset local.delim = chr(7)>

		<cfloop list="#arguments.event.getTrimValue('sf_learnObjectiveID','')#" item="local.thisObjectiveID">
			<cfset arrayAppend(local.returnStruct.arrObjectives, arguments.event.getTrimValue('sf_learnObjective_#local.thisObjectiveID#',''))>
		</cfloop>

		<cfif arguments.event.getValue('sf_includeLinks',0)>
			<cfloop list="#arguments.event.getTrimValue('sf_linkID','')#" item="local.thisLinkID">
				<cfset arrayAppend(local.returnStruct.arrLinks, {
					url=arguments.event.getTrimValue('sf_linkURL_#local.thisLinkID#',''),
					name=arguments.event.getTrimValue('sf_linkName_#local.thisLinkID#','')
				})>
			</cfloop>
		</cfif>
		<cfif arguments.event.getValue('sf_allowCatalog',0)>
			<cfloop list="#arguments.event.getTrimValue('sf_rateID','')#" item="local.thisRateID">
				<cfset arrayAppend(local.returnStruct.arrRates, {
					rateName=arguments.event.getTrimValue('sf_rateName_#local.thisRateID#',''),
					rate=NumberFormat(replace(arguments.event.getTrimValue('sf_rate_#local.thisRateID#',0),',','','ALL'),"0.00"),
					arrGroupIDs=listToArray(arguments.event.getTrimValue('sf_rateGroupIDs_#local.thisRateID#',''),"|"),
					arrDeniedGroupIDs=listToArray(arguments.event.getTrimValue('sf_deniedRateGroupIDs_#local.thisRateID#',''),"|")
				})>
			</cfloop>
		</cfif>
		<cfif arguments.event.getValue('sf_offerCECredit',0)>
			<cfloop list="#arguments.event.getTrimValue('sf_CECreditID','')#" item="local.thisCreditID">
				<cfset local.arrCreditsAvail = ArrayNew(1)>
				<cfloop collection="#arguments.event.getCollection()#" item="local.key">
					<cfif findNoCase("sf_xcredit_#local.thisCreditID#_",local.key)>
						<cfif isValid("numeric",arguments.event.getValue(local.key))>
							<cfset arrayAppend(local.arrCreditsAvail, {
								"fieldname":getToken(replaceNoCase(local.key,"_#local.thisCreditID#_",local.delim),2,local.delim),
								"value":arguments.event.getValue(local.key)
							})>
						</cfif>
					</cfif>
				</cfloop>
				<cfwddx action="CFML2WDDX" input="#local.arrCreditsAvail#" output="local.thisWDDXCredits">
				<cfset local.creditsAvailable = ToString(local.thisWDDXCredits)>

				<cfset arrayAppend(local.returnStruct.arrCredits, {
					CSALinkID=arguments.event.getValue('sf_CSALinkID_#local.thisCreditID#',0),
					courseApproval=arguments.event.getTrimValue('sf_creditApprovalNum_#local.thisCreditID#',''),
					statusID=arguments.event.getValue('sf_creditStatusID_#local.thisCreditID#',0),
					creditOfferedStartDate=arguments.event.getTrimValue('sf_creditOfferedStartDate_#local.thisCreditID#',''),
					creditOfferedEndDate=arguments.event.getTrimValue('sf_creditOfferedEndDate_#local.thisCreditID#',''),
					creditCompleteByDate=arguments.event.getTrimValue('sf_creditCompleteByDate_#local.thisCreditID#',''),
					isCreditRequired=arguments.event.getValue('sf_isCreditRequired_#local.thisCreditID#',0),
					wddxCreditsAvailable=local.creditsAvailable
				})>
			</cfloop>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="insertSWODSubmission" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structnew()>
		<cfset local.returnStruct = structnew()>

		<cfset local.arrSpeakers = listToArray(arguments.event.getValue('sf_speaker_authorID',''))>
		<cfset local.arrModerators = listToArray(arguments.event.getValue('sf_moderator_authorID',''))>
		<cfset local.arrForms = arrayNew(1)>
		<cfset local.arrCategories = listToArray(arguments.event.getValue('sf_categoryID',''))>

		<cftry>
			<cfset local.strDataFromGrids = getSubmissionDataFromGrids(event=arguments.event)>
			<cfset local.arrObjectives = local.strDataFromGrids.arrObjectives>
			<cfset local.arrLinks = local.strDataFromGrids.arrLinks>
			<cfset local.arrRates = local.strDataFromGrids.arrRates>
			<cfset local.arrCredits = local.strDataFromGrids.arrCredits>

			<cfquery name="local.qryInsertSWODSubmission" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					DECLARE @participantID int, @submissionID int, @subRateID int, @seminarName varchar(250), @orgCode varchar(10), @orgID int, @siteID int, @recordedByMemberID int,
						@authorTypeIDSpeaker int, @authorTypeIDModerator int, @recordedByMember varchar(500), @msgjson varchar(max);

					SET @participantID = dbo.fn_getParticipantIDFromOrgCode(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('mc_siteInfo.sitecode')#">);
					SET @recordedByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">;
					SET @seminarName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getTrimValue('sf_seminarName','')#">;

					SELECT @participantID = p.participantID, @orgCode = p.orgCode, @orgID=mcs.orgID, @siteID = mcs.siteID
					FROM dbo.tblParticipants AS p
					INNER JOIN membercentral.dbo.sites AS mcs ON mcs.siteCode = p.orgCode
					WHERE p.participantID = @participantID;

					SELECT @recordedByMember = firstname + ' ' + lastname
					FROM memberCentral.dbo.ams_members
					WHERE memberID = @recordedByMemberID;

					SELECT @authorTypeIDSpeaker = authorTypeID from dbo.tblAuthorTypes where authorType = 'Speaker';
					SELECT @authorTypeIDModerator = authorTypeID from dbo.tblAuthorTypes where authorType = 'Moderator';

					BEGIN TRAN;

						INSERT INTO dbo.tblSWODSubmissions(participantID, seminarName, seminarSubTitle, seminarDesc, dateOrigPublished, seminarLength,
							offerQA, blankOnInactivity, offerCertificate, offerEval, offerExam, introMessageText, endOfSeminarText,
							isPriceBasedOnActual, dateCatalogStart, dateCatalogEnd, allowSyndication, createOwnPricingToOptIns, priceSyndication, isFeatured,
							additionalInfo, status, submittedByMemberID, dateSubmitted)
						VALUES (
							@participantID,
							@seminarName,
							<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getTrimValue('sf_seminarSubTitle','')#">,
							<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getTrimValue('sf_seminarDesc','')#">,
							<cfif len(arguments.event.getTrimValue('sf_dateOrigPublished','')) and isDate(arguments.event.getTrimValue('sf_dateOrigPublished'))>
								<cfqueryparam cfsqltype="CF_SQL_DATE" value="#arguments.event.getTrimValue('sf_dateOrigPublished')#">,
							<cfelse>
								<cfqueryparam cfsqltype="CF_SQL_DATE" value="#now()#">,
							</cfif>
							<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('sf_seminarLength',0)#">,
							<cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('sf_offerQA',0)#">,
							<cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('sf_blankOnInactivity',0)#">,
							<cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('sf_offerCertificate',0)#">,
							<cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('sf_offerEval',0)#">,
							<cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('sf_offerExam',0)#">,
							NULLIF(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getTrimValue('sf_introMessageText','')#">,''),
							NULLIF(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getTrimValue('sf_endofSeminartext','')#">,''),
							<cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('sf_isPriceBasedOnActual',0)#">,
							<cfif arguments.event.getValue('sf_allowCatalog',0)>
								<cfqueryparam cfsqltype="CF_SQL_DATE" value="#arguments.event.getTrimValue('sf_dateCatalogStart')#">,
								<cfqueryparam cfsqltype="CF_SQL_DATE" value="#arguments.event.getTrimValue('sf_dateCatalogEnd')#">,
							<cfelse>
								NULL, NULL,
							</cfif>
							<cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('sf_allowSyndication',0)#">,
							<cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('sf_createOwnPricingToOptIns',0)#">,
							<cfif arguments.event.getValue('sf_allowSyndication',0) eq 1 AND arguments.event.getValue('sf_createOwnPricingToOptIns',0) eq 0>
								<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.event.getTrimValue('sf_syndXML','<syndication></syndication>')#">,
							<cfelse>
								NULL,
							</cfif>
							<cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('sf_isFeaturedProgram',0)#">,
							NULLIF(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getTrimValue('sf_additionalInfo','')#">,''),
							'P',
							<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">,
							<cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#now()#">
						);

						SELECT @submissionID = SCOPE_IDENTITY();

						<cfloop array="#local.arrObjectives#" index="local.thisObjective">
							INSERT INTO dbo.tblSWODSubmissionsAndObjectives (submissionID, objective)
							VALUES (@submissionID, <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.thisObjective#">);
						</cfloop>
						<cfif arrayLen(local.arrSpeakers) OR arrayLen(local.arrModerators)>
							INSERT INTO dbo.tblSWODSubmissionsAndAuthors (submissionID, authorID, authorTypeID)
							SELECT @submissionID, listitem, @authorTypeIDSpeaker
							FROM memberCentral.dbo.fn_intListToTable(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arrayToList(local.arrSpeakers)#">,',')
							<cfif arguments.event.getValue('sf_offerQA',0)>
								UNION
								SELECT @submissionID, listitem, @authorTypeIDModerator
								FROM memberCentral.dbo.fn_intListToTable(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arrayToList(local.arrModerators)#">,',')
							</cfif>;
						</cfif>
						<cfloop array="#local.arrLinks#" index="local.thisLink">
							INSERT INTO dbo.tblSWODSubmissionsAndLinks (submissionID, linkName, linkURL)
							VALUES (@submissionID, <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.thisLink.name#">, <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.thisLink.url#">);
						</cfloop>
						<cfif arguments.event.getValue('sf_offerEval',0)>
							<cfset arrayAppend(local.arrForms,listToArray(arguments.event.getValue('sf_formID','')),true)>
							<cfif arrayLen(local.arrForms)>
								INSERT INTO dbo.tblSWODSubmissionsAndForms (submissionID, formID)
								SELECT @submissionID, listitem
								FROM memberCentral.dbo.fn_intListToTable(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arrayToList(local.arrForms)#">,',');
							</cfif>
						</cfif>
						<cfif arguments.event.getValue('sf_allowCatalog',0)>
							<cfloop array="#local.arrRates#" index="local.thisRate">
								INSERT INTO dbo.tblSWODSubmissionsAndRates (submissionID, rateName, rate)
								VALUES (@submissionID, <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.thisRate.rateName#">, <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#local.thisRate.rate#">);

								SELECT @subRateID = SCOPE_IDENTITY();
								
								<cfif arguments.event.getValue('sf_isPriceBasedOnActual',0) and arrayLen(local.thisRate.arrGroupIDs)>
									INSERT INTO dbo.tblSWODSubmissionsAndRateGroups (rateID, groupID, include)
									SELECT @subRateID, listitem, 1
									FROM memberCentral.dbo.fn_intListToTable(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arrayToList(local.thisRate.arrGroupIDs)#">,',');
								</cfif>
								<cfif arguments.event.getValue('sf_isPriceBasedOnActual',0) and arrayLen(local.thisRate.arrDeniedGroupIDs)>
									INSERT INTO dbo.tblSWODSubmissionsAndRateGroups (rateID, groupID, include)
									SELECT @subRateID, listitem, 0
									FROM memberCentral.dbo.fn_intListToTable(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arrayToList(local.thisRate.arrDeniedGroupIDs)#">,',');
								</cfif>

							</cfloop>
						</cfif>
						<cfif arrayLen(local.arrCategories)>
							INSERT INTO dbo.tblSWODSubmissionsAndCategories (submissionID, categoryID)
							SELECT @submissionID, listitem
							FROM memberCentral.dbo.fn_intListToTable(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arrayToList(local.arrCategories)#">,',');
						</cfif>

						<cfif arguments.event.getValue('sf_offerCECredit',0)>
							<cfloop array="#local.arrCredits#" index="local.thisCredit">
								INSERT INTO dbo.tblSWODSubmissionsAndCredits (submissionID, CSALinkID, statusID, courseApproval, wddxCreditsAvailable, creditOfferedStartDate,
									creditOfferedEndDate, creditCompleteByDate, isCreditRequired)
								VALUES (
									@submissionID,
									<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisCredit.CSALinkID#">,
									<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisCredit.statusID#">,
									NULLIF(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.thisCredit.courseApproval#">,''),
									<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#local.thisCredit.wddxCreditsAvailable#">,
									<cfif len(local.thisCredit.creditOfferedStartDate) and isDate(local.thisCredit.creditOfferedStartDate)>
										<cfqueryparam cfsqltype="CF_SQL_DATE" value="#local.thisCredit.creditOfferedStartDate#">,
									<cfelse>
										<cfqueryparam cfsqltype="CF_SQL_DATE" null="Yes">,
									</cfif>
									<cfif len(local.thisCredit.creditOfferedEndDate) and isDate(local.thisCredit.creditOfferedEndDate)>
										<cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#local.thisCredit.creditOfferedEndDate# 23:59:59">,
									<cfelse>
										<cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" null="Yes">,
									</cfif>
									<cfif len(local.thisCredit.creditCompleteByDate) and isDate(local.thisCredit.creditCompleteByDate)>
										<cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#local.thisCredit.creditCompleteByDate# 23:59:59">,
									<cfelse>
										<cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" null="Yes">,
									</cfif>
									<cfqueryparam cfsqltype="CF_SQL_BIT" value="#local.thisCredit.isCreditRequired#">
								);
							</cfloop>
						</cfif>
					
					COMMIT TRAN;

					SELECT @msgjson = 'SWOD Submission [' + @seminarName + '] for [' + @orgCode + '] was created by ' + @recordedByMember + '.';

					INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
					VALUES ('{ "c":"auditLog", "d": {
						"AUDITCODE":"SW",
						"ORGID":' + CAST(@orgID AS VARCHAR(10)) + ',
						"SITEID":' + CAST(@siteID AS VARCHAR(10)) + ',
						"ACTORMEMBERID":' + CAST(@recordedByMemberID AS VARCHAR(20)) + ',
						"ACTIONDATE":"' + CONVERT(VARCHAR(20),GETDATE(),120) + '",
						"MESSAGE":"' + replace(memberCentral.dbo.fn_cleanInvalidXMLChars(@msgjson),'"','\"') + '" } }');

					SELECT @submissionID as submissionID;
				END TRY
				BEGIN CATCH
					IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
					EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset sendSubmissionNotification(submissionID=val(local.qryInsertSWODSubmission.submissionID))>

			<cfset local.returnStruct["submissionid"] = val(local.qryInsertSWODSubmission.submissionID)>
			<cfset local.returnStruct["success"] = true>
		<cfcatch type="Any">
			<cfset local.returnStruct["success"] = false>
			<cfset local.returnStruct["errmsg"] = cfcatch.message & (len(cfcatch.detail) ? " " & cfcatch.detail : "")>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="convertSubmissionToOnDemand" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structnew()>
		<cfset local.returnStruct = structnew()>
		<cfset local.submissionID = arguments.event.getValue("submissionID",0)>

		<cfset local.arrSpeakers = listToArray(arguments.event.getValue('sf_speaker_authorID',''))>
		<cfset local.arrModerators = listToArray(arguments.event.getValue('sf_moderator_authorID',''))>
		<cfset local.arrForms = arrayNew(1)>
		<cfset local.arrCategories = listToArray(arguments.event.getValue('sf_categoryID',''))>

		<cftry>
			<cfset local.strDataFromGrids = getSubmissionDataFromGrids(event=arguments.event)>
			<cfset local.arrObjectives = local.strDataFromGrids.arrObjectives>
			<cfset local.arrLinks = local.strDataFromGrids.arrLinks>
			<cfset local.arrRates = local.strDataFromGrids.arrRates>
			<cfset local.arrCredits = local.strDataFromGrids.arrCredits>
			<cfset local.qualifySWProgramRateRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="SWProgramRate", functionName="qualify")>

			<cfquery name="local.qryConvertToOnDemand" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					DECLARE @participantID int, @orgCode varchar(10), @siteID int, @orgID int, @submissionID int, @seminarID int, @titleID int, @objectiveID int, @rateID int,
						@siteResourceID int, @seminarName varchar(250), @seminarSubTitle varchar(250), @seminarDesc varchar(max), @introMessageText varchar(max), @endofSeminartext varchar(max),
						@allowCatalog bit, @isPriceBasedOnActual bit, @dateCatalogStart date, @dateCatalogEnd date, @isFeatured bit, @isPublished bit, @dateOrigPublished date,
						@offerQA bit, @blankOnInactivity bit, @offerCertificate bit, @seminarLength int, @recordedByMemberID int, @recordedByMember varchar(500), @nowDate datetime=getdate(),
						@msgjson varchar(max), @objective varchar(max), @authorID int, @linkName varchar(100), @linkURL varchar(200), @rateName varchar(100), @rate money, @subRateID int,
						@categoryID int, @authorTypeIDSpeaker int, @authorTypeIDModerator int, @formID int, @seminarFormID int, @allowSyndication bit, @createOwnPricingToOptIns bit,
						@priceSyndication xml, @programCode varchar(15), @largeVideoLayoutID int, @fileName varchar(100), @fileTitle varchar(200);
					
					SET @submissionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.submissionID#">;
					SET @recordedByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">;
					SET @seminarName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getTrimValue('sf_seminarName','')#">;
					SET @seminarSubTitle = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getTrimValue('sf_seminarSubTitle','')#">;
					SET @seminarDesc = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getTrimValue('sf_seminarDesc','')#">;
					
					SET @introMessageText  = NULLIF(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getTrimValue('sf_introMessageText','')#">,'');
					SET @endofSeminartext  = NULLIF(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getTrimValue('sf_endofSeminartext','')#">,'');
					SET @isFeatured = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('sf_isFeaturedProgram',0)#">;
					<cfif len(arguments.event.getTrimValue('sf_dateOrigPublished','')) and isDate(arguments.event.getTrimValue('sf_dateOrigPublished'))>
						SET @dateOrigPublished = <cfqueryparam cfsqltype="CF_SQL_DATE" value="#arguments.event.getTrimValue('sf_dateOrigPublished')#">;
					<cfelse>
						SET @dateOrigPublished = <cfqueryparam cfsqltype="CF_SQL_DATE" value="#now()#">;
					</cfif>
					SET @offerQA = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('sf_offerQA',0)#">;
					SET @blankOnInactivity = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('sf_blankOnInactivity',0)#">;
					SET @offerCertificate = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('sf_offerCertificate',0)#">;
					SET @seminarLength = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('sf_seminarLength',0)#">;
					<cfif arguments.event.getValue('sf_allowCatalog',0)>
						SET @dateCatalogStart = <cfqueryparam cfsqltype="CF_SQL_DATE" value="#arguments.event.getTrimValue('sf_dateCatalogStart')#">;
						SET @dateCatalogEnd = <cfqueryparam cfsqltype="CF_SQL_DATE" value="#arguments.event.getTrimValue('sf_dateCatalogEnd')#">;
					<cfelse>
						SET @dateCatalogStart = NULL;
						SET @dateCatalogEnd = NULL;
					</cfif>
					SET @allowCatalog = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('sf_allowCatalog',0)#">;
					SET @isPriceBasedOnActual = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('sf_isPriceBasedOnActual',0)#">;
					SET @allowSyndication = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('sf_allowSyndication',0)#">;
					SET @createOwnPricingToOptIns = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('sf_createOwnPricingToOptIns',0)#">;
					<cfif arguments.event.getValue('sf_allowSyndication',0) eq 1 AND arguments.event.getValue('sf_createOwnPricingToOptIns',0) eq 0>
						SET @priceSyndication = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.event.getTrimValue('sf_syndXML','<syndication></syndication>')#">;
					<cfelse>
						SET @priceSyndication = NULL;
					</cfif>

					SELECT @participantID = s.participantID, @orgCode = p.orgCode, @orgID=mcs.orgID, @siteID = mcs.siteID
					FROM dbo.tblSWODSubmissions AS s
					INNER JOIN dbo.tblParticipants AS p ON p.participantID = s.participantID
					INNER JOIN membercentral.dbo.sites AS mcs ON mcs.siteCode = p.orgCode
					WHERE s.submissionID = @submissionID;

					SELECT @recordedByMember = firstname + ' ' + lastname
					FROM memberCentral.dbo.ams_members
					WHERE memberID = @recordedByMemberID;

					SELECT @authorTypeIDSpeaker = authorTypeID from dbo.tblAuthorTypes where authorType = 'Speaker';
					SELECT @authorTypeIDModerator = authorTypeID from dbo.tblAuthorTypes where authorType = 'Moderator';

					SELECT @largeVideoLayoutID = layoutID
					FROM dbo.tblSeminarsSWODLayouts
					WHERE layout = 'largeVideo';

					BEGIN TRAN;

						UPDATE dbo.tblSWODSubmissions
						SET seminarName = @seminarName,
							seminarSubTitle = @seminarSubTitle,
							seminarDesc = @seminarDesc,
							dateOrigPublished = @dateOrigPublished,
							seminarLength = @seminarLength,
							offerQA = @offerQA,
							blankOnInactivity = @blankOnInactivity,
							offerCertificate = @offerCertificate,
							offerEval = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('sf_offerEval',0)#">,
							offerExam = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('sf_offerExam',0)#">,
							introMessageText = @introMessageText,
							endOfSeminarText = @endofSeminartext,
							isPriceBasedOnActual = @isPriceBasedOnActual,
							dateCatalogStart = @dateCatalogStart,
							dateCatalogEnd = @dateCatalogEnd,
							allowSyndication = @allowSyndication,
							createOwnPricingToOptIns = @createOwnPricingToOptIns,
							priceSyndication = @priceSyndication,
							isFeatured = @isFeatured,
							additionalInfo = NULLIF(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getTrimValue('sf_additionalInfo','')#">,''),
							status = 'R',
							reviewedByMemberID = @recordedByMemberID,
							dateReviewed = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#now()#">
						WHERE submissionID = @submissionID;

						EXEC dbo.swod_createSeminar @orgcode=@orgCode, @seminarName=@seminarName, @seminarSubTitle=@seminarSubTitle, @freeRateDisplay="$0.00",
							@recordedByMemberID=@recordedByMemberID, @introMessageText=@introMessageText, @dateOrigPublished=@dateOrigPublished, @seminarID=@seminarID OUTPUT;

						SELECT @programCode = programCode 
						FROM dbo.tblSeminars 
						WHERE seminarID = @seminarID;
						
						EXEC dbo.swod_updateSeminar @seminarID=@seminarID, @sitecode=@orgCode, @seminarName=@seminarName, @seminarSubTitle=@seminarSubTitle,
							@programCode=@programCode, @seminarDesc=@seminarDesc, @isPublished=0, 
							@dateActivated=@nowDate, @dateOrigPublished=@dateOrigPublished, @layoutID=@largeVideoLayoutID, 
							@seminarLength=@seminarLength, @lockSettings=0, @recordedByMemberID=@recordedByMemberID;

						UPDATE dbo.tblSeminars
						SET preventSeminarFees = 0
						WHERE seminarID = @seminarID;

						EXEC dbo.swod_updateSeminarSettings @seminarID=@seminarID, @sitecode=@orgCode, @introMessageText=@introMessageText, @endofSeminartext=@endofSeminartext, @isPublisher=1,
							@offerQA=@offerQA, @blankOnInactivity=@blankOnInactivity, @offerCertificate=@offerCertificate, @preReqSeminarID=0, @recordedByMemberID=@recordedByMemberID;

						IF @isFeatured=1 
							EXEC dbo.sw_toggleFeaturedProgram @orgCode=@orgCode, @programType='SWOD', @programID=@seminarID, @isFeatured=@isFeatured, @recordedByMemberID=@recordedByMemberID;

						EXEC dbo.sw_updateSeminarPricing @seminarID=@seminarID, @allowCatalog=@allowCatalog, @isPriceBasedOnActual=@isPriceBasedOnActual,
							@dateCatalogStart=@dateCatalogStart, @dateCatalogEnd=@dateCatalogEnd, @freeRateDisplay="$0.00", @revenueGLAccountID=NULL,
							@recordedByMemberID=@recordedByMemberID;
						
						EXEC dbo.sw_saveProgramRateSyndication @participantID=@participantID, @programID=@seminarID, @programType='SWOD', @allowSyndication=@allowSyndication,
							@pushDefaultPricingToOptIns=0, @allowOptInRateChange=@createOwnPricingToOptIns, @pricesyndication=@priceSyndication, @recordedByMemberID=@recordedByMemberID;
						
						EXEC dbo.sw_addTitle @orgCode=@orgCode, @seminarID=@seminarID, @titleName=@seminarName, @recordedByMemberID=@recordedByMemberID, @titleID=@titleID OUTPUT;

						UPDATE dbo.tblSWODSubmissions
						SET convertedSeminarID = @seminarID
						WHERE submissionID = @submissionID;

						<!--- remove existing entries --->
						DELETE FROM dbo.tblSWODSubmissionsAndCredits WHERE submissionID = @submissionID;
						DELETE FROM dbo.tblSWODSubmissionsAndCategories WHERE submissionID = @submissionID;
						DELETE rg
						FROM dbo.tblSWODSubmissionsAndRateGroups AS rg
						INNER JOIN dbo.tblSWODSubmissionsAndRates AS r ON r.autoID = rg.rateID
							AND r.submissionID = @submissionID;
						DELETE FROM dbo.tblSWODSubmissionsAndRates WHERE submissionID = @submissionID;
						DELETE FROM dbo.tblSWODSubmissionsAndForms WHERE submissionID = @submissionID;
						DELETE FROM dbo.tblSWODSubmissionsAndLinks WHERE submissionID = @submissionID;
						DELETE FROM dbo.tblSWODSubmissionsAndAuthors WHERE submissionID = @submissionID;
						DELETE FROM dbo.tblSWODSubmissionsAndObjectives WHERE submissionID = @submissionID;

						<!--- inserting updated data from grids --->
						<cfloop array="#local.arrObjectives#" index="local.thisObjective">
							SET @objective = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.thisObjective#">;

							INSERT INTO dbo.tblSWODSubmissionsAndObjectives (submissionID, objective)
							VALUES (@submissionID, @objective);

							EXEC dbo.sw_createLearningObjective @siteCode=@orgCode, @objective=@objective, @programType='SWOD', @programID=@seminarID,
								@recordedByMemberID=@recordedByMemberID, @objectiveID=@objectiveID OUTPUT;
						</cfloop>
						<cfloop array="#local.arrSpeakers#" index="local.thisAuthorID">
							SET @authorID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisAuthorID#">;

							INSERT INTO dbo.tblSWODSubmissionsAndAuthors (submissionID, authorID, authorTypeID)
							VALUES (@submissionID, @authorID, @authorTypeIDSpeaker);

							EXEC dbo.sw_addSeminarAndAuthor @seminarID=@seminarID, @authorID=@authorID, @recordedByMemberID=@recordedByMemberID;
						</cfloop>
						<cfif arguments.event.getValue('sf_offerQA',0)>
							<cfloop array="#local.arrModerators#" index="local.thisAuthorID">
								SET @authorID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisAuthorID#">;

								INSERT INTO dbo.tblSWODSubmissionsAndAuthors (submissionID, authorID, authorTypeID)
								VALUES (@submissionID, @authorID, @authorTypeIDModerator);

								EXEC dbo.sw_addSeminarAndAuthor @seminarID=@seminarID, @authorID=@authorID, @recordedByMemberID=@recordedByMemberID;
							</cfloop>
						</cfif>
						<cfloop array="#local.arrLinks#" index="local.thisLink">
							SET @linkName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.thisLink.name#">;
							SET @linkURL = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.thisLink.url#">;

							INSERT INTO dbo.tblSWODSubmissionsAndLinks (submissionID, linkName, linkURL)
							VALUES (@submissionID, @linkName, @linkURL);

							EXEC dbo.sw_saveProgramLink @programID=@seminarID, @programType='SWOD', @siteCode=@orgCode, @linkID=0,
								@linkURL=@linkURL, @linkName=@linkName, @linkDesc='', @purchaseURL='', @recordedByMemberID=@recordedByMemberID;
						</cfloop>
						<cfif arguments.event.getValue('sf_offerEval',0)>
							<cfset arrayAppend(local.arrForms,listToArray(arguments.event.getValue('sf_formID','')),true)>
							<cfloop array="#local.arrForms#" index="local.thisForm">
								SET @formID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisForm#">;

								INSERT INTO dbo.tblSWODSubmissionsAndForms (submissionID, formID)
								VALUES (@submissionID, @formID);

								EXEC dbo.sw_addSeminarForm @seminarID=@seminarID, @formID=@formID, @loadPoint='evaluation', @numResponsesPerEnrollment=0,
									@recordedByMemberID=@recordedByMemberID, @seminarformID=@seminarFormID OUTPUT;
							</cfloop>
						</cfif>
						<cfif arguments.event.getValue('sf_allowCatalog',0)>
							DECLARE @qualifyFunctionID int, @resourceRightsID int;
							DECLARE @tblNewRRIDs TABLE (resourceRightsID int);

							SELECT @qualifyFunctionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qualifySWProgramRateRFID#">;

							<cfloop array="#local.arrRates#" index="local.thisRate">
								SELECT @siteResourceID = NULL, @rateID = NULL;

								SET @rateName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.thisRate.rateName#">;
								SET @rate = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#local.thisRate.rate#">;

								INSERT INTO dbo.tblSWODSubmissionsAndRates (submissionID, rateName, rate)
								VALUES (@submissionID, @rateName, @rate);

								SELECT @subRateID = SCOPE_IDENTITY();

								EXEC dbo.sw_createSeminarRate @participantID=@participantID, @seminarID=@seminarID, @rateGroupingID=NULL, @rateName=@rateName,
									@rate=@rate, @isHidden=0, @revenueGLAccountID=NULL, @recordedByMemberID=@recordedByMemberID, @rateID=@rateID OUTPUT, @siteResourceID=@siteResourceID OUTPUT;
								
								<!--- add permissions groups --->
								<cfif arguments.event.getValue('sf_isPriceBasedOnActual',0) and (arrayLen(local.thisRate.arrGroupIDs) or arrayLen(local.thisRate.arrDeniedGroupIDs))>
									DELETE FROM @tblNewRRIDs;

									<cfif arrayLen(local.thisRate.arrGroupIDs)>
										INSERT INTO dbo.tblSWODSubmissionsAndRateGroups (rateID, groupID, include)
										SELECT @subRateID, listitem, 1
										FROM memberCentral.dbo.fn_intListToTable(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arrayToList(local.thisRate.arrGroupIDs)#">,',');
									</cfif>
									<cfif arrayLen(local.thisRate.arrDeniedGroupIDs)>
										INSERT INTO dbo.tblSWODSubmissionsAndRateGroups (rateID, groupID, include)
										SELECT @subRateID, listitem, 0
										FROM memberCentral.dbo.fn_intListToTable(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arrayToList(local.thisRate.arrDeniedGroupIDs)#">,',');
									</cfif>

									INSERT INTO memberCentral.dbo.cms_siteResourceRights(siteID, resourceID, [include], functionID, roleID, groupID, inheritedRightsResourceID, inheritedRightsFunctionID)
										OUTPUT inserted.resourceRightsID INTO @tblNewRRIDs(resourceRightsID)
									SELECT @siteID, @siteResourceID, [include], @qualifyFunctionID, NULL, groupID, NULL, NULL
									FROM dbo.tblSWODSubmissionsAndRateGroups
									WHERE rateID = @subRateID;

									SELECT @resourceRightsID = MIN(resourceRightsID) FROM @tblNewRRIDs;
									WHILE @resourceRightsID IS NOT NULL BEGIN
										EXEC memberCentral.dbo.cache_perms_refreshSiteResourceRight @siteID=@siteID, @siteResourceID=@siteResourceID, @resourceRightID=@resourceRightsID;
										SELECT @resourceRightsID = MIN(resourceRightsID) FROM @tblNewRRIDs WHERE resourceRightsID > @resourceRightsID;
									END
								</cfif>
							</cfloop>
						</cfif>
						<cfloop array="#local.arrCategories#" index="local.thisCategoryID">
							SET @categoryID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisCategoryID#">;

							INSERT INTO dbo.tblSWODSubmissionsAndCategories (submissionID, categoryID)
							VALUES (@submissionID, @categoryID);

							EXEC dbo.sw_addSeminarAndCategory @seminarID=@seminarID, @categoryID=@categoryID, @recordedByMemberID=@recordedByMemberID;
						</cfloop>
						<cfif arguments.event.getValue('sf_offerCECredit',0) and arrayLen(local.arrCredits)>
							<cfloop array="#local.arrCredits#" index="local.thisCredit">
								INSERT INTO dbo.tblSWODSubmissionsAndCredits (submissionID, CSALinkID, statusID, courseApproval, wddxCreditsAvailable, creditOfferedStartDate,
									creditOfferedEndDate, creditCompleteByDate, isCreditRequired)
								VALUES (
									@submissionID,
									<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisCredit.CSALinkID#">,
									<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisCredit.statusID#">,
									NULLIF(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.thisCredit.courseApproval#">,''),
									<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#local.thisCredit.wddxCreditsAvailable#">,
									<cfif len(local.thisCredit.creditOfferedStartDate) and isDate(local.thisCredit.creditOfferedStartDate)>
										<cfqueryparam cfsqltype="CF_SQL_DATE" value="#local.thisCredit.creditOfferedStartDate#">,
									<cfelse>
										<cfqueryparam cfsqltype="CF_SQL_DATE" null="Yes">,
									</cfif>
									<cfif len(local.thisCredit.creditOfferedEndDate) and isDate(local.thisCredit.creditOfferedEndDate)>
										<cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#local.thisCredit.creditOfferedEndDate# 23:59:59">,
									<cfelse>
										<cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" null="Yes">,
									</cfif>
									<cfif len(local.thisCredit.creditCompleteByDate) and isDate(local.thisCredit.creditCompleteByDate)>
										<cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#local.thisCredit.creditCompleteByDate# 23:59:59">,
									<cfelse>
										<cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" null="Yes">,
									</cfif>
									<cfqueryparam cfsqltype="CF_SQL_BIT" value="#local.thisCredit.isCreditRequired#">
								);
							</cfloop>

							INSERT INTO dbo.tblSeminarsAndCredit (seminarID, CSALinkID, statusID, courseapproval, wddxcreditsAvailable, creditOfferedStartDate,
								creditOfferedEndDate, creditCompleteByDate, isCreditRequired, isCreditDefaulted)
							SELECT @seminarID, CSALinkID, statusID, ISNULL(courseapproval,''), wddxcreditsAvailable, creditOfferedStartDate,
								creditOfferedEndDate, creditCompleteByDate, isCreditRequired, isCreditRequired
							FROM dbo.tblSWODSubmissionsAndCredits
							WHERE submissionID = @submissionID;

							EXEC dbo.swod_populateSearchText @seminarID = @seminarID;
						</cfif>

					COMMIT TRAN;

					SELECT @msgjson = 'SWOD Submission [' + @seminarName + '] for [' + @orgCode + '] was converted into SWOD-'+CAST(@seminarID AS VARCHAR(10))+' by ' + @recordedByMember + '.';

					INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
					VALUES ('{ "c":"auditLog", "d": {
						"AUDITCODE":"SW",
						"ORGID":' + CAST(@orgID AS VARCHAR(10)) + ',
						"SITEID":' + CAST(@siteID AS VARCHAR(10)) + ',
						"ACTORMEMBERID":' + CAST(@recordedByMemberID AS VARCHAR(20)) + ',
						"ACTIONDATE":"' + CONVERT(VARCHAR(20),GETDATE(),120) + '",
						"MESSAGE":"' + replace(memberCentral.dbo.fn_cleanInvalidXMLChars(@msgjson),'"','\"') + '" } }');

					SELECT @seminarID as seminarID, @participantID as participantID, @siteID as siteID, @orgCode as participantOrgCode;
				END TRY
				BEGIN CATCH
					IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
					EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfif local.qryConvertToOnDemand.recordCount and arguments.event.getValue('sf_addNewProgramImg',0)>
				<cfset local.ftdImageResult = setupProgramFeaturedImageFromSubmission(
					submissionID=local.submissionID,
					seminarID=local.qryConvertToOnDemand.seminarID,
					siteID=local.qryConvertToOnDemand.siteID,
					participantOrgCode=local.qryConvertToOnDemand.participantOrgCode,
					participantID=local.qryConvertToOnDemand.participantID
				)>
			</cfif>

			<cfset local.returnStruct["submissionid"] = local.submissionID>
			<cfset local.returnStruct["seminarid"] = val(local.qryConvertToOnDemand.seminarID)>
			<cfset local.returnStruct["success"] = true>
		<cfcatch type="Any">
			<cfset local.returnStruct["success"] = false>
			<cfset local.returnStruct["errmsg"] = cfcatch.message & (len(cfcatch.detail) ? " " & cfcatch.detail : "")>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="sendSubmissionNotification" access="private" output="false" returntype="void">
		<cfargument name="submissionID" type="numeric" required="true">
		
		<cfset var local = structNew()>

		<cfset local.qrySubmissionDetail = getSWODSubmissionDetail(submissionID=arguments.submissionID)>
		<cfset local.sendingSiteInfo = application.objSiteInfo.getSiteInfo(local.qrySubmissionDetail.participantOrgCode)>
		
		<cftry>
			<cfsavecontent variable="local.notifyContent">
				<cfoutput>
					<div>
						<b>The following program was submitted via the SWOD Submission Form</b>:<br/><hr>
						<a href="#application.objSiteInfo.getSiteInfo('SW').scheme#://#application.objSiteInfo.getSiteInfo('SW').mainHostName#/?pg=admin&jumpToTool=SeminarWebDesktopAdmin%7ClistSWODSubmissions&submissionID=#arguments.submissionID#" target="_blank">Click here</a> to review this submission.<br/><br/><br/>

						<b>Submitted By</b>:<br/><hr>
						#local.qrySubmissionDetail.submittedByMember#<br/>
						#local.qrySubmissionDetail.submittedByEmail#<br/><br/><br/>

						<b>Submission Details</b>:<br/><hr>
						Publisher: #local.qrySubmissionDetail.participantOrgCode# - #local.sendingSiteInfo.sitename#<br/><br/>
						Seminar Title: #encodeForHTML(local.qrySubmissionDetail.seminarName)#<br/><br/>
						<cfif len(local.qrySubmissionDetail.seminarSubTitle)>
							Seminar SubTitle: #encodeForHTML(local.qrySubmissionDetail.seminarSubTitle)#<br/><br/>
						</cfif>
						<cfif len(local.qrySubmissionDetail.dateCatalogStart)>
							Catalog Dates: #dateFormat(local.qrySubmissionDetail.dateCatalogStart,"m/d/yyyy")# - #dateFormat(local.qrySubmissionDetail.dateCatalogEnd,"m/d/yyyy")#<br/><br/>
						</cfif>
					</div>
				</cfoutput>
			</cfsavecontent>
			<cfset local.notifyContent = trim(replace(replace(replace(local.notifyContent,chr(9),"","ALL"),chr(13),"","ALL"),chr(10),"","ALL"))>

			<cfset local.emailSubject = "SWOD Program Submission #local.qrySubmissionDetail.participantOrgCode#-#arguments.submissionID#: #local.qrySubmissionDetail.seminarName#">

			<!--- create ticket via API --->
			<cfsavecontent variable="local.apiPayload"><cfoutput>{"ticket": { "subject": #serializeJSON(local.emailSubject)#, "comment": { "html_body": #serializeJSON(local.notifyContent)#, "public": false }, "requester": { "name": "#local.qrySubmissionDetail.submittedByMember#", "email": "#local.qrySubmissionDetail.submittedByEmail#" }, "tags": [ "no_csat","no_solve_confirmation","no_initial_email","on-demand" ], "group_id": 360000404992, "email_ccs": [{ "user_email": "#local.qrySubmissionDetail.submittedByEmail#", "user_name": "#local.qrySubmissionDetail.submittedByMember#", "action": "put"}] }}</cfoutput></cfsavecontent>

			<cfhttp method="POST" url="https://trialsmith.zendesk.com/api/v2/tickets.json?async=true" useragent="MemberCentral.com" result="local.apiResult" throwonerror="true">
				<cfhttpparam type="header" name="accept" value="application/json">
				<cfhttpparam type="header" name="content-type" value="application/json">
				<cfhttpparam type="header" name="authorization" value="Basic #ToBase64("#application.strPlatformAPIKeys.zendesk.email#/token:#application.strPlatformAPIKeys.zendesk.token#")#">
				<cfhttpparam type="body" value="#trim(local.apiPayload)#">
			</cfhttp>

			<!--- confirmation email to submitter --->
			<cfif len(local.qrySubmissionDetail.submittedByEmail)>
				<cfsavecontent variable="local.emailHTMLContent">
					<cfoutput>
						Hi #local.qrySubmissionDetail.submitterFirstName#,<br/><br/>
						Your program has been submitted successfully. A member of our team will follow up on your submission within 2 business days. To submit additional files for this program, <a href="https://spaces.hightail.com/uplink/MemberCentral" target="_blank">click here</a>.<br/><br/>
						Below is a summary of your submission:<br/><hr>
						Publisher: #local.sendingSiteInfo.sitename#<br/><br/>
						Seminar Title: #encodeForHTML(local.qrySubmissionDetail.seminarName)#
						<cfif len(local.qrySubmissionDetail.seminarSubTitle)>
							<br/><br/>
							Seminar SubTitle: #encodeForHTML(local.qrySubmissionDetail.seminarSubTitle)#
						</cfif>
						<cfif len(local.qrySubmissionDetail.dateCatalogStart)>
							<br/><br/>
							Catalog Dates: #dateFormat(local.qrySubmissionDetail.dateCatalogStart,"m/d/yyyy")# - #dateFormat(local.qrySubmissionDetail.dateCatalogEnd,"m/d/yyyy")#
						</cfif>
						<hr><br/>
						<p style="margin-top:2px;margin-bottom:2px;">SeminarWeb</p>
						737-201-2059
					</cfoutput>
				</cfsavecontent>
				<cfset local.emailHTMLContent = trim(replace(replace(replace(local.emailHTMLContent,chr(9),"","ALL"),chr(13),"","ALL"),chr(10),"","ALL"))>
				<cfset local.emailsubject = "[Submitted] #local.qrySubmissionDetail.seminarName#">

				<cfset application.objEmailWrapper.sendMailESQ(
					emailfrom={ name="SeminarWeb", email="<EMAIL>" },
					emailto=[{ name=local.qrySubmissionDetail.submittedByMember, email=local.qrySubmissionDetail.submittedByEmail }],
					emailreplyto="",
					emailsubject=local.emailsubject,
					emailtitle="Program Submitted",
					emailhtmlcontent=local.emailHTMLContent,
					siteID=local.qrySubmissionDetail.participantSiteID,
					memberID=local.qrySubmissionDetail.submittedByMemberID,
					messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="SEMWEBSWODSUBM"),
					sendingSiteResourceID=local.sendingSiteInfo.siteSiteResourceID
				)>
			</cfif>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
		</cfcatch>
		</cftry>
	</cffunction>

	<cffunction name="setupProgramFeaturedImageFromSubmission" access="public" output="false" returntype="struct">
		<cfargument name="submissionID" type="numeric" required="true">
		<cfargument name="seminarID" type="numeric" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="participantOrgCode" type="string" required="true">
		<cfargument name="participantID" type="numeric" required="true">
		
		<cfset var local = structNew()>
		<cfset local.returnStruct = { "success": false, "msg":"", "featureImageID":0 }>
		<cfset local.objFeaturedImages = createObject("component","model.admin.common.modules.featuredImages.featuredImages")>
		<cfset local.orgCode = application.objSiteInfo.getSiteInfo(arguments.participantOrgCode).orgCode>
		
		<cftry>
			<cfset local.s3Directory = lcase("swodsubmissions/#application.MCEnvironment#/#arguments.participantOrgCode#/#arguments.participantID#")>
			<cfset local.prefixS = "#local.s3Directory#/#arguments.submissionID#-ftdimg">
			<cfset local.qryObjects = application.objS3.listObjects(bucket="seminarweb", prefix=local.prefixS, delimiter="", maxResults=0)>

			<cfif local.qryObjects.recordCount and application.objS3.s3FileExists(bucket='seminarweb', objectKey=local.qryObjects.objectKey, requestType='vhost')>
				<cfset local.featureImageConfigID = local.objFeaturedImages.getFeaturedImageConfigID(referenceID=arguments.siteID, referenceType="swProgram")>

				<cfif local.featureImageConfigID>
					<cfset local.s3FileURL = application.objS3.s3Url(bucket="seminarweb", objectKey=local.qryObjects.objectKey, expireInMinutes=5)>
					<cfset local.fileName = replace(local.qryObjects.objectKey,"#local.s3Directory#/","")>
					<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.participantOrgCode)>

					<cfhttp method="get" getasbinary="yes" url="#local.s3FileURL#" path="#local.strFolder.folderPath#/" file="#local.fileName#" timeout="60" throwonerror="true">

					<cfset local.addFtdImageResult = local.objFeaturedImages.addFeaturedImageFromPath(orgCode=local.orgCode,
						siteCode=arguments.participantOrgCode, sourceFileName=local.fileName, sourceFileFullPath="#local.strFolder.folderPath#/#local.fileName#",
						referenceID=arguments.seminarID, referenceType="swodProgram", featureImageConfigIDList=local.featureImageConfigID)>

					<cfset local.returnStruct["success"] = local.addFtdImageResult.success>
					<cfset local.returnStruct["featureImageID"] = local.addFtdImageResult.featureImageID>
					<cfset local.returnStruct["msg"] = local.addFtdImageResult.msg>
				<cfelse>
					<cfset local.returnStruct["msg"] = "Program Image Configuration not defined.">
				</cfif>
			</cfif>
			<cfcatch type="Any">
				<cfset local.returnStruct["success"] = false>
				<cfset local.returnStruct["msg"] = cfcatch.message>
			</cfcatch>
		</cftry>

		<cfreturn returnStruct>
	</cffunction>

	<cffunction name="getSWODSubmittedAssociations" access="public" output="false" returntype="query">
		<cfargument name="Event" type="any">

		<cfset var qryParticipants = "">

		<cfquery name="qryParticipants" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SELECT distinct p.participantID, p.orgcode, tla.description, tla.shortname
			FROM dbo.tblParticipants as p
			INNER JOIN dbo.tblSWODSubmissions as s on s.participantID = p.participantID
			INNER JOIN trialsmith.dbo.depotla as tla on tla.state = p.orgcode
			WHERE p.isActive = 1
			ORDER BY p.orgcode
		</cfquery>

		<cfreturn qryParticipants>
	</cffunction>

	<cffunction name="getSWODSubmissions" access="public" output="false" returntype="Any">
		<cfargument name="keyword" type="string" required="true">
		<cfargument name="origPublishDateFrom" type="string" required="false" default="">
		<cfargument name="origPublishDateTo" type="string" required="false" default="">
		<cfargument name="fPublisher" type="string" required="false" default="">
		<cfargument name="orderby" type="numeric" required="false" default="0">
		<cfargument name="direct" type="string" required="false" default="asc">
		<cfargument name="posstart" type="numeric" required="false" default="0">
		<cfargument name="count" type="numeric" required="false" default="50">
		
		<cfset var local = structNew()>
		
		<cfset local.arrCols = arrayNew(1)>		
		<cfset arrayAppend(local.arrCols,"s.seminarName")>
		<cfset arrayAppend(local.arrCols,"p.orgcode")>
		<cfset arrayAppend(local.arrCols,"s.dateSubmitted")>
		<cfset arrayAppend(local.arrCols,"mActive.firstname + ' ' + mActive.lastname")>
		<cfif arguments.direct eq "DES"><cfset arguments.direct = 'DESC'></cfif>
		<cfset local.orderby = local.arrcols[arguments.orderby+1]>

		<cfquery name="local.qrySubmissions" datasource="#application.dsn.tlasites_seminarweb.dsn#" result="local.qrySubmissionsResult">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				-- create temp table to store all submissions
				DECLARE @tmpTable TABLE (autoID int IDENTITY(1,1), submissionID int, seminarName varchar(250), seminarSubTitle varchar(250), participantID int,
					publisherOrgCode varchar(10), dateOrigPublished date, dateCatalogStart date, dateCatalogEnd date, submittedByMemberID int,
					submittedByMemberName varchar(500), dateSubmitted datetime, status char(1), convertedSeminarID int, row int);

				DECLARE @programName varchar(200), @maxrows int, @startRow int, @totalCount int, @posStart int, @posStartAndCount int,
					@opsd date, @oped date;
				SET @posStart = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.posStart#">;
				SET @posStartAndCount = @posStart + <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.count#">;
				SET @programName = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.keyword#">;
				<cfif len(arguments.origPublishDateFrom)>
					SET @opsd = <cfqueryparam cfsqltype="cf_sql_date" value="#arguments.origPublishDateFrom#">;
				</cfif>
				<cfif len(arguments.origPublishDateTo)>
					SET @oped = <cfqueryparam cfsqltype="cf_sql_date" value="#arguments.origPublishDateTo#">;
				</cfif>
				
				INSERT INTO @tmpTable (submissionID, seminarName, seminarSubTitle, participantID, publisherOrgCode, dateOrigPublished, dateCatalogStart, dateCatalogEnd,
					submittedByMemberID, submittedByMemberName, dateSubmitted, status, convertedSeminarID, row)
				select s.submissionID, s.seminarName, s.seminarSubTitle, p.participantID, p.orgcode as publisherOrgCode, s.dateOrigPublished,
					s.dateCatalogStart, s.dateCatalogEnd, s.submittedByMemberID, mActive.firstname + ' ' + mActive.lastname, s.dateSubmitted,
					s.status, s.convertedSeminarID, ROW_NUMBER() OVER (ORDER BY #preserveSingleQuotes(local.orderby)# #arguments.direct#) as row
				FROM dbo.tblSWODSubmissions as s
				INNER JOIN dbo.tblParticipants as p on p.participantID = s.participantID
				INNER JOIN memberCentral.dbo.ams_members as m on m.memberID = s.submittedByMemberID
				INNER JOIN memberCentral.dbo.ams_members as mActive on mActive.memberID = m.activeMemberID
				WHERE 1=1
				<cfif len(arguments.keyword)>
					and s.seminarName + ISNULL(' ' + s.seminarSubTitle,'') LIKE '%' + @programName + '%'
				</cfif>
				<cfif len(arguments.origPublishDateFrom) and len(arguments.origPublishDateTo)>
					and s.dateOrigPublished between @opsd and @oped 
				<cfelseif len(arguments.origPublishDateFrom)>
					and s.dateOrigPublished >= @opsd
				<cfelseif len(arguments.origPublishDateTo)>
					and s.dateOrigPublished <= @oped
				</cfif>
				<cfif len(arguments.fPublisher)>
					and p.participantID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.fPublisher#">
				</cfif>;

				SET @totalCount = @@ROWCOUNT;
				
				SELECT submissionID, seminarName, seminarSubTitle, participantID, publisherOrgCode, dateOrigPublished,
					submittedByMemberID, submittedByMemberName, dateSubmitted, dateCatalogStart, dateCatalogEnd,
					status, convertedSeminarID, @totalCount as totalCount
				FROM @tmpTable
				WHERE row > @posStart AND row <= @posStartAndCount
				ORDER BY row;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfreturn local.qrySubmissions>
	</cffunction>

	<cffunction name="getSWODSubmissionDetail" access="public" returntype="query" output="no">
		<cfargument name="submissionID" type="numeric" required="yes">

		<cfset var qrySubmission = "">

		<cfquery name="qrySubmission" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SELECT s.submissionID, s.participantID, s.status, s.seminarName, s.seminarSubTitle, s.seminarDesc, s.dateOrigPublished, s.seminarLength, s.offerQA,
				s.blankOnInactivity, s.offerCertificate, s.offerEval, s.offerExam, s.introMessageText, s.endOfSeminarText,
				s.isPriceBasedOnActual, s.dateCatalogStart, s.dateCatalogEnd, s.allowSyndication, s.createOwnPricingToOptIns, s.priceSyndication, s.isFeatured,
				s.additionalInfo, p.orgCode AS participantOrgCode, mcs.orgID AS participantOrgID, mcs.siteID AS participantSiteID,
				mActive.memberID as submittedByMemberID, mActive.firstname AS submitterFirstName, mActive.firstname + ' ' + mActive.lastname AS submittedByMember, me.email as submittedByEmail,
				STUFF((SELECT ',' + CAST(tmp.categoryID AS VARCHAR(10)) FROM dbo.tblSWODSubmissionsAndCategories tmp WHERE tmp.submissionID = s.submissionID FOR XML PATH ('')),1,1,'') AS categoryIDs
			FROM dbo.tblSWODSubmissions AS s
			INNER JOIN dbo.tblParticipants AS p ON p.participantID = s.participantID
			INNER JOIN membercentral.dbo.sites AS mcs ON mcs.siteCode = p.orgCode
			INNER JOIN memberCentral.dbo.ams_members as m on m.memberID = s.submittedByMemberID
			INNER JOIN memberCentral.dbo.ams_members as mActive on mActive.memberID = m.activeMemberID
			INNER JOIN memberCentral.dbo.ams_memberEmails as me on me.memberID = mActive.memberID AND me.orgID in (mcs.orgID,1)
			INNER JOIN memberCentral.dbo.ams_memberEmailTags AS metag ON metag.orgID = m.orgID AND metag.memberID = me.memberID AND metag.emailTypeID = me.emailTypeID
			INNER JOIN memberCentral.dbo.ams_memberEmailTagTypes AS metagt ON metagt.orgID = m.orgID AND metagt.emailTagTypeID = metag.emailTagTypeID AND metagt.emailTagType = 'Primary'
			WHERE s.submissionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.submissionID#">
		</cfquery>
		
		<cfreturn qrySubmission>
	</cffunction>

	<cffunction name="getSubmissionObjectives" access="public" output="false" returntype="struct">
		<cfargument name="submissionID" type="numeric" required="yes">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { 'success': true, 'arrobjectives':arrayNew(1) }>

		<cfquery name="local.qryObjectives" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SELECT autoID, objective
			FROM dbo.tblSWODSubmissionsAndObjectives
			WHERE submissionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.submissionID#">
		</cfquery>

		<cfloop query="local.qryObjectives">
			<cfset local.tmp = structNew()>
			<cfset local.tmp['autoid'] = local.qryObjectives.autoID>
			<cfset local.tmp['objective'] = local.qryObjectives.objective>
			<cfset arrayAppend(local.returnStruct['arrobjectives'],local.tmp)>
		</cfloop>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getSubmissionAuthors" access="public" output="false" returntype="struct">
		<cfargument name="submissionID" type="numeric" required="yes">
		<cfargument name="authorType" type="string" required="yes">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { 'success': true, 'arrauthors':arrayNew(1) }>

		<cfquery name="local.qrySpeakers" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			DECLARE @authorTypeID int;

			SELECT @authorTypeID = authorTypeID
			FROM dbo.tblAuthorTypes
			WHERE authorType = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.authorType#">;

			SELECT sa.authorID, a.lastName + ', ' + a.firstName AS authorName
			FROM dbo.tblSWODSubmissionsAndAuthors AS sa
			INNER JOIN dbo.tblSWODSubmissions AS s ON s.submissionID = sa.submissionID
			INNER JOIN dbo.tblAuthors AS a ON a.authorID = sa.authorID
				AND a.participantID = s.participantID
			WHERE sa.submissionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.submissionID#">
			AND sa.authorTypeID = @authorTypeID;
		</cfquery>

		<cfloop query="local.qrySpeakers">
			<cfset local.tmp = structNew()>
			<cfset local.tmp['authorid'] = local.qrySpeakers.authorID>
			<cfset local.tmp['authorname'] = local.qrySpeakers.authorName>
			<cfset arrayAppend(local.returnStruct['arrauthors'],local.tmp)>
		</cfloop>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getSubmissionLinks" access="public" output="false" returntype="struct">
		<cfargument name="submissionID" type="numeric" required="yes">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { 'success': true, 'arrlinks':arrayNew(1) }>

		<cfquery name="local.qryLinks" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SELECT autoID, linkURL, linkName
			FROM dbo.tblSWODSubmissionsAndLinks
			WHERE submissionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.submissionID#">
		</cfquery>

		<cfloop query="local.qryLinks">
			<cfset local.tmp = structNew()>
			<cfset local.tmp['autoid'] = local.qryLinks.autoID>
			<cfset local.tmp['linkurl'] = local.qryLinks.linkURL>
			<cfset local.tmp['linkname'] = local.qryLinks.linkName>
			<cfset arrayAppend(local.returnStruct['arrlinks'],local.tmp)>
		</cfloop>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getSubmissionForms" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="submissionID" type="numeric" required="yes">
		<cfargument name="format" type="string" required="yes">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { 'success': true, 'arrorgforms':arrayNew(1) }>

		<cfif arguments.submissionID>
			<cfquery name="local.qrySubmission" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				SELECT s.submissionID, s.participantID, mcs.orgID AS participantOrgID, mcs.siteID AS participantSiteID
				FROM dbo.tblSWODSubmissions AS s
				INNER JOIN dbo.tblParticipants AS p ON p.participantID = s.participantID
				INNER JOIN membercentral.dbo.sites AS mcs ON mcs.siteCode = p.orgCode
				WHERE s.submissionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.submissionID#">;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
			
			<cfset local.siteID = local.qrySubmission.participantSiteID>
		<cfelse>
			<cfset local.siteID = arguments.mcproxy_siteID>
		</cfif>

		<cfset local.qryOrgFormsAll = createObject("component","seminarWebForm").getFormsByType(siteID=local.siteID, format=arguments.format)>

		<cfquery name="local.qryOrgForms" dbtype="query">
			SELECT formID, formTitle
			FROM [local].qryOrgFormsAll
			WHERE isPublished = 1
		</cfquery>

		<cfquery name="local.qrySubmissionForms" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SELECT formID
			FROM dbo.tblSWODSubmissionsAndForms
			WHERE submissionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.submissionID#">
		</cfquery>

		<cfset local.selectedFormIDList = ValueList(local.qrySubmissionForms.formID)>

		<cfloop query="local.qryOrgForms">
			<cfset local.tmp = structNew()>
			<cfset local.tmp['formid'] = local.qryOrgForms.formID>
			<cfset local.tmp['formtitle'] = left(local.qryOrgForms.formTitle,100) & (len(local.qryOrgForms.formTitle) gt 100 ? '...' : '')>
			<cfset local.tmp['selected'] = listFindNoCase(local.selectedFormIDList, local.tmp['formid'])>
			<cfset arrayAppend(local.returnStruct['arrorgforms'],local.tmp)>
		</cfloop>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getSubmissionRates" access="public" output="false" returntype="struct">
		<cfargument name="submissionID" type="numeric" required="yes">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { 'success': true, 'arrrates':arrayNew(1) }>

		<cfquery name="local.qryRates" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SELECT r.autoID, r.rateName, r.rate
				,STRING_AGG(case when rg.include=1 then g.groupID end,'|') AS groupIDs
				,STRING_AGG(case when rg.include=0 then g.groupID end,'|') as deniedGroupIDs
				,replace(STRING_AGG(replace(case when rg.include=0 then 'Denied: '+g.groupPathExpanded else g.groupPathExpanded end,'|',char(7)),'^--^'),char(7),'|') as groupPathsList
			FROM dbo.tblSWODSubmissionsAndRates r
			LEFT JOIN dbo.tblSWODSubmissionsAndRateGroups AS rg
				INNER JOIN memberCentral.dbo.ams_groups AS g ON g.groupID = rg.groupID
				ON rg.rateID = r.autoID
			WHERE r.submissionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.submissionID#">
			GROUP BY r.autoID, r.rateName, r.rate
			ORDER BY r.autoID;
		</cfquery>

		<cfloop query="local.qryRates">
			<cfset local.tmp = structNew()>
			<cfset local.tmp['autoid'] = local.qryRates.autoID>
			<cfset local.tmp['ratename'] = local.qryRates.rateName>
			<cfset local.tmp['rate'] = local.qryRates.rate>
			<cfset local.tmp['groupids'] = local.qryRates.groupIDs>
			<cfset local.tmp['deniedgroupids'] = local.qryRates.deniedGroupIDs>
			<cfset local.tmp['arrgroupnames'] = listToArray(replaceNoCase(local.qryRates.groupPathsList, "&amp;", "&", "all"),"^--^",false,true)>
			<cfset arrayAppend(local.returnStruct['arrrates'],local.tmp)>
		</cfloop>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getSubmissionCredits" access="public" output="false" returntype="struct">
		<cfargument name="submissionID" type="numeric" required="yes">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { 'success': true, 'arrcecredits':arrayNew(1) }>

		<cfquery name="local.qryCredits" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SELECT c.autoID, c.CSALinkID, c.statusID, s.status AS statusName, c.courseApproval, c.wddxCreditsAvailable, c.creditOfferedStartDate,
				c.creditOfferedEndDate, c.creditCompleteByDate, c.isCreditRequired, ca.code AS authorityCode, ca.authorityName, cs.sponsorName,
				ca.wddxCreditTypes
			FROM dbo.tblSWODSubmissionsAndCredits AS c
			INNER JOIN dbo.tblCreditSponsorsAndAuthorities AS csa ON csa.CSALinkID = c.CSALinkID
			INNER JOIN dbo.tblCreditAuthorities AS ca ON csa.authorityID = ca.authorityID 
			INNER JOIN dbo.tblCreditSponsors AS cs ON csa.sponsorID = cs.sponsorID
			INNER JOIN dbo.tblCreditStatuses AS s ON s.statusID = c.statusID
			WHERE c.submissionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.submissionID#">
			ORDER BY c.autoID
		</cfquery>

		<cfset local.objSWCredit = CreateObject("model.seminarWeb.SWCredits")>
		<cfloop query="local.qryCredits">
			<cfset local.tmp = structNew()>
			<cfset local.tmp['autoid'] = local.qryCredits.autoID>
			<cfset local.tmp['csalinkid'] = local.qryCredits.CSALinkID>
			<cfset local.tmp['statusid'] = local.qryCredits.statusID>
			<cfset local.tmp['statusname'] = local.qryCredits.statusName>
			<cfset local.tmp['offeredstartdate'] = DateFormat(local.qryCredits.creditOfferedStartDate,'m/d/yyyy')>
			<cfset local.tmp['offeredenddate'] = DateFormat(local.qryCredits.creditOfferedEndDate,'m/d/yyyy')>
			<cfset local.tmp['completebydate'] = DateFormat(local.qryCredits.creditCompleteByDate,'m/d/yyyy')>
			<cfset local.tmp['iscreditrequired'] = local.qryCredits.isCreditRequired>
			<cfset local.tmp['creditapprovalnum'] = local.qryCredits.courseApproval>
			<cfset local.tmp['authoritycode'] = local.qryCredits.authorityCode>
			<cfset local.tmp['authorityname'] = local.qryCredits.authorityName>
			<cfset local.tmp['sponsorname'] = local.qryCredits.sponsorName>
			<cfset local.qryCreditValues = local.objSWCredit.getCreditsFromWDDX(local.qryCredits.wddxCreditTypes,local.qryCredits.wddxcreditsAvailable,true)>
			<cfset local.tmp['arrcreditvalues'] = arrayNew(1)>
			<cfloop query="local.qryCreditValues">
				<cfset arrayAppend(local.tmp['arrcreditvalues'],{
					"fieldname":local.qryCreditValues.fieldname,
					"displayname": local.qryCreditValues.displayname,
					"value":local.qryCreditValues.numcredits
				})>
			</cfloop>
			<cfset arrayAppend(local.returnStruct['arrcecredits'],local.tmp)>
		</cfloop>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getSubmissionFileUploads" access="public" output="false" returntype="struct">
		<cfargument name="submissionID" type="numeric" required="yes">
		<cfargument name="participantID" type="numeric" required="yes">
		<cfargument name="participantOrgCode" type="string" required="yes">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { 'success': true, 'arruploads':arrayNew(1) }>

		<cfset local.returnStruct["arruploads"] = getS3UploadedSubmissionFilesArray(submissionID=arguments.submissionID, participantID=arguments.participantID,
				participantOrgCode=arguments.participantOrgCode, includeS3Url=1)>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="removeProgramSubmission" access="public" returntype="struct" output="no">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="submissionID" type="numeric" required="yes">
		<cfargument name="participantID" type="numeric" required="yes">
		<cfargument name="participantOrgCode" type="string" required="yes">

		<cfset var local = structnew()>
		<cfset local.data.success = false>

		<cfset local.arrS3ObjectKeys = getS3UploadedSubmissionFilesArray(submissionID=arguments.submissionID, participantID=arguments.participantID, participantOrgCode=arguments.participantOrgCode)>

		<cfquery name="local.qryRemoveSubmission" datasource="#application.dsn.platformQueue.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				DECLARE @siteID int, @submissionID int, @s3DeleteReadyStatusID int, @nowdate datetime = getdate(), @recordedByMemberID int;

				SET @submissionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.submissionID#">;
				SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">;
				SET @recordedByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">;

				SELECT @s3DeleteReadyStatusID = qs.queueStatusID
				FROM dbo.tblQueueTypes AS qt
				INNER JOIN dbo.tblQueueStatuses AS qs ON qs.queueTypeID = qt.queueTypeID
				WHERE qt.queueType = 's3Delete'
				AND qs.queueStatus = 'readyToProcess';

				BEGIN TRAN;

					<cfloop array="#local.arrS3ObjectKeys#" item="local.thisObjectKey">
						INSERT INTO dbo.queue_S3Delete (statusID, s3bucketName, objectKey, dateAdded, dateUpdated)
						VALUES (@s3DeleteReadyStatusID, 'seminarweb', <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.thisObjectKey.objectkey#">, @nowdate, @nowdate);
					</cfloop>

					EXEC seminarWeb.dbo.swod_removeProgramSubmission @submissionID=@submissionID, @siteID=@siteID, @recordedByMemberID=@recordedByMemberID;

				COMMIT TRAN;
			END TRY
			BEGIN CATCH
				IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
				EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfset local.data.success = true>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getS3UploadedSubmissionFilesArray" access="public" returntype="array" output="no">
		<cfargument name="submissionID" type="numeric" required="yes">
		<cfargument name="participantID" type="numeric" required="yes">
		<cfargument name="participantOrgCode" type="string" required="yes">
		<cfargument name="includeS3Url" type="boolean" required="no" default="0">

		<cfset var local = structnew()>
		<cfset local.data.success = false>
		<cfset local.directory = lcase("swodsubmissions/#application.MCEnvironment#/#arguments.participantOrgCode#/#arguments.participantID#")>

		<cfset local.arrS3Prefixes = [
			{"type":"customeval", "prefix":"#local.directory#/#arguments.submissionID#-customeval."},
			{"type":"customexam", "prefix":"#local.directory#/#arguments.submissionID#-customexam."},
			{"type":"materials", "prefix":"#local.directory#/#arguments.submissionID#-materials/"},
			{"type":"ftdimage", "prefix":"#local.directory#/#arguments.submissionID#-ftdimg."}
		]>

		<cfset local.qryObjectsAll = application.objS3.listObjects(bucket="seminarweb", prefix="#local.directory#/#arguments.submissionID#-", delimiter="", maxResults=0)>

		<cfset local.arrS3Uploads = []>
		<cfif local.qryObjectsAll.recordCount>
			<cfloop array="#local.arrS3Prefixes#" item="local.thisPrefix">
				<cfquery dbtype="query" name="local.qryObjects">
					select objectKey
					from [local].qryObjectsAll
					where objectKey like '%#local.thisPrefix.prefix#%'
				</cfquery>

				<cfloop query="local.qryObjects">
					<cfset local.objTemp = {
						"type":local.thisPrefix.type,
						"objectkey":local.qryObjects.objectKey,
						"filename": (local.thisPrefix.type == "materials" ? replace(local.qryObjects.objectKey,local.thisPrefix.prefix,"") : replace(local.qryObjects.objectKey,"#local.directory#/",""))
					}>
					<cfif arguments.includeS3Url>
						<cfset local.objTemp["s3url"] = application.objS3.s3Url(bucket="seminarweb", objectKey=local.qryObjects.objectKey, expireInMinutes=15)>
					</cfif>
					<cfset arrayAppend(local.arrS3Uploads, local.objTemp)>
				</cfloop>
			</cfloop>
		</cfif>

		<cfreturn local.arrS3Uploads>
	</cffunction>

	<cffunction name="copySWODProgram" access="public" output="false" returntype="numeric">
		<cfargument name="programID" type="numeric" required="true">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="incBasics" type="boolean" required="true">
		<cfargument name="incSpeakers" type="boolean" required="true">
		<cfargument name="incSettings" type="boolean" required="true">
		<cfargument name="incTitles" type="boolean" required="true">
		<cfargument name="incCatalog" type="boolean" required="true">
		<cfargument name="incSyndication" type="boolean" required="true">
		<cfargument name="incCredit" type="boolean" required="true">

		<cfset var newSeminarID = 0>
		<cfset local.objSponsor = createObject("component","model.admin.common.modules.sponsors.sponsors")>
		<cfset local.featuredImagesObj = createObject("component","model.admin.common.modules.featuredImages.featuredImages")>
		<cfset local.objSWCommon = CreateObject("component","model.admin.seminarweb.seminarWebSWCommon")>

		<cfstoredproc procedure="swod_copySeminar" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.incBasics#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.incSpeakers#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.incSettings#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.incTitles#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.incCatalog#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.incSyndication#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.incCredit#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
			<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="newSeminarID">
		</cfstoredproc>

		<cfset local.includedSponsor = local.objSponsor.getSponsorsByReferenceID(siteID=arguments.siteID, referenceType="swodProgram", referenceID=arguments.programID)>
		<cfif arguments.incCatalog AND local.includedSponsor.recordCount>
			<cfloop query="#local.includedSponsor#">
				<cfset local.objSponsor.associateSponsor(mcproxy_siteID=arguments.siteID,sponsorID=local.includedSponsor.sponsorID,referenceType="swodProgram",referenceID=newSeminarID)>
			</cfloop>
		</cfif>
		<cfset local.qryFeaturedImageDetails = local.featuredImagesObj.getFeaturedImageDetails(referenceID=arguments.programID, referenceType='swodProgram')>
		<cfif local.qryFeaturedImageDetails.recordCount>
			<cfset local.orgCode = local.qryFeaturedImageDetails.orgCode>
			<cfset local.siteCode = local.qryFeaturedImageDetails.siteCode>
			<cfset local.orgSiteFolder = LCASE("#local.orgCode#/#local.siteCode#")>
			<cfset local.featuredImageFullRootPath='#application.paths.RAIDUserAssetRoot.path##local.orgSiteFolder#/featuredimages/originals/'>
			<cfset local.featureImageConfigID = local.featuredImagesObj.getFeaturedImageConfigID(referenceID=arguments.siteID, referenceType='swProgram')>
			
			<cfif arguments.incCatalog AND val(local.qryFeaturedImageDetails.featureImageID) gt 0 AND fileExists("#local.featuredImageFullRootPath##local.qryFeaturedImageDetails.featureImageID#.#local.qryFeaturedImageDetails.fileExtension#")>
				<cfset local.featuredImagesObj.doCopyFeaturedImage(sourceOrgCode=local.orgCode, sourceSiteCode=local.siteCode,
					destinationOrgCode=local.orgCode, destinationSiteCode=local.siteCode, copyFromFtdImgID=local.qryFeaturedImageDetails.featureImageID,
					featureImageConfigID=local.featureImageConfigID, referenceID=newSeminarID,
					referenceType='swodProgram', generateThumbnailsByQueue=0)>
			</cfif>
		</cfif>
		
		<cfset local.qryProgramVideoPreviews = local.objSWCommon.getProgramVideoPreviews(programType="SWOD", programID=arguments.programID)>
		<cfif arguments.incCatalog AND arguments.incTitles AND ArrayLen(local.qryProgramVideoPreviews.arrprogramvideopreviews)>
			<cfloop array="#local.qryProgramVideoPreviews.arrprogramvideopreviews#" index="local.preview">
				<cfquery datasource="#application.dsn.tlasites_seminarweb.dsn#" name="local.qryGetFileID">
					SELECT f.fileID 
					FROM tblFiles as f
					INNER JOIN dbo.tblFilesTypes as ft on ft.fileTypeID = f.fileTypeID  
					INNER JOIN dbo.tblTitlesAndFiles as taf on taf.fileID = f.fileID  
					INNER JOIN dbo.tblTitles as t on t.titleid = taf.titleID  
					INNER JOIN dbo.tblSeminarsAndTitles as sat on sat.titleID = taf.titleID 
					WHERE sat.seminarID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#newSeminarID#"> 
					AND f.fileTitle = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.preview.fileTitle#">
				</cfquery>
				<cfstoredproc procedure="sw_createVideoPreview" datasource="#application.dsn.tlasites_seminarweb.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#newSeminarID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryGetFileID.fileid#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.preview.timecodestart#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.preview.timecodeend#">
					<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="0">
					<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.data.previewID">
				</cfstoredproc>
			</cfloop>
		</cfif>
		
		<cfreturn newSeminarID>
	</cffunction>

	<cffunction name="bulkDeactivatePrograms" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">
		<cfargument name="seminarIDList" type="string" required="true">

		<cfset var local = structnew()>
		<cfset local.data = { "success":false, "deactivateProgramCount":0 }>
		
		<cfif len(arguments.seminarIDList)>
			<cfset local.objSWODSeminar = createObject("component","model.admin.seminarweb.seminarwebSWOD")>
			
			<cfquery datasource="#application.dsn.tlasites_seminarweb.dsn#" name="local.qryBulkDeactivatePrograms">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					IF OBJECT_ID('tempdb..##tmpLogMessages') IS NOT NULL
						DROP TABLE ##tmpLogMessages;
					CREATE TABLE ##tmpLogMessages (rowID INT IDENTITY(1,1), msg VARCHAR(MAX));

					DECLARE @participantID int, @msgjson VARCHAR(MAX), @crlf VARCHAR(10);
					SELECT @participantID = dbo.fn_getParticipantIDFromOrgcode(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.mcproxy_sitecode#">);
					SET @crlf = CHAR(13) + CHAR(10);

					INSERT INTO ##tmpLogMessages (msg)
					SELECT seminarName + '[SWOD-' + CAST(seminarID AS varchar(10)) + '] deactivated.'
					FROM dbo.tblSeminars
					WHERE seminarID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="yes" value="#arguments.seminarIDList#">)
					AND participantID = @participantID
					AND isPublished = 1;

					UPDATE dbo.tblSeminars
					SET isPublished = 0
					WHERE seminarID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="yes" value="#arguments.seminarIDList#">)
					AND participantID = @participantID;

					IF EXISTS(SELECT 1 FROM ##tmpLogMessages) BEGIN
						SET @msgjson = @msgjson + @crlf + 'The following changes have been made:';

						SELECT @msgjson = COALESCE(@msgjson + @crlf, '') + memberCentral.dbo.fn_cleanInvalidXMLChars(msg)
						FROM ##tmpLogMessages
						WHERE msg IS NOT NULL;
					
						INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
						VALUES ('{ "c":"auditLog", "d": {
							"AUDITCODE":"SW",
							"ORGID":' + CAST(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#"> AS VARCHAR(10)) + ',
							"SITEID":' + CAST(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#"> AS VARCHAR(10)) + ',
							"ACTORMEMBERID":' + CAST(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberData.memberID#"> AS VARCHAR(20)) + ',
							"ACTIONDATE":"' + CONVERT(VARCHAR(20),GETDATE(),120) + '",
							"MESSAGE":"' + REPLACE(@msgjson,'"','\"') + '" } }');
					END

					IF OBJECT_ID('tempdb..##tmpLogMessages') IS NOT NULL
						DROP TABLE ##tmpLogMessages;
					
					SELECT @participantID as participantID;
					
				END TRY
				BEGIN CATCH
					IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
					EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
			
			<cfset local.data.deactivateProgramCount = local.objSWODSeminar.getCountOfDefaultProgramsToDeactivate(participantID=local.qryBulkDeactivatePrograms.participantID)>
			
			<cfset local.data.success = true>
		</cfif>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getEnrollments" access="public" output="false" returntype="query">
		<cfargument name="seminarID" type="numeric" required="yes">
		<cfargument name="orgcode" type="string" required="yes">
		
		<cfset var qryEnrollments = "">
		
		<cfquery name ="qryEnrollments" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			select * from seminarweb.dbo.fn_swod_getEnrollments(<cfqueryparam value="#arguments.seminarID#" cfsqltype="CF_SQL_INTEGER">,<cfqueryparam value="#arguments.orgcode#" cfsqltype="CF_SQL_VARCHAR">)
		</cfquery>
		
		<cfreturn qryEnrollments>
	</cffunction>

	<cffunction name="hasUpdateSeminarRights" access="public" returntype="boolean" output="no">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="action" type="string" required="true">
		<cfargument name="seminarID" type="numeric" required="false" default="0">
		<cfargument name="enrollmentID" type="numeric" required="false" default="0">
		<cfargument name="checkLockSettings" type="boolean" required="false" default="0">

		<cfset var local = structNew()>
		<cfset local.hasRights = false>

		<cfif arguments.seminarID eq 0 and arguments.enrollmentID eq 0>
			<cfreturn false>
		</cfif>

		<cfquery datasource="#application.dsn.tlasites_seminarweb.dsn#" name="local.qrySWInfo">
			SET NOCOUNT ON;

			DECLARE @siteID int, @siteResourceID int, @siteCode varchar(10);

			SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
			SELECT @siteResourceID = memberCentral.dbo.fn_getSiteResourceIDForResourceType('SeminarWebAdmin', @siteID);
			SELECT @siteCode = memberCentral.dbo.fn_getSiteCodeFromSiteID(@siteID);

			<cfif arguments.seminarID gt 0>
				SELECT @siteResourceID as semWebAdminSRID, isPublisher = CASE WHEN p.orgcode = @siteCode THEN 1 ELSE 0 END,
					s.lockSettings, swod.allowSyndication, p.handlesOwnPayment
				FROM dbo.tblSeminars AS s
				INNER JOIN dbo.tblParticipants AS p ON p.participantID = s.participantID
				INNER JOIN dbo.tblSeminarsSWOD AS swod ON swod.seminarID = s.seminarID
				WHERE s.seminarID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">
				AND s.isDeleted = 0;
			<cfelseif arguments.enrollmentID gt 0>
				SELECT @siteResourceID as semWebAdminSRID, isPublisher = CASE WHEN p.orgcode = @siteCode THEN 1 ELSE 0 END,
					s.lockSettings, swod.allowSyndication, p.handlesOwnPayment
				FROM dbo.tblEnrollments AS e
				INNER JOIN dbo.tblParticipants AS p ON p.participantID = e.participantID
				INNER JOIN dbo.tblSeminars AS s ON s.seminarID = e.seminarID
					AND s.isDeleted = 0
				INNER JOIN dbo.tblSeminarsSWOD AS swod ON swod.seminarID = s.seminarID
				WHERE e.enrollmentID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.enrollmentID#">;
			</cfif>
		</cfquery>

		<cfset local.tmpRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.qrySWInfo.semWebAdminSRID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.siteID)>

		<cfswitch expression="#arguments.action#">
			<cfcase value="Edit">
				<cfset local.hasRights = (local.tmpRights['editSWODProgramAll'] is 1 OR local.tmpRights['editSWODProgramPublish'] is 1) AND local.qrySWInfo.isPublisher EQ 1>
			</cfcase>
			<cfcase value="manageSWOptIns">
				<cfset local.hasRights = local.tmpRights['manageSWOptIns'] is 1 AND local.qrySWInfo.isPublisher EQ 1 AND val(local.qrySWInfo.handlesOwnPayment) EQ 0 AND val(local.qrySWInfo.allowSyndication) EQ 1>
			</cfcase>
			<cfcase value="removeEnrollment">
				<cfset local.hasRights = (local.tmpRights['deleteSWODRegistrantSignUp'] is 1 AND local.qrySWInfo.isPublisher EQ 1) OR local.tmpRights['deleteSWODRegistrantAll'] is 1 >
			</cfcase>
			<cfcase value="manageRegProgress">
				<cfset local.hasRights = (local.tmpRights['manageSWODRegProgressSignUp'] is 1 AND local.qrySWInfo.isPublisher EQ 1) OR local.tmpRights['manageSWODRegProgressAll'] is 1 >
			</cfcase>
			<cfcase value="billing">
				<cfset local.hasRights = (local.tmpRights['editSWODProgramAll'] is 1 OR local.tmpRights['editSWODProgramPublish'] is 1)>
			</cfcase>
			<cfcase value="catalog">
				<cfset local.hasRights = (local.tmpRights['editSWODProgramAll'] is 1 OR local.tmpRights['editSWODProgramPublish'] is 1)>
			</cfcase>
		</cfswitch>

		<cfreturn local.hasRights AND (arguments.checkLockSettings ? NOT local.qrySWInfo.lockSettings : true)>
	</cffunction>

	<cffunction name="getScheduledTaskDetails" access="public" output="false" returntype="query">
		<cfargument name="participantID" type="numeric" required="yes">

		<cfset var getScheduledTask = "">

		<cfquery name="getScheduledTask" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SELECT isCompletionReminderEnabled, emailOption, selectedTimeframes, emailSubject
			FROM dbo.tblSeminarsSWODTasks
			WHERE participantID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.participantID#">
		</cfquery>
		
		<cfreturn getScheduledTask>
	</cffunction>

	<cffunction name="updateScheduledTaskDetails" access="public" output="false" returntype="struct">
		<cfargument name="participantID" type="numeric" required="true">
		<cfargument name="isCompletionReminderEnabled" type="boolean" required="true">
		<cfargument name="emailOption" type="numeric" required="false" default="2">
		<cfargument name="selectedTimeframes" type="string" required="false" default="">
		<cfargument name="emailSubject" type="string" required="false" default="">
	
		<cfset var local = structNew()>
		<cfset local.returnStruct = { success=false }>

		<cftry>
			<cfquery name="local.qryUpdate" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				UPDATE dbo.tblSeminarsSWODTasks
				SET isCompletionReminderEnabled = <cfqueryparam value="#arguments.isCompletionReminderEnabled#" cfsqltype="cf_sql_bit">,
					emailOption = <cfqueryparam value="#arguments.emailOption#" cfsqltype="cf_sql_integer">,
					selectedTimeframes = <cfqueryparam value="#arguments.selectedTimeframes#" cfsqltype="cf_sql_varchar">,
					emailSubject = <cfqueryparam value="#arguments.emailSubject#" cfsqltype="cf_sql_varchar">
				WHERE participantID = <cfqueryparam value="#arguments.participantID#" cfsqltype="cf_sql_integer">
			</cfquery>

			<cfset local.returnStruct.success = true>
		
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>
	
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getCountOfDefaultProgramsToDeactivate" access="public" output="false" returntype="numeric">
		<cfargument name="participantID" type="numeric" required="yes">

		<cfset var local = structNew()>

		<cfstoredproc procedure="swod_getProgramsForDeactivation" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.participantID#">
			<cfprocresult name="local.qryActiveSWODPrograms" resultset="1">
			<cfprocresult name="local.qryExpiredSWODPrograms" resultset="2">
		</cfstoredproc>

		<cfreturn local.qryExpiredSWODPrograms.recordcount>
	</cffunction>
</cfcomponent>