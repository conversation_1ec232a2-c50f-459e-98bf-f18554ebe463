ALTER PROC dbo.pg_deletePhoto
@albumID int,
@photoID int,
@contributorMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @siteID int, @siteCode varchar(10), @orgCode varchar(10), @applicationTypeID int, 
		@siteResourceID int, @applicationInstanceID int, @userAssetsPath varchar(60), @photoExt varchar(10),
		@thumbFilePath varchar(200), @photoFilePath varchar(200), @queueTypeID int, @queueStatusID int;
	select @applicationTypeID = dbo.fn_getApplicationTypeIDFromName('PhotoGallery');

	select @siteID = ai.siteID, @siteCode = s.siteCode, @orgCode = o.orgCode, @photoExt = p.photoExt,
		@applicationInstanceID = g.applicationInstanceID, @siteResourceID = p.siteResourceID
	from dbo.pg_photos as p
	inner join dbo.pg_albums as a on a.albumID = p.albumID
	inner join dbo.pg_gallery as g on g.galleryID = a.galleryID
	inner join dbo.cms_applicationInstances as ai on ai.applicationInstanceID = g.applicationInstanceID
	inner join dbo.sites as s on s.siteID = ai.siteID
	inner join dbo.organizations as o on o.orgID = s.orgID
	where p.photoID = @photoID
	and p.albumID = @albumID;

	select @userAssetsPath = userAssetsPath
	from dbo.fn_getServerSettings();

	set @thumbFilePath = @userAssetsPath + lower(@orgCode + '\' + @siteCode + '\galleries\thumbs\' + cast(@photoID as varchar(10)) + '.' + @photoExt);
	set @photoFilePath = @userAssetsPath + lower(@orgCode + '\' + @siteCode + '\galleries\photos\' + cast(@photoID as varchar(10)) + '.' + @photoExt);

	select @queueTypeID = queueTypeID from platformQueue.dbo.tblQueueTypes where queueType = 'MCFileDelete';
	select @queueStatusID = queueStatusID from platformQueue.dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'readyToProcess';

	BEGIN TRAN;
		-- Update the status 
		UPDATE dbo.cms_siteResources
		SET siteResourceStatusID = 3
		WHERE siteResourceID = @siteResourceID;

		UPDATE dbo.pg_albums
		SET featuredPhotoID = NULL
		WHERE featuredPhotoID = @photoID
		AND albumID = @albumID;

		UPDATE dbo.pg_photos
		SET dateModified = GETDATE()
		WHERE photoID = @photoID;

		EXEC dbo.pg_reorderPhotos @albumID=@albumID, @siteID=@siteID;

		-- create activity log entry
		EXEC platformstatsMC.dbo.act_recordLog @memberID=@contributorMemberID, @activityType='remove', 
			@applicationTypeID=@applicationTypeID, @applicationInstanceID=@applicationInstanceID,
			@supportSiteResourceID=@siteResourceID, @supportMemberID=NULL, @supportMessage=NULL;
	COMMIT TRAN;

	IF dbo.fn_fileExists(@thumbFilePath) = 1
		INSERT INTO platformQueue.dbo.queue_MCFileDelete (filePath, dateAdded, dateUpdated, statusID)
		VALUES (@thumbFilePath, GETDATE(), GETDATE(), @queueStatusID);

	IF dbo.fn_fileExists(@photoFilePath) = 1
		INSERT INTO platformQueue.dbo.queue_MCFileDelete (filePath, dateAdded, dateUpdated, statusID)
		VALUES (@photoFilePath, GETDATE(), GETDATE(), @queueStatusID);


	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
