<cfset local.assetCachingKey = application.objCMS.getPlatformCacheBusterKey()>
<cfsavecontent variable="local.calendarFormJS">
	<cfoutput>
	<script type="text/javascript" src="/assets/admin/javascript/featuredImages.js#local.assetCachingKey#"></script>
	<script type="text/javascript">
		var #ToScript(local.permsGotoLink,"mca_perms_link")#

		function saveCalendarSettings(fext) {
			var thisForm = $('##frmCalendar');
			mca_hideAlert('err_calendar');
			var arrReq = new Array();

			if($('##newapplicationInstanceName').val() == '') arrReq[arrReq.length] = "Enter the calendar name.";
			if ($('##defaultGLAccountID').val() == 0 || $('##defaultGLAccountID').val().length == 0) arrReq[arrReq.length] = 'Choose the GL Account for this event.';
			if ($("##isSWL").attr('checked') && $('##categoryID').val().length == 0) arrReq[arrReq.length] = 'Choose a Calendar Category.';
			if($('##remarketingText').val() == '') arrReq[arrReq.length] = 'Enter the Past Event Re-marketing Text.';
			if($('##remarketingBtnText').val() == '') arrReq[arrReq.length] = 'Enter the Past Event Re-marketing Button Text.';
			if (arrReq.length > 0) {
				mca_showAlert('err_calendar', arrReq.join('<br/>'), true);
				return false;
			}
			
			var arrFrmData = thisForm.serializeArray();
			var fd = new Object();

			var arrCheckboxFields = $.unique(thisForm.find('input[type="checkbox"]').map( function() { return $(this).attr('name'); }).get());

			$.each(arrFrmData, function() {
				if (fd[this.name] !== undefined && typeof this.value !== undefined) {
					fd[this.name] = fd[this.name] + ',' + this.value || '';
				} else {
					fd[this.name] = this.value || '';
				}
			});

			/*empty checkbox handling*/
			$.each(arrCheckboxFields, function() {
				if (fd[this] === undefined) fd[this] = '';
			});

			thisForm.hide();
			var loadingHTML = $('##divCalSettingsSaveLoading').html();
			
			$("##divCalSettingsFormSubmitArea").html(loadingHTML).show().load('#this.link.saveCalendar#', fd, function() { onCalSettingsSaveComplete(fext); });
			return false;
		}
		function onCalSettingsSaveComplete(fext) {
			$("##divCalSettingsFormSubmitArea").html('').hide();

			if (fext && $("##oldFeaturedImageConfigID").val() != $("##featureImageConfigID").val()) {
				$('div[data-ftdExt="'+fext+'"]').find('.mcftd_div_previewThumbnails').html('<div class="alert alert-warning">This image is currently in the queue for generating thumbnails and will be processed soon.</div>');
			}
			
			$("##oldapplicationInstanceName").val($("##newapplicationInstanceName").val());
			$("##oldFeaturedImageConfigID").val($("##featureImageConfigID").val());

			$('##frmCalendar').show();
			$('##saveResponse').html('Details saved').show().fadeOut(5000);
			
			if (fext) {
				if (window['mcftd_editClick'+fext] && window['mcftd_editClick'+fext] == true) mcftd_doEditFeaturedImage(fext);
				window['mcftd_editClick'+fext] = false;
			}
		}
		function onChangeFeaturedImageConfig(cid,sizeid) {
			$('##featureImageSizeID').find('option[value!="0"]').remove();
			if (cid > 0) {
				var objParams = { featureImageConfigID:cid, limitWidth:400, limitHeight:400 };
				$.getJSON('/?event=proxy.ts_json&c=FTDIMAGES&m=getFeaturedImageSizesForConfigID', objParams)
					.done(function(r) { populateFeatureImageSizes(r,sizeid); })
					.fail(populateFeatureImageSizes);
			}
		}
		function populateFeatureImageSizes(r,sizeid) {
			if (r.success) {
				if(r.arrsizes && r.arrsizes.length) {
					$.each(r.arrsizes, function (i, item) {
						var optionText = item.featuredimagesizename + ' (' + item.width + ' x ' + item.height + ')';
						$('##featureImageSizeID').append( $('<option>', { value:item.featureImagesizeid, text:optionText }) );
					});
					if (sizeid) $('##featureImageSizeID').val(sizeid);

					$('##divNoEligibleSizeWarning').addClass('d-none');
				} else {
					$('##divNoEligibleSizeWarning').removeClass('d-none');
				}
			} else {
				alert('We were unable to load this featured image config sizes.');
			}
			return false;
		}
		function editCalendarDefaultFeaturedImage(fext) {
			window['mcftd_editClick'+fext] = true;
			saveCalendarSettings(fext);
		}
		function saveCalendarDefaultFtdImgResult() {
			self.location.href = '#this.link.listCalEvents#&cID=#arguments.event.getValue('cID',0)#&tab=calendarSettings';
		}
		function selectGLAccount() {
			var selectGLAccountLink = '#buildLinkToTool(toolType="GLAccountSelector",mca_ta="showSelector")#&mode=direct&selectFN=top.selectGLAccountResult&glatid=3';
			let title = '<span id="topModalTitle">Select a Revenue Account</span>';
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'lg',
				title: title,
				iframe: true,
				contenturl: selectGLAccountLink,
				strmodalfooter: {
					classlist: 'd-flex',
					showclose: false,
					showextrabutton: false
				}
			});
		}
		function selectGLAccountResult(objGL) {
			if (objGL.thepathexpanded.length > 0) {
				$('##defaultGLAccountPath').html('<span class="mr-1">' + objGL.thepathexpanded + '</span> (<span class="text-danger font-weight-bold">Remember to save!</span>)');
				$('##defaultGLAccountID').val(objGL.glaccountid);
				MCModalUtils.hideModal();
			} else {
				var msg = '<div style="margin:10px;"><h4>Error selecting GL Account</h4><div>There was a problem selecting the default GL Account for events on this calendar.<br/>Try again; if the issue persists, contact MemberCentral for assistance.</div></div>';
				$('##MCModalBody').html(msg);
			}
		}
		function toggleCalendarCategory(f){
			$('##calCategories').toggleClass('d-none', !f);
		}
		
		$(function() {
			toggleCalendarCategory(#val(local.qryCalendar.isSWL)#);
		
			$("##isSWL").click(function(){
				if ($("##isSWL").is(':checked')) {
					toggleCalendarCategory(1);
				}
				else {
					$("##categoryID").find("option").attr("selected", false);
					toggleCalendarCategory(0);
				}
			});

			onChangeFeaturedImageConfig(#val(local.qryCalendar.featureImageConfigID)#,#val(local.qryCalendar.featureImageSizeID)#);
		});
	</script>
	#local.strFeaturedImages.js#
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.calendarFormJS)#">

<cfoutput>
<form name="frmCalendar" id="frmCalendar" onsubmit="return saveCalendarSettings();">
<input type="hidden" name="cID" id="cID" value="#arguments.event.getValue('cID',0)#">
<input type="hidden" name="regEditRefundContentID" value="#local.qryCalendar.registrantEditRefundContentID#">
<div class="text-right mb-2">
	<span id="saveResponse" class="badge badge-success"></span>
	<button type="button" name="btnShowPerms" class="btn btn-sm btn-secondary" onclick="mca_showPermissions(#local.qryCalendar.siteResourceID#,'#encodeForJavaScript(local.qryCalendar.calendarName)#');">Permissions</button>&nbsp;
	<button type="submit" id="saveBtn" class="btn btn-sm btn-primary">Save Information</button>
</div>

<div id="err_calendar" class="alert alert-danger mb-2 d-none"></div>
<cfif len(local.qryCalendar.communityName)>
	<div class="form-group row mb-2">
		<div class="col-sm-3">Community Name</div>
		<div class="col-sm-9">#local.qryCalendar.communityName#</div>
	</div>
</cfif>
<div class="form-group row">
	<label for="newapplicationInstanceName" class="col-sm-3 col-form-label-sm font-size-md">Calendar Name *</label>
	<div class="col-sm-9">
		<input type="hidden" id="oldapplicationInstanceName" name="oldapplicationInstanceName" value="#local.qryCalendar.baseCalendarName#">
		<input type="text" id="newapplicationInstanceName" name="newapplicationInstanceName" value="#local.qryCalendar.baseCalendarName#" class="form-control form-control-sm" maxlength="100">
	</div>
</div>
<div class="form-group row">
	<label for="defaultView" class="col-sm-3 col-form-label-sm font-size-md">Default Calendar View *</label>
	<div class="col-sm-9">
		<select name="defaultView" id="defaultView" class="form-control form-control-sm">
			<option value="viewMonth" <cfif local.qryCalendar.defaultView eq "viewMonth">selected="selected"</cfif>>Calendar View</option>
			<option value="listAll" <cfif local.qryCalendar.defaultView eq "listAll">selected="selected"</cfif>>List All View</option>
			<option value="listMonth" <cfif local.qryCalendar.defaultView eq "listMonth">selected="selected"</cfif>>List Month View</option>
		</select>
	</div>
</div>
<cfif len(local.SWLbranding)>
	<div class="form-group row">
		<div class="col-sm-3">SeminarWeb Live</div>
		<div class="col-sm-9">
			<div class="form-check">
				<input type="checkbox" name="isSWL" id="isSWL" value="1" class="form-check-input" <cfif val(local.qryCalendar.isSWL)>checked="checked"</cfif>>
				<label for="isSWL" class="form-check-label">Include #local.SWLbranding# (SeminarWeb Live) on this calendar</label>
			</div>
		</div>
	</div>
	<div class="form-group row" id="calCategories">
		<label for="redirectSubType" class="col-sm-3 col-form-label-sm font-size-md">Category for these events</label>
		<div class="col-sm-9">
			<select name="categoryID" id="categoryID" class="form-control form-control-sm">
				<option value="">Select Category</option>
				<cfloop query="local.qryCalendarCategories">
					<option value="#local.qryCalendarCategories.categoryID#" <cfif val(local.qryCalendar.categoryID) eq local.qryCalendarCategories.categoryID>selected="selected"</cfif>>#local.qryCalendarCategories.category#</option>
				</cfloop>
			</select>
		</div>
	</div>
</cfif>

<div class="form-group row mt-3">
	<div class="col-sm-3">Add To Calendar links</div>
	<div class="col-sm-9">
		<div class="form-check">
			<input type="checkbox" name="showAddCalendarLinks" id="showAddCalendarLinks" value="1" class="form-check-input" <cfif val(local.qryCalendar.showAddCalendarLinks)>checked="checked"</cfif>>
			<label for="showAddCalendarLinks" class="form-check-label">Display "Add to Calendar" links (Outlook, Google, iCal) on events in this calendar</label>
		</div>
	</div>
</div>
<div class="form-group row">
	<div class="col-sm-3">Allow WebCal Subscriptions</div>
	<div class="col-sm-9">
		<div class="form-check">
			<input type="checkbox" name="allowWebCalSubscriptions" id="allowWebCalSubscriptions" value="1" class="form-check-input" <cfif val(local.qryCalendar.allowWebCalSubscriptions)>checked="checked"</cfif>>
			<label for="allowWebCalSubscriptions" class="form-check-label">Offer ability for end users to subscribe in their external calendaring software.</label>
		</div>
	</div>
</div>
<div class="form-group row mt-3">
	<div class="col-sm-3">Add/Edit Registrant Defaults</div>
	<div class="col-sm-9">
		<div class="form-check">
			<input type="checkbox" name="defRegConfirmation" id="defRegConfirmation" value="1" class="form-check-input" <cfif val(local.qryCalendar.defRegConfirmation)>checked="checked"</cfif>>
			<label for="defRegConfirmation" class="form-check-label">By default, the "E-mail registrant confirmation to" checkbox will be checked.</label>
		</div>
		<div class="form-check">
			<input type="checkbox" name="defStaffConfirmation" id="defStaffConfirmation" value="1" value="1" class="form-check-input" <cfif val(local.qryCalendar.defStaffConfirmation)>checked="checked"</cfif>>
			<label for="defStaffConfirmation" class="form-check-label">By default, the "E-mail staff confirmation to" checkbox will be checked.</label>
		</div>
	</div>
</div>
<div class="form-group row mt-3">
	<div class="col-sm-3">Display Time Zone</div>
	<div class="col-sm-9">
		<div class="form-check">
			<input type="checkbox" name="dspTZAllEvents" id="dspTZAllEvents" value="1" class="form-check-input" <cfif val(local.qryCalendar.alwaysShowEventTimezone)>checked="checked"</cfif>>
			<label for="dspTZAllEvents" class="form-check-label">Display time zone for all events, not just those outside my website time zone.</label>
		</div>
	</div>
</div>
<div class="form-group row mt-5">
	<div class="col-sm-3">Registrant Roster Default</div>
	<div class="col-sm-9">
		<div class="form-check">
			<input type="checkbox" name="enRealRegRoster" id="enRealRegRoster" value="1" class="form-check-input" <cfif val(local.qryCalendar.enableRealTimeRoster)>checked="checked"</cfif>>
			<label for="enRealRegRoster" class="form-check-label">Enable Real-Time Registrant Roster (Who's Attending)</label>
		</div>
	</div>
</div>
<div class="form-group row mt-2">
	<label class="col-sm-3 col-form-label-sm font-size-md">Use this Field Set when <br>showing Registrant Roster</label>
	<div class="col-sm-9">
		#local.strMemberFSSelector.html#
	</div>
</div>
<div class="form-group row mt-5">
	<label for="regEditRefundContent" class="col-sm-3 col-form-label-sm font-size-md">
		Refund Policy:<br/>
		<i>(shown when lowering the paid amount of edited registrations)</i>
	</label>
	<div class="col-sm-9">
		<textarea name="regEditRefundContent" id="regEditRefundContent" class="form-control form-control-sm" rows="3">#local.qryCalendar.registrantEditRefundContent#</textarea>
	</div>
</div>
<div class="form-group row mt-5">
	<div class="col-sm-3">Revenue Account for New Events *</div>
	<div class="col-sm-9">
		<input type="hidden" name="defaultGLAccountID" id="defaultGLAccountID" value="#val(local.qryCalendar.defaultGLAccountID)#" />
		<span id="defaultGLAccountPath"><cfif len(local.qryCalendar.defaultGLAccountPath)>#local.qryCalendar.defaultGLAccountPath#<cfelse>(no account selected)</cfif></span>
		<br/><a href="javascript:selectGLAccount();">Choose GL Account</a>
	</div>
</div>
<div class="form-group row mt-5">
	<label for="featureImageConfigID" class="col-sm-3 col-form-label-sm font-size-md">Featured Image Configuration</label>
	<div class="col-sm-9">
		<input type="hidden" name="oldFeaturedImageConfigID" id="oldFeaturedImageConfigID" value="#val(local.qryCalendar.featureImageConfigID)#">
		<select name="featureImageConfigID" id="featureImageConfigID" class="form-control form-control-sm" onchange="onChangeFeaturedImageConfig(this.value);">
			<option value="0">Select a Featured Image Configuration</option>
			<cfloop query="local.qrySiteFeaturedImageConfigs">
				<option value="#local.qrySiteFeaturedImageConfigs.featureImageConfigID#"<cfif local.qrySiteFeaturedImageConfigs.featureImageConfigID eq local.qryCalendar.featureImageConfigID> selected="selected"</cfif>>#local.qrySiteFeaturedImageConfigs.featuredImageConfigName#</option>
			</cfloop>
		</select>
	</div>
</div>
<div class="form-group row">
	<label for="featureImageSizeID" class="col-sm-3 col-form-label-sm font-size-md">Size on Event Detail Page</label>
	<div class="col-sm-9">
		<select name="featureImageSizeID" id="featureImageSizeID" class="form-control form-control-sm">
			<option value="0">Not Displayed</option>
		</select> (<small><i>max width x height is 400 x 400</i></small>)
		<div id="divNoEligibleSizeWarning" class="alert alert-warning mt-2 d-none">
			This featured image config does not contain any eligible sizes.
		</div>
	</div>
</div>
<div class="form-group row">
	<div class="offset-sm-3 col-sm-9">
		#local.strFeaturedImages.html#
	</div>
</div>
<div class="form-group row mt-5">
	<label for="remarketingText" class="col-sm-3 col-form-label-sm font-size-md">Past Event Re-marketing Text</label>
	<div class="col-sm-9">
		<input type="text" id="remarketingText" name="remarketingText" value="#local.qryCalendar.remarketingText#" class="form-control form-control-sm" maxlength="max">
	</div>
</div>
<div class="form-group row">
	<label for="remarketingBtnText" class="col-sm-3 col-form-label-sm font-size-md">Past Event Re-marketing Button Text</label>
	<div class="col-sm-9">
		<input type="text" id="remarketingBtnText" name="remarketingBtnText" value="#local.qryCalendar.remarketingBtnText#" class="form-control form-control-sm" maxlength="100">
	</div>
</div>
<div class="form-group row mt-5">
	<label for="justview1" class="col-sm-3 col-form-label-sm font-size-md">Internal Calendar URL</label>
	<div class="col-sm-9">
		<div class="input-group input-group-sm">
			<input type="text" name="justview1" id="justview1" value="/?pg=#local.qryCalendar.pageName#" class="form-control form-control-sm" readonly="readonly" onclick="this.select();"/>
			<a target="_blank" href="/?pg=#local.qryCalendar.pageName#" class="ml-1 d-flex align-items-center">Test Link</a>
		</div>
	</div>
</div>
<div class="form-group row">
	<label for="justview2" class="col-sm-3 col-form-label-sm font-size-md">External Calendar URL</label>
	<div class="col-sm-9">
		<div class="input-group input-group-sm">
			<input type="text" name="justview2" id="justview2" value="#arguments.event.getValue('mc_siteInfo.scheme')#://#arguments.event.getValue('mc_siteInfo.mainhostname')#/?pg=#local.qryCalendar.pageName#" class="form-control form-control-sm" readonly="readonly" onclick="this.select();"/>
			<a target="_blank" href="/?pg=#local.qryCalendar.pageName#" class="ml-1 d-flex align-items-center">Test Link</a>
		</div>
	</div>
</div>
</form>

<div id="divCalSettingsFormSubmitArea"></div>

<div id="divCalSettingsSaveLoading" class="d-none">
	<div class="mt-4 text-center">
		<div class="spinner-border" role="status"></div>
		<div class="font-weight-bold mt-2">Please wait while we validate and save the details.</div>
	</div>
</div>
</cfoutput>