ALTER PROC dbo.ev_emailAttendees
@siteID int,
@eventID int, 
@registrantIDList varchar(max), 
@messageToParse varchar(max),
@messageWrapper varchar(max),
@emailTagTypeID int,
@emailFromName varchar(200),
@emailReplyTo varchar(200), 
@emailSubject varchar(200), 
@contentVersionID int,
@recordedByMemberID int,
@deliveryReportEmail varchar(200),
@sendOnDate datetime

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @orgID int, @numRecipients int, @messageTypeID int, @messageStatusIDInserting int, 
		@sendingSiteResourceID int, @supportProviderEmail varchar(100), @supportProviderName varchar(100), 
		@defaultOrgIdentityID int, @messageID int, @rawcontent varchar(max), @fieldID int, @fieldName varchar(300), 
		@vwSQL varchar(max), @ParamDefinition nvarchar(100), @mcSQL nvarchar(max), @colList varchar(max), 
		@colDataType varchar(40), @fieldValueString varchar(200), @eventAdminSRID int, @alterSQL varchar(max), 
		@updateSQL varchar(max), @updateSETsql varchar(max), @updatePVTsql varchar(max), @updateSELECTsql varchar(max), 
		@emailTypeID int, @mergeContent varchar(max), @queueTypeID int, @readyQueueStatusID int, @itemGroupUID uniqueIdentifier;
	declare @tblSubEvents TABLE (masterEventID int, childEventID int);
	declare @metadataFields TABLE (fieldName varchar(300), fieldID int NULL);

	IF OBJECT_ID('tempdb..#tmpRecipientsRID') IS NOT NULL 
		DROP TABLE #tmpRecipientsRID;
	IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
		DROP TABLE #tmpRecipients;
	IF OBJECT_ID('tempdb..#tmpRecipientsCols') IS NOT NULL 
		DROP TABLE #tmpRecipientsCols;
	IF OBJECT_ID('tempdb..#tmpRegRecipientDetails') IS NOT NULL 
		DROP TABLE #tmpRegRecipientDetails;
	IF OBJECT_ID('tempdb..#tmpEventCodes') IS NOT NULL 
		DROP TABLE #tmpEventCodes;
	IF OBJECT_ID('tempdb..#tmpEventAssetTypes') IS NOT NULL 
		DROP TABLE #tmpEventAssetTypes;
	IF OBJECT_ID('tempdb..#tmpEventFields') IS NOT NULL 
		DROP TABLE #tmpEventFields;
	IF OBJECT_ID('tempdb..#tmpEventRoleNames') IS NOT NULL 
		DROP TABLE #tmpEventRoleNames;
	IF OBJECT_ID('tempdb..##tmpEmailEventAttendees') IS NOT NULL 
		DROP TABLE ##tmpEmailEventAttendees;
	IF OBJECT_ID('tempdb..#tmp_CF_ItemIDs') IS NOT NULL
		DROP TABLE #tmp_CF_ItemIDs;
	IF OBJECT_ID('tempdb..#tmp_CF_FieldData') IS NOT NULL
		DROP TABLE #tmp_CF_FieldData;
	IF OBJECT_ID('tempdb..#tmpMergeMDMemberIDs') IS NOT NULL
		DROP TABLE #tmpMergeMDMemberIDs;
	IF OBJECT_ID('tempdb..#tmpMergeMDResults') IS NOT NULL
		DROP TABLE #tmpMergeMDResults;
	CREATE TABLE #tmpRecipientsCols (ORDINAL_POSITION int, COLUMN_NAME sysname, datatype varchar(40));
	CREATE TABLE #tmpRecipientsRID (autoID int IDENTITY(1,1), registrantID int, eventID int, memberID int, mc_emailBlast_email varchar(255), mc_emailBlast_emailTypeID int);
	CREATE TABLE #tmpRegRecipientDetails (recipientID int, memberID int, mc_emailBlast_email varchar(255), registrantID int);
	CREATE TABLE #tmpEventCodes (evRegistrantID int, evEventTitle varchar(200), evEventCode varchar(15), evContactTitle varchar(200),
		evLocationTitle varchar(200), evCancellationTitle varchar(200), evTravelTitle varchar(200),
		evRateName varchar(100), evCategories varchar(max), evRegRole varchar(max));
	CREATE TABLE #tmpEventAssetTypes (assetType varchar(208) PRIMARY KEY);
	CREATE TABLE #tmpEventFields (fieldReference varchar(128) PRIMARY KEY);
	CREATE TABLE #tmpEventRoleNames (columnName varchar(214) PRIMARY KEY);
	CREATE TABLE #tmp_CF_ItemIDs (itemID int, itemType varchar(20));
	CREATE TABLE #tmp_CF_FieldData (fieldID int, fieldValue varchar(max), amount decimal(14,2), itemID int);
	CREATE TABLE #tmpMergeMDMemberIDs (memberID int PRIMARY KEY);
	CREATE TABLE #tmpMergeMDResults (MCAutoID int IDENTITY(1,1) NOT NULL);

	select @orgID = s.orgID, @sendingSiteResourceID = st.siteResourceID, @defaultOrgIdentityID = s.defaultOrgIdentityID
	from dbo.sites as s
	inner join dbo.admin_siteTools as st on st.siteID = s.siteID and st.siteID = @siteID
	inner join dbo.admin_toolTypes as tt on tt.toolTypeID = st.toolTypeID and tt.toolType = 'EventAdmin';

	select @emailTypeID = emailTypeID from dbo.ams_memberEmailTypes where orgID = @orgID and emailTypeOrder = 1;

	IF @sendOnDate < getDate()
		set @sendOnDate = getDate();

	select @eventAdminSRID = dbo.fn_getSiteResourceIDForResourceType('EventAdmin', @siteID);

	-- get the event and sub events so we can check for certificates for any of them		
	insert into @tblSubEvents
	select eventID as masterEventID, eventID as childEventID
	FROM dbo.ev_events
	where eventID = @eventID
		union
	select e.eventID as masterEventID, subEvent.eventID as childEventID
	FROM dbo.ev_events as e
	inner join dbo.ev_subEvents as subEvent on subEvent.parentEventID = e.eventID and e.eventID = @eventID;

	-- get registrants
	insert into #tmpRecipientsRID (registrantID, eventID, memberID, mc_emailBlast_email, mc_emailBlast_emailTypeID)
	select distinct r.registrantID, er.eventID, m.activeMemberID, me.email, metag.emailTypeID
	from dbo.ev_registrants as r
	inner join dbo.fn_intListToTable(isnull(@registrantIDList,''),',') as limitm on limitm.listitem = r.registrantID and r.attended = 1
	inner join dbo.ev_registration as er on er.registrationID = r.registrationID and er.siteID = @siteID
	INNER JOIN @tblSubEvents as se on se.childEventID = er.eventID
	inner join dbo.ams_members as m on m.orgID = @orgID
		and m.memberID = r.memberID 
	inner join dbo.ams_members as m2 on m2.orgID = @orgID
		and m2.memberID = m.activeMemberID
	inner join dbo.crd_requests as rc on rc.registrantID = r.registrantID
		and rc.creditAwarded = 1 
	inner join dbo.crd_offeringTypes as ect on ect.offeringTypeID = rc.offeringTypeID 
	inner join dbo.crd_authoritySponsorTypes as ecast on ecast.astid = ect.astid 
	inner join dbo.crd_certificates as c on c.certificateID = ecast.LiveApprovedCertificateID
	inner join dbo.ams_memberEmails as me on me.orgID = @orgID
		and m2.memberID = me.memberID
	inner join dbo.ams_memberEmailTags as metag on metag.orgID = @orgID
		and metag.memberID = me.memberID
		and metag.emailTypeID = me.emailTypeID
	inner join dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = @orgID
		and metagt.emailTagTypeID = metag.emailTagTypeID
		and metagt.emailTagTypeID = @emailTagTypeID
	where m2.[status] = 'A'
	and r.[status] = 'A'
	and len(me.Email) > 0;

	insert into #tmpRecipientsRID (registrantID, eventID, memberID, mc_emailBlast_email, mc_emailBlast_emailTypeID)
	select distinct r.registrantID, er.eventID, m.activeMemberID, eao.email, @emailTypeID
	from dbo.ev_registrants as r
	inner join dbo.fn_intListToTable(isnull(@registrantIDList,''),',') as limitm on limitm.listitem = r.registrantID and r.attended = 1
	inner join dbo.ev_registration as er on er.registrationID = r.registrationID and er.siteID = @siteID
	INNER JOIN @tblSubEvents as se on se.childEventID = er.eventID
	inner join dbo.ams_members as m on m.orgID = @orgID
		and m.memberID = r.memberID 
	inner join dbo.ams_members as m2 on m2.orgID = @orgID
		and m2.memberID = m.activeMemberID
	inner join dbo.crd_requests as rc on rc.registrantID = r.registrantID
		and rc.creditAwarded = 1 
	inner join dbo.crd_offeringTypes as ect on ect.offeringTypeID = rc.offeringTypeID 
	inner join dbo.crd_authoritySponsorTypes as ecast on ecast.astid = ect.astid 
	inner join dbo.crd_certificates as c on c.certificateID = ecast.LiveApprovedCertificateID
	inner join dbo.ams_emailAppOverrides as eao on eao.itemID = r.registrantID
		and eao.itemType = 'eventReg'
	left outer join #tmpRecipientsRID as tmp on tmp.registrantID = r.registrantID
		and tmp.mc_emailBlast_email = eao.email
	where m2.[status] = 'A'
	and r.[status] = 'A'
	and tmp.autoID is null;

	select @numRecipients = count(autoID) from #tmpRecipientsRID;

	IF @numRecipients = 0 
		RAISERROR('No recipients for message.',16,1);

	select @messageTypeID = messageTypeID from platformMail.dbo.email_messageTypes where messageTypeCode = 'EMAILCERTS';
	select @messageStatusIDInserting = statusID from platformMail.dbo.email_statuses where statusCode = 'I';

	select @queueTypeID = queueTypeID from platformQueue.dbo.tblQueueTypes where queueType = 'eventCertificate';
	select @itemGroupUID = newid();

	select @readyQueueStatusID = queueStatusID
	from platformQueue.dbo.tblQueueStatuses
	where queueTypeID = @queueTypeID 
	and queueStatus = 'readyToProcess';

	select TOP 1 @supportProviderName = net.supportProviderName, @supportProviderEmail = net.supportProviderEmail
	from dbo.networks as net
	inner join dbo.networkSites as ns on net.networkID = ns.networkID
	inner join dbo.sites as s on s.siteID = ns.siteID
	where s.siteID = @siteID 
	and ns.isLoginNetwork = 1;

	-- merge codes.
	SET @mergeContent = isnull(@emailSubject,'') + isnull(@messageToParse,'');

	declare @regexMergeCode varchar(40);
	select @regexMergeCode = regexMergeCode from dbo.fn_getServerSettings();
		
	insert into @metadataFields (fieldName)
	select distinct left([Text],300)
	from dbo.fn_RegexMatches(@mergeContent,@regexMergeCode);
	
	INSERT INTO #tmpMergeMDMemberIDs (memberID)
	SELECT DISTINCT memberID
	FROM #tmpRecipientsRID;

	EXEC dbo.ams_getMemberDataByMergeCodeContent @orgID=@orgID, @content=@mergeContent,
		@codePrefix='', @membersTableName='#tmpMergeMDMemberIDs', @membersResultTableName='#tmpMergeMDResults',
		@colList=@colList OUTPUT;

	IF @colList is null
		select memberID
		into ##tmpEmailEventAttendees
		from #tmpRecipientsRID;
	ELSE BEGIN
		set @vwSQL = 'select m.memberid, ' + @colList + ' 
			into ##tmpEmailEventAttendees 
			from #tmpRecipientsRID as m 
			inner join #tmpMergeMDResults as vwmd on vwmd.memberID = m.memberID;';
		EXEC(@vwSQL);
	END


	-- get event data for merge codes
	INSERT INTO #tmpEventCodes
	select r.registrantID as evRegistrantID, clEvent.contentTitle as evEventTitle, e.reportCode as evEventCode,
		clContact.contentTitle as evContactTitle, clLocation.contentTitle as evLocationTitle,
		clCancel.contentTitle as evCancellationTitle, clTravel.contentTitle as evTravelTitle,
		isnull(rate.rateName,'') as evRateName,
		(select STRING_AGG(c.category,', ')
		from dbo.ev_categories as c
		inner join dbo.ev_eventCategories as ec on ec.categoryID = c.categoryID
		where ec.eventID = e.eventID) as evCategories,
		(select STRING_AGG(c.categoryName,', ')
		from dbo.ev_registrantCategories as rc
		INNER JOIN dbo.cms_categories as c on c.categoryID = rc.categoryID and c.isActive = 1
		where rc.registrantID = r.registrantID) as evRegRole
	from #tmpRecipientsRID as tmp
	inner join dbo.ev_registrants as r on r.registrantID = tmp.registrantID
	inner join dbo.ev_registration as reg on reg.registrationID = r.registrationID and reg.siteID = @siteID
	inner join dbo.ev_events as e on e.eventID = reg.eventID
	inner join dbo.cms_contentLanguages as clEvent on clEvent.contentID = e.eventContentID and clEvent.languageID = 1
	inner join dbo.cms_contentLanguages as clContact on clContact.contentID = e.contactcontentID and clContact.languageID = 1
	inner join dbo.cms_contentLanguages as clLocation on clLocation.contentID = e.locationcontentID and clLocation.languageID = 1
	inner join dbo.cms_contentLanguages as clCancel on clCancel.contentID = e.cancellationPolicycontentID and clCancel.languageID = 1
	inner join dbo.cms_contentLanguages as clTravel on clTravel.contentID = e.travelcontentID and clTravel.languageID = 1
	left outer join dbo.ev_rates as rate on rate.rateID = r.rateID;


	-- get event assets data for merge codes
	IF exists (select 1 from @metadataFields where left(fieldName,8) = 'evAsset_') BEGIN
		declare @evAssetsCatTreeID int;
		select @evAssetsCatTreeID = dbo.fn_getCategoryTreeIDForSiteResourceIDandTree(@eventAdminSRID, 'Event Asset Types');

		INSERT INTO #tmpEventAssetTypes
		select 'evAsset_' + categoryName 
		from dbo.cms_categories
		where categoryTreeID = @evAssetsCatTreeID
		and isActive = 1 
		and parentCategoryID is NULL;

		IF @@ROWCOUNT > 0 BEGIN
			select @alterSQL = coalesce(@alterSQL + ' ','') + 'ALTER TABLE #tmpEventCodes ADD ' + quoteName(assetType) + ' varchar(max) default('''');'
				from #tmpEventAssetTypes;
			EXEC(@alterSQL);

			select @updateSETsql = coalesce(@updateSETsql + ', ','') + 'tmpEC.' + quoteName(assetType) + ' = pivoted.' + quoteName(assetType)
				from #tmpEventAssetTypes;
			select @updatePVTsql = coalesce(@updatePVTsql + ', ','') + quoteName(assetType)
				from #tmpEventAssetTypes;
			select @updateSELECTsql = coalesce(@updateSELECTsql + ', ','') + 'replace(isnull(' + quoteName(assetType) + ',''''),''|'','', '') as ' + quoteName(assetType)
				from #tmpEventAssetTypes;

			SET @updateSQL = 'UPDATE tmpEC SET ' + @updateSETsql + ' from #tmpEventCodes as tmpEC
				inner join (
					select registrantID, ' + @updateSELECTsql + '
					from (
						select r.registrantID, ''evAsset_'' + cp.categoryName as typeName, STRING_AGG(c.categoryName,''|'') as categoryList
						from #tmpRecipientsRID as tmp
						inner join dbo.ev_registrants as r on r.registrantID = tmp.registrantID
						inner join dbo.ev_registration as reg on reg.registrationID = r.registrationID and reg.siteID = @siteID
						inner join dbo.ev_events as e on e.eventID = reg.eventID
						inner join dbo.cms_categorySiteResources as csr on csr.siteResourceID = e.siteResourceID
						inner join dbo.cms_categories as c on c.categoryID = csr.categoryID and c.isActive = 1
						inner join dbo.cms_categories as cP on cP.categoryID = c.parentCategoryID
						group by r.registrantID, ''evAsset_'' + cp.categoryName
					) as tmp 
					PIVOT (min(categoryList) FOR typeName in (' + @updatePVTsql + ')) as pvt
				) as pivoted on pivoted.registrantID = tmpEC.evRegistrantID;';
			EXEC(@updateSQL);
		END
	END


	-- get event fields for merge codes
	IF exists (select 1 from @metadataFields where left(fieldName,8) = 'evField_') BEGIN
		
		-- cross-event custm fields
		declare @crossEventFieldUsageID int;
		set @crossEventFieldUsageID = dbo.fn_cf_getUsageID('EventAdmin','Event',NULL);

		INSERT INTO #tmpEventFields
		select distinct 'evField_' + f.fieldReference
		from dbo.cf_fields as f
		inner join @metadataFields as mf on mf.fieldname = 'evField_' + f.fieldReference
		where f.controllingSiteResourceID = @eventAdminSRID
		and f.usageID = @crossEventFieldUsageID
		and f.isActive = 1
		and len(f.fieldReference) > 0;


		IF @@ROWCOUNT > 0 BEGIN
			select @alterSQL=null, @updateSETsql=null, @updatePVTsql=null, @updateSELECTsql=null, @updateSQL=null;

			select @alterSQL = coalesce(@alterSQL + ' ','') + 'ALTER TABLE #tmpEventCodes ADD ' + quoteName(fieldReference) + ' varchar(max) default('''');'
				from #tmpEventFields;
			EXEC(@alterSQL);

			select @updateSETsql = coalesce(@updateSETsql + ', ','') + 'tmpEC.' + quoteName(fieldReference) + ' = pivoted.' + quoteName(fieldReference)
				from #tmpEventFields;
			select @updatePVTsql = coalesce(@updatePVTsql + ', ','') + quoteName(fieldReference)
				from #tmpEventFields;
			select @updateSELECTsql = coalesce(@updateSELECTsql + ', ','') + 'replace(isnull(' + quoteName(fieldReference) + ',''''),''|'','', '') as ' + quoteName(fieldReference)
				from #tmpEventFields;

			-- truncate temp field tables
			TRUNCATE TABLE #tmp_CF_ItemIDs;
			TRUNCATE TABLE #tmp_CF_FieldData;

			INSERT INTO #tmp_CF_ItemIDs (itemID, itemType)
			SELECT DISTINCT e.siteResourceID, 'CrossEvent'
			FROM #tmpRecipientsRID AS tmp
			INNER JOIN dbo.ev_registrants AS r ON r.registrantID = tmp.registrantID
			INNER JOIN dbo.ev_events AS e ON e.eventID = tmp.eventID;

			EXEC dbo.cf_getFieldData;

			SET @updateSQL = 'UPDATE tmpEC SET ' + @updateSETsql + ' from #tmpEventCodes as tmpEC
				inner join (
					select registrantID, ' + @updateSELECTsql + '
					from (
						select distinct r.registrantID, ''evField_'' + f.fieldReference as titleOnInvoice, fd.fieldValue as answer 
						from #tmpRecipientsRID as tmp
						inner join dbo.ev_registrants as r on r.registrantID = tmp.registrantID
						inner join dbo.ev_events as e on e.eventID = tmp.eventID
						inner join #tmp_CF_FieldData as fd on fd.itemID = e.siteResourceID
						inner join dbo.cf_fields as f on f.fieldID = fd.fieldID
							and f.controllingSiteResourceID = ' + cast(@eventAdminSRID as varchar(10)) + '
						where f.isActive = 1
					) as tmp 
					PIVOT (min(answer) FOR titleOnInvoice in (' + @updatePVTsql + ')) as pvt
				) as pivoted on pivoted.registrantID = tmpEC.evRegistrantID;';
			EXEC(@updateSQL);
		END

		TRUNCATE TABLE #tmpEventFields;


		-- event role fields
		declare @roleFieldUsageID int;
		select @roleFieldUsageID = dbo.fn_cf_getUsageID('EventAdmin','Role',NULL);

		INSERT INTO #tmpEventFields
		select distinct 'evField_' + c.categoryName + '_' + f.fieldReference
		from dbo.cf_fields as f
		inner join dbo.cms_categories as c on c.categoryID = f.detailID
		inner join @metadataFields as mf on mf.fieldname = 'evField_' + c.categoryName + '_' + f.fieldReference
		where f.controllingSiteResourceID = @eventAdminSRID
		and f.usageID = @roleFieldUsageID
		and f.isActive = 1
		and c.isActive = 1
		and len(f.fieldReference) > 0;

		IF @@ROWCOUNT > 0 BEGIN
			select @alterSQL=null, @updateSETsql=null, @updatePVTsql=null, @updateSELECTsql=null, @updateSQL=null;

			select @alterSQL = coalesce(@alterSQL + ' ','') + 'ALTER TABLE #tmpEventCodes ADD ' + quoteName(fieldReference) + ' varchar(max) default('''');'
				from #tmpEventFields;
			EXEC(@alterSQL);

			select @updateSETsql = coalesce(@updateSETsql + ', ','') + 'tmpEC.' + quoteName(fieldReference) + ' = pivoted.' + quoteName(fieldReference)
				from #tmpEventFields;
			select @updatePVTsql = coalesce(@updatePVTsql + ', ','') + quoteName(fieldReference)
				from #tmpEventFields;
			select @updateSELECTsql = coalesce(@updateSELECTsql + ', ','') + 'replace(isnull(' + quoteName(fieldReference) + ',''''),''|'','', '') as ' + quoteName(fieldReference)
				from #tmpEventFields;

			-- truncate temp field tables
			TRUNCATE TABLE #tmp_CF_ItemIDs;
			TRUNCATE TABLE #tmp_CF_FieldData;

			INSERT INTO #tmp_CF_ItemIDs (itemID, itemType)
			SELECT DISTINCT registrantID, 'EventRole'
			FROM #tmpRecipientsRID;

			EXEC dbo.cf_getFieldData;

			SET @updateSQL = 'UPDATE tmpEC SET ' + @updateSETsql + ' from #tmpEventCodes as tmpEC
				inner join (
					select registrantID, ' + @updateSELECTsql + '
					from (
						select distinct r.registrantID, ''evField_'' + c.categoryName + ''_'' + f.fieldReference as titleOnInvoice,
							fd.fieldValue as answer
						from #tmpRecipientsRID as tmp
						inner join dbo.ev_registrants as r on r.registrantID = tmp.registrantID
						inner join dbo.ev_registrantCategories as regcat on regcat.registrantID = r.registrantID
						inner join dbo.cms_categories as c on c.categoryID = regcat.categoryID
						inner join #tmp_CF_FieldData as fd on fd.itemID = r.registrantID
						inner join dbo.cf_fields as f on f.fieldID = fd.fieldID
							and f.detailID = regcat.categoryID
						where f.isActive = 1
					) as tmp 
					PIVOT (min(answer) FOR titleOnInvoice in (' + @updatePVTsql + ')) as pvt
				) as pivoted on pivoted.registrantID = tmpEC.evRegistrantID;';
			EXEC(@updateSQL);
		END
		
		TRUNCATE TABLE #tmpEventFields;


		-- event registrant fields
		INSERT INTO #tmpEventFields
		select distinct 'evField_' + f.fieldReference
		from #tmpRecipientsRID as tmp
		inner join dbo.ev_events as e on e.eventID = tmp.eventID
		inner join dbo.cf_fields as f on f.controllingSiteResourceID = e.siteResourceID
		inner join @metadataFields as mf on mf.fieldName = 'evField_' + f.fieldReference
		where f.isActive = 1
		and len(f.fieldReference) > 0;

		IF @@ROWCOUNT > 0 BEGIN
			select @alterSQL=null, @updateSETsql=null, @updatePVTsql=null, @updateSELECTsql=null, @updateSQL=null;

			select @alterSQL = coalesce(@alterSQL + ' ','') + 'ALTER TABLE #tmpEventCodes ADD ' + quoteName(fieldReference) + ' varchar(max) default('''');'
				from #tmpEventFields;
			EXEC(@alterSQL);

			select @updateSETsql = coalesce(@updateSETsql + ', ','') + 'tmpEC.' + quoteName(fieldReference) + ' = pivoted.' + quoteName(fieldReference)
				from #tmpEventFields;
			select @updatePVTsql = coalesce(@updatePVTsql + ', ','') + quoteName(fieldReference)
				from #tmpEventFields;
			select @updateSELECTsql = coalesce(@updateSELECTsql + ', ','') + 'replace(isnull(' + quoteName(fieldReference) + ',''''),''|'','', '') as ' + quoteName(fieldReference)
				from #tmpEventFields;

			-- truncate temp field tables
			TRUNCATE TABLE #tmp_CF_ItemIDs;
			TRUNCATE TABLE #tmp_CF_FieldData;

			INSERT INTO #tmp_CF_ItemIDs (itemID, itemType)
			SELECT DISTINCT registrantID, 'EventRegCustom'
			FROM #tmpRecipientsRID;

			EXEC dbo.cf_getFieldData;

			SET @updateSQL = 'UPDATE tmpEC SET ' + @updateSETsql + ' from #tmpEventCodes as tmpEC
				inner join (
					select registrantID, ' + @updateSELECTsql + '
					from (
						select distinct r.registrantID, ''evField_'' + f.fieldReference as titleOnInvoice, fd.fieldValue as answer
						from #tmpRecipientsRID as tmp
						inner join dbo.ev_registrants as r on r.registrantID = tmp.registrantID
						inner join dbo.ev_events as e on e.eventID = tmp.eventID
						inner join #tmp_CF_FieldData as fd on fd.itemID = r.registrantID
						inner join dbo.cf_fields as f on f.fieldID = fd.fieldID
							and f.controllingSiteResourceID = e.siteResourceID
						where f.isActive = 1
					) as tmp 
					PIVOT (min(answer) FOR titleOnInvoice in (' + @updatePVTsql + ')) as pvt
				) as pivoted on pivoted.registrantID = tmpEC.evRegistrantID;';
			EXEC(@updateSQL);
		END
	END
	

	-- get role members for merge codes
	IF exists (select 1 from @metadataFields where left(fieldName,14) = 'evRoleMembers_') BEGIN
		declare @categoryTreeID int;
		select @categoryTreeID = dbo.fn_getCategoryTreeIDForSiteResourceIDandTree(@eventAdminSRID,'Event Roles');

		INSERT INTO #tmpEventRoleNames
		select 'evRoleMembers_' + c.categoryName
		from dbo.cms_categories as c
		where c.categoryTreeID = @categoryTreeID
		and c.isActive = 1 
		and c.parentCategoryID is NULL;

		IF @@ROWCOUNT > 0 BEGIN
			select @alterSQL=null, @updateSETsql=null, @updatePVTsql=null, @updateSELECTsql=null, @updateSQL=null;

			select @alterSQL = coalesce(@alterSQL + ' ','') + 'ALTER TABLE #tmpEventCodes ADD ' + quoteName(columnName) + ' varchar(max) default('''');'
				from #tmpEventRoleNames;
			EXEC(@alterSQL);

			select @updateSETsql = coalesce(@updateSETsql + ', ','') + 'tmpEC.' + quoteName(columnName) + ' = pivoted.' + quoteName(columnName)
				from #tmpEventRoleNames;
			select @updatePVTsql = coalesce(@updatePVTsql + ', ','') + quoteName(columnName)
				from #tmpEventRoleNames;
			select @updateSELECTsql = coalesce(@updateSELECTsql + ', ','') + 'replace(isnull(' + quoteName(columnName) + ',''''),''|'','', '') as ' + quoteName(columnName)
				from #tmpEventRoleNames;

			SET @updateSQL = 'UPDATE tmpEC SET ' + @updateSETsql + ' from #tmpEventCodes as tmpEC
				inner join (
					select registrantID, ' + @updateSELECTsql + '
					from (
						select outerrid.registrantID, tmpRM.titleOnInvoice, tmpRM.memberName
						from #tmpRecipientsRID as outerrid
						inner join (
							select eventID, titleOnInvoice, STRING_AGG(memberName,'','') as memberName
							from (
								select distinct rid.eventID, ''evRoleMembers_'' + c.categoryName as titleOnInvoice, mActive.firstName + isnull('' '' + nullif(mActive.middleName,''''),'''') + '' '' + mActive.lastName as memberName
								from #tmpRecipientsRID as rid
								inner join dbo.ev_registration as reg on reg.eventID = rid.eventid 
									and reg.siteID = @siteID
									and reg.status = ''A''
								inner join dbo.ev_registrants as r on r.registrationID = reg.registrationID 
									and r.status = ''A''
								inner join dbo.ev_registrantCategories as rc on rc.registrantID = r.registrantID
								inner join dbo.cms_categories as c on c.categoryID = rc.categoryID 
									and c.categoryTreeID = ' + cast(@categoryTreeID as varchar(10)) + ' 
									and c.isActive = 1 
									and c.parentCategoryID is NULL
								inner join dbo.ams_members as m on m.memberID = r.memberID
								inner join dbo.ams_members as mActive on mActive.memberID = m.activeMemberID 
							) as tmp
							group by eventID, titleOnInvoice
						) as tmpRM on tmpRM.eventID = outerrid.eventID
					) as tmp 
					PIVOT (min(memberName) FOR titleOnInvoice in (' + @updatePVTsql + ')) as pvt
				) as pivoted on pivoted.registrantID = tmpEC.evRegistrantID;';
			EXEC(@updateSQL);
		END
	END


	select distinct tmp.registrantID, m.prefix, m.firstName, m.middlename, m.lastName, m.company, m.suffix, m.professionalsuffix, 
		m.membernumber, m.firstname + ' ' + m.lastname as fullname, 
		m.firstname + isnull(' ' + nullif(m.middlename,''),'') + ' ' + m.lastname + isnull(' ' + nullif(m.suffix,''),'') as extendedname, 
		i.organizationName, i.organizationShortName, i.address1 as organizationAddress1, i.address2 as organizationAddress2,
		i.city as organizationCity, s.Code as organizationStateCode, s.Name as organizationState, c.country as organizationCountry, 
		c.countryCode as organizationCountryCode, i.postalCode as organizationPostalCode, i.phone as organizationPhone, i.fax as organizationFax, 
		i.email as organizationEmail, i.website as organizationWebsite, i.XUserName as organizationXUsername, tmp.mc_emailBlast_email, tmp.mc_emailBlast_emailTypeID, ec.*, vw.*
	into #tmpRecipients
	FROM #tmpRecipientsRID as tmp
	INNER JOIN dbo.ams_members as m on m.orgID = @orgID
		AND m.memberID = tmp.memberID
	INNER JOIN ##tmpEmailEventAttendees as vw on vw.memberID = m.memberID
	INNER JOIN #tmpEventCodes as ec on ec.evRegistrantID = tmp.registrantID
	INNER JOIN dbo.orgIdentities as i on i.orgIdentityID = @defaultOrgIdentityID
	INNER JOIN dbo.ams_states as s on s.stateID = i.stateID
	INNER JOIN dbo.ams_countries c on c.countryID = s.countryID;

	IF OBJECT_ID('tempdb..##tmpEmailEventAttendees') IS NOT NULL 
		DROP TABLE ##tmpEmailEventAttendees;

	-- get tmpRecipients columns
	insert into #tmpRecipientsCols
	select c.column_id, c.name, t.name
	from tempdb.sys.columns as c
	INNER JOIN tempdb.sys.types AS t ON c.user_type_id = t.user_type_id
	where c.object_id = object_id('tempdb..#tmpRecipients');

	BEGIN TRAN;
		-- add email_message
		EXEC platformMail.dbo.email_insertMessage @messageTypeID=@messageTypeID, @siteID=@siteID, 
			@sendingSiteResourceID=@sendingSiteResourceID, @isTestMessage=0, @sendOnDate=@sendOnDate, @recordedByMemberID=@recordedByMemberID, 
			@fromName=@emailFromName, @fromEmail=@supportProviderEmail, @replyToEmail=@emailReplyTo, 
			@senderEmail='', @subject=@emailSubject, @contentVersionID=@contentVersionID, @messageWrapper=@messageWrapper, 
			@referenceType=null, @referenceID=null, @consentListIDs=null, @messageID=@messageID OUTPUT;

		-- update deliveryReportEmail
		IF nullIf(@deliveryReportEmail,'') is not null
			update platformMail.dbo.email_messages
			set deliveryReportEmail = @deliveryReportEmail
			where messageID = @messageID;
	COMMIT TRAN;

	declare @initialQueuePriority int, @expectedRecipientCount int;
	select @expectedRecipientCount = count(*) from #tmpRecipients;
	select @initialQueuePriority = platformMail.dbo.fn_getInitialRecipientQueuePriority(@messageTypeID,	@expectedRecipientCount);

	-- add recipients as I (not ready to be queued yet)
	-- we do it this way because insert with OUTPUT INTO can only refer to columns of the inserted table
	MERGE INTO platformMail.dbo.email_messageRecipientHistory as mh USING #tmpRecipients AS tmp on 1 = 0
	WHEN NOT MATCHED THEN
		INSERT (messageID, memberID, dateLastUpdated, toName, toEmail, emailStatusID, batchID, batchStartDate, emailTypeID, siteID,queuePriority)
		VALUES (@messageID, memberID, getdate(), fullname, mc_emailBlast_email, @messageStatusIDInserting, null, null, mc_emailBlast_emailTypeID, @siteID,@initialQueuePriority)
		OUTPUT INSERTED.recipientID, INSERTED.memberID, INSERTED.toEmail, tmp.registrantID
		INTO #tmpRegRecipientDetails (recipientID, memberID, mc_emailBlast_email, registrantID);
	
	-- add recepient references
	insert into platformMail.dbo.email_messageRecipientReferences (recipientID, referenceType, referenceID)
	select recipientID, 'EventAttendee', registrantID 
	from #tmpRegRecipientDetails;

	-- add any necessary metadata fields
	insert into platformMail.dbo.email_metadataFields (fieldName, isMergeField)
	select fieldName, 1
	from @metadataFields
		except
	select fieldName, isMergeField
	from platformMail.dbo.email_metadataFields;

	update tmp
	set tmp.fieldID = MF.fieldID
	from @metadataFields as tmp
	inner join platformMail.dbo.email_metadataFields as MF on MF.fieldName = tmp.fieldName
	where MF.isMergeField = 1;

	-- add recipient metadata
	set @ParamDefinition = N'@messageID int, @fieldID int';		
	select @fieldID = min(fieldID) from @metadataFields;
	while @fieldID is not null BEGIN
		select @fieldName = fieldName from @metadataFields where fieldID = @fieldID;

		-- ensure field is available (could be a bad merge code)
		IF EXISTS (select ORDINAL_POSITION from #tmpRecipientsCols where column_name = @fieldName) BEGIN
			set @fieldValueString = 'isnull(cast(tmp.[' + @fieldName + '] as varchar(max)),'''')';

			set @mcSQL = 'insert into platformMail.dbo.email_messageMetadataFields (messageID, fieldID, memberID, fieldValue, recipientID)
				select @messageID, @fieldID, reg.memberID, fieldValue = ' + @fieldValueString + ', reg.recipientID
				from #tmpRegRecipientDetails as reg
				inner join #tmpRecipients as tmp on tmp.registrantID = reg.registrantID;';
			exec sp_executesql @mcSQL, @ParamDefinition, @messageID=@messageID, @fieldID=@fieldID;
		END

		select @fieldID = min(fieldID) from @metadataFields where fieldID > @fieldID;
	END

	-- platformQueue to handle certificate attachments		
	INSERT INTO platformQueue.dbo.queue_eventCertificate (itemGroupUID, siteID, eventID, registrantID, recipientID, recordedByMemberID, statusID, dateAdded, dateUpdated)
	select @itemGroupUID, @siteID, @eventID, registrantID, recipientID, @recordedByMemberID, @readyQueueStatusID, getdate(), getdate()
	from #tmpRegRecipientDetails;

	-- resume task
	EXEC dbo.sched_resumeTask @name='Process Email Event Certificates Queue', @engine='MCLuceeLinux';
	

	-- return recipients
	select recipientID, registrantID, memberID, mc_emailBlast_email as recipientEmail, @messageID as messageID
	from #tmpRegRecipientDetails;

	IF OBJECT_ID('tempdb..#tmpRecipientsRID') IS NOT NULL 
		DROP TABLE #tmpRecipientsRID;
	IF OBJECT_ID('tempdb..#tmpRecipients') IS NOT NULL 
		DROP TABLE #tmpRecipients;
	IF OBJECT_ID('tempdb..#tmpRecipientsCols') IS NOT NULL 
		DROP TABLE #tmpRecipientsCols;
	IF OBJECT_ID('tempdb..#tmpRegRecipientDetails') IS NOT NULL 
		DROP TABLE #tmpRegRecipientDetails;
	IF OBJECT_ID('tempdb..#tmpEventCodes') IS NOT NULL 
		DROP TABLE #tmpEventCodes;
	IF OBJECT_ID('tempdb..#tmpEventAssetTypes') IS NOT NULL 
		DROP TABLE #tmpEventAssetTypes;
	IF OBJECT_ID('tempdb..#tmpEventFields') IS NOT NULL 
		DROP TABLE #tmpEventFields;
	IF OBJECT_ID('tempdb..#tmpEventRoleNames') IS NOT NULL 
		DROP TABLE #tmpEventRoleNames;
	IF OBJECT_ID('tempdb..#tmp_CF_ItemIDs') IS NOT NULL
		DROP TABLE #tmp_CF_ItemIDs;
	IF OBJECT_ID('tempdb..#tmp_CF_FieldData') IS NOT NULL
		DROP TABLE #tmp_CF_FieldData;
	IF OBJECT_ID('tempdb..#tmpMergeMDMemberIDs') IS NOT NULL
		DROP TABLE #tmpMergeMDMemberIDs;
	IF OBJECT_ID('tempdb..#tmpMergeMDResults') IS NOT NULL
		DROP TABLE #tmpMergeMDResults;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
