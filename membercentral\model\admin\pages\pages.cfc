<cfcomponent output="no">
	
	<cffunction name="getRootSectionID" access="public" output="false" returntype="numeric">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfset var local = structNew()>
		<cfquery name="local.getRoot" datasource="#application.dsn.membercentral.dsn#">
			SELECT siteResourceID
			FROM dbo.cms_pageSections 
			WHERE sectionID = dbo.fn_getRootSectionID(#arguments.siteid#)
			AND siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
		</cfquery>	
		<cfset local.siteResourceID = 0 />
		<cfif local.getRoot.recordCount>
			<cfset local.siteResourceID = local.getRoot.siteResourceID />
		</cfif>
		<cfreturn local.siteResourceID />
	</cffunction>
	
	<cffunction name="getSiteResourceChildCount" access="public" output="false" returntype="numeric">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="siteResourceID" type="numeric" required="yes">
		<cfset var local = structNew() />
		<cfset local.childCount = 0 />
		<cfset local.sectionID = getSectionIDFromResourceID(arguments.siteID,arguments.siteResourceID) />
		<cfquery name="local.getSections" datasource="#application.dsn.membercentral.dsn#">
			SELECT COUNT(s.sectionID) as childCount
			FROM dbo.cms_pageSections s
			INNER JOIN dbo.cms_siteResources sr ON s.siteResourceID = sr.siteResourceID
			INNER JOIN dbo.cms_siteResourceStatuses srs ON sr.siteResourceStatusID = srs.siteResourceStatusID and srs.siteResourceStatusDesc <> 'Deleted'
			WHERE s.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">
			AND s.parentSectionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.sectionID#">
		</cfquery>
		<cfquery name="local.getPages" datasource="#application.dsn.membercentral.dsn#">
			SELECT COUNT(p.pageID) as childCount
			FROM dbo.cms_pages p
			INNER JOIN dbo.cms_siteResources sr ON p.siteResourceID = sr.siteResourceID
			INNER JOIN dbo.cms_siteResourceStatuses srs ON sr.siteResourceStatusID = srs.siteResourceStatusID and srs.siteResourceStatusDesc <> 'Deleted'
			WHERE p.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">
			AND p.sectionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.sectionID#">
		</cfquery>
		<cfquery name="local.getDocuments" datasource="#application.dsn.membercentral.dsn#">
			SELECT COUNT(d.documentID) as childCount
			FROM dbo.cms_documents d
			INNER JOIN dbo.cms_siteResources sr ON d.siteResourceID = sr.siteResourceID
			INNER JOIN dbo.cms_siteResourceStatuses srs ON sr.siteResourceStatusID = srs.siteResourceStatusID and srs.siteResourceStatusDesc <> 'Deleted'
			WHERE d.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">
			AND d.sectionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.sectionID#">
		</cfquery>	
		<cfset local.childCount = local.childCount + local.getSections.childCount />
		<cfset local.childCount = local.childCount + local.getPages.childCount />
		<cfset local.childCount = local.childCount + local.getDocuments.childCount />
		
		<cfreturn local.childCount />
	</cffunction>
	
	<cffunction name="getPage" access="public" output="false" returntype="struct">
		<cfargument name="pageID" type="numeric" required="yes">
		<cfargument name="siteID" type="numeric" required="yes">
		
		<cfset var local = structNew()>
		
		<cfquery name="local.returnStruct.qryPage" datasource="#application.dsn.membercentral.dsn#">
			select p.pageid, p.sectionid, p.ovTemplateID, p.ovTemplateIDMobile, p.ovModeID, p.siteid, p.pageName, p.allowReturnAfterLogin, p.dateCreated
			from dbo.cms_pages as p
			where p.pageid = <cfqueryparam value="#arguments.pageid#" cfsqltype="CF_SQL_INTEGER">
			and p.siteID = <cfqueryparam value="#arguments.siteID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>
		<cfif local.returnStruct.qryPage.recordcount>
			<cfset arguments.pageID = local.returnStruct.qryPage.pageID>
			<cfset arguments.siteID = local.returnStruct.qryPage.siteID>
		<cfelse>
			<cfset arguments.pageID = 0>
			<cfset arguments.siteID = arguments.siteID>
		</cfif>

		<cfquery name="local.returnStruct.qryPageLanguages" datasource="#application.dsn.membercentral.dsn#">
			SELECT pl.pageLanguageID, pl.languageID, pl.pageTitle, pl.pageDesc, pl.keywords, pl.dateCreated, pl.dateModified
			FROM dbo.cms_pageLanguages AS pl
			WHERE pl.pageID = <cfqueryparam value="#arguments.pageID#" cfsqltype="CF_SQL_INTEGER">
			ORDER BY pl.pageLanguageID
		</cfquery>		

		<cfreturn local.returnStruct>
	</cffunction>		
	
	<cffunction name="getContentZoneData" access="public" output="false" returntype="query">
		<cfargument name="containerSRID" type="numeric" required="yes">
		<cfargument name="itemSRID" type="numeric" required="yes">
		
		<cfset var local = structNew()>
		
		<cfquery name="local.data" datasource="#application.dsn.membercentral.dsn#">
			select pzr.pzrID,pzr.pageID,null as sectionID, p.siteResourceID as containerResourceID, pzr.zoneID,pzr.siteResourceID,pzr.sortOrder, itemResourceType.resourceTypeID, itemResourceType.resourceType
			from dbo. cms_pageZonesResources as pzr
			inner join dbo.cms_pages p ON p.pageID = pzr.pageID
				AND pzr.siteResourceID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.itemSRID#">
			INNER JOIN dbo.cms_siteResources sr on sr.siteResourceID = p.siteResourceID
				and sr.siteResourceID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.containerSRID#">
			INNER JOIN dbo.cms_siteResources itemResource on pzr.siteResourceID = itemResource.siteResourceID
			INNER JOIN dbo.cms_siteResourceTypes itemResourceType on itemResourceType.resourceTypeID = itemResource.resourceTypeID
		
			union
			
			select pszr.pszrID,null as pageID, pszr.sectionID, ps.siteResourceID as containerResourceID, pszr.zoneID,pszr.siteResourceID,pszr.sortOrder, itemResourceType.resourceTypeID, itemResourceType.resourceType
			from dbo. cms_pageSectionsZonesResources as pszr
			inner join dbo.cms_pageSections ps ON ps.sectionID = pszr.sectionID
				AND pszr.siteResourceID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.itemSRID#">
			INNER JOIN dbo.cms_siteResources sr on sr.siteResourceID = ps.siteResourceID
				and sr.siteResourceID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.containerSRID#">
			INNER JOIN dbo.cms_siteResources itemResource on pszr.siteResourceID = itemResource.siteResourceID
			INNER JOIN dbo.cms_siteResourceTypes itemResourceType on itemResourceType.resourceTypeID = itemResource.resourceTypeID

		</cfquery>
		
		<cfreturn local.data>
	</cffunction>		
	
	<cffunction name="getData" access="public" output="false" returntype="struct">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="siteResourceID" type="numeric" required="yes">
		<cfscript>
			var local = structNew();
			local.returnStruct.isPage			= FALSE;
			local.returnStruct.isSection 	= FALSE;
		</cfscript>
		
		<cfquery name="local.returnStruct.qryResourceData" datasource="#application.dsn.membercentral.dsn#">
			SELECT sr.siteResourceID, sr.siteResourceStatusID, sr.siteID, sr.isVisible, sr.parentSiteResourceID, srt.resourceTypeID,
				srt.resourceType, srtc.resourceTypeClassID, srtc.resourceTypeClassName
			FROM dbo.cms_siteResources sr
			INNER JOIN dbo.cms_siteResourceTypes srt ON sr.resourceTypeID = srt.resourceTypeID
			INNER JOIN dbo.cms_siteResourceTypeClasses srtc ON srt.resourceTypeClassID = srtc.resourceTypeClassID
			WHERE sr.siteResourceID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteResourceID#">
		</cfquery>		
		
		<cfswitch expression="#local.returnStruct.qryResourceData.resourceTypeClassName#">
			<cfcase value="page">
				<cfset local.returnStruct.isPage = TRUE>
				<cfquery name="local.returnStruct.qryPage" datasource="#application.dsn.membercentral.dsn#">
					SELECT p.pageid, p.siteid, p.siteResourceID, p.sectionID, p.ovTemplateID, p.ovTemplateIDMobile,
						p.ovModeID, p.pageName, p.allowReturnAfterLogin, p.dateCreated, p.inheritPlacements,
						p.pageDirectives, p.dateUnavailable, isnull(rd.redirectID,'0') as redirectID, rd.redirectName,
						isnull(p.ovTemplateID, dpss.ovTemplateID) as derivedTemplateID, 
						isnull(p.ovTemplateIDMobile,dpss.ovTemplateIDMobile) as derivedTemplateIDMobile, 
						isnull(p.ovModeID,  dpss.ovModeID) as derivedModeID, pt.templateName as devrivedTemplateName,
						mobilept.templateName as devrivedTemplateNameMobile, pm.modeName as derivedModeName,
						ficu.featureImageConfigID as websitePageFeatureImageConfigID, fiu.featureImageID as pageFeatureImageID
					FROM dbo.cms_pages AS p
					INNER JOIN dbo.cache_cms_derivedPageSectionSettings dpss
						on dpss.sectionID = p.sectionID
					LEFT OUTER JOIN dbo.cms_pageTemplates pt
						on pt.templateID = isnull(p.ovTemplateID, dpss.ovTemplateID)
					LEFT OUTER JOIN dbo.cms_pageTemplates mobilept
						on mobilept.templateID = isnull(p.ovTemplateIDMobile,dpss.ovTemplateIDMobile)
					LEFT OUTER JOIN dbo.cms_pageModes pm
						on pm.modeID = isnull(p.ovModeID,  dpss.ovModeID)
					LEFT OUTER JOIN dbo.siteRedirects rd ON p.redirectID = rd.redirectID
					LEFT OUTER JOIN dbo.cms_featuredImageConfigUsages AS ficu ON ficu.referenceID = p.siteID
						AND ficu.referenceType = 'websitePage'
					LEFT OUTER JOIN dbo.cms_featuredImageUsages AS fiu ON fiu.featureImageConfigID = ficu.featureImageConfigID
						AND fiu.referenceID = p.pageID
						AND fiu.referenceType = 'websitePage'
					WHERE p.siteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteResourceID#">
					AND p.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
				</cfquery>
				<cfif local.returnStruct.qryPage.recordcount>
					<cfset arguments.pageID = local.returnStruct.qryPage.pageID>
					<cfset arguments.siteID = local.returnStruct.qryPage.siteID>
				<cfelse>
					<cfset arguments.pageID = 0>
					<cfset arguments.siteID = arguments.siteID>
				</cfif>
				
				<!--- This may not be ideal but 2 DB queries will be issued to get the menu usages --->
				<cfset local.returnStruct.qryTemplateMUT = getTemplateMenuUsageTypes(arguments.siteID, val(local.returnStruct.qryPage.derivedTemplateID), val(arguments.siteResourceID)) />
				<cfset local.returnStruct.qryTemplateMobileMUT = getTemplateMenuUsageTypes(arguments.siteID, val(local.returnStruct.qryPage.derivedTemplateIDMobile), val(arguments.siteResourceID)) />

				<cfquery name="local.returnStruct.qryPageLanguages" datasource="#application.dsn.membercentral.dsn#">
					SELECT 
						pl.pageLanguageID, 
						pl.languageID, 
						pl.pageTitle, 
						pl.pageDesc, 
						pl.keywords, 
						pl.dateCreated, 
						pl.dateModified,
						l.languageName
					FROM 
						dbo.cms_pageLanguages AS pl
						INNER JOIN dbo.cms_languages l ON pl.languageID = l.languageID 
					WHERE 
						pl.pageID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.pageID#">
					ORDER BY 
						pl.pageLanguageID
				</cfquery>
			</cfcase>
			
			<cfcase value="section">
				<cfset local.returnStruct.isSection = TRUE>
				<cfquery name="local.returnStruct.qrySection" datasource="#application.dsn.membercentral.dsn#">
					SELECT s.sectionID, s.siteID, s.ovTemplateID, s.ovTemplateIDMobile, s.ovModeID, s.parentSectionID, s.sectionName,
						s.sectionCode, s.sectionBreadcrumb, s.siteResourceID, s.inheritPlacements, 
						isnull(s.ovTemplateID, dpss.ovTemplateID) as derivedTemplateID, 
						isnull(s.ovTemplateIDMobile,dpss.ovTemplateIDMobile) as derivedTemplateIDMobile, 
						isnull(s.ovModeID,  dpss.ovModeID) as derivedModeID,
						pt.templateName as devrivedTemplateName, mobilept.templateName as devrivedTemplateNameMobile,
						pm.modeName as derivedModeName, ficu.featureImageConfigID AS websitePageSectionFeatureImageConfigID
					FROM dbo.cms_pageSections s
					INNER JOIN dbo.cache_cms_derivedPageSectionSettings dpss on dpss.sectionID = s.sectionID
					LEFT OUTER JOIN dbo.cms_pageTemplates pt on pt.templateID = isnull(s.ovTemplateID, dpss.ovTemplateID)
					LEFT OUTER JOIN dbo.cms_pageTemplates mobilept on mobilept.templateID = isnull(s.ovTemplateIDMobile,dpss.ovTemplateIDMobile)
					LEFT OUTER JOIN dbo.cms_pageModes pm on pm.modeID = isnull(s.ovModeID,  dpss.ovModeID)
					LEFT OUTER JOIN dbo.cms_featuredImageConfigUsages AS ficu ON ficu.referenceID = s.siteID
						AND ficu.referenceType = 'websitePageSection'
					WHERE s.siteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteResourceID#">
					AND s.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
				</cfquery>

				<!--- This may not be ideal but 2 DB queries will be issued to get the menu usages --->
				<cfset local.returnStruct.qryTemplateMUT = getTemplateMenuUsageTypes(arguments.siteID, val(local.returnStruct.qrySection.derivedTemplateID), val(local.returnStruct.qrySection.siteResourceID)) />
				<cfset local.returnStruct.qryTemplateMobileMUT = getTemplateMenuUsageTypes(arguments.siteID, val(local.returnStruct.qrySection.derivedTemplateIDMobile), val(local.returnStruct.qrySection.siteResourceID)) />
			</cfcase>
			
		</cfswitch>
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getTemplateMenuUsageTypes" access="private" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="templateID" type="numeric" required="yes">
		<cfargument name="siteResourceID" type="numeric" required="yes">
		<cfscript>
			var local = structNew();
		</cfscript>
		<cfquery name="local.qryTemplateMenuUsageTypes" datasource="#application.dsn.membercentral.dsn#">
			set nocount on

			declare @siteID int
			set @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">

			select ptmut.templateUsageTypeID, pt.templateID, pt.templateName, mut.usageTypeID, mut.usageName, mut.usageCode, mu.menuID
			from dbo.cms_pageTemplates pt
			inner join cms_pageTemplateMenuUsageTypes ptmut
				on ptmut.templateID = pt.templateID
				and (pt.siteID = @siteID or pt.siteID is null)
				and pt.templateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.templateID#">
			inner join cms_menuUsageTypes mut
				on ptmut.usageTypeID = mut.usageTypeID
			left outer join cms_menuUsages mu on mu.templateUsageTypeID = ptmut.templateUsageTypeID
				and mu.usedBySiteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteResourceID#"> 

			set nocount off
		</cfquery>

		<cfreturn local.qryTemplateMenuUsageTypes>
	</cffunction>


	<cffunction name="updatePage" access="public" output="false" returntype="void">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="pageID" type="numeric" required="true">
		<cfargument name="pageName" type="string" required="true">
		<cfargument name="sectionID" type="numeric" required="true">
		<cfargument name="ovModeID" type="numeric" required="true">
		<cfargument name="ovTemplateID" type="numeric" required="true">
		<cfargument name="ovTemplateIDMobile" type="numeric" required="true">
		<cfargument name="inheritPlacements" type="Boolean" required="true">
		<cfargument name="pageDirectives" type="string" required="true">
		<cfargument name="dateUnavailable" type="string" required="true">
		<cfargument name="siteResourceStatusID" type="numeric" required="true">

		<cfstoredproc procedure="cms_updatePage" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.pageID#">
			<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			<cfif len(arguments.pageName)>
				<cfprocparam type="IN" cfsqltype="CF_SQL_VARCHAR" value="#arguments.pageName#">
			<cfelse>
				<cfprocparam type="IN" cfsqltype="CF_SQL_VARCHAR" null="true">
			</cfif>
			<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteResourceStatusID#">
			<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.sectionID#">
			<cfif arguments.ovTemplateID gt 0>
				<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.ovTemplateID#">
			<cfelse>
				<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" null="true">
			</cfif>
			<cfif arguments.ovTemplateIDMobile gt 0>
				<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.ovTemplateIDMobile#">
			<cfelse>
				<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" null="true">
			</cfif>
			<cfif arguments.ovModeID gt 0>
				<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.ovModeID#">
			<cfelse>
				<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" null="true">
			</cfif>
			<cfprocparam type="IN" cfsqltype="CF_SQL_BIT" value="#arguments.inheritPlacements#">
			<cfprocparam type="IN" cfsqltype="CF_SQL_VARCHAR" value="#arguments.pageDirectives#">
			<cfif len(arguments.dateUnavailable)>
				<cfprocparam type="IN" cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.dateUnavailable#">
			<cfelse>
				<cfprocparam type="IN" cfsqltype="CF_SQL_TIMESTAMP" null="true">
			</cfif>
		</cfstoredproc>		
	</cffunction>
	
	<cffunction name="updateSection" access="public" output="false" returntype="void">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="ovTemplateID" type="numeric" required="true">
		<cfargument name="ovTemplateIDMobile" type="numeric" required="true">
		<cfargument name="ovModeID" type="numeric" required="true">
		<cfargument name="parentSectionID" type="numeric" required="true">
		<cfargument name="sectionName" type="string" required="true">
		<cfargument name="sectionCode" type="string" required="true">
		<cfargument name="sectionBreadcrumb" type="string" required="true">
		<cfargument name="siteResourceStatusID" type="numeric" required="true">
		<cfargument name="inheritPlacements" type="Boolean" required="true">
		<cfargument name="sectionID" type="numeric" required="true">
		
		<cfset var qryUpdateSection = "">

		<cfquery name="qryUpdateSection" datasource="#application.dsn.membercentral.dsn#">
			set nocount on;

			declare @sectionID int, @sectionCode varchar(50);
			set @sectionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.sectionID#">;
			<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) and arguments.parentSectionID gt 0>
				set @sectionCode = NULLIF(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.sectionCode#">,'');
			<cfelse>
				select @sectionCode = sectionCode 
				from dbo.cms_pagesections 
				where sectionID = @sectionID;
			</cfif>

			exec dbo.cms_updatePageSection
				@sectionID = @sectionID,
				@siteid = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">,
				@siteResourceStatusID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteResourceStatusID#">,
				<cfif arguments.parentSectionID gt 0>
					@parentSectionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.parentSectionID#">,
				<cfelse>
					@parentSectionID = <cfqueryparam cfsqltype="cf_sql_integer" null="true">,
				</cfif>
				@sectionName = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.sectionName#">,
				@sectionCode = @sectionCode,
				@sectionBreadcrumb = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.sectionBreadcrumb#">,
				<cfif arguments.ovTemplateID gt 0>
					@ovTemplateID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.ovTemplateID#">,
				<cfelse>
					@ovTemplateID = <cfqueryparam cfsqltype="cf_sql_integer" null="true">,
				</cfif>
				<cfif arguments.ovTemplateIDMobile gt 0>
					@ovTemplateIDMobile = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.ovTemplateIDMobile#">,
				<cfelse>
					@ovTemplateIDMobile = <cfqueryparam cfsqltype="cf_sql_integer" null="true">,
				</cfif>				
				<cfif arguments.ovModeID gt 0>
					@ovModeID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.ovModeID#">,
				<cfelse>
					@ovModeID = <cfqueryparam cfsqltype="cf_sql_integer" null="true">,
				</cfif>
				@inheritPlacements = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.inheritPlacements#">;
		</cfquery>
	</cffunction>
	
	<cffunction name="updateMenu" access="public" output="false" returntype="void">
		<cfargument name="menuID" type="numeric" required="true">
		<cfargument name="templateUsageTypeID" type="numeric" required="true">
		<cfargument name="siteResourceID" type="numeric" required="true">
		<cfargument name="isMobileUsage" type="boolean" required="true">
		
		<cfset var local = structNew()>

		<cfquery name="local.updatepage" datasource="#application.dsn.membercentral.dsn#">
			exec dbo.cms_updateMenu @menuID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.menuID#">,
				@templateUsageTypeID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.templateUsageTypeID#">,
				@siteResourceID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteResourceID#">,
				@isMobileUsage = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.isMobileUsage#">;
		</cfquery>
	</cffunction>

	<cffunction name="insertSection" access="public" output="false" returntype="numeric" hint="Insert Section">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="ovTemplateID" type="numeric" required="true">
		<cfargument name="ovTemplateIDMobile" type="numeric" required="true">
		<cfargument name="ovModeID" type="numeric" required="true">
		<cfargument name="parentSectionID" type="numeric" required="true">
		<cfargument name="sectionName" type="string" required="true">
		<cfargument name="sectionCode" type="string" required="true">
		<cfargument name="sectionBreadcrumb" type="string" required="true">
		<cfargument name="siteResourceStatusID" type="numeric" required="true">
		<cfargument name="inheritPlacements" type="Boolean" required="true">
		
		<cfset var local = structNew()>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.insertSection">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				DECLARE @siteID int, @sectionResourceTypeID int, @ovTemplateID int, @ovTemplateIDMobile int, @ovModeID int, 
					@parentSectionID int, @sectionName varchar(50), @sectionBreadcrumb varchar(200), @sectionCode varchar(50), @inheritPlacements bit,
					@siteResourceStatusID int, @sectionID int;
				
				SELECT @siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">;
				SELECT @sectionResourceTypeID = dbo.fn_getResourceTypeID('UserCreatedSection');
				SELECT @siteResourceStatusID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteResourceStatusID#">;
				SELECT @parentSectionID = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.parentSectionID#">;
				SELECT @sectionName = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.sectionName#">;
				SELECT @sectionBreadcrumb = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.sectionBreadcrumb#">;
				SELECT @inheritPlacements = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.inheritPlacements#">;
				
				<cfif arguments.ovTemplateID GT 0>
					SELECT @ovTemplateID = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.ovTemplateID#">;
				</cfif>
				<cfif arguments.ovTemplateIDMobile GT 0>
					SELECT @ovTemplateIDMobile = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.ovTemplateIDMobile#">;
				</cfif>
				<cfif arguments.ovModeID GT 0>
					SELECT @ovModeID = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.ovModeID#">;
				</cfif>
				<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) AND len(arguments.sectionCode)>
					SELECT @sectionCode = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.sectionCode#">;
				</cfif>

				BEGIN TRAN;
					EXEC dbo.cms_createPageSection @siteID = @siteID, @sectionResourceTypeID = @sectionResourceTypeID, 
						@ovTemplateID = @ovTemplateID, @ovTemplateIDMobile = @ovTemplateIDMobile, @ovModeID = @ovModeID, 
						@parentSectionID = @parentSectionID, @sectionName = @sectionName, @sectionCode = @sectionCode,@sectionBreadcrumb = @sectionBreadcrumb,
						@inheritPlacements = @inheritPlacements, @sectionID = @sectionID OUTPUT;
					
					<cfif arguments.siteResourceStatusID GT 0>
						update sr
						set siteResourceStatusID = @siteResourceStatusID
						from dbo.cms_pageSections s
						inner join dbo.cms_siteResources sr on s.siteResourceID=sr.siteResourceID
							and s.sectionID=@sectionID
							and s.siteID=@siteID;
					</cfif>
				COMMIT TRAN;

				SELECT siteResourceID
				FROM dbo.cms_pageSections
				WHERE sectionID = @sectionID;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfreturn local.insertSection.siteResourceID>
	</cffunction>
	
	<cffunction name="insertContentPage" access="public" output="false" returntype="numeric">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="languageID" type="numeric" required="true">
		<cfargument name="sectionID" type="numeric" required="true">
		<cfargument name="ovModeID" type="numeric" required="true">
		<cfargument name="ovTemplateID" type="numeric" required="true">
		<cfargument name="ovTemplateIDMobile" type="numeric" required="true">
		<cfargument name="inheritPlacements" type="boolean" required="true">
		<cfargument name="pageName" type="string" required="true">
		<cfargument name="pageTitle" type="string" required="true">
		<cfargument name="pageDesc" type="string" required="true">
		<cfargument name="keywords" type="string" required="true">
		<cfargument name="commSRID" required="false"  type="numeric">
		<cfargument name="isFromCommunity" type="boolean" required="false" default="0">
		
		<cfset var local = structNew()>

		<cfquery name="local.insertpage" datasource="#application.dsn.membercentral.dsn#">
			set nocount on;

			DECLARE @siteid int, @languageID int, @resourceTypeID int, @siteResourceStatusID int, @pgParentResourceID int, 
				@isVisible bit, @sectionID int, @ovTemplateID int, @ovTemplateIDMobile int, @ovModeID int, 
				@pageName varchar(100), @pageTitle varchar(200), @pageDesc varchar(400), @keywords varchar(400),
				@inheritPlacements bit, @allowReturnAfterLogin bit, @checkReservedNames bit, @zoneID int, 
				@contentResourceTypeID int, @pageID int, @contentID int, @contentSiteResourceID int, @memberID int,@commSRID int ;
				
			select @siteid = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">,
				@languageID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.languageID#">,			
				@isVisible = 1,				
				@ovTemplateID = <cfif arguments.ovTemplateID gt 0><cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.ovTemplateID#"><cfelse>null</cfif>,
				@ovTemplateIDMobile = <cfif arguments.ovTemplateIDMobile gt 0><cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.ovTemplateIDMobile#"><cfelse>null</cfif>,
				@ovModeID = <cfif arguments.ovModeID gt 0><cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.ovModeID#"><cfelse>null</cfif>,
				@pageName = dbo.fn_regexReplace(<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.pageName#">,'[^A-Z0-9\-_]+',''),
				@pageTitle = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.pageTitle#">,
				@pageDesc = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.pageDesc#">,
				@keywords = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.keywords#">,
				@inheritPlacements = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.inheritPlacements#">,
				@allowReturnAfterLogin = 1,
				@checkReservedNames = 1,
				@zoneID = dbo.fn_getZoneID('Main'),
				@contentResourceTypeID = dbo.fn_getResourceTypeID('UserCreatedContent'),
				@memberID = <cfqueryparam value="#application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).orgID)#" cfsqltype="cf_sql_integer">;
			<cfif arguments.isFromCommunity>
					SET @commSRID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.commSRID#">;
					SELECT @sectionID = comm.rootSectionID
						from dbo.comm_communities comm
						inner join dbo.cms_applicationInstances ai on comm.applicationInstanceID = ai.applicationInstanceID
						AND ai.siteResourceID = @commSRID
						AND ai.siteID = @siteID;
					SELECT @resourceTypeID = dbo.fn_getResourceTypeID('ApplicationSubPage');
					SELECT @pgParentResourceID = @commSRID,
					@siteResourceStatusID = 1;
			<cfelse>
				SELECT 
				@sectionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.sectionID#">,
				@resourceTypeID = dbo.fn_getResourceTypeID('UserCreatedPage'),
				@pgParentResourceID = null,
				@siteResourceStatusID = 2;

			</cfif>		
			set @pageID = null;
			
			exec dbo.cms_createpage @siteid=@siteid, @languageID=@languageID, @resourceTypeID=@resourceTypeID, 
				@siteResourceStatusID=@siteResourceStatusID, @pgParentResourceID=@pgParentResourceID, @isVisible=@isVisible,
				@sectionID=@sectionID, @ovTemplateID=@ovTemplateID, @ovTemplateIDMobile=@ovTemplateIDMobile, @ovModeID=@ovModeID,
				@pageName=@pageName, @pageTitle=@pageTitle, @pageDesc=@pageDesc, @keywords=@keywords, 
				@inheritPlacements=@inheritPlacements, @allowReturnAfterLogin=@allowReturnAfterLogin, 
				@checkReservedNames=@checkReservedNames, @pageID=@pageID OUTPUT;

			if @pageID is not null
				exec dbo.cms_createContent @siteID=@siteID, @pageID=@pageID, @zoneID=@zoneID, @resourceTypeID=@contentResourceTypeID,
					@siteResourceStatusID=@siteResourceStatusID, @isHTML=1, @languageID=@languageID, @isActive=1, @contentTitle=@pageTitle,
					@contentDesc=@pageDesc, @rawContent='', @memberID=@memberID, @contentID=@contentID OUTPUT, 
					@contentSiteResourceID=@contentSiteResourceID OUTPUT;
		
			select @pageID as pageID;
		</cfquery>

		<cfreturn local.insertpage.pageID>
	</cffunction>

	<cffunction name="getPageLanguages" access="public" output="false" returntype="query">
		<cfargument name="pageID" type="numeric" required="yes">
		<cfscript>
			var local = structNew();
		</cfscript>		
		<cfquery name="local.data" datasource="#application.dsn.memberCentral.dsn#">
			SELECT 
				pl.pageLanguageID, 
				pl.languageID, 
				pl.pageTitle, 
				pl.pageDesc, 
				pl.keywords, 
				pl.dateCreated, 
				pl.dateModified,
				l.languageName
			FROM 
				dbo.cms_pageLanguages AS pl
				INNER JOIN dbo.cms_languages l 
					ON pl.languageID = l.languageID 
			WHERE 
				pl.pageID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.pageID#">
			ORDER BY 
				pl.pageLanguageID
		</cfquery>
		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="getSectionIDFromResourceID" access="public" output="false" returntype="numeric">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="siteResourceID" type="numeric" required="yes">
		
		<cfset var local = structNew()>
		
		<cfquery name="local.data" datasource="#application.dsn.membercentral.dsn#">
			SELECT 
				s.sectionID
			FROM dbo.cms_pageSections s
			WHERE s.siteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteResourceID#">
			AND s.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
		</cfquery>
		<cfset local.sectionID = 0 />
		<cfif local.data.recordCount>
			<cfset local.sectionID = local.data.sectionID />
		</cfif>
		
		<cfreturn local.sectionID>
	</cffunction>		
	
	<cffunction name="removePZR" access="public" output="false" returntype="void" hint="Removes cms_pageZonesResources entry">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="pzrID" type="numeric" required="true">

		<cfstoredproc procedure="cms_deletePageZonesResource" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.pzrID#">
		</cfstoredproc>
	</cffunction>
	
	<cffunction name="removePSZR" access="public" output="false" returntype="void" hint="Removes cms_pageSectionZonesResources entry">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="pszrID" type="numeric" required="true">

		<cfstoredproc procedure="cms_deletePageSectionsZonesResource" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.pszrID#">
		</cfstoredproc>
	</cffunction>
	
	<cffunction name="relocateContent" access="public" output="false" returntype="void" hint="Update Section">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="oldParent" type="numeric" required="true">
		<cfargument name="newParent" type="numeric" required="true">
		
		<cfset var local = structNew()>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryUpdate">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				declare @newSectionID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.newParent#">;
				declare @oldSectionID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.oldParent#">;
				declare @siteID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">;

				BEGIN TRAN;
					UPDATE dbo.cms_pageSections
					SET parentSectionID = @newSectionID
					WHERE parentSectionID = @oldSectionID
					AND siteID = @siteID;
				
					UPDATE dbo.cms_documents
					SET sectionID = @newSectionID
					WHERE sectionID = @oldSectionID
					AND siteID = @siteID;

					UPDATE dbo.cms_pages
					SET sectionID = @newSectionID
					WHERE sectionID = @oldSectionID
					AND siteID = @siteID;
				COMMIT TRAN;

				exec dbo.cache_cms_updateRecursivePageSections @siteID = @siteID, @restrictToSectionID = NULL;
				exec dbo.cache_cms_updateDerivedPageSectionSettings @restrictToSiteID = @siteID;
				exec dbo.cms_updateMenuUsageAutoCorrect @restrictToSiteID = @siteID;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH		
		</cfquery>
	</cffunction>

	<cffunction name="getPageTypes" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="includeApplicationCreatedSections" type="boolean" required="no" default="false">

		<cfset var qryPageTypes = "">

		<cfquery name="qryPageTypes" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;

			SELECT DISTINCT at.applicationTypeID, at.applicationTypeDesc 
			FROM dbo.cms_pages page
			INNER JOIN dbo.cms_siteResources pageResource on page.siteResourceID = pageResource.siteResourceID
				AND pageResource.siteID = @siteID
				AND pageResource.isVisible = 1
			<cfif not arguments.includeApplicationCreatedSections>
				inner join dbo.cache_cms_derivedPageSectionSettings dps on dps.sectionID = page.sectionID
					and dps.isAppSectionDescendant = 0
			</cfif>
			INNER JOIN dbo.cms_siteResources AppResource on AppResource.parentSiteResourceID = pageResource.siteResourceID
				AND AppResource.siteID = @siteID
			INNER JOIN dbo.cms_applicationInstances ai on ai.siteResourceID = AppResource.siteResourceID
				AND ai.siteID = @siteID
			INNER JOIN dbo.cms_applicationTypes at on ai.applicationTypeID = at.applicationTypeID
			WHERE page.siteID = @siteID
				UNION ALL
			SELECT 0 as applicationTypeID, 'Content Page' AS applicationTypeDesc
			ORDER BY applicationTypeDesc;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryPageTypes>
	</cffunction>

	<cffunction name="addPermissionForZoneContent" access="public" output="false" returntype="void">
		<cfargument name="siteResourceID" type="numeric" required="true">
		<cfargument name="groupID" type="string" required="true">

		<cfset var local = structNew()>

		<cfif len(arguments.groupID)>
			<cfscript>
			local.objPermissionAdmin = createObject("component","model.admin.permissions.permissionAdmin");
			local.qryFunctions = local.objPermissionAdmin.getFunctionsQuery(siteResourceID=arguments.siteResourceID, functionName='View');
			local.arrGroups = listToArray(arguments.groupID);
			for (local.i = 1; local.i lte arrayLen(local.arrGroups); local.i = local.i + 1) {
				local.objPermissionAdmin.insertPermission(resourceID=arguments.siteResourceID, include=1, functionIDList=local.qryFunctions.functionID, 
					roleID=0, groupIDList=local.arrGroups[local.i], inheritedRightsResourceID=0, inheritedRightsFunctionID=0);
			}
			</cfscript>
		</cfif>
	</cffunction>

	<cffunction name="importPages" access="package" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfargument name="qryImportColumns" type="query" required="yes">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.errMsg = "">
		
  		<cftry>
  			<cfset local.strSQLPrep = createObject("component","model.admin.common.modules.import.import").prepBCPToTableSQL(event=arguments.event, qryImportColumns=arguments.qryImportColumns, importTableName='##mc_pagesImport')>
			<cfif not local.strSQLPrep.success>
				<cfthrow message="There was an error processing final import.">
			</cfif>

			<cfquery name="local.qryImport" datasource="#application.dsn.membercentral.dsn#" result="local.qryImportResult">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					IF OBJECT_ID('tempdb..##mc_pagesImport') IS NOT NULL 
						DROP TABLE ##mc_pagesImport;

					DECLARE @siteID int, @runByMemberID int, @importResult xml;
					SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteID')#">;
					SET @runByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcUser.memberData.memberID#">;

					-- bcp to table
					BEGIN TRY
						#PreserveSingleQuotes(local.strSQLPrep.sql)#
					END TRY
					BEGIN CATCH
						select @importResult = '<import><errors><error msg="Unable to import the file for processing." /><error msg="' + error_message() + '" /></errors></import>';
						GOTO on_done;
					END CATCH
					
					-- import file
					BEGIN TRY
						SET @importResult = null;
						EXEC dbo.cms_importPages @siteID=@siteID, @runByMemberID=@runByMemberID, @importResult=@importResult OUTPUT;
					END TRY
					BEGIN CATCH
						select @importResult = '<import><errors><error msg="Unable to process the import file." /><error msg="' + error_message() + '" /></errors></import>';
						GOTO on_done;
					END CATCH
				
					on_done:
					DECLARE @errCount int;
					SELECT @errCount = @importResult.value('count(/import/errors/error)','int');
					SELECT @importResult as importResult, @errCount as errCount; 

					IF OBJECT_ID('tempdb..##mc_pagesImport') IS NOT NULL 
						DROP TABLE ##mc_pagesImport;
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
			<cfset local.returnStruct.importResultXML = xmlparse(local.qryImport.importResult)> 
			<cfset local.returnStruct.errCount = local.qryImport.errCount>
			<cfif local.returnStruct.errCount gt 0>
				<cfset local.returnStruct.success = false>
				<cfset local.returnStruct.strImportDetails = getPageImportDetails(event=arguments.event, qryImportColumns=arguments.qryImportColumns)>
			</cfif>
			<cfcatch type="Any">
				<cfset local.returnStruct.success = false>
				<cfset local.returnStruct.errMsg = "There was a problem importing the files. Try the upload again or contact us for assistance.">
				<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			 </cfcatch> 
		</cftry>	
		
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getPageImportDetails" access="private" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfargument name="qryImportColumns" type="query" required="yes">

		<cfset var local = structNew()>
		
		<cfset local.strFormFields = structNew()>
		<cfset local.arrImportColumnDetails = arrayNew(1)>

		<cfset structInsert(local.strFormFields, 'resourceType', arguments.event.getValue('resourceType',''))>
		<cfset structInsert(local.strFormFields, 'bcpfilename', arguments.event.getValue('bcpfilename',''))>
		<cfset structInsert(local.strFormFields, 'uploadedFileFieldList', arguments.event.getValue('uploadedFileFieldList',''))>

		<cfloop query="arguments.qryImportColumns">
			<cfset local.tmpStr = { columnID=arguments.qryImportColumns.columnID, mappedColValue=arguments.event.getValue('mcimpcol_map_#arguments.qryImportColumns.columnID#',''),
									mappedColOverrideValue='' }>
			<cfif local.tmpStr.mappedColValue EQ '_override_value_'>
				<cfset local.tmpStr.mappedColOverrideValue = arguments.event.getValue('mcimpcol_override_#arguments.qryImportColumns.columnID#','')>
			</cfif>
			<cfset arrayAppend(local.arrImportColumnDetails, local.tmpStr)>
		</cfloop>

		<cfset local.strImportDetails = { strFormFields=local.strFormFields, arrImportColumnDetails=local.arrImportColumnDetails }>

		<cfreturn local.strImportDetails>
	</cffunction>

	<cffunction name="showImportResults" access="package" output="false" returntype="string">
		<cfargument name="strImportResult" type="struct" required="yes">
		<cfargument name="doAgainURL" type="string" required="yes">

		<cfscript>
			var local = structNew();
			local.data = '';
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<cfif NOT arguments.strImportResult.success>
				<div id="divPageImportErrorScreen" class="alert alert-danger">
					<h4>Import Results</h4>
					<div><b>The import was stopped and requires your attention.</b></div>
					<br/>			

					<cfif structKeyExists(arguments.strImportResult,"importResultXML")>
						<cfset local.arrErrors = XMLSearch(arguments.strImportResult.importResultXML,"/import/errors/error")>
						<div>
						<cfif arrayLen(local.arrErrors) gt 200>
							<b>Only the first 200 errors are shown.</b><br/><br/>
						</cfif>
						<cfset local.thisErrNum = 0>
						<cfloop array="#local.arrErrors#" index="local.thisErr">
							<cfset local.thisErrNum = local.thisErrNum + 1>
							#local.thisErr.xmlAttributes.msg#<br/>
							<cfif local.thisErrNum is 200>
								<cfbreak>
							</cfif>
						</cfloop>
						</div>
					<cfelse>
						<div>#arguments.strImportResult.errMsg#</div>
					</cfif>

					<br/>
					<button class="btn btn-sm btn-secondary" name="btnDoOver" type="button" onClick="self.location.href='#arguments.doAgainURL#';">Try upload again</button>
					<cfif structKeyExists(arguments.strImportResult,"previousMappingScreen")>
						<a href="##" class="btn btn-sm btn-secondary" onclick="$('##divPageImportErrorScreen').hide();$('##divPageImportMappingScreen').show(300);return false;">Return to Column Mapping</a>
					</cfif>
				</div>
				<cfif structKeyExists(arguments.strImportResult,"previousMappingScreen")>
					<div id="divPageImportMappingScreen" style="display:none;">
						#arguments.strImportResult.previousMappingScreen#
					</div>
				</cfif>
			<cfelse>
				<h4>Import Results</h4>
				<p class="font-weight-bold">Import Has Been Scheduled</p>
				<div class="mb-2">
					The import of the uploaded file has been scheduled and will begin shortly.<br/>
					You will be sent an e-mail with the results of the import.
				</div>
				<button class="btn btn-sm btn-secondary" name="btnDoOver" type="button" onClick="self.location.href='#arguments.doAgainURL#';">Upload another file</button>
			</cfif>
			</cfoutput>
		</cfsavecontent>
			
		<cfreturn local.data>
	</cffunction>

	<cffunction name="addToImportPagesJSONQueue" access="public" output="false" returntype="struct">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="itemGroupUID" type="string" required="true">
		<cfargument name="pageJSONContent" type="string" required="true">
		
		<cfset var local = structNew()>

		<cfquery name="local.qryImportJSONInsert" datasource="#application.dsn.platformQueue.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				DECLARE @submittedMemberID int, @statusReady INT, @queueTypeID INT, @nowDate DATETIME;
				SET @submittedMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">;
				SELECT @queueTypeID = queueTypeID FROM platformQueue.dbo.tblQueueTypes WHERE queueType = 'importPagesJSON';
				SELECT @statusReady = queueStatusID FROM platformQueue.dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID AND queueStatus = 'ReadyToProcess';
				SET @nowDate = getdate();

				INSERT INTO dbo.queue_importPagesJSON (itemGroupUID, siteID, submittedMemberID, pageJSON, statusID, dateAdded, dateUpdated)
				VALUES (
					<cfqueryparam cfsqltype="CF_SQL_IDSTAMP" value="#arguments.itemGroupUID#">,
					<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">,
					@submittedMemberID,
					<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.pageJSONContent#">,
					@statusReady,
					@nowDate,
					@nowDate
				);

				SELECT SCOPE_IDENTITY() AS itemID;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfset local.result["success"] = true>
		<cfset local.result["itemid"] = local.qryImportJSONInsert.itemID>

		<cfreturn local.result>
	</cffunction>

	<cffunction name="savePagesFilter" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();
			getPagesFilter();
			local.PagesFilter = application.mcCacheManager.sessionGetValue(keyname='PagesFilter',defaultValue={});
			local.PagesFilter_listFilter_hash = hash(serializeJSON(local.PagesFilter.listFilter), "SHA", "UTF-8");
			local.PagesFilter.listFilter.kw = arguments.event.getValue('kw','');
			local.PagesFilter.listFilter.shNP = arguments.event.getValue('shNP',0);
			local.PagesFilter.listFilter.ce = arguments.event.getValue('ce','');
			local.PagesFilter.listFilter.cs = arguments.event.getValue('cs','');
			local.PagesFilter.listFilter.me = arguments.event.getValue('me','');
			local.PagesFilter.listFilter.ms = arguments.event.getValue('ms','');
			local.PagesFilter.listFilter.pt = arguments.event.getValue('pt','');
			local.PagesFilter.listFilter.ps = arguments.event.getValue('ps',1);
			local.PagesFilter.listFilter.advancedMode = arguments.event.getValue('advancedMode',0);
			local.PagesFilter.listFilter.isSearch = arguments.event.getValue('isSearch',0);
			
			if (local.PagesFilter_listFilter_hash NEQ hash(serializeJSON(local.PagesFilter.listFilter), "SHA", "UTF-8")) 
				application.mcCacheManager.sessionSetValue(keyname='PagesFilter', value=local.PagesFilter);
			local.data.success = true;
		</cfscript>
		<cfreturn local.data>
	</cffunction>

	<cffunction name="getPagesFilter" access="public" output="false" returntype="struct">
		<cfscript>
			var local = structNew();
			local.tmpStr = { kw='', shNP=0, ce='', cs='', me='', ms='', pt='', 
				ps=1,advancedMode=0,isSearch=0};

			local.PagesFilter = application.mcCacheManager.sessionGetValue(keyname='PagesFilter',defaultValue={});
			local.PagesFilter_hash = hash(serializeJSON(local.PagesFilter), "SHA", "UTF-8");
			if (NOT structKeyExists(local.PagesFilter,"listFilter"))
				local.PagesFilter.listFilter = duplicate(local.tmpStr);
			
			for (local.thiskey in local.tmpStr) {
				if (not structKeyExists(local.PagesFilter.listFilter, local.thiskey))
					structInsert(local.PagesFilter.listFilter, local.thiskey, local.tmpStr[local.thiskey], true);
			}
			if (local.PagesFilter_hash NEQ hash(serializeJSON(local.PagesFilter), "SHA", "UTF-8")) 
				application.mcCacheManager.sessionSetValue(keyname='PagesFilter', value=local.PagesFilter);
		</cfscript>

		<cfreturn local.PagesFilter>
	</cffunction>
	<cffunction name="getSectionIDFromPageResourceID" access="public" output="false" returntype="numeric">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="siteResourceID" type="numeric" required="yes">
		
		<cfset var local = structNew()>
		
		<cfquery name="local.data" datasource="#application.dsn.membercentral.dsn#">
			SELECT 
				p.sectionID
			FROM dbo.cms_pages p
			WHERE p.siteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteResourceID#">
			AND p.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
		</cfquery>
		<cfset local.sectionID = 0 />
		<cfif local.data.recordCount>
			<cfset local.sectionID = local.data.sectionID />
		</cfif>
		
		<cfreturn local.sectionID>
	</cffunction>

</cfcomponent>