CREATE PROC dbo.sub_populateSubsActiveEmailsQueue
@taskID int,
@itemCount int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	
	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	DECLARE @orgID int, @queueTypeID int, @queueStatusID int, @nowDate datetime = getdate(),
		@activeStatusID int, @inactiveStatusID int, @activatedPaymentStatusID int, @taskCFC varchar(100),
		@thisMonthStart datetime, @dtNow dateTime, @dtLastRun datetime, @lastRunDate datetime;

	DECLARE @tblOrgs TABLE (orgID int PRIMARY KEY);
	DECLARE @subscribersToCheck TABLE (subscriberID int PRIMARY KEY, orgID int);
					
	SELECT @queueTypeID = queueTypeID from platformQueue.dbo.tblQueueTypes where queueType = 'subscriptionActiveEmails';
	SELECT @queueStatusID = queueStatusID from platformQueue.dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'readyToProcess';
	SELECT @taskCFC = taskCFC FROM dbo.scheduledTasks where taskid = @taskID;
	SELECT @activeStatusID = statusID from dbo.sub_statuses where statusCode = 'A';
	SELECT @inactiveStatusID = statusID from dbo.sub_statuses where statusCode = 'I';
	SELECT @activatedPaymentStatusID = statusID from dbo.sub_paymentStatuses where statusCode = 'P';

	SET @thisMonthStart = DATEFROMPARTS(YEAR(GETDATE()),MONTH(GETDATE()),1);
	SET @dtNow = DateAdd(Day, DateDiff(Day, 0, getDate()), 0);

	EXEC dbo.sched_getLastRunDate @taskCFC=@taskCFC, @defaultDate=@thisMonthStart, @lastDate=@dtLastRun OUTPUT;

	IF (DateDiff(dd, @dtLastRun, @dtNow) > 4)
		SET @lastRunDate = @dtNow;
	ELSE
		SET @lastRunDate = @dtLastRun;

	INSERT INTO @tblOrgs (orgID)
	select distinct s.orgID
	from dbo.sub_types as t
	inner join dbo.sites as s on s.siteID = t.typeID;

	select @orgID = min(orgID) from @tblOrgs;
	while @orgID is not null begin
		insert into @subscribersToCheck (subscriberID, orgID)
		select sth.subscriberID, @orgID
		from dbo.sub_statusHistory as sth
		inner join dbo.sub_subscriptions as subs on subs.orgID = @orgID
			and subs.subscriptionID = sth.subscriptionID
			and subs.emailTemplateID is not null
		where sth.orgID = @orgID
		and sth.statusID = @activeStatusID
		and sth.updateDate >= @lastRunDate
		and (sth.oldStatusID is null or sth.oldStatusID <> @inactiveStatusID);

		insert into @subscribersToCheck (subscriberID, orgID)
		select sth.subscriberID, @orgID
		from dbo.sub_paymentStatusHistory as sth
		inner join dbo.sub_subscribers as ss on ss.orgID = @orgID
			and ss.subscriberID = sth.subscriberID
		inner join dbo.sub_subscriptions as subs on subs.orgID = @orgID
			and subs.subscriptionID = ss.subscriptionID
			and subs.emailTemplateID is not null
		where sth.orgID = @orgID
		and sth.paymentStatusID = @activatedPaymentStatusID
		and sth.updateDate >= @lastRunDate
			except
		select subscriberID, @orgID
		from @subscribersToCheck;

		-- remove subscribers who have received notification previously from a subscriberID with matching subscriptionID
		-- also removes those who notification has been suppressed by an admin
		delete stc
		from @subscribersToCheck stc
		inner join dbo.sub_subscribers as ss on ss.orgID = @orgID and ss.subscriberID = stc.subscriberID
		inner join dbo.ams_members as m on m.orgID = @orgID and m.memberID = ss.memberID
		inner join dbo.ams_members as allMembers on allMembers.orgID = @orgID and allMembers.activeMemberID = m.activeMemberID
		inner join dbo.sub_subscribers as allss on allss.orgID = @orgID and allss.memberID = allMembers.memberID
			and allss.subscriptionID = ss.subscriptionID
		inner join dbo.ams_emailLog as el on el.subscriberID = allss.subscriberID;

		select @orgID = min(orgID) from @tblOrgs where orgID > @orgID;
	end

	INSERT INTO platformQueue.dbo.queue_subscriptionActiveEmails (orgID, siteID, subscriberID, statusID, dateAdded, dateUpdated)
	select s.orgID, t.siteID, s.subscriberID, @queueStatusID, @nowDate, @nowDate
	from @subscribersToCheck stc
	inner join dbo.sub_subscribers s on s.subscriberID = stc.subscriberID
	inner join dbo.sub_subscriptions subs on subs.subscriptionID = s.subscriptionID
		and subs.emailTemplateID is not null
	inner join dbo.et_emailTemplates et on et.templateID = subs.emailTemplateID
	inner join dbo.sub_types t on t.typeID = subs.typeID
	inner join dbo.sub_statuses st on st.statusID = s.statusID
		and st.statusCode = 'A'
	inner join dbo.sub_paymentStatuses pst on pst.statusID = s.paymentStatusID
		and pst.statusCode = 'P'
		except
	select orgID, siteID, subscriberID, statusID, @nowDate, @nowDate
	from platformQueue.dbo.queue_subscriptionActiveEmails;

	SET @itemCount = @@ROWCOUNT;

	IF @itemCount > 0
		EXEC dbo.sched_resumeTask @name='Process Subscription Active Emails Queue', @engine='MCLuceeLinux';

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO