ALTER PROC dbo.sendgrid_createSubusers
@siteID int,
@siteCode varchar(10)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @tier varchar(12);
	select @tier = tier from membercentral.dbo.fn_getServerSettings();

	if @tier <> 'Production'
		GOTO on_done;
	if @siteCode is null 
		GOTO on_done;

	DECLARE @subuserTransactionalID int, @subuserMarketingID int, @sysMemberID int, @queueTypeID int, @environmentID int,
			@statusReadyToCreate int, @domainAuthQueueTypeID int, @statusWaitingForSubuserCreation int, @xmlMessage xml, @statusID int,
			@ippool_medium INT,  @ippool_renewal INT, @ippool_transactional INT,
			@mailstreamID_marketing int, @mailstreamID_transactional int, @mailstreamID_renewal int

	select @mailstreamID_marketing = mailStreamID from email_mailstreams where mailStreamCode = 'MKT'
	select @mailstreamID_transactional = mailStreamID from email_mailstreams where mailStreamCode = 'TXN'
	select @mailstreamID_renewal = mailStreamID from email_mailstreams where mailStreamCode = 'RENEWAL'

	select @ippool_medium = ipPoolID from sendgrid_ipPools where poolName = 'Medium'
	select @ippool_transactional = ipPoolID from sendgrid_ipPools where poolName = 'Transactional'
	select @ippool_renewal = ipPoolID from sendgrid_ipPools where poolName = 'Renewal'

	select @environmentID = environmentID from membercentral.dbo.platform_environments where environmentName='production'

	select @queueTypeID = queueTypeID from platformQueue.dbo.tblQueueTypes where queueType = 'SendgridSubuserCreate';
	select @statusReadyToCreate = queueStatusID from platformQueue.dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'ReadyToCreate';    
	select @domainAuthQueueTypeID = queueTypeID from platformQueue.dbo.tblQueueTypes where queueType = 'SendgridSubuserDomainAuth';
	select @statusWaitingForSubuserCreation = queueStatusID from platformQueue.dbo.tblQueueStatuses where queueTypeID = @domainAuthQueueTypeID and queueStatus = 'waitingForSubuserCreation';
	select @sysMemberID = membercentral.dbo.fn_ams_getMCSystemMemberID();
	set @siteCode = UPPER(@siteCode);

	select @statusID = subuserStatusID from dbo.sendgrid_subuserStatuses where [status] = 'Not Created';
	if @statusID is null 
		GOTO on_done;

	-- Create Transactional subuser
	insert into dbo.sendgrid_subusers (siteID, firstname, lastname, email, username, [password], environmentID, statusID)
	values (@siteID, @sitecode, 'Transactional', '<EMAIL>', 'membercentral_' + LOWER(@sitecode) + '_transactional', 
		@sitecode + '-' + CAST(NEWID() as varchar(36)), @environmentID, @statusID);
	select @subuserTransactionalID = SCOPE_IDENTITY();

	-- Create Marketing subuser
	insert into dbo.sendgrid_subusers (siteID, firstname, lastname, email, username, [password], environmentID, statusID)
	values (@siteID, @sitecode, 'Marketing', '<EMAIL>', 'membercentral_' + LOWER(@sitecode) + '_marketing', 
		@sitecode + '-' + CAST(NEWID() as varchar(36)), @environmentID, @statusID);
	select @subuserMarketingID = SCOPE_IDENTITY();

	-- Create Listservers subuser (This will be down in a job in OPS-374)

	-- Create the subuser mailstream relationships default <NAME_EMAIL>(firstname).membercentral.org for transactional
	insert into dbo.sendgrid_subuserMailstreams (subuserID, mailstreamID, ipPoolID, defaultFromUsername)
	select subuserid, @mailstreamID_transactional as mailstreamId, @ippool_renewal as ipPoolID, 'noreply' as defaultFromUsername
	from dbo.sendgrid_subusers 
	where subuserID = @subuserTransactionalID;

	insert into dbo.sendgrid_subuserMailstreams (subuserID, mailstreamID, ipPoolID, defaultFromUsername)
	select subuserid, @mailstreamID_renewal as mailstreamId, @ippool_transactional as ipPoolID, 'noreply' as defaultFromUsername
	from dbo.sendgrid_subusers 
	where subuserID = @subuserTransactionalID;


	-- Create the subuser mailstream relationships default <NAME_EMAIL>(firstname).membercentral.org for marketing
	insert into dbo.sendgrid_subuserMailstreams (subuserID, mailstreamID, ipPoolID, defaultFromUsername)
	select subuserid, @mailstreamID_marketing as mailstreamId, @ippool_medium as ipPoolID, 'noreply' as defaultFromUsername
	from dbo.sendgrid_subusers 
	where subuserID = @subuserMarketingID;

	-- Create the subuser mailstream relationships default <NAME_EMAIL>(firstname).membercentral.org for listservers (OPS-374)

	-- populate sendgrid_subuserDomains and activeSubuserDomainID for subuser
	insert into dbo.sendgrid_subuserDomains (subuserID, dateCreated, sendingHostname, linkBrandHostname, statusID)
	select subuserID, getdate(), lower(left(lastname,1) + '.' + firstname + '.membercentral.org'),
		'link.' + lower(left(lastname,1) + '.' + firstname + '.membercentral.org'), @statusID
	from dbo.sendgrid_subusers
	where subuserID in (@subuserTransactionalID, @subuserMarketingID);
    
	update u
	set activeSubuserDomainID = sud.subuserDomainID
	from dbo.sendgrid_subuserDomains sud
	inner join dbo.sendgrid_subusers u
	on sud.subuserID = u.subuserID
	and u.subuserID in (@subuserTransactionalID, @subuserMarketingID);

	BEGIN TRAN;
		-- transactional subuser
		INSERT INTO platformQueue.dbo.queue_sendgridSubuserCreate (siteID, subUserID, subUserIDType, recordedByMemberID, statusID, dateAdded, dateUpdated)
		VALUES (@siteID, @subuserTransactionalID, 'subuserTransactionalID', @sysMemberID, @statusReadyToCreate, getdate(), getdate());

		INSERT INTO platformQueue.dbo.queue_SendgridSubuserDomainAuth (siteID, subUserID, subUserIDType, recordedByMemberID, statusID, dateAdded, dateUpdated)
		VALUES (@siteID, @subuserTransactionalID, 'subuserTransactionalID', @sysMemberID, @statusWaitingForSubuserCreation, getdate(), getdate());

		-- marketing subuser
		INSERT INTO platformQueue.dbo.queue_sendgridSubuserCreate (siteID, subUserID, subUserIDType, recordedByMemberID, statusID, dateAdded, dateUpdated)
		VALUES (@siteID, @subuserMarketingID, 'subuserMarketingID', @sysMemberID, @statusReadyToCreate, getdate(), getdate());

		INSERT INTO platformQueue.dbo.queue_SendgridSubuserDomainAuth (siteID, subUserID, subUserIDType, recordedByMemberID, statusID, dateAdded, dateUpdated)
		VALUES (@siteID, @subuserMarketingID, 'subuserMarketingID', @sysMemberID, @statusWaitingForSubuserCreation, getdate(), getdate());

		-- listservers subuser (OPS-374)

		-- resume Subuser Create task
		EXEC memberCentral.dbo.sched_resumeTask @name='Sendgrid Subuser Create Queue', @engine='BERLinux';

		-- resume Domain Auth task
		EXEC memberCentral.dbo.sched_resumeTask @name='Sendgrid Domain Auth Queue', @engine='BERLinux';

	COMMIT TRAN;

	on_done:
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
