ALTER PROC dbo.DBA_scannedRenewalsImport
@profileID int,
@batchID int,
@runByMemberID int,
@importResult xml OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tblImportErrors') IS NOT NULL 
		DROP TABLE #tblImportErrors;
	CREATE TABLE #tblImportErrors (rowid int IDENTITY(1,1), msg varchar(300));

	-- ensure temp table exists
	IF OBJECT_ID('tempdb..#DBA_scannedRenewalsImport') IS NULL BEGIN
		INSERT INTO #tblImportErrors (msg)
		VALUES ('Unable to locate the imported data for processing.');

		GOTO on_done;
	END

	DECLARE @orgID int, @siteID int, @gatewayType varchar(30), @subscriberIDList varchar(max), @itemGroupUID uniqueidentifier, @markSubsAsAcceptedImportResult xml, 
		@overridePendingPayment bit, @statusReady int, @queueTypeID int;
	
	SET @importResult = null;

	SELECT @orgID = orgID, @siteID = siteID 
	FROM memberCentral.dbo.sites 
	WHERE siteCode = 'DBA';

	SELECT @gatewayType = g.gatewayType
	FROM memberCentral.dbo.mp_profiles as mp
	INNER JOIN memberCentral.dbo.mp_gateways as g on g.gatewayID = mp.gatewayID
	WHERE mp.profileID = @profileID;

	SELECT @queueTypeID = queueTypeID
	FROM platformQueue.dbo.tblQueueTypes as qt
	WHERE qt.queueType = 'subscriptionAccept';

	SELECT @statusReady = qs.queueStatusID 
	FROM platformQueue.dbo.tblQueueStatuses as qs
	WHERE qs.queueTypeID = @queueTypeID
	AND qs.queueStatus = 'readyToProcess';


	-- ************************
	-- validate columns 
	-- ************************
	BEGIN TRY
		
		-- no blank MemberNumber
		update #DBA_scannedRenewalsImport set MemberNumber = '' where MemberNumber is null;

		INSERT INTO #tblImportErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' is missing MemberNumber.'
		FROM #DBA_scannedRenewalsImport
		WHERE MemberNumber = ''
		ORDER BY rowID;
			IF @@ROWCOUNT > 0 GOTO on_done;

		-- match on member
		update tmp 
		set tmp.MCMemberID = m.memberID
		from #DBA_scannedRenewalsImport as tmp 
		inner join memberCentral.dbo.ams_members as m on m.memberNumber = tmp.MemberNumber
			and m.orgID = @orgID
			and m.memberID = m.activeMemberID
			and m.status <> 'D';

		-- check for missing members
		BEGIN TRY
			ALTER TABLE #DBA_scannedRenewalsImport ALTER COLUMN MCMemberID int not null;
		END TRY
		BEGIN CATCH
			INSERT INTO #tblImportErrors (msg)
			SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ': ' + MemberNumber + ' does not match an existing member.'
			FROM #DBA_scannedRenewalsImport
			WHERE MCMemberID IS NULL
			ORDER BY rowID;
				IF @@ROWCOUNT > 0 GOTO on_done;
		END CATCH


		-- check for missing subscriberID
		INSERT INTO #tblImportErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' is missing SubscriberID.'
		FROM #DBA_scannedRenewalsImport
		WHERE SubscriberID IS NULL
		ORDER BY rowID;
			IF @@ROWCOUNT > 0 GOTO on_done;

		-- no dupe subscriberIDs
		INSERT INTO #tblImportErrors (msg)
		SELECT TOP 100 PERCENT 'SubscriberID ' + cast(SubscriberID as varchar(10)) + ' appears multiple times; they must be unique.'
		FROM #DBA_scannedRenewalsImport
		GROUP BY SubscriberID
		HAVING COUNT(*) > 1
		ORDER BY 1;
			IF @@ROWCOUNT > 0 GOTO on_done;

		-- verify subscriberID
		INSERT INTO #tblImportErrors (msg)
		SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' does not match an existing subscriber.'
		FROM #DBA_scannedRenewalsImport AS tmp
		LEFT OUTER JOIN memberCentral.dbo.sub_subscribers AS s 
				INNER JOIN memberCentral.dbo.ams_members AS m ON m.orgID = @orgID and m.memberID = s.memberID
				INNER JOIN memberCentral.dbo.ams_members AS mActive ON mActive.memberID = m.activeMemberID
				INNER JOIN memberCentral.dbo.sub_subscriptions AS sub ON sub.subscriptionID = s.subscriptionID
					AND s.subscriberID = s.rootSubscriberID
			ON s.subscriberID = tmp.subscriberID
		WHERE tmp.MCMemberID <> ISNULL(mActive.memberID,0)
		ORDER BY tmp.rowID;
			IF @@ROWCOUNT > 0 GOTO on_done;


		-- clean AmountToCharge
		update #DBA_scannedRenewalsImport set AmountToCharge = memberCentral.dbo.fn_regexReplace(AmountToCharge,'[^0-9\.\-\(\)]','') where AmountToCharge is not null;

		-- check for null or negative AmountToCharge
		BEGIN TRY
			ALTER TABLE #DBA_scannedRenewalsImport ALTER COLUMN AmountToCharge decimal(18,2) not null;
			ALTER TABLE #DBA_scannedRenewalsImport ADD CONSTRAINT AmountToChargeCheck CHECK (AmountToCharge >= 0);
		END TRY
		BEGIN CATCH
			INSERT INTO #tblImportErrors (msg)
			SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' does not have a positive AmountToCharge amount.'
			FROM #DBA_scannedRenewalsImport
			WHERE AmountToCharge IS NULL OR AmountToCharge < 0
			ORDER BY rowID;
				IF @@ROWCOUNT > 0 GOTO on_done;
		END CATCH

		IF @gatewayType = 'OfflineCash' BEGIN
			-- check for missing checknumber
			INSERT INTO #tblImportErrors (msg)
			SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' is missing CheckNumber.'
			FROM #DBA_scannedRenewalsImport
			WHERE CheckNumber = ''
			ORDER BY rowID;
				IF @@ROWCOUNT > 0 GOTO on_done;

			IF ISNULL(@batchID,0) = 0 BEGIN
				INSERT INTO #tblImportErrors (msg)
				VALUES ('Select a valid open batch.');
				GOTO on_done;
			END

			-- check for missing paymentDate
			INSERT INTO #tblImportErrors (msg)
			SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' is missing Payment Date.'
			FROM #DBA_scannedRenewalsImport
			WHERE PaymentDate IS NULL
			ORDER BY rowID;
				IF @@ROWCOUNT > 0 GOTO on_done;

			-- check for invalid paymentDate
			BEGIN TRY
				ALTER TABLE #DBA_scannedRenewalsImport ALTER COLUMN PaymentDate date not null;
			END TRY
			BEGIN CATCH
				INSERT INTO #tblImportErrors (msg)
				VALUES ('Import file contains invalid payment dates.');
				GOTO on_done;
			END CATCH

			SET @overridePendingPayment = 1;
			
		END
		ELSE BEGIN
			-- check for missing payprofileid
			INSERT INTO #tblImportErrors (msg)
			SELECT TOP 100 PERCENT 'Row ' + cast(rowID as varchar(10)) + ' is missing PayProfileID.'
			FROM #DBA_scannedRenewalsImport
			WHERE PayProfileID IS NULL
			ORDER BY rowID;
				IF @@ROWCOUNT > 0 GOTO on_done;

			SET @batchID = NULL;
			SET @overridePendingPayment = 0;
		END


	END TRY
	BEGIN CATCH
		INSERT INTO #tblImportErrors (msg)
		VALUES ('Unable to validate data in required columns.');

		INSERT INTO #tblImportErrors (msg)
		VALUES (left(error_message(),300));

		GOTO on_done;
	END CATCH


	-- ************************
	-- queue import data
	-- ************************
	BEGIN TRY
		SELECT @subscriberIDList = COALESCE(@subscriberIDList + ',', '') + cast(subscriberID as varchar(10)) 
		FROM #DBA_scannedRenewalsImport;

		IF @subscriberIDList IS NOT NULL BEGIN
			BEGIN TRAN;
			
				EXEC memberCentral.dbo.sub_queueMarkAccepted @recordedByMemberID=@runByMemberID, @subscriberIDList=@subscriberIDList, 
					@suppressEmails=0, @markQueueAsReady=0, @importResult=@markSubsAsAcceptedImportResult OUTPUT;

				SELECT @itemGroupUID = @markSubsAsAcceptedImportResult.value('(/import/@itemGroupUID)[1]','uniqueidentifier');

				-- update payment information & mark queue as ready
				UPDATE qi
				SET qi.merchantProfileID = @profileID,
					qi.paymentDate = tmp.PaymentDate,
					qi.batchID = @batchID,
					qi.memberPayProfileID = tmp.PayProfileID,
					qi.checkNumber = tmp.checkNumber,
					qi.amountToCharge = tmp.amountToCharge,
					qi.overridePendingPayment = @overridePendingPayment,
					qi.statusID = @statusReady,
					qi.dateUpdated = getdate()
				FROM platformQueue.dbo.queue_subscriptionAccept as qi
				INNER JOIN #DBA_scannedRenewalsImport as tmp on tmp.subscriberID = qi.subscriberID
				WHERE qi.itemGroupUID = @itemGroupUID;
			COMMIT TRAN;
		END

	END TRY
	BEGIN CATCH
		IF @@trancount > 0 ROLLBACK TRANSACTION;

		INSERT INTO #tblImportErrors (msg)
		VALUES ('Unable to add data to the queue.');

		INSERT INTO #tblImportErrors (msg)
		VALUES (left(error_message(),300));
	END CATCH

	

	-- ************************
	-- generate result xml file 
	-- ************************
	on_done:
	select @importResult = (
		select getdate() as "@date",
			isnull((select top 100 PERCENT memberCentral.dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg"
			from #tblImportErrors
			order by rowid
			FOR XML path('error'), root('errors'), type),'<errors/>')
		for xml path('import'), TYPE);

	IF OBJECT_ID('tempdb..#tblImportErrors') IS NOT NULL 
		DROP TABLE #tblImportErrors;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
