ALTER PROC dbo.sw_populateCompleteSeminarReminder
@itemCount int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @queueStatusID int, @nowDate datetime = getdate(), @participantID int, @orgID int, 
		@emailTagTypeID int, @emailsubject varchar(255), @selectedTimeframes varchar(50);
	DECLARE @tblParticipants TABLE (participantID int, orgID int, emailTagTypeID int, selectedTimeframes varchar(50), emailsubject varchar(255));
	DECLARE @tblTimeFrames TABLE (timeframe int);
	DECLARE @tblTimeFrames2 TABLE (participantID int, timeframe int, dateValue date);
	SELECT @queueTypeID = queueTypeID FROM platformQueue.dbo.tblQueueTypes WHERE queueType = 'completeSeminarReminder';
	SELECT @queueStatusID = queueStatusID FROM platformQueue.dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID AND queueStatus = 'readyToProcess';
	SET @itemCount = 0;

	-- emailOption1 is based on enrollment date
	INSERT INTO @tblParticipants (participantID, orgID, emailTagTypeID, selectedTimeframes, emailsubject)
	SELECT p.participantID, s.orgID, etag.emailTagTypeID, st.selectedTimeframes, st.emailSubject
	FROM dbo.tblSeminarsSWODTasks AS st
	INNER JOIN dbo.tblParticipants AS p ON p.participantID = st.participantID and p.isActive = 1
	INNER JOIN membercentral.dbo.sites as s on s.siteCode = p.orgcode
	INNER JOIN membercentral.dbo.ams_memberEmailTagTypes as etag ON etag.orgID = s.orgID and etag.emailTagType = 'Primary'
	WHERE st.isCompletionReminderEnabled = 1
	AND st.emailOption = 1;

	SELECT @participantID = min(participantID) FROM @tblParticipants;
	WHILE @participantID IS NOT NULL BEGIN
		SELECT @orgID = orgID, @emailTagTypeID = emailTagTypeID, @selectedTimeframes = selectedTimeframes, @emailsubject = emailsubject
		FROM @tblParticipants 
		WHERE participantID = @participantID;

		INSERT INTO @tblTimeFrames (timeframe)
		SELECT listitem
		FROM membercentral.dbo.fn_intListToTableInline(@selectedTimeframes,',');
		
		IF @@ROWCOUNT > 0 BEGIN
			INSERT INTO platformQueue.dbo.queue_completeSeminarReminder (orgcode, memberID, email, firstName, lastName, overrideEmail,
				EmailFrom, TimeLapse, CatalogURL, seminarID, seminarName, dateEnrolled, supportPhone, supportEmail,
				lastdatetoComplete, statusID, dateAdded, dateUpdated, emailOptionID, orgIdentityID, emailsubject)
			SELECT DISTINCT p.orgcode, m2.memberID, me.email, m2.firstName, m2.lastName, ISNULL(eo.email,''), 
				p.EmailFrom, DATEDIFF(dd,e.dateEnrolled,getdate()), p.CatalogURL, s.seminarID, s.seminarName, e.dateEnrolled, 
				p.supportPhone, p.supportEmail, null, @queueStatusID, @nowDate, @nowDate, 1, p.orgIdentityID, @emailsubject
			FROM dbo.tblEnrollments AS e
			INNER JOIN dbo.tblParticipants AS p ON e.participantID = p.participantID 
			INNER JOIN dbo.tblEnrollmentsSWOD AS eswod ON e.enrollmentID = eswod.enrollmentID 
			INNER JOIN dbo.tblSeminars AS s ON s.seminarID = e.seminarID 
				AND s.isPublished = 1 
				AND s.isDeleted = 0
			INNER JOIN memberCentral.dbo.ams_members AS m on m.orgID = @orgID and m.memberID = e.MCMemberID
			INNER JOIN membercentral.dbo.ams_members AS m2 on m2.orgID = @orgID and m2.memberID = m.activeMemberID
			INNER JOIN memberCentral.dbo.ams_memberEmails AS me on me.orgID = @orgID and me.memberID = m2.memberID
			INNER JOIN memberCentral.dbo.ams_memberEmailTags AS metag ON metag.orgID = @orgID 
				and metag.emailTagTypeID = @emailTagTypeID 
				and metag.memberID = me.memberID 
				AND metag.emailTypeID = me.emailTypeID
			LEFT OUTER JOIN memberCentral.dbo.ams_emailAppOverrides AS eo ON eo.itemID = e.enrollmentID 
				AND eo.itemType = 'semwebreg'
			WHERE e.participantID = @participantID
			AND e.dateCompleted IS NULL
			AND e.isActive = 1
			AND DATEDIFF(dd,e.dateEnrolled,getdate()) in (select timeframe from @tblTimeFrames);

			SET @itemCount = @itemCount + @@ROWCOUNT;

			DELETE FROM @tblTimeFrames;
		END

		SELECT @participantID = min(participantID) FROM @tblParticipants WHERE participantID > @participantID;
	END

	DELETE FROM @tblParticipants;


	-- emailOption2 is based on credit complete by date
	INSERT INTO @tblParticipants (participantID, orgID, emailTagTypeID, selectedTimeframes, emailsubject)
	SELECT p.participantID, s.orgID, etag.emailTagTypeID, st.selectedTimeframes, st.emailSubject
	FROM dbo.tblSeminarsSWODTasks AS st
	INNER JOIN dbo.tblParticipants AS p ON p.participantID = st.participantID and p.isActive = 1
	INNER JOIN membercentral.dbo.sites as s on s.siteCode = p.orgcode
	INNER JOIN membercentral.dbo.ams_memberEmailTagTypes as etag ON etag.orgID = s.orgID and etag.emailTagType = 'Primary'
	WHERE st.isCompletionReminderEnabled = 1
	AND st.emailOption = 2;

	INSERT INTO @tblTimeFrames2 (participantID, timeframe, dateValue)
	SELECT distinct p.participantID, li.listitem, DATEADD(DAY,li.listItem,@nowDate)
	FROM @tblParticipants as p
	CROSS APPLY membercentral.dbo.fn_intListToTableInline(p.selectedTimeframes,',') as li;

	SELECT @participantID = min(participantID) FROM @tblParticipants;
	WHILE @participantID IS NOT NULL BEGIN
		SELECT @orgID = orgID, @emailTagTypeID = emailTagTypeID, @selectedTimeframes = selectedTimeframes, @emailsubject = emailsubject
		FROM @tblParticipants 
		WHERE participantID = @participantID;

		IF (SELECT COUNT(*) FROM @tblTimeFrames2 where participantID = @participantID) > 0 BEGIN
			INSERT INTO platformQueue.dbo.queue_completeSeminarReminder (orgcode, memberID, email, firstName, lastName, overrideEmail,
				EmailFrom, TimeLapse, CatalogURL, seminarID, seminarName, dateEnrolled, supportPhone, supportEmail,
				lastdatetoComplete, statusID, dateAdded, dateUpdated, emailOptionID, orgIdentityID, emailsubject)
			SELECT DISTINCT p.orgcode, m2.memberID, me.email, m2.firstName, m2.lastName, ISNULL(eo.email,''), 
				p.EmailFrom, null, p.CatalogURL, s.seminarID, s.seminarName, e.dateEnrolled, p.supportPhone, p.supportEmail, 
				eac.lastdatetoComplete, @queueStatusID, @nowDate, @nowDate, 2, p.orgIdentityID, @emailsubject
			FROM dbo.tblEnrollments AS e
			INNER JOIN dbo.tblParticipants AS p ON e.participantID = p.participantID 
			INNER JOIN dbo.tblEnrollmentsSWOD AS eswod ON e.enrollmentID = eswod.enrollmentID 
			INNER JOIN dbo.tblSeminars AS s ON s.seminarID = e.seminarID 
				AND s.isPublished = 1 
				AND s.isDeleted = 0
			INNER JOIN dbo.tblEnrollmentsAndCredit as eac ON e.enrollmentID = eac.enrollmentID
				AND cast(eac.lastdatetoComplete as date) in (select dateValue from @tblTimeFrames2 where participantID = @participantID)
			INNER JOIN dbo.tblSeminarsAndCredit as sac on sac.seminarCreditID = eac.seminarCreditID
				and sac.creditCompleteByDate > @nowDate
			INNER JOIN memberCentral.dbo.ams_members AS m on m.orgID = @orgID and m.memberID = e.MCMemberID
			INNER JOIN membercentral.dbo.ams_members AS m2 on m2.orgID = @orgID and m2.memberID = m.activeMemberID
			INNER JOIN memberCentral.dbo.ams_memberEmails AS me on me.orgID = @orgID and me.memberID = m2.memberID
			INNER JOIN memberCentral.dbo.ams_memberEmailTags AS metag ON metag.orgID = @orgID 
				and metag.emailTagTypeID = @emailTagTypeID 
				and metag.memberID = me.memberID 
				AND metag.emailTypeID = me.emailTypeID
			LEFT OUTER JOIN memberCentral.dbo.ams_emailAppOverrides AS eo ON eo.itemID = e.enrollmentID 
				AND eo.itemType = 'semwebreg'
			WHERE e.participantID = @participantID
			AND e.dateCompleted IS NULL
			AND e.isActive = 1;
		
			SET @itemCount = @itemCount + @@ROWCOUNT;
		END

		SELECT @participantID = min(participantID) FROM @tblParticipants WHERE participantID > @participantID;
	END

	DELETE FROM @tblParticipants;

	-- resume task 
	IF @itemCount > 0
		EXEC membercentral.dbo.sched_resumeTask @name='Daily Complete Seminar Reminder', @engine='MCLuceeLinux';

	RETURN 0;
	
END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
