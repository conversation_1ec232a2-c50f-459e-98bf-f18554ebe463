ALTER PROC dbo.sw_deleteFile
@fileID int,
@pathToFileS varchar(400),
@pathToFileD varchar(400)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpFiles') IS NOT NULL 
		DROP TABLE #tmpFiles;
	CREATE TABLE #tmpFiles (objectKey varchar(800), fileSize bigint);

	DECLARE @s3DeleteReadyStatusID int, @siteResourceID int, @siteID int;
	
	IF(membercentral.dbo.fn_FileExists(@pathToFileS) = 1 OR membercentral.dbo.fn_FileExists(@pathToFileD) = 1)
	BEGIN
		select @s3DeleteReadyStatusID = qs.queueStatusID
		from platformQueue.dbo.tblQueueTypes as qt
		inner join platformQueue.dbo.tblQueueStatuses as qs on qs.queueTypeID = qt.queueTypeID
		where qt.queueType = 's3Delete'
		and qs.queueStatus = 'readyToProcess';

		-- import from file
		declare @fullSql varchar(max);
		IF(membercentral.dbo.fn_FileExists(@pathToFileS) = 1)
		BEGIN
			set @fullSql = 'BULK INSERT #tmpFiles FROM ''' + @pathToFileS + ''' WITH (FIELDTERMINATOR = ''|'')';
			EXEC(@fullSql);
		END
		IF(membercentral.dbo.fn_FileExists(@pathToFileD) = 1)
		BEGIN
			set @fullSql = 'BULK INSERT #tmpFiles FROM ''' + @pathToFileD + ''' WITH (FIELDTERMINATOR = ''|'')';
			EXEC(@fullSql);
		END

		INSERT INTO platformQueue.dbo.queue_S3Delete (statusID, s3bucketName, objectKey, dateAdded, dateUpdated)
		SELECT @s3DeleteReadyStatusID, 'seminarweb', objectKey, GETDATE(), GETDATE()
		FROM #tmpFiles;

		-- also delete any video previews
		IF(membercentral.dbo.fn_FileExists(@pathToFileS) = 1)
		BEGIN
			INSERT INTO platformQueue.dbo.queue_S3Delete (statusID, s3bucketName, objectKey, dateAdded, dateUpdated)
			select @s3DeleteReadyStatusID, 'seminarweb', LOWER('swod/' + sP.orgcode + '/' + cast(sP.participantID as varchar(10)) + '/' 
										+ cast(p.baseFileID as varchar(10)) + '_s_'
										+ cast(p.seminarID as varchar(10)) + '_preview.mp4'), 
					GETDATE(), GETDATE()
			from dbo.tblVideoPreviews as p
			inner join seminarWeb.dbo.tblSeminars as s on s.seminarID = p.seminarID
			inner join seminarWeb.dbo.tblParticipants as sP on sP.participantID = s.participantID
			where p.baseFileID = @fileID
			and p.isOnline = 1;
		END
	END
	
	SELECT @siteResourceID = f.siteResourceID, @siteID = mcs.siteID
	FROM dbo.tblFiles as f
	INNER JOIN dbo.tblParticipants as p on p.participantID = f.participantID
	INNER JOIN membercentral.dbo.sites as mcs on mcs.sitecode = p.orgcode
	WHERE f.fileID = @fileID;

	BEGIN TRAN;
		UPDATE dbo.tblFiles
		SET isDeleted = 1, 
			formatsAvailable = '<formats/>'
		WHERE fileID = @fileID;
		
		IF @siteResourceID IS NOT NULL
			EXEC membercentral.dbo.cms_deleteSiteResourceAndChildren @siteID=@siteID, @siteResourceID=@siteResourceID;

		DELETE FROM platformQueue.dbo.queue_SWVideoPreview
		WHERE baseFileID = @fileID;

		DELETE FROM dbo.tblVideoPreviews
		WHERE baseFileID = @fileID;

		DELETE FROM dbo.tblTitlesAndFiles
		WHERE fileID = @fileID;

		DELETE FROM dbo.tblSeminarAndFilesSyncPoints
		WHERE fileID = @fileID;

		DELETE FROM dbo.tblSeminarAndFilesSyncPoints
		WHERE linkedfileID = @fileID;
	COMMIT TRAN;

	IF OBJECT_ID('tempdb..#tmpFiles') IS NOT NULL 
		DROP TABLE #tmpFiles

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
