ALTER PROC dbo.sub_queueRenewals
@siteID int,
@orgID int,
@recordedByMemberID int,
@rescindDate datetime,
@overrideStartDate datetime, 
@subscriberIDList varchar(max)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpSubscribers') IS NOT NULL 
		DROP TABLE #tmpSubscribers;
	CREATE TABLE #tmpSubscribers (subscriberID int PRIMARY KEY, treeSize int);

	declare @queueTypeID int, @statusReady int;
	select @queueTypeID = queueTypeID from platformQueue.dbo.tblQueueTypes where queueType = 'subscriptionRenew';
	select @statusReady = queueStatusID from platformQueue.dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'readyToProcess';

	-- there should only be one itemGroupUID for these subscribers
	declare @itemGroupUID uniqueidentifier;
	set @itemGroupUID = NEWID();

	-- get subscribers to renew
	insert into #tmpSubscribers (subscriberID, treeSize)
	select tmp.subscriberID, count(schild.subscriberID)
	from (
		select s.subscriberID
		from dbo.fn_intListToTable(@subscriberIDList,',') as tmp
		inner join dbo.sub_subscribers as s on s.orgID = @orgID and s.subscriberID = tmp.listitem
			except
		select qid.subscriberID
		from platformQueue.dbo.queue_subscriptionRenew as qid
		inner join platformQueue.dbo.tblQueueStatuses as qs on qs.queueStatusID = qid.statusID
			and qs.queueStatus not in ('readyToNotify','grabbedForNotifying','done')
		where qid.orgID = @orgID
	) tmp
	inner join dbo.sub_subscribers as schild on schild.orgID = @orgID and schild.rootSubscriberID = tmp.subscriberID
	group by tmp.subscriberID;

	-- queue items
	insert into platformQueue.dbo.queue_subscriptionRenew (itemGroupUID, recordedByMemberID, orgID, siteID, subscriberID, 
		rescindDate, overrideStartDate, treeSize, statusID, dateAdded, dateUpdated)
	select @itemGroupUID, @recordedByMemberID, @orgID, @siteID, subscriberID, @rescindDate, @overrideStartDate,
		treeSize, @statusReady, getdate(), getdate()
	from #tmpSubscribers;

	EXEC dbo.sched_resumeTask @name='Process Subscription Renewal Queue', @engine='MCLuceeLinux';

	IF OBJECT_ID('tempdb..#tmpSubscribers') IS NOT NULL 
		DROP TABLE #tmpSubscribers;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
