ALTER PROCEDURE dbo.cache_perms_refreshSiteResourceRight 
@siteID int, 
@siteResourceID int,
@resourceRightID int 

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#holding_siteResourceRightsCache') IS NOT NULL
		DROP TABLE #holding_siteResourceRightsCache;
	IF OBJECT_ID('tempdb..#holding_siteResourceRightsCacheToDelete') IS NOT NULL
		DROP TABLE #holding_siteResourceRightsCacheToDelete;
	IF OBJECT_ID('tempdb..#holding_siteResourceRightsCacheToInsert') IS NOT NULL
		DROP TABLE #holding_siteResourceRightsCacheToInsert;
	IF OBJECT_ID('tempdb..#siteResourcesToProcess') IS NOT NULL
		DROP TABLE #siteResourcesToProcess;
	CREATE TABLE #holding_siteResourceRightsCache (autoid int IDENTITY(1,1), resourceID int, functionID int, groupID int,[include] bit, universalRoleResourceRightsID int, universalRoleResourceTypeID int, INDEX IX_holding_siteResourceRightsCache_include_functionID_ResourceID (include, functionID, resourceID));
	CREATE TABLE #holding_siteResourceRightsCacheToDelete (cachedRightsID int PRIMARY KEY, resourceID int);
	CREATE TABLE #holding_siteResourceRightsCacheToInsert (autoid int IDENTITY(1,1), resourceID int, functionID int, groupID int, [include] bit, universalRoleResourceRightsID int, universalRoleResourceTypeID int INDEX IX_holding_siteResourceRightsCacheToInsert_universalRoleResourceTypeID);
	CREATE TABLE #siteResourcesToProcess (siteResourceID int PRIMARY KEY, resourceTypeID int);

	DECLARE @newGroupIDs TABLE (autoid int IDENTITY(1,1), groupID int);
	DECLARE @isValidUniversalRoleAssignment bit, @orgID int, @membersToUpdateXML xml, @siteResourcesToProcessXML xml;

	select @orgID = dbo.fn_getOrgIDFromSiteID(@siteID);
	set @isValidUniversalRoleAssignment = 0;

	if (EXISTS (
		SELECT s.siteResourceID
		FROM sites s
		inner join cms_siteResourceRights srr on srr.resourceRightsID = @resourceRightID
			and s.siteResourceID = srr.resourceID
			and s.siteResourceID = @siteResourceID
			and s.siteID = @siteID
			and srr.siteID = @siteID
		inner join organizations o on s.orgID = o.orgID
		INNER JOIN ams_groups g on g.orgID in (o.orgID,1)
			and g.groupID = srr.groupID
		) and EXISTS (
			select srroles.roleID
			from cms_siteResourceRoles as srroles
			inner join cms_siteResourceRights srr on srr.resourceRightsID = @resourceRightID
				and srr.roleID = srroles.roleID
				and srr.siteID = @siteID
			INNER join cms_siteResourceRoleTypes as srrt on srroles.roleTypeID = srrt.roleTypeID
				and srrt.roleTypeName = 'UniversalRole'
		))
		 set @isValidUniversalRoleAssignment = 1;

	if (@isValidUniversalRoleAssignment = 0) BEGIN
		-- rebuild rights cache and add anything new
		-- similar code exists in three places -- here, cms_deleteSiteResourceRight, and cms_populateSiteResourceRightsCache
		;with downStreamInheritingResources as (
			-- find pool of resources that directly or indirectly inherit from this resource 
			select ResourceID, null as inheritedRightsResourceID
			from cms_siteResourceRights srr
			where ResourceID = @siteResourceID and srr.siteID = @siteID
				union all
			select srr.ResourceID, srr.inheritedRightsResourceID
			from cms_siteresourceRights srr
			inner join downStreamInheritingResources on downStreamInheritingResources.ResourceID = srr.inheritedRightsResourceID and srr.resourceID <> downStreamInheritingResources.resourceID
			where srr.siteID = @siteID
		),
		rightsRoleFunc as (
			-- get rights for functions
			SELECT srr.resourceID, srr.functionID, srr.groupID, srr.include, srr.inheritedRightsResourceID, srr.inheritedRightsFunctionID
			from dbo.cms_siteResourceRights as srr
			inner join dbo.cms_siteResources as sr on sr.siteResourceID = srr.resourceID and sr.siteID = @siteID
			inner join downStreamInheritingResources resourcePool on srr.resourceID = resourcePool.resourceID
			where srr.roleID is null 
			and srr.functionID is not null
			and srr.siteID = @siteID
				union all
			-- get rights for roles		
			SELECT srr.resourceID, srtf.functionID, srr.groupID, srr.include, srr.inheritedRightsResourceID, srr.inheritedRightsFunctionID
			from dbo.cms_siteResourceRights as srr
			inner join dbo.cms_siteResourceRoles as srroles on srroles.roleID = srr.RoleID
			inner join dbo.cms_siteResourceRoleTypes as srrt on srroles.roleTypeID = srrt.roleTypeID and srrt.roleTypeName = 'InstanceRole'
			inner join dbo.cms_siteResourceRoleFunctions as srrf on srroles.roleID = srrf.roleID
			INNER JOIN cms_siteResourceTypeFunctions srtf on srrf.resourceTypeFunctionID = srtf.resourceTypeFunctionID
			inner join dbo.cms_siteResources as sr on sr.siteResourceID = srr.resourceID and sr.siteID = @siteID
			inner join downStreamInheritingResources resourcePool on srr.resourceID = resourcePool.resourceID
			where srr.siteID = @siteID
		), resourceRights as (
			select resourceID as rootResourceID, functionID as rootFunctionID, resourceID, functionID, groupID, include, inheritedRightsResourceID, inheritedRightsFunctionID
			from rightsRoleFunc
				union all
			-- find upstream inheritance stuff (things that this resource inherits from)
			select rr.RootResourceID, rr.rootFunctionID, srr.resourceID, srr.functionID, srr.groupID, srr.include, srr.inheritedRightsResourceID, srr.inheritedRightsFunctionID
			from dbo.cms_siteResourceRights as srr
			inner join resourceRights as rr on rr.inheritedRightsResourceID = srr.resourceID
				and rr.inheritedRightsFunctionID = srr.functionID
			inner join dbo.cms_siteResources as sr on sr.siteResourceID = srr.resourceID and sr.siteID = @siteID
			where srr.siteID = @siteID
		)
		insert into #holding_siteResourceRightsCache (resourceID, functionID, groupID, [include],universalRoleResourceRightsID)
		select distinct rr.RootresourceID, rr.RootfunctionID, rr.groupID, rr.include, null as universalRoleResourceRightsID
		from resourceRights as rr
		where inheritedRightsResourceID is null 
		and inheritedRightsFunctionID is null
		OPTION(RECOMPILE);

		-- find holding_siteResourceRightsCacheToDelete
		insert into #holding_siteResourceRightsCacheToDelete (cachedRightsID, resourceID)
		select distinct srrc.cachedRightsID, srrc.resourceID
		from dbo.cms_siteResourceRightsCache srrc
		inner join (
			select distinct resourceID from #holding_siteResourceRightsCache
		) as affectedResources on affectedResources.resourceID = srrc.resourceID
		inner join dbo.cms_siteResources sr on affectedResources.ResourceID = sr.siteResourceID
			and sr.siteID = @siteID
		left outer join #holding_siteResourceRightsCache hold_srrc on hold_srrc.resourceID = srrc.resourceID
			and hold_srrc.functionID = srrc.functionID
			and isnull(hold_srrc.groupID,0) = isnull(srrc.groupID,0)
			and hold_srrc.[include] = srrc.[include]
			and isnull(hold_srrc.universalRoleResourceRightsID,0) = isnull(srrc.universalRoleResourceRightsID,0)
			and isnull(hold_srrc.universalRoleResourceTypeID,0) = isnull(srrc.universalRoleResourceTypeID,0)
		where hold_srrc.autoid is null and srrc.universalRoleResourceRightsID is null and srrc.siteID = @siteID;

		-- find #holding_siteResourceRightsCacheToInsert
		insert into #holding_siteResourceRightsCacheToInsert (resourceID, functionID, groupID, [include], universalRoleResourceRightsID, universalRoleResourceTypeID)
		select hold_srrc.resourceID, hold_srrc.functionID, hold_srrc.groupID, hold_srrc.[include], hold_srrc.universalRoleResourceRightsID, hold_srrc.universalRoleResourceTypeID
		from #holding_siteResourceRightsCache hold_srrc 
		left outer join dbo.cms_siteResourceRightsCache srrc on hold_srrc.resourceID = srrc.resourceID
			and hold_srrc.functionID = srrc.functionID
			and isnull(hold_srrc.groupID,0) = isnull(srrc.groupID,0)
			and hold_srrc.[include] = srrc.[include]
			and isnull(hold_srrc.universalRoleResourceRightsID,0) = isnull(srrc.universalRoleResourceRightsID,0)
			and isnull(hold_srrc.universalRoleResourceTypeID,0) = isnull(srrc.universalRoleResourceTypeID,0)
			and srrc.siteID = @siteID
		where srrc.cachedRightsID is null and srrc.universalRoleResourceRightsID is null;

		-- build @siteResourcesToProcessXML 
		-- Sample XML: <srs><sr rID="10244" /><sr rID="10101" /></srs>
		insert into #siteResourcesToProcess (siteResourceID)
		select distinct resourceID as rID from #holding_siteResourceRightsCacheToDelete
			union
		select distinct resourceID as rID from #holding_siteResourceRightsCacheToInsert;

	END ELSE BEGIN

		insert into #holding_siteResourceRightsCache (resourceID, functionID, groupID, [include],universalRoleResourceRightsID,universalRoleResourceTypeID)
		SELECT	s.siteResourceID, srtf.functionID, g.groupID, srrights.include, srrights.resourceRightsID as universalRoleResourceRightsID,srtf.resourceTypeID as universalRoleResourceTypeID
		FROM cms_siteResourceRights srrights
		inner join sites s 
			on srrights.siteID = @siteID 
			and srrights.resourceID = s.siteResourceID
			and srrights.resourceRightsID = @resourceRightID
		inner join cms_siteResourceRoles as srroles on srrights.roleID = srroles.roleID
		INNER join cms_siteResourceRoleTypes as srrt on srroles.roleTypeID = srrt.roleTypeID and srrt.roleTypeName = 'UniversalRole'
		INNER JOIN cms_siteResourceRoleFunctions srrf on srroles.roleID = srrf.roleID
		INNER JOIN cms_siteResourceTypeFunctions srtf on srtf.resourceTypeFunctionID = srrf.resourceTypeFunctionID
		INNER JOIN ams_groups g on srrights.groupID = g.groupID
		-- restrict to resourceTypeIDs that have at least one resource created on this site
		where exists (select resourceTypeID from cms_siteResources esr where esr.resourceTypeID = srtf.resourceTypeID and esr.siteID = @siteID);
			
		-- find holding_siteResourceRightsCacheToDelete
		insert into #holding_siteResourceRightsCacheToDelete (cachedRightsID, resourceID)
		select srrc.cachedRightsID, srrc.resourceID
		from dbo.cms_siteResourceRightsCache srrc
		inner join dbo.cms_siteResources sr on srrc.ResourceID = sr.siteResourceID
			and sr.siteID = @siteID
			and srrc.universalRoleResourceRightsID = @resourceRightID
		left outer join #holding_siteResourceRightsCache hold_srrc on hold_srrc.resourceID = srrc.resourceID
			and hold_srrc.functionID = srrc.functionID
			and isnull(hold_srrc.groupID,0) = isnull(srrc.groupID,0)
			and hold_srrc.[include] = srrc.[include]
			and isnull(hold_srrc.universalRoleResourceRightsID,0) = isnull(srrc.universalRoleResourceRightsID,0)
			and isnull(hold_srrc.universalRoleResourceTypeID,0) = isnull(srrc.universalRoleResourceTypeID,0)
		where hold_srrc.autoid is null and srrc.siteID = @siteID
		group by srrc.cachedRightsID, srrc.resourceID

		-- find #holding_siteResourceRightsCacheToInsert
		insert into #holding_siteResourceRightsCacheToInsert (resourceID, functionID, groupID, [include], universalRoleResourceRightsID, universalRoleResourceTypeID)
		select hold_srrc.resourceID, hold_srrc.functionID, hold_srrc.groupID, hold_srrc.[include], hold_srrc.universalRoleResourceRightsID, hold_srrc.universalRoleResourceTypeID
		from #holding_siteResourceRightsCache hold_srrc 
		left outer join dbo.cms_siteResourceRightsCache srrc on hold_srrc.resourceID = srrc.resourceID
			and hold_srrc.functionID = srrc.functionID
			and isnull(hold_srrc.groupID,0) = isnull(srrc.groupID,0)
			and hold_srrc.[include] = srrc.[include]
			and isnull(hold_srrc.universalRoleResourceRightsID,0) = isnull(srrc.universalRoleResourceRightsID,0)
			and isnull(hold_srrc.universalRoleResourceTypeID,0) = isnull(srrc.universalRoleResourceTypeID,0)
			and srrc.siteID = @siteID
		where srrc.cachedRightsID is null;

		-- build @siteResourcesToProcessXML 
		-- Sample XML: <srs><sr rID="10244" /><sr rID="10101" /></srs>
		insert into #siteResourcesToProcess (siteResourceID)
		-- include all resources for newly added cache rules
		select distinct sr.siteresourceID as rID
		from #holding_siteResourceRightsCacheToInsert hold_srrc
		inner join cms_siteResources sr on hold_srrc.universalRoleResourceTypeID = sr.resourceTypeID and sr.siteID = @siteID
			union
		-- include all resources for cache rules being removed
		select distinct sr.siteresourceID as rID
		from #holding_siteResourceRightsCacheToDelete hold_srrc
		inner join dbo.cms_siteResourceRightsCache srrc on hold_srrc.cachedRightsID = srrc.cachedRightsID and srrc.siteID = @siteID
		inner join cms_siteResources sr on srrc.universalRoleResourceTypeID = sr.resourceTypeID and sr.siteID = @siteID
			union
		-- include all newly created resources that have no srfrp's
		select distinct sr.siteresourceID as rID
		from #holding_siteResourceRightsCache hold_srrc
		inner join cms_siteResources sr on sr.siteID = @siteID
			and hold_srrc.universalRoleResourceTypeID = sr.resourceTypeID
		left outer join dbo.cache_perms_siteResourceFunctionRightPrints srfrp on srfrp.siteID = @siteID
			and srfrp.siteResourceID = sr.siteResourceID
		where srfrp.siteResourceID is null;
	END

	-- identify non-empty groupIDs in holding table that are not currently represented in cache
	insert into @newGroupIDs (groupID)
	select distinct hold_srrc.groupID
	from #holding_siteResourceRightsCache hold_srrc
	inner join cache_members_groups mg on mg.groupID = hold_srrc.groupID
	where not exists(select srrc.groupID from cms_siteResourceRightsCache srrc where srrc.groupID = hold_srrc.groupID and srrc.siteID = @siteID);
	
	-- insert into cms_siteResourceRightsCache
	insert into dbo.cms_siteResourceRightsCache (siteID, resourceID, functionID, groupID, [include],universalRoleResourceRightsID,universalRoleResourceTypeID)
	select @siteID, resourceID, functionID, groupID, [include],universalRoleResourceRightsID,universalRoleResourceTypeID
	from #holding_siteResourceRightsCacheToInsert;

	-- delete from cms_siteResourceRightsCache
	delete srrc
	from dbo.cms_siteResourceRightsCache srrc
	inner join #holding_siteResourceRightsCacheToDelete del on del.cachedRightsID = srrc.cachedRightsID
	where srrc.siteID = @siteID;

	IF OBJECT_ID('tempdb..#holding_siteResourceRightsCache') IS NOT NULL
		DROP TABLE #holding_siteResourceRightsCache;
	IF OBJECT_ID('tempdb..#holding_siteResourceRightsCacheToDelete') IS NOT NULL
		DROP TABLE #holding_siteResourceRightsCacheToDelete;
	IF OBJECT_ID('tempdb..#holding_siteResourceRightsCacheToInsert') IS NOT NULL
		DROP TABLE #holding_siteResourceRightsCacheToInsert;

	if dbo.fn_cache_perms_getStatus(@orgID) <> 'enabled'
		exec dbo.cache_perms_setStatus @orgid=@orgID, @status='disabled-needsRebuild';
	else BEGIN

		-- populate table to process group prints
		declare @queueTypeID int, @queueStatusID int, @itemGroupUID uniqueidentifier = NEWID(), @dateAdded datetime = GETDATE();
		select @queueTypeID = queueTypeID from platformQueue.dbo.tblQueueTypes where queueType = 'MemberGroupPrints';
		select @queueStatusID = queuestatusID from platformQueue.dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'readyToProcess';

		INSERT INTO platformQueue.dbo.queue_memberGroupPrints (itemGroupUID, orgID, memberID, statusID, dateAdded, dateUpdated)
		select distinct @itemGroupUID, @orgID, m.memberID, @queueStatusID, @dateAdded, @dateAdded
		from dbo.ams_members m
		inner join dbo.cache_members_groups mg on mg.orgID = @orgID and m.memberID = mg.memberID 
		inner join @newGroupIDs nG on nG.groupID = mg.groupID
		where m.orgID = @orgID;

		-- set groupPrintID to null for members about to be updated
		update m
		set m.groupPrintID = null
		from dbo.ams_members m
		inner join dbo.cache_members_groups mg on mg.orgID = @orgID and m.memberID = mg.memberID
		inner join @newGroupIDs nG on nG.groupID = mg.groupID
		where m.orgID = @orgID;

		if exists(select siteResourceID from #siteResourcesToProcess) BEGIN
			update temp 
			set temp.resourceTypeID = sr.resourceTypeID
			from #siteResourcesToProcess temp
			inner join dbo.cms_siteResources sr on sr.siteResourceID = temp.siteResourceID;

			exec dbo.cache_perms_updateSiteResourceFunctionRightPrintsForSiteResourcesBulk @siteID=@siteID, @processNewPrints=1;
		END

		exec dbo.cache_perms_updateGroupPrintsForMembersBulk @itemGroupUID=@itemGroupUID;
	END

	IF OBJECT_ID('tempdb..#siteResourcesToProcess') IS NOT NULL
		DROP TABLE #siteResourcesToProcess;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
