ALTER PROC dbo.queue_AcctOption2Import_grabForNotification
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @queueTypeID int, @statusReady int, @statusGrabbed int, @orgID int, @recordedByMemberID int, 
		@itemGroupID uniqueidentifier = NEWID();
	select @queueTypeID = queueTypeID from dbo.tblQueueTypes where queueType = 'importAcctOpt2';
	select @statusReady = queueStatusID from dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'readyToNotify';
	select @statusGrabbed = queueStatusID from dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'grabbedForNotifying';

	IF OBJECT_ID('tempdb..#tmpNotifyItemGroupUID') IS NOT NULL
		DROP TABLE #tmpNotifyItemGroupUID;
	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	IF OBJECT_ID('tempdb..#tmpNotify2') IS NOT NULL
		DROP TABLE #tmpNotify2;
	CREATE TABLE #tmpNotifyItemGroupUID (itemGroupUID uniqueidentifier);
	CREATE TABLE #tmpNotify (itemGroupUID uniqueidentifier, itemGroupUIDStr char(36) PRIMARY KEY);
	CREATE TABLE #tmpNotify2 (itemGroupUID uniqueidentifier PRIMARY KEY, orgID int, siteID int, recordedByMemberID int);

	-- dequeue 
	; WITH itemGroupUIDs AS (
		select distinct itemGroupUID
		from dbo.queue_acctOption2Import
		where statusID = @statusReady
			except
		select distinct itemGroupUID
		from dbo.queue_acctOption2Import
		where statusID <> @statusReady
	)
	UPDATE qi
	SET qi.statusID = @statusGrabbed,
		qi.dateUpdated = getdate()
		OUTPUT itemGroupUIDs.itemGroupUID
		INTO #tmpNotifyItemGroupUID
	FROM dbo.queue_acctOption2Import as qi
	INNER JOIN itemGroupUIDs on itemGroupUIDs.itemGroupUID = qi.itemGroupUID
	where qi.statusID = @statusReady;

	IF @@ROWCOUNT = 0 BEGIN
		select * from #tmpNotify2;
		GOTO on_done;
	END

	-- get distinct list of groupUIDs
	insert into #tmpNotify
	select distinct itemGroupUID, replace(cast(itemGroupUID as char(36)),'-','') as itemGroupUIDStr
	from #tmpNotifyItemGroupUID;

	insert into #tmpNotify2
	select distinct tmpN.itemGroupUID, qid.orgID, qid.siteID, qid.recordedByMemberID
	from #tmpNotify as tmpN
	inner join dbo.queue_acctOption2Import as qid on qid.itemGroupUID = tmpN.itemGroupUID
	order by tmpN.itemGroupUID;

	-- queue the posting of the batches
	select top 1 @orgID=orgID, @recordedByMemberID=recordedByMemberID
	from #tmpNotify2;

	INSERT INTO dbo.queue_batchPost (itemGroupID, orgID, batchID, addedByMemberID, dateAdded, dateUpdated)
	select distinct @itemGroupID, @orgID, b.batchID, @recordedByMemberID, getdate(), getdate()
	FROM membercentral.dbo.tr_batches as b
	inner join #tmpNotify as tmpN on b.batchCode = convert(char(8),b.depositDate,112) + tmpN.itemGroupUIDStr
	where b.orgID = @orgID
	and b.statusID <> 4;

	-- send message to service broker to create all the individual messages
	DECLARE @xmlMessage xml;
	select @xmlMessage = isnull((
		select 'batchPostLoad' as t, cast(@itemGroupID as varchar(60)) as u
		FOR XML RAW('mc'), TYPE
	),'<mc/>');
	EXEC dbo.queue_DataImport_sendMessage @xmlMessage=@xmlMessage;

	-- return itemGroupUIDs that can be marked as done
	select tmpN.itemGroupUID, me.email as reportEmail, s.siteName, s.siteCode, mActive.firstname, mActive.lastname, mActive.memberNumber, mActive.memberID
	from #tmpNotify2 as tmpN
	INNER JOIN membercentral.dbo.sites as s on s.siteID = tmpN.siteID
	INNER JOIN membercentral.dbo.ams_members as m on m.orgID in (@orgID,1) and m.memberID = tmpN.recordedByMemberID
	INNER JOIN membercentral.dbo.ams_members as mActive on mActive.orgID in (@orgID,1) and mActive.memberID = m.activeMemberID
	INNER JOIN membercentral.dbo.ams_memberEmailTags as metag on metag.orgID in (@orgID,1) and metag.memberID = mActive.memberID
	INNER JOIN membercentral.dbo.ams_memberEmailTagTypes as metagt on metagt.orgID in (@orgID,1)
		and metagt.emailTagTypeID = metag.emailTagTypeID
		and metagt.emailTagType = 'Primary'
	INNER JOIN membercentral.dbo.ams_memberEmails as me on me.orgID in (@orgID,1)
		and me.memberID = metag.memberID
		and me.emailTypeID = metag.emailTypeID
	order by tmpN.itemGroupUID;
	
	on_done:
	IF OBJECT_ID('tempdb..#tmpNotifyItemGroupUID') IS NOT NULL
		DROP TABLE #tmpNotifyItemGroupUID;
	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	IF OBJECT_ID('tempdb..#tmpNotify2') IS NOT NULL
		DROP TABLE #tmpNotify2;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
