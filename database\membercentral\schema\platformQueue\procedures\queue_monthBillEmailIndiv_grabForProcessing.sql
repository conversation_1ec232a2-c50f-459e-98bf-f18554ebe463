ALTER PROC dbo.queue_monthBillEmailIndiv_grabForProcessing
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @batchSize int, @queueTypeID int, @statusReady int, @statusGrabbed int,
		@applicationTypeID int = membercentral.dbo.fn_getApplicationTypeIDFromName('BuyNow'),
		@siteID int = membercentral.dbo.fn_getSiteIDFromSiteCode('MC'), @settingsXML xml;

	set @batchSize = 60;
	select @queueTypeID = queueTypeID from dbo.tblQueueTypes where queueType = 'monthBillEmailIndiv';
	select @statusReady = queueStatusID from dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'readyToProcess';
	select @statusGrabbed = queueStatusID from dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'grabbedForProcessing';

	SELECT @settingsXML = settingsXML
	FROM membercentral.dbo.cms_applicationTypeSettings
	WHERE siteID = @siteID
	AND applicationTypeID = @applicationTypeID;

	SELECT @settingsXML = ISNULL(@settingsXML,'<settings />');

	IF OBJECT_ID('tempdb..#tmpMembers') IS NOT NULL 
		DROP TABLE #tmpMembers;
	CREATE TABLE #tmpMembers (itemID int);

	-- dequeue in order of dateAdded. get @batchsize statements
	UPDATE qi WITH (UPDLOCK, READPAST)
	SET qi.statusID = @statusGrabbed,
		qi.dateUpdated = getdate()
		OUTPUT inserted.itemID
		INTO #tmpMembers
	FROM dbo.queue_monthBillEmailIndiv as qi
	INNER JOIN (
		SELECT top(@BatchSize) qi2.itemID 
		from dbo.queue_monthBillEmailIndiv as qi2
		WHERE qi2.statusID = @statusReady
		ORDER BY qi2.dateAdded, qi2.itemID
	) as batch on batch.itemID = qi.itemID
	WHERE qi.statusID = @statusReady;

	-- final data
	select qieo.itemID, bp.EOMPeriod, d.FirstName, d.lastName, d.sourceID, qitsa.balanceForward, qitsa.charges, 
		qitsa.credits, qitsa.balanceEnd, qitsa.folderPath as tsFolderPath, qitsa.[filename] as tsFileName,
		qitsa.payLinkDirect, qitsa.payLinkCode,
		case when d.Email is not null and d.Email <> '' and membercentral.dbo.fn_RegExReplace(d.email,'^[a-zA-Z_0-9-''\&\+~]+(\.[a-zA-Z_0-9-''\&\+~]+)*@([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,63}$','') = '' then d.email else null end as billingEmail1,
		case when d.BillingContactEmail is not null and d.BillingContactEmail <> '' and d.BillingContactEmail <> d.Email then d.BillingContactEmail else null end as billingEmail2,
		case 
		when d.paymentType = 'C' AND exists (select depoMemberDataID from trialsmith.dbo.ccMemberPaymentProfiles WHERE depomemberdataID = qieo.depomemberdataID and orgcode = 'TS' and declined = 0) then 1 
		else 0 
		end as willChargeCC,
		(select top 1 m.memberID 
			from membercentral.dbo.ams_members as m 
			where m.orgID = 1 
			and m.status = 'A' 
			and m.memberNumber = 'TSDEPOID_' + CAST(qieo.depoMemberDataID as varchar(10))) as MCMemberID
	from #tmpMembers as tmp
	inner join dbo.queue_monthBillEmailIndiv as qieo on qieo.itemID = tmp.itemID
	inner join trialsmith.dbo.billingPeriods as bp on bp.periodID = qieo.billingPeriodID
	inner join trialsmith.dbo.depoMemberData as d on d.depomemberdataID = qieo.depoMemberDataID
	inner join dbo.queue_monthBillTSA as qitsa on qitsa.billingPeriodID = qieo.billingPeriodID and qitsa.depoMemberDataID = qieo.depoMemberDataID and qitsa.isOrg = 0
	order by tmp.itemID;

	IF OBJECT_ID('tempdb..#tmpMembers') IS NOT NULL 
		DROP TABLE #tmpMembers;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
