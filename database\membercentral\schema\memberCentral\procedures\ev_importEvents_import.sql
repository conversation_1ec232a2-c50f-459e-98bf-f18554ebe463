ALTER PROC dbo.ev_importEvents_import
@siteID int,
@recordedByMemberID int,
@ovAction char(1),
@importResult xml OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpCrossEventCustomDetails') IS NOT NULL 
		DROP TABLE #tmpCrossEventCustomDetails;
	CREATE TABLE #tmpCrossEventCustomDetails (rowID int, fieldID int, columnName varchar(255), fieldValue varchar(max));

	declare @nowDate datetime, @queueTypeID int, @statusInserting int, @statusReady int, @itemGroupUID uniqueidentifier, 
			@colList varchar(max), @selColList varchar(max), @dynSQL nvarchar(max), @xmlMessage xml;
	set @nowDate = getdate();
	select @queueTypeID = queueTypeID from platformQueue.dbo.tblQueueTypes where queueType = 'importEvents';
	select @statusInserting = queueStatusID from platformQueue.dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'insertingItems';
	select @statusReady = queueStatusID from platformQueue.dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'readyToProcess';

	-- all imported events get the same ItemGroupUID
	select @itemGroupUID = NEWID();

	BEGIN TRY
		-- string columns
		set @colList = null;
		select @colList = COALESCE(@colList + ',', '') + quotename(columnName) 
		from #tblEvImportCustomCols
		where dataTypeCode = 'STRING'
		and displayTypeCode not in ('SELECT','RADIO','CHECKBOX');

		if @colList is not null BEGIN
			set @selColList = null;
			select @selColList = COALESCE(@selColList + '; ', '') + 'ALTER TABLE #mc_EvImport ALTER COLUMN ' + tbl.listitem + ' varchar(max) null'
				from dbo.fn_varcharListToTable(@colList,',') as tbl;
			exec(@selColList);

			set @selColList = null;
			select @selColList = COALESCE(@selColList + ', ', '') + tbl.listitem + ' = isnull(' + tbl.listitem + ','''')'
			from dbo.fn_varcharListToTable(@colList,',') as tbl;

			set @dynSQL = 'update #mc_EvImport set ' +  @selColList + ';';
			exec(@dynSQL);

			set @dynSQL = 'select tmp.rowID, f.fieldID, tmp.columnName, tmp.valueString
							from (
								select rowID, columnName, valueString
								from #mc_EvImport
								unpivot (valueString for columnName in (' + @colList + ')) u
							) tmp
							inner join #tblEvImportCustomCols as impCols on impCols.columnName = tmp.columnName
							inner join dbo.cf_fields as f on f.fieldID = impCols.fieldID';
			
			INSERT INTO #tmpCrossEventCustomDetails (rowID, fieldID, columnName, fieldValue)
			EXEC(@dynSQL);
		end

		-- integer columns
		set @colList = null;
		select @colList = COALESCE(@colList + ',', '') + quotename(columnName) 
		from #tblEvImportCustomCols
		where dataTypeCode = 'INTEGER'
		and displayTypeCode not in ('SELECT','RADIO','CHECKBOX');

		if @colList is not null BEGIN
			set @selColList = null;
			select @selColList = COALESCE(@selColList + '; ', '') + 'ALTER TABLE #mc_EvImport ALTER COLUMN ' + tbl.listitem + ' varchar(10) null'
				from dbo.fn_varcharListToTable(@colList,',') as tbl;
			exec(@selColList);

			set @selColList = null;
			select @selColList = COALESCE(@selColList + ', ', '') + tbl.listitem + ' = isnull(' + tbl.listitem + ','''')'
			from dbo.fn_varcharListToTable(@colList,',') as tbl;

			set @dynSQL = 'update #mc_EvImport set ' +  @selColList + ';';
			exec(@dynSQL);

			set @dynSQL = 'select tmp.rowID, f.fieldID, tmp.columnName, tmp.valueInteger
							from (
								select rowID, columnName, valueInteger
								from #mc_EvImport
								unpivot (valueInteger for columnName in (' + @colList + ')) u
							) tmp
							inner join #tblEvImportCustomCols as impCols on impCols.columnName = tmp.columnName
							inner join dbo.cf_fields as f on f.fieldID = impCols.fieldID
							inner join dbo.cf_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID';
			
			INSERT INTO #tmpCrossEventCustomDetails (rowID, fieldID, columnName, fieldValue)
			EXEC(@dynSQL);
		end

		-- decimal columns
		set @colList = null;
		select @colList = COALESCE(@colList + ',', '') + quotename(columnName) 
		from #tblEvImportCustomCols
		where dataTypeCode = 'DECIMAL2'
		and displayTypeCode not in ('SELECT','RADIO','CHECKBOX');

		if @colList is not null BEGIN
			set @selColList = null;
			select @selColList = COALESCE(@selColList + '; ', '') + 'ALTER TABLE #mc_EvImport ALTER COLUMN ' + tbl.listitem + ' varchar(15) null'
				from dbo.fn_varcharListToTable(@colList,',') as tbl;
			exec(@selColList);

			set @selColList = null;
			select @selColList = COALESCE(@selColList + ', ', '') + tbl.listitem + ' = isnull(' + tbl.listitem + ','''')'
			from dbo.fn_varcharListToTable(@colList,',') as tbl;

			set @dynSQL = 'update #mc_EvImport set ' +  @selColList + ';';
			exec(@dynSQL);

			set @dynSQL = 'select tmp.rowID, f.fieldID, tmp.columnName, tmp.valueDecimal2
							from (
								select rowID, columnName, valueDecimal2
								from #mc_EvImport
								unpivot (valueDecimal2 for columnName in (' + @colList + ')) u
							) tmp
							inner join #tblEvImportCustomCols as impCols on impCols.columnName = tmp.columnName
							inner join dbo.cf_fields as f on f.fieldID = impCols.fieldID';
			
			INSERT INTO #tmpCrossEventCustomDetails (rowID, fieldID, columnName, fieldValue)
			EXEC(@dynSQL);
		end

		-- date columns
		set @colList = null;
		select @colList = COALESCE(@colList + ',', '') + quotename(columnName) 
		from #tblEvImportCustomCols
		where dataTypeCode = 'DATE'
		and displayTypeCode not in ('SELECT','RADIO','CHECKBOX');

		if @colList is not null BEGIN
			set @selColList = null;
			select @selColList = COALESCE(@selColList + '; ', '') + 'ALTER TABLE #mc_EvImport ALTER COLUMN ' + tbl.listitem + ' varchar(30) null'
				from dbo.fn_varcharListToTable(@colList,',') as tbl;
			exec(@selColList);

			set @selColList = null;
			select @selColList = COALESCE(@selColList + ', ', '') + tbl.listitem + ' = isnull(' + tbl.listitem + ','''')'
			from dbo.fn_varcharListToTable(@colList,',') as tbl;

			set @dynSQL = 'update #mc_EvImport set ' +  @selColList + ';';
			exec(@dynSQL);

			set @dynSQL = 'select tmp.rowID, f.fieldID, tmp.columnName, tmp.valueDate
							from (
								select rowID, columnName, valueDate
								from #mc_EvImport
								unpivot (valueDate for columnName in (' + @colList + ')) u
							) tmp
							inner join #tblEvImportCustomCols as impCols on impCols.columnName = tmp.columnName
							inner join dbo.cf_fields as f on f.fieldID = impCols.fieldID';
			
			INSERT INTO #tmpCrossEventCustomDetails (rowID, fieldID, columnName, fieldValue)
			EXEC(@dynSQL);
		end

		-- select, radio, and checkbox custom field columns
		set @colList = null;
		select @colList = COALESCE(@colList + ',', '') + quotename(columnName) 
		from #tblEvImportCustomCols
		where displayTypeCode in ('SELECT','RADIO','CHECKBOX');

		if @colList is not null BEGIN
			set @selColList = null;
			select @selColList = COALESCE(@selColList + '; ', '') + 'ALTER TABLE #mc_EvImport ALTER COLUMN ' + tbl.listitem + ' varchar(max) null'
				from dbo.fn_varcharListToTable(@colList,',') as tbl;
			exec(@selColList);

			set @selColList = null;
			select @selColList = COALESCE(@selColList + ', ', '') + tbl.listitem + ' = isnull(' + tbl.listitem + ','''')'
			from dbo.fn_varcharListToTable(@colList,',') as tbl;

			set @dynSQL = 'update #mc_EvImport set ' +  @selColList + ';';
			exec(@dynSQL);

			set @dynSQL = 'select tmp.rowID, f.fieldID, tmp.columnName, tbl.listitem
							from (
								select rowID, columnName, columnValue
								from #mc_EvImport
								unpivot (columnValue for columnName in (' + @colList + ')) u
							) tmp
							cross apply dbo.fn_varcharListToTable(tmp.columnValue,''|'') as tbl
							inner join #tblEvImportCustomCols as impCols on impCols.columnName = tmp.columnName
							inner join dbo.cf_fields as f on f.fieldID = impCols.fieldID
							inner join dbo.cf_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID';
			
			INSERT INTO #tmpCrossEventCustomDetails (rowID, fieldID, columnName, fieldValue)
			EXEC(@dynSQL);
		end

	END TRY
	BEGIN CATCH
		INSERT INTO #tblEvErrors (msg)
		VALUES ('Unable to prepare cross-event fields for import.');

		INSERT INTO #tblEvErrors (msg)
		VALUES (left(error_message(),300));
	END CATCH

	BEGIN TRY
		BEGIN TRAN;
			insert into platformQueue.dbo.tblQueueItems (itemUID, queueStatusID)
			select itemUID, @statusInserting
			from #mc_EvImport;
			
			insert into platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueInteger)
			select @itemGroupUID, tmp.itemUID, @recordedByMemberID, @siteID, dc.columnID, tmp.rowID, unPvtInt.columnValueInt
			from #mc_EvImport as tmp
			inner join (
				select rowID, columnname, columnValueInt
				from #mc_EvImport
				unpivot (columnValueInt for columnname in (MCCalendarID, MCEventID, MCRegistrationID, MCParentEventID, MCRecurrenceOrder)) u
			) as unPvtInt on unPvtInt.rowID = tmp.rowID
			inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID and dc.columnname = unPvtInt.columnname;

			insert into platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueString)
			select @itemGroupUID, tmp.itemUID, @recordedByMemberID, @siteID, dc.columnID, tmp.rowID, unPvtString.columnValueString
			from #mc_EvImport as tmp
			inner join (
				select rowID, columnname, columnValueString
				from #mc_EvImport
				unpivot (columnValueString for columnname in (EventTitle, EventCode, ParentEventCode, RecurringSeriesCode, RegistrationReplyEmail, ContactTitle, LocationTitle, CancellationTitle, TravelTitle, InformationTitle)) u
			) as unPvtString on unPvtString.rowID = tmp.rowID
			inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID and dc.columnname = unPvtString.columnname;

			insert into platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueDate)
			select @itemGroupUID, tmp.itemUID, @recordedByMemberID, @siteID, dc.columnID, tmp.rowID, unPvtDate.columnValueDate
			from #mc_EvImport as tmp
			inner join (
				select rowID, columnname, columnValueDate
				from #mc_EvImport
				unpivot (columnValueDate for columnname in (EventStart, EventEnd)) u
			) as unPvtDate on unPvtDate.rowID = tmp.rowID
			inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID and dc.columnname = unPvtDate.columnname;

			insert into platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueBit)
			select @itemGroupUID, tmp.itemUID, @recordedByMemberID, @siteID, dc.columnID, tmp.rowID, unPvtBit.columnValueBit
			from #mc_EvImport as tmp
			inner join (
				select rowID, columnname, columnValueBit
				from #mc_EvImport
				unpivot (columnValueBit for columnname in (EventHidden, EventAllDay, ContactInclude, LocationInclude, CancellationInclude, TravelInclude, DisplayCredits, EnableRealTimeRoster)) u
			) as unPvtBit on unPvtBit.rowID = tmp.rowID
			inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID and dc.columnname = unPvtBit.columnname;

			insert into platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueText)
			select @itemGroupUID, tmp.itemUID, @recordedByMemberID, @siteID, dc.columnID, tmp.rowID, unPvtText.columnValueText
			from #mc_EvImport as tmp
			inner join (
				select rowID, columnname, columnValueText
				from #mc_EvImport
				unpivot (columnValueText for columnname in (EventDescription, Contact, Location, Cancellation, Travel, Information, InternalNotes, MCCategoryIDList)) u
			) as unPvtText on unPvtText.rowID = tmp.rowID
			inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID and dc.columnname = unPvtText.columnname;

			insert into platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueString)
			select @itemGroupUID, tmp.itemUID, @recordedByMemberID, @siteID, dc.columnID, tmp.rowID, @ovAction
			from #mc_EvImport as tmp
			inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID and dc.columnname = 'ovAction';

			-- add credits
			set @colList = null;
			select @colList = COALESCE(@colList + ',', '') + authorityCode + '_approval,' + authorityCode + '_status' from #tblPossibleCredits;
			if @colList is not null BEGIN
				select @dynSQL = '
					select ''' + cast(@itemGroupUID as varchar(60)) + ''', tmp.itemUID, ' + cast(@recordedByMemberID as varchar(20)) + ', ' + cast(@siteID as varchar(10)) + ', dc.columnID, tmp.rowID, unPvtStr.columnValueString
					from #mc_EvImport as tmp
					inner join (
						select rowID, columnname, columnValueString
						from #mc_EvImport 
						unpivot (columnValueString for columnname in (' + @colList + ')) u
					) as unPvtStr on unPvtStr.rowID = tmp.rowID
					inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = ' + cast(@queueTypeID as varchar(10)) + ' and dc.columnname = unPvtStr.columnname';
				insert into platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueString)
				EXEC(@dynSQL);
			end

			set @colList = null;
			select @colList = COALESCE(@colList + ',', '') + column_name from #tblPossibleCreditCols;
			IF @colList is not null BEGIN
				select @dynSQL = '
					select ''' + cast(@itemGroupUID as varchar(60)) + ''', tmp.itemUID, ' + cast(@recordedByMemberID as varchar(20)) + ', ' + cast(@siteID as varchar(10)) + ', dc.columnID, tmp.rowID, unPvtDec.columnValueDecimal2
					from #mc_EvImport as tmp
					inner join (
						select rowID, columnname, columnValueDecimal2
						from #mc_EvImport 
						unpivot (columnValueDecimal2 for columnname in (' + @colList + ')) u
					) as unPvtDec on unPvtDec.rowID = tmp.rowID
					inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = ' + cast(@queueTypeID as varchar(10)) + ' and dc.columnname = unPvtDec.columnname';
				insert into platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueDecimal2)
				EXEC(@dynSQL);
			END

			insert into platformQueue.dbo.tblQueueItemData (itemGroupUID, itemUID, recordedByMemberID, siteID, columnID, dataKey, columnValueString)
			select @itemGroupUID, tmp.itemUID, @recordedByMemberID, @siteID, dc.columnID, tmp.rowID, fd.fieldValue
			from #mc_EvImport as tmp
			inner join #tmpCrossEventCustomDetails as fd on fd.rowID = tmp.rowID
			inner join platformQueue.dbo.tblQueueTypeDataColumns as dc on dc.queueTypeID = @queueTypeID and dc.columnname = fd.columnname;

			-- resume task
			EXEC dbo.sched_resumeTask @name='Events Import Queue', @engine='BERLinux';

			-- update queue item groups to show ready to process
			update qi WITH (UPDLOCK, HOLDLOCK)
			set qi.queueStatusID = @statusReady,
				qi.dateUpdated = getdate()
			from platformQueue.dbo.tblQueueItems as qi
			inner join #mc_EvImport as tmp on tmp.itemUID = qi.itemUID;

			-- send message to service broker to create all the individual messages
			select @xmlMessage = isnull((
				select 'importEventsLoad' as t, cast(@itemGroupUID as varchar(60)) as u
				FOR XML RAW('mc'), TYPE
			),'<mc/>');
			EXEC platformQueue.dbo.queue_DataImport_sendMessage @xmlMessage=@xmlMessage;
		COMMIT TRAN;
	END TRY
	BEGIN CATCH
		IF @@trancount > 0 ROLLBACK TRANSACTION;

		INSERT INTO #tblEvErrors (msg)
		VALUES ('Unable to queue events for import.');

		INSERT INTO #tblEvErrors (msg)
		VALUES (left(error_message(),300));
	END CATCH

	select @importResult = (
		select getdate() as "@date",
			isnull((select top 100 PERCENT dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg"
			from #tblEvErrors
			order by rowid
			FOR XML path('error'), root('errors'), type),'<errors/>')
		for xml path('import'), TYPE);

	IF OBJECT_ID('tempdb..#tmpCrossEventCustomDetails') IS NOT NULL 
		DROP TABLE #tmpCrossEventCustomDetails;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
