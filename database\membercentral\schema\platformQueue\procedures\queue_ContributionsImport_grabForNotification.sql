ALTER PROC dbo.queue_ContributionsImport_grabForNotification
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @statusReady int, @statusGrabbed int, @queueTypeID int;
	select @queueTypeID = queueTypeID from dbo.tblQueueTypes where queueType = 'importContributions';
	select @statusReady = queueStatusID from dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'readyToNotify';
	select @statusGrabbed = queueStatusID from dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'grabbedForNotifying';

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	IF OBJECT_ID('tempdb..#tmpNotifyContributions') IS NOT NULL
		DROP TABLE #tmpNotifyContributions;
	CREATE TABLE #tmpNotify (itemGroupUID uniqueidentifier);
	CREATE TABLE #tmpNotifyContributions (itemGroupUID uniqueidentifier, itemUID uniqueidentifier, siteID int, recordedByMemberID int, programName varchar(200));

	-- dequeue. 
	; WITH itemGroupUIDs AS (
		select distinct qid.itemGroupUID
		from dbo.tblQueueItems as qi
		inner join dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
		where qi.queueStatusID = @statusReady
			except
		select distinct qid.itemGroupUID
		from dbo.tblQueueItems as qi
		inner join dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
		where qi.queueStatusID <> @statusReady
	)
	UPDATE dbo.tblQueueItems WITH (UPDLOCK, READPAST)
	SET queueStatusID = @statusGrabbed,
		dateUpdated = getdate()
		OUTPUT qid.itemGroupUID
		INTO #tmpNotify
	FROM dbo.tblQueueItems as qi
	inner join dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
	INNER JOIN itemGroupUIDs on itemGroupUIDs.itemGroupUID = qid.itemGroupUID
	where qi.queueStatusID = @statusReady;

	-- if none found, no need to continue. return empty query.
	IF @@ROWCOUNT = 0 BEGIN
		select * from #tmpNotifyContributions;
		GOTO on_done;
	END

	-- contributions in groupUID
	insert into #tmpNotifyContributions (itemGroupUID, itemUID, siteID, recordedByMemberID, programName)
	select itemGroupUID, itemUID, siteID, recordedByMemberID, programName
	from (
		select qid.itemGroupUID, qid.itemUID, qid.siteID, qid.recordedByMemberID, dc.columnname, qid.columnValueInteger
		from (select distinct itemGroupUID from #tmpNotify) as tmpN
		inner join dbo.tblQueueItemData as qid on qid.itemGroupUID = tmpN.itemGroupUID
		inner join dbo.tblQueueTypeDataColumns as dc on dc.columnID = qid.columnID and dc.columnname = 'programID'
	) as tmp
	PIVOT (min(columnValueInteger) for columnname in (programID)) as pvt
	inner join membercentral.dbo.cp_programs as cp on cp.programID = pvt.programID;

	select distinct tmpN.itemGroupUID, me.email as reportEmail, s.siteName, s.siteCode, mActive.firstname, 
		mActive.lastname, mActive.memberNumber, mActive.memberID, tmpN.programName
	from #tmpNotifyContributions as tmpN
	INNER JOIN membercentral.dbo.sites as s on s.siteID = tmpN.siteID
	INNER JOIN membercentral.dbo.ams_members as m on m.orgID in (s.orgID,1) and m.memberID = tmpN.recordedByMemberID
	INNER JOIN membercentral.dbo.ams_members as mActive on mActive.orgID in (s.orgID,1) and mActive.memberID = m.activeMemberID
	INNER JOIN membercentral.dbo.ams_memberEmailTags as metag on metag.orgID in (s.orgID,1) and metag.memberID = mActive.memberID
	INNER JOIN membercentral.dbo.ams_memberEmailTagTypes as metagt on metagt.orgID in (s.orgID,1) and metagt.emailTagTypeID = metag.emailTagTypeID
		and metagt.emailTagType = 'Primary'
	INNER JOIN membercentral.dbo.ams_memberEmails as me on me.orgID in (s.orgID,1)
		and me.memberID = metag.memberID
		and me.emailTypeID = metag.emailTypeID
	order by tmpN.itemGroupUID, tmpN.programName;
	
	on_done:
	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	IF OBJECT_ID('tempdb..#tmpNotifyContributions') IS NOT NULL
		DROP TABLE #tmpNotifyContributions;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
