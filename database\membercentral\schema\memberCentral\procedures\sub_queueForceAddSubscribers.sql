ALTER PROC dbo.sub_queueForceAddSubscribers
@recordedByMemberID int,
@subscriptionIDToAdd int,
@subscriberIDList varchar(max)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpSubscribersToForceAdd') IS NOT NULL 
		DROP TABLE #tmpSubscribersToForceAdd;
	CREATE TABLE #tmpSubscribersToForceAdd (subscriberID int PRIMARY KEY);

	declare @statusReady int, @queueTypeID int, @orgID int, @xmlMessage xml, @itemGroupUID uniqueidentifier = NEWID();
	select @queueTypeID = queueTypeID from platformQueue.dbo.tblQueueTypes where queueType = 'subscriptionForceAdd';
	select @statusReady = queueStatusID from platformQueue.dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'readyToProcess';

	select @orgID = orgID
	from dbo.sub_subscriptions
	where subscriptionID = @subscriptionIDToAdd;

	-- get subscribers to modify
	insert into #tmpSubscribersToForceAdd (subscriberID)
	select distinct s.subscriberID
	from dbo.fn_intListToTable(@subscriberIDList,',') as tmp
	inner join dbo.sub_subscribers as s on s.orgID = @orgID and s.subscriberID = tmp.listitem
		except
	select qi.subscriberID
	from platformQueue.dbo.queue_subscriptionForceAdd as qi
	where qi.subscriptionID = @subscriptionIDToAdd;

	IF @@ROWCOUNT > 0 BEGIN
		INSERT INTO platformQueue.dbo.queue_subscriptionForceAdd (itemGroupUID, subscriberID, subscriptionID, recordedByMemberID, statusID, dateAdded, dateUpdated)
		select @itemGroupUID, subscriberID, @subscriptionIDToAdd, @recordedByMemberID, @statusReady, getdate(), getdate()
		from #tmpSubscribersToForceAdd;

		EXEC dbo.sched_resumeTask @name='Subscriber ForceAdd Queue', @engine='BERLinux';

		select @xmlMessage = isnull((
			select 'subscriptionForceAddLoad' as t, cast(@itemGroupUID as varchar(60)) as u
			FOR XML RAW('mc'), TYPE
		),'<mc/>');
		EXEC platformQueue.dbo.queue_DataImport_sendMessage @xmlMessage=@xmlMessage;
	END

	IF OBJECT_ID('tempdb..#tmpSubscribersToForceAdd') IS NOT NULL 
		DROP TABLE #tmpSubscribersToForceAdd;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
