ALTER PROC dbo.queue_sendgridSubuserDomainAuth_clearDone
@itemID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @statusDone int, @subuserID int;

	select @queueTypeID = queueTypeID
	from dbo.tblQueueTypes
	where queueType = 'SendgridSubuserDomainAuth';

	select @statusDone = queueStatusID 
	from dbo.tblQueueStatuses
	where queueTypeID = @queueTypeID
	and queueStatus = 'Done';
	
	select @subuserID = subUserID 
	from dbo.queue_SendgridSubuserDomainAuth
	WHERE itemID = @itemID
	AND statusID = @statusDone;

	DELETE from dbo.queue_SendgridSubuserDomainAuth
	WHERE itemID = @itemID
	AND statusID = @statusDone;

	-- Remove the sub user from queue_sendgridSubuserCreate if this was a subuser ALTER PROCess.
	DELETE from dbo.queue_sendgridSubuserCreate
	WHERE subUserID = @subuserID;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
