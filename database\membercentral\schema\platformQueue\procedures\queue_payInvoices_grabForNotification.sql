ALTER PROC dbo.queue_payInvoices_grabForNotification
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @queueTypeID int, @statusReady int, @statusGrabbed int;

	select @queueTypeID = queueTypeID from dbo.tblQueueTypes where queueType = 'payInvoices';
	select @statusReady = queueStatusID from dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'readyToNotify';
	select @statusGrabbed = queueStatusID from dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'grabbedForNotifying';

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL 
		DROP TABLE #tmpNotify;
	CREATE TABLE #tmpNotify (itemGroupUID uniqueidentifier);

	-- dequeue
	; WITH itemGroupUIDs AS (
		select distinct itemGroupUID
		from dbo.queue_payInvoices
		where statusID = @statusReady
			except
		select distinct itemGroupUID
		from dbo.queue_payInvoices
		where statusID <> @statusReady
	)
	UPDATE qi
	SET qi.statusID = @statusGrabbed,
		qi.dateUpdated = getdate()
		OUTPUT itemGroupUIDs.itemGroupUID
		INTO #tmpNotify
	FROM dbo.queue_payInvoices as qi
	INNER JOIN itemGroupUIDs on itemGroupUIDs.itemGroupUID = qi.itemGroupUID
	where qi.statusID = @statusReady;

	-- get system account
	declare @enteredByMemberID int;
	select @enteredByMemberID = membercentral.dbo.fn_ams_getMCSystemMemberID();

	-- return report information
	-- status of mp_profile is not important here
	select tmpN.itemGroupUID, qid.itemID, m2.memberID, s.siteID, o.orgID, 
		m2.lastname + ', ' + m2.firstname as memberName, m2.membernumber, m2.company, 
		qid.paymentAmount, qidd.invoiceDueAmount, qidd.additionalPaymentFee, o.orgcode + membercentral.dbo.fn_tr_padInvoiceNumber(i.invoiceNumber) as invoiceNumber,
		mpp.detail as payMethodDetail, mp.profileName as payProfileName, qid.paymentErrorMessage, oi.organizationName as orgName, h.datePaid,
		m4.memberid as recordedByMemberID, s.siteCode, ip.profileName as invoiceProfileName, i.dateDue,
		case when qid.recordedByMemberID = @enteredByMemberID then o.accountingEmail else m4e.email end as accountingEmail,
		case when qid.recordedByMemberID = @enteredByMemberID then 1 else 0 end as isSystemGenerated,
		case when qidd.additionalPaymentFee > 0 and mp.enableProcessingFeeDonation = 1 then mp.processingFeeLabel 
			when  qidd.additionalPaymentFee > 0 and mp.enableSurcharge = 1 then 'Surcharge'
		end as additionalFeeLabel
	from (select distinct itemGroupUID from #tmpNotify) as tmpN
	INNER JOIN platformQueue.dbo.queue_payInvoices as qid ON qid.itemGroupUID = tmpN.itemGroupUID
	INNER JOIN platformQueue.dbo.queue_payInvoicesDetail as qidd ON qidd.itemID = qid.itemID
	INNER JOIN membercentral.dbo.tr_invoices as i on i.invoiceID = qidd.invoiceID
	INNER JOIN membercentral.dbo.tr_invoiceProfiles as ip on ip.profileID = i.invoiceProfileID
	INNER JOIN membercentral.dbo.ams_members as m on m.memberID = i.assignedToMemberID
	INNER JOIN membercentral.dbo.ams_members as m2 on m2.memberid = m.activeMemberID
	INNER JOIN membercentral.dbo.ams_memberPaymentProfiles as mpp on mpp.payProfileID = qid.memberPaymentProfileID
	INNER JOIN membercentral.dbo.mp_profiles as mp on mp.profileID = qid.MPProfileID
	INNER JOIN membercentral.dbo.sites as s on s.siteID = mp.siteID
	INNER JOIN membercentral.dbo.organizations as o on o.orgID = qid.orgID
	INNER JOIN membercentral.dbo.orgIdentities as oi on oi.orgID = o.orgID and oi.orgIdentityID = o.defaultOrgIdentityID
	LEFT OUTER JOIN membercentral.dbo.tr_paymentHistory as h on h.historyID = qid.paymentHistoryID
	INNER JOIN membercentral.dbo.ams_members as m3 on m3.memberID = qid.recordedByMemberID
	INNER JOIN membercentral.dbo.ams_members as m4 on m4.memberid = m3.activeMemberID
	INNER JOIN membercentral.dbo.ams_memberEmailTags as metag on metag.orgID = m4.orgID and metag.memberID = m4.memberID
	INNER JOIN membercentral.dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = m4.orgID and metagt.emailTagTypeID = metag.emailTagTypeID
		and metagt.emailTagType = 'Primary'
	INNER JOIN membercentral.dbo.ams_memberEmails as m4e on m4e.orgID = m4.orgID
		and m4e.memberID = metag.memberID
		and m4e.emailTypeID = metag.emailTypeID
	order by tmpN.itemGroupUID, case when isnull(qid.paymentErrorMessage,'') <> '' then 1 else 2 end, m2.lastname, m2.firstname, m2.membernumber, qid.itemID;

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL 
		DROP TABLE #tmpNotify;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
