<cfcomponent output="false" extends="model.scheduledTasks.scheduledTasks">

	<cffunction name="runTask" access="public" returntype="void" output="false">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structnew()>

		<cfsetting requesttimeout="600">

		<cfset local.itemCountOrg = getOrgQueueItemCount()>
		<cfset local.itemCountIndiv = getIndivQueueItemCount()>
		<cfset local.itemCount = local.itemCountOrg + local.itemCountIndiv>

		<cfif local.itemCountOrg GT 0>
			<cfset checkOrgQueue()>
		</cfif>

		<cfif local.itemCountIndiv GT 0>
			<cfset checkIndivQueue()>
		</cfif>

		<cfif local.itemCount GT 0>
			<cfset local.strResult = processQueue()>
			<cfif NOT local.strResult.success>
				<cfthrow message="Error running processQueue()">
			</cfif>
		</cfif>

		<cfset local.historyID = (arguments.strTask.historyID ?: 0)>
		<cfset addBatchIdentifierToHistory(historyID=local.historyID, batchIdentifier="", itemCount=local.itemCount)>
	</cffunction>

	<cffunction name="updateEmailOrgQueueItemStatus" access="private" output="false" returntype="void">
		<cfargument name="itemID" type="numeric" required="true">
		<cfargument name="queueStatus" type="string" required="true">

		<cfquery name="local.qryUpdateToProcessingItem" datasource="#application.dsn.platformQueue.dsn#">
			SET NOCOUNT ON;

			declare @queueTypeID int, @queueStatusID int;
			select @queueTypeID = queueTypeID from dbo.tblQueueTypes where queueType = 'monthBillEmailOrg';
			select @queueStatusID = queueStatusID from dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.queueStatus#">;

			UPDATE dbo.queue_monthBillEmailOrg
			SET statusID = @queueStatusID,
				dateUpdated = getdate()
			WHERE itemID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.itemID#">;
		</cfquery>
	</cffunction>

	<cffunction name="updateEmailIndivQueueItemStatus" access="private" output="false" returntype="void">
		<cfargument name="itemID" type="numeric" required="true">
		<cfargument name="queueStatus" type="string" required="true">

		<cfquery name="local.qryUpdateToProcessingItem" datasource="#application.dsn.platformQueue.dsn#">
			SET NOCOUNT ON;

			declare @queueTypeID int, @queueStatusID int;
			select @queueTypeID = queueTypeID from dbo.tblQueueTypes where queueType = 'monthBillEmailIndiv';
			select @queueStatusID = queueStatusID from dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.queueStatus#">;

			UPDATE dbo.queue_monthBillEmailIndiv
			SET statusID = @queueStatusID,
				dateUpdated = getdate()
			WHERE itemID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.itemID#">;
		</cfquery>
	</cffunction>

	<cffunction name="checkOrgQueue" access="private" output="false" returntype="void">
		<!--- check to see if any orgs need to be moved from waitingToProcess to readyToProcess --->
		<cfstoredproc procedure="queue_monthBillEmailOrg_moveWaiting" datasource="#application.dsn.platformQueue.dsn#">
		</cfstoredproc>

		<!--- check to see if all orgs are done and clean things up --->
		<cfstoredproc procedure="queue_monthBillEmailOrg_clearDone" datasource="#application.dsn.platformQueue.dsn#">
		</cfstoredproc>
	</cffunction>

	<cffunction name="checkIndivQueue" access="private" output="false" returntype="void">
		<!--- check to see if any indiv need to be moved from waitingToProcess to readyToProcess --->
		<cfstoredproc procedure="queue_monthBillEmailIndiv_moveWaiting" datasource="#application.dsn.platformQueue.dsn#">
		</cfstoredproc>

		<!--- check to see if all indiv are done and clean things up --->
		<cfstoredproc procedure="queue_monthBillEmailIndiv_clearDone" datasource="#application.dsn.platformQueue.dsn#">
		</cfstoredproc>
	</cffunction>

	<cffunction name="processQueue" access="private" output="false" returntype="struct">
		<cfset var local = structnew()>
		<cfset local.strReturn = { success:true, itemCount:0 }>

		<cftry>
			<cfstoredproc procedure="queue_monthBillEmailOrg_grabForProcessing" datasource="#application.dsn.platformQueue.dsn#">
				<cfprocresult name="local.qryOrgs" resultset="1">
			</cfstoredproc>
			<cfset local.strReturn.itemCount = local.qryOrgs.recordCount>

			<cfif local.qryOrgs.recordcount gt 0>
				<cfset local.qryOrgs.each(function(row) {
					updateEmailOrgQueueItemStatus(itemID=arguments.row.itemID, queueStatus="processingItem");

					var missingfile = false;
					if (!missingfile and len(arguments.row.tsFolderPath) and not fileExists("#arguments.row.tsFolderPath#/#arguments.row.tsFileName#"))
						missingfile = true;
					if (!missingfile and len(arguments.row.swFolderPath) and not fileExists("#arguments.row.swFolderPath#/#arguments.row.swFileName#"))
						missingfile = true;
					if (!missingfile and len(arguments.row.tsrFolderPath) and not fileExists("#arguments.row.tsrFolderPath#/#arguments.row.tsrFileName#"))
						missingfile = true;

					if (!missingfile) {
						if (arguments.row.swIsPayable is 1 and len(arguments.row.swFolderPath)) {
							emailStatementToBillCom(pathToFile="#arguments.row.swFolderPath#/#arguments.row.swFileName#", subject="#arguments.row.siteCode# SeminarWeb Statement");
						}

						if (len(arguments.row.tsrFolderPath))
							emailStatementToBillCom(pathToFile="#arguments.row.tsrFolderPath#/#arguments.row.tsrFileName#", subject="#arguments.row.siteCode# TrialSmith Royalty Statement");

						if (len(arguments.row.tsFolderPath) OR len(arguments.row.swFolderPath) OR len(arguments.row.tsrFolderPath))
							emailStatement(billingEmail=arguments.row.billingEmail, EOMPeriod=arguments.row.EOMPeriod, accountName=arguments.row.orgName, 
								sourceID=val(arguments.row.sourceID), balanceForward=val(arguments.row.balanceForward), charges=val(arguments.row.charges), 
								credits=val(arguments.row.credits), balanceEnd=val(arguments.row.balanceEnd), willChargeCC=arguments.row.willChargeCC, 
								swIsPayable=val(arguments.row.swIsPayable), swPayableAmount=val(arguments.row.swPayableAmount), 
								swFolderPath=arguments.row.swFolderPath, swFileName=arguments.row.swFileName, 
								tsrFolderPath=arguments.row.tsrFolderPath, tsrFileName=arguments.row.tsrFileName, tsrPayableAmount=val(arguments.row.tsrPayableAmount),
								tsFolderPath=arguments.row.tsFolderPath, tsFileName=arguments.row.tsFileName, 
								MCMemberID=val(arguments.row.MCMemberID), payLinkDirect=arguments.row.payLinkDirect, payLinkCode=arguments.row.payLinkCode);

						updateEmailOrgQueueItemStatus(itemID=arguments.row.itemID, queueStatus="done");
					}
					}, true, 20)>
			</cfif>

			<cfstoredproc procedure="queue_monthBillEmailIndiv_grabForProcessing" datasource="#application.dsn.platformQueue.dsn#">
				<cfprocresult name="local.qryMembers" resultset="1">
			</cfstoredproc>
			<cfset local.strReturn.itemCount = local.strReturn.itemCount + local.qryMembers.recordCount>

			<cfif local.qryMembers.recordcount gt 0>
				<cfset local.qryMembers.each(function(row) {
					updateEmailIndivQueueItemStatus(itemID=arguments.row.itemID, queueStatus="processingItem");
					
					var missingfile = false;
					if (len(arguments.row.tsFolderPath) and not fileExists("#arguments.row.tsFolderPath#/#arguments.row.tsFileName#"))
						missingfile = true;

					if (!missingfile) {
						if (len(arguments.row.tsFolderPath)) {
							local.accountName = trim(arguments.row.firstName & " " & arguments.row.lastName);
							local.billingEmail = listAppend(arguments.row.billingEmail1,arguments.row.billingEmail2,";");

							emailStatement(billingEmail=local.billingEmail, EOMPeriod=arguments.row.EOMPeriod, accountName=local.accountName, 
								sourceID=val(arguments.row.sourceID), balanceForward=val(arguments.row.balanceForward), charges=val(arguments.row.charges), 
								credits=val(arguments.row.credits), balanceEnd=val(arguments.row.balanceEnd), willChargeCC=arguments.row.willChargeCC, 
								swIsPayable=0, swPayableAmount=0, swFolderPath='', swFileName='', tsrFolderPath='', tsrFileName='', tsrPayableAmount=0,
								tsFolderPath=arguments.row.tsFolderPath, tsFileName=arguments.row.tsFileName, MCMemberID=val(arguments.row.MCMemberID), 
								payLinkDirect=arguments.row.payLinkDirect, payLinkCode=arguments.row.payLinkCode);
						}

						updateEmailIndivQueueItemStatus(itemID=arguments.row.itemID, queueStatus="done");
					}
					}, true, 20)>
			</cfif>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfset local.strReturn.success = false>
		</cfcatch>
		</cftry>			
					
		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="emailStatementToBillCom" access="private" output="false" returntype="void">
		<cfargument name="pathToFile" type="string" required="true">
		<cfargument name="subject" type="string" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfset local.emailRecipient = "<EMAIL>">
			<cfif application.MCEnvironment neq "production">
				<cfset local.emailRecipient = "<EMAIL>">
			</cfif>

			<cfmail from="<EMAIL>" to="#local.emailRecipient#" subject="#arguments.subject#" mimeattach="#arguments.pathToFile#">
			</cfmail>	
		<cfcatch type="Any">
			<cfset arguments.objError.sendError(cfcatch=cfcatch, objectToDump=arguments)>
		</cfcatch>		
		</cftry>
	</cffunction>

	<cffunction name="emailStatement" access="private" output="false" returntype="void">
		<cfargument name="billingEmail" type="string" required="true">
		<cfargument name="EOMPeriod" type="date" required="true">
		<cfargument name="accountName" type="string" required="true">
		<cfargument name="sourceID" type="numeric" required="true">
		<cfargument name="balanceForward" type="numeric" required="true">
		<cfargument name="charges" type="numeric" required="true">
		<cfargument name="credits" type="numeric" required="true">
		<cfargument name="balanceEnd" type="numeric" required="true">
		<cfargument name="willChargeCC" type="boolean" required="true">
		<cfargument name="swIsPayable" type="boolean" required="true">
		<cfargument name="swPayableAmount" type="numeric" required="true">
		<cfargument name="swFolderPath" type="string" required="true">
		<cfargument name="swFileName" type="string" required="true">
		<cfargument name="tsrFolderPath" type="string" required="true">
		<cfargument name="tsrFileName" type="string" required="true">
		<cfargument name="tsrPayableAmount" type="numeric" required="true">
		<cfargument name="tsFolderPath" type="string" required="true">
		<cfargument name="tsFileName" type="string" required="true">
		<cfargument name="MCMemberID" type="numeric" required="true">
		<cfargument name="payLinkDirect" type="string" required="true">
		<cfargument name="payLinkCode" type="string" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfsavecontent variable="local.emailContent">
				<cfoutput>
				<table>
				<tr>
					<td style="padding-right:20px;"><img src="https://www.trialsmith.com/images/TS_288x28.png" width="288" height="28" alt="[TrialSmith Logo]"></td>
					<td style="padding-right:20px;"><img src="https://www.trialsmith.com/images/MC_275x50.png" width="275" height="50" alt="[MemberCentral Logo]"></td>
					<td><img src="https://www.trialsmith.com/images/SW_215x50.png" width="215" height="50" alt="[SeminarWeb Logo]"></td>
				</tr>
				</table>

				<br/>
				<div style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;padding-bottom:6px;">Account:</div>
				<div>#arguments.accountName#</div>
				<cfif arguments.sourceID gt 0>
					<div>Account Number: #arguments.sourceID#</div>
				</cfif>

				<br/><br/>
				<div style="font:bold 14px Verdana,Helvetica,Arial,sans-serif;color:##069;padding-bottom:8px;">Activity Summary for #DateFormat(arguments.EOMPeriod,'mmmm yyyy')#</div>
				<table cellspacing="0" cellpadding="4">
				<cfif len(arguments.tsFolderPath)>
					<tr>
						<td style="font-size:12px;font-family:verdana;line-height:14px;text-align:right;padding-bottom:6px;padding-right:8px;">Previous Balance:</td>
						<td style="font-size:12px;font-family:verdana;line-height:14px;text-align:right;padding-bottom:6px;">#DollarFormat(arguments.balanceForward)#<cfif arguments.balanceForward gte 0>&nbsp;</cfif></td>
						<td></td>
					</tr>
					<tr>
						<td style="font-size:12px;font-family:verdana;line-height:14px;text-align:right;padding-bottom:6px;padding-right:8px;">(+) Total Charges on Statement:</td>
						<td style="font-size:12px;font-family:verdana;line-height:14px;text-align:right;padding-bottom:6px;">#DollarFormat(arguments.charges)#<cfif arguments.charges gte 0>&nbsp;</cfif></td>
						<td></td>
					</tr>
					<tr>
						<td style="font-size:12px;font-family:verdana;line-height:14px;text-align:right;padding-bottom:6px;padding-right:8px;">(-) Total Credits/Payments on Statement:</td>
						<td style="font-size:12px;font-family:verdana;line-height:14px;text-align:right;padding-bottom:6px;">#DollarFormat(arguments.credits)#<cfif arguments.credits gte 0>&nbsp;</cfif></td>
						<td></td>
					</tr>
					<tr valign="top">
						<td style="font-size:13px;font-family:verdana;line-height:14px;text-align:right;padding-bottom:6px;padding-right:8px;"><b>Total <cfif arguments.balanceEnd gte 0>Amount Due<cfelse>Credit</cfif>:</b></td>
						<td style="font-size:13px;font-family:verdana;line-height:14px;text-align:right;padding-bottom:6px;"><b>#DollarFormat(arguments.balanceEnd)#</b><cfif arguments.balanceEnd gte 0>&nbsp;</cfif></td>
						<td><cfif len(arguments.payLinkDirect) AND NOT arguments.willChargeCC><a href="https://trialsmith.com/tss#arguments.payLinkDirect#">Pay Online</a></cfif></td>
					</tr>
				</cfif>
				<cfif arguments.swIsPayable is 1 and arguments.swPayableAmount gt 0>
					<tr valign="top">
						<td style="font-size:13px;font-family:verdana;line-height:14px;text-align:right;padding-bottom:6px;padding-top:12px;padding-right:8px;"><b>SeminarWeb Royalty Payment (issued via bill.com):</b></td>
						<td style="font-size:13px;font-family:verdana;line-height:14px;text-align:right;padding-bottom:6px;padding-top:12px;color:##6B9C31;"><b>#DollarFormat(arguments.swPayableAmount)#</b>&nbsp;</td>
						<td></td>
					</tr>
				</cfif>
				<cfif arguments.tsrPayableAmount gt 0>
					<tr valign="top">
						<td style="font-size:13px;font-family:verdana;line-height:14px;text-align:right;padding-bottom:6px;padding-top:12px;padding-right:8px;"><b>TrialSmith Royalty Payment (issued via bill.com):</b></td>
						<td style="font-size:13px;font-family:verdana;line-height:14px;text-align:right;padding-bottom:6px;padding-top:12px;color:##6B9C31;"><b>#DollarFormat(arguments.tsrPayableAmount)#</b>&nbsp;</td>
						<td></td>
					</tr>
				</cfif>
				</table>
				<cfif len(arguments.tsFolderPath) and arguments.balanceEnd gt 0 and arguments.willChargeCC>
					<div style="padding-top:6px;">
						<b>You are setup for Autopay. DO NOT PAY -- We will collect this Amount Due automatically.</b>	
					</div>
				</cfif>
				<cfif len(arguments.tsFolderPath) and arguments.balanceEnd lt 0>
					<div style="padding-top:6px;">
						You have a credit balance of #DollarFormat(abs(arguments.balanceEnd))#. 
						We will apply this credit to future statement balances automatically or you may request a refund.
					</div>
				</cfif>

				<br/><br/>
				<div style="font:bold 14px Verdana,Helvetica,Arial,sans-serif;color:##069;padding-bottom:8px;">Attached Statements for #DateFormat(arguments.EOMPeriod,'mmmm yyyy')#</div>
				<cfif len(arguments.swFolderPath)>
					<div style="padding-left:30px;padding-bottom:4px;">&bull; SeminarWeb Statement of Activity  - This statement provides a detailed summary of your SeminarWeb activity.</div>
				</cfif>
				<cfif len(arguments.tsrFolderPath)>
					<div style="padding-left:30px;padding-bottom:4px;">&bull; TrialSmith Royalty Statement - This statement provides a summary of your TrialSmith royalties.</div>
				</cfif>
				<cfif len(arguments.tsFolderPath)>
					<div style="padding-left:30px;padding-bottom:4px;">&bull; Billing Statement - This statement shows the balance forward, all fees, payments, and account balance.</div>
				</cfif>

				<br/><br/>
				<div style="font:bold 14px Verdana,Helvetica,Arial,sans-serif;color:##069;padding-bottom:8px;">Payments and Billing Assistance</div>
				<cfif arguments.balanceEnd gt 0>
					<div>For Payments:</div>
					<cfif LEN(arguments.payLinkDirect) AND NOT arguments.willChargeCC>
						<div style="padding-left:30px;padding-top:4px;">&bull; <a href="https://trialsmith.com/tss#arguments.payLinkDirect#">Pay online @ trialsmith.com/tss</a> using Statement Code: #arguments.payLinkCode#</div>
					</cfif>
					<div style="padding-left:30px;padding-top:4px;">&bull; Call ************ between 9-6 CT to pay by phone.</div>
					<div style="padding-left:30px;padding-top:4px;padding-bottom:10px;">&bull; Mail check payments to TrialSmith, Inc., PO Box 669250, Dallas, TX 75266-9250 *</div>
					<div style="padding-left:30px;padding-top:4px;padding-bottom:10px;">&bull; * Note our new address as of Feb 1, 2022</div>
				</cfif>
				<br/>
				<div style="padding-top:4px;">Email <a href="mailto:<EMAIL>"><EMAIL></a> for SeminarWeb questions.</div>
				<div style="padding-top:4px;">Email <a href="mailto:<EMAIL>"><EMAIL></a> for MemberCentral questions.</div>
				<div style="padding-top:4px;">Email <a href="mailto:<EMAIL>"><EMAIL></a> for TrialSmith questions.</div>

				<br/><br/><br/>
				<div>MemberCentral, SeminarWeb, and TrialSmith are registered service marks of TrialSmith, Inc, Austin, Texas.</div>
				</cfoutput>
			</cfsavecontent>

			<cfscript>
			local.mc_siteinfo = application.objSiteInfo.getSiteInfo('TS');

			local.emailRecipient = arguments.billingEmail;
			if (NOT len(local.emailRecipient))
				local.emailRecipient = "<EMAIL>";
			if (NOT arguments.MCMemberID gt 0)
				arguments.MCMemberID = local.mc_siteinfo.sysMemberID;

			local.arrEmailTo = [];
			local.emailRecipient = replace(replace(local.emailRecipient,",",";","ALL")," ","","ALL");
			local.toEmailArr = listToArray(local.emailRecipient,';');
			for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
				local.arrEmailTo.append({ name:'', email:trim(local.toEmailArr[local.i]) });
			}

			local.arrAttachments = [];
			if (len(arguments.swFolderPath))
				local.arrAttachments.append( { file=arguments.swFileName, folderpath=arguments.swFolderPath } );
			if (len(arguments.tsrFolderPath))
				local.arrAttachments.append( { file=arguments.tsrFileName, folderpath=arguments.tsrFolderPath } );
			if (len(arguments.tsFolderPath))
				local.arrAttachments.append( { file=arguments.tsFileName, folderpath=arguments.tsFolderPath } );

			local.responseStruct = application.objEmailWrapper.sendMailESQ(
				emailfrom={ name="TrialSmith", email="<EMAIL>" },
				emailto=local.arrEmailTo,
				emailreplyto="",
				emailsubject="#DateFormat(arguments.EOMPeriod,'mmmm yyyy')# Billing Statement for TrialSmith, SeminarWeb, and MemberCentral - #arguments.accountName#",
				emailtitle="#DateFormat(EOMPeriod,'mmmm yyyy')# Billing Statement",
				emailhtmlcontent=local.emailContent,
				emailAttachments=local.arrAttachments,
				siteID=local.mc_siteinfo.siteID,
				memberID=arguments.MCMemberID,
				messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="MONTHLYBILL"),
				sendingSiteResourceID=local.mc_siteinfo.siteSiteResourceID,
				doWrapEmail=true
			);
			</cfscript>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=arguments)>
		</cfcatch>		
		</cftry>
	</cffunction>

	<cffunction name="getOrgQueueItemCount" access="private" output="false" returntype="numeric">
		<cfset var qryQueueItems = "">

		<cfquery name="qryQueueItems" datasource="#application.dsn.platformQueue.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select count(itemID) as itemCount
			from dbo.queue_monthBillEmailOrg;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryQueueItems.itemCount>
	</cffunction>

	<cffunction name="getIndivQueueItemCount" access="private" output="false" returntype="numeric">
		<cfset var qryQueueItems = "">

		<cfquery name="qryQueueItems" datasource="#application.dsn.platformQueue.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select count(itemID) as itemCount
			from dbo.queue_monthBillEmailIndiv;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryQueueItems.itemCount>
	</cffunction>
</cfcomponent>