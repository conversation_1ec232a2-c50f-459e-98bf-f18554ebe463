ALTER PROC dbo.queue_monthBillTSA_grabForProcessing
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @batchSize int, @queueTypeID int, @statusReady int, @statusGrabbed int;

	set @batchSize = 60;
	select @queueTypeID = queueTypeID from dbo.tblQueueTypes where queueType = 'monthBillTSA';
	select @statusReady = queueStatusID from dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'readyToProcess';
	select @statusGrabbed = queueStatusID from dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'grabbedForProcessing';

	IF OBJECT_ID('tempdb..#tmpStatements') IS NOT NULL 
		DROP TABLE #tmpStatements;
	CREATE TABLE #tmpStatements (itemID int);

	-- dequeue in order of dateAdded. get @batchsize statements
	UPDATE qi WITH (UPDLOCK, READPAST)
	SET qi.statusID = @statusGrabbed,
		qi.dateUpdated = getdate()
		OUTPUT inserted.itemID
		INTO #tmpStatements
	FROM dbo.queue_monthBillTSA as qi
	INNER JOIN (
		SELECT top(@BatchSize) qi2.itemID 
		from dbo.queue_monthBillTSA as qi2
		WHERE qi2.statusID = @statusReady
		ORDER BY qi2.dateAdded, qi2.itemID
	) as batch on batch.itemID = qi.itemID
	WHERE qi.statusID = @statusReady;

	-- final data
	select qi.itemID, qi.depomemberDataID, qi.transStartDate, qi.transEndDate, qi.statementDate
	from #tmpStatements as tmp
	inner join dbo.queue_monthBillTSA as qi on qi.itemID = tmp.itemID
	order by qi.itemID;

	IF OBJECT_ID('tempdb..#tmpStatements') IS NOT NULL 
		DROP TABLE #tmpStatements;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
