ALTER PROC dbo.job_CLEReport_grabForNotification
@batchSize int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @statusReady int, @statusGrabbed int, @queueTypeID int;

	select @queueTypeID = queueTypeID from dbo.tblQueueTypes where queueType = 'caCLECredits';
	select @statusReady = queueStatusID from dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'readyToNotify';
	select @statusGrabbed = queueStatusID from dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'grabbedForNotifying';

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	CREATE TABLE #tmpNotify (itemGroupUID uniqueidentifier);

	-- dequeue. 
	; WITH itemGroupUIDs AS (
		select distinct top(@BatchSize) qid.itemGroupUID
		from dbo.tblQueueItems as qi
		inner join dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
		where qi.queueStatusID = @statusReady
			except
		select distinct qid.itemGroupUID
		from dbo.tblQueueItems as qi
		inner join dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
		where qi.queueStatusID <> @statusReady
	)
	UPDATE dbo.tblQueueItems WITH (UPDLOCK, READPAST)
	SET queueStatusID = @statusGrabbed,
		dateUpdated = getdate()
		OUTPUT qid.itemGroupUID
		INTO #tmpNotify
	FROM dbo.tblQueueItems as qi
	INNER JOIN dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
	INNER JOIN itemGroupUIDs on itemGroupUIDs.itemGroupUID = qid.itemGroupUID
	where qi.queueStatusID = @statusReady;

	select distinct tmpN.itemGroupUID, me.email as reportEmail, s.siteName, s.siteCode, mActive.firstname, mActive.lastname, mActive.memberNumber, mActive.memberID
	from (select distinct itemGroupUID from #tmpNotify) as tmpN
	INNER JOIN platformQueue.dbo.tblQueueItemData as qid on qid.itemGroupUID = tmpN.itemGroupUID
	INNER JOIN membercentral.dbo.sites as s on s.siteID = qid.siteID
	INNER JOIN membercentral.dbo.ams_members as m on m.orgID in (s.orgID,1)
		AND m.memberID = qid.recordedByMemberID
	INNER JOIN membercentral.dbo.ams_members as mActive on mActive.orgID in (s.orgID,1)
		AND mActive.memberID = m.activeMemberID
	INNER JOIN membercentral.dbo.ams_memberEmailTags as metag on metag.orgID in (s.orgID,1)
		AND metag.memberID = mActive.memberID
	INNER JOIN membercentral.dbo.ams_memberEmailTagTypes as metagt on metagt.orgID in (s.orgID,1)
		and metagt.emailTagTypeID = metag.emailTagTypeID
		and metagt.emailTagType = 'Primary'
	INNER JOIN membercentral.dbo.ams_memberEmails as me on me.orgID in (s.orgID,1)
		and me.memberID = metag.memberID
		and me.emailTypeID = metag.emailTypeID
	order by tmpN.itemGroupUID;

	IF OBJECT_ID('tempdb..#tmpNotify') IS NOT NULL
		DROP TABLE #tmpNotify;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
