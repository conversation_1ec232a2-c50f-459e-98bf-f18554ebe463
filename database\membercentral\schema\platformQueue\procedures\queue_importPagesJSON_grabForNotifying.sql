ALTER PROC dbo.queue_importPagesJSON_grabForNotifying

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @statusReady int, @statusGrabbed int;
	SELECT @queueTypeID = queueTypeID FROM dbo.tblQueueTypes WHERE queueType = 'importPagesJSON';
	SELECT @statusReady = queuestatusID FROM dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID and queueStatus = 'ReadyToNotify';
	SELECT @statusGrabbed = queuestatusID FROM dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID and queueStatus = 'GrabbedForNotifying';

	IF OBJECT_ID('tempdb..#tmpImportPagesJSON') IS NOT NULL
		DROP TABLE #tmpImportPagesJSON;
	CREATE TABLE #tmpImportPagesJSON (itemID int);

	WITH processedJobs AS (
		SELECT qi.itemID 
		FROM dbo.queue_importPagesJSON AS qi
		WHERE qi.statusID = @statusReady
		AND NOT EXISTS (
			SELECT 1
			FROM dbo.queue_importPagesJSON AS tmp
			WHERE tmp.itemGroupUID = qi.itemGroupUID
			AND tmp.statusID <> @statusReady
		)
	)
	UPDATE qid WITH (UPDLOCK, READPAST)
	SET qid.statusID = @statusGrabbed,
		qid.dateUpdated = getdate()
		OUTPUT inserted.itemID
		INTO #tmpImportPagesJSON
	FROM dbo.queue_importPagesJSON AS qid
	INNER JOIN processedJobs AS batch ON batch.itemID = qid.itemID;

	SELECT DISTINCT qid.itemGroupUID, qid.itemID, qid.siteID, qid.submittedMemberID, qid.pageJSON, qid.errorMessage, mActive.firstName + ' ' + mActive.lastName as memberName, me.email AS memberEmail
	FROM #tmpImportPagesJSON AS tmp
	INNER JOIN dbo.queue_importPagesJSON AS qid ON qid.itemID = tmp.itemID
	INNER JOIN membercentral.dbo.sites as s on s.siteID = qid.siteID
	INNER JOIN membercentral.dbo.ams_members AS m ON m.orgID in (s.orgID,1) and m.memberID = qid.submittedMemberID
	INNER JOIN membercentral.dbo.ams_members as mActive on mActive.orgID = m.orgID and mActive.memberID = m.activeMemberID
	INNER JOIN membercentral.dbo.ams_memberEmails AS me ON me.orgID = m.orgID and me.memberID = mActive.memberID
	INNER JOIN membercentral.dbo.ams_memberEmailTags AS metag ON metag.orgID = m.orgID and metag.memberID = me.memberID AND metag.emailTypeID = me.emailTypeID
	INNER JOIN membercentral.dbo.ams_memberEmailTagTypes AS metagt ON metagt.orgID = m.orgID and metagt.emailTagTypeID = metag.emailTagTypeID AND metagt.emailTagType = 'Primary'
	ORDER BY qid.itemGroupUID, qid.itemID;

	IF OBJECT_ID('tempdb..#tmpImportPagesJSON') IS NOT NULL
		DROP TABLE #tmpImportPagesJSON;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
