<cfcomponent output="false" extends="model.scheduledTasks.scheduledTasks">

	<cffunction name="runTask" access="public" returntype="void" output="false">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structnew()>

		<cfsetting requesttimeout="500">

		<cfset local.itemCount = getQueueItemCount()>

		<cfif local.itemCount GT 0>
			<cfscript>
  				local.arrTaskFields = [ { name="BatchSize", type="INTEGER", desc="Batch Size", value="15" } ];
  				local.strTaskFields = setTaskCustomFields(siteID=application.objSiteInfo.getSiteInfo('MC').siteID, siteResourceID=arguments.strTask.siteResourceID, arrTaskFields=local.arrTaskFields);
  			</cfscript>
  			<cfset local.success = processQueue(batchSize=local.strTaskFields.batchSize)>
			<cfif NOT local.success>
				<cfthrow message="Error running processQueue()">
			</cfif>
		</cfif>

		<cfset local.historyID = (arguments.strTask.historyID ?: 0)>
		<cfset addBatchIdentifierToHistory(historyID=local.historyID, batchIdentifier="", itemCount=local.itemCount)>
	</cffunction>
	
	<cffunction name="processQueue" access="private" output="false" returntype="boolean">
		<cfargument name="batchSize" type="numeric" required="true">

		<cfset var local = structnew()>
		<cfset local.success = true>

		<cftry>
			<cfstoredproc procedure="queue_depoCrawlerIndex_grabForProcessing" datasource="#application.dsn.platformQueue.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.batchSize#">
				<cfprocresult name="local.qryNames" resultset="1">
			</cfstoredproc>

			<cfif local.qryNames.recordCount>
				<cfset local.siteID = application.objSiteInfo.getSiteInfo('TS').siteID>

				<cfset local.objBuckets = {
					"Depos" = createObject("component", "model.search.depositions"),
					"Related" = createObject("component", "model.search.depositions"),
					"Discactions" = createObject("component", "model.search.DisciplinaryActions"),
					"Mdex" = createObject("component", "model.search.mdex"),
					"Lyris" = createObject("component", "model.search.lyris"),
					"Searches" = createObject("component", "model.search.searches"),
					"Courtdocs" = createObject("component", "model.search.courtdocuments")
				}>

				<cfquery name="local.getBuckets" datasource="#application.dsn.tlasites_search.dsn#">
					select bucketID, bucketName, bucketSettings
					from dbo.tblSearchBuckets
					where siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.siteID#"> 
					and isActive = 1
				</cfquery>

				<cfset local.arrBuckets = arrayNew(1)>
				<cfloop query="local.getBuckets">
					<cfswitch expression="#local.getBuckets.bucketName#">
						<cfcase value="Deposition Testimony">
							<cfset local.arrBuckets.append({name="depos", bucketID=local.getBuckets.bucketID})>
						</cfcase>
						<cfcase value="Related Testimony">
							<cfset local.arrBuckets.append({name="related", bucketID=local.getBuckets.bucketID})>
						</cfcase>
						<cfcase value="Disciplinary Actions">
							<cfset local.arrBuckets.append({name="discactions", bucketID=local.getBuckets.bucketID})>
						</cfcase>
						<cfcase value="Expert Challenges">
							<cfset local.arrBuckets.append({name="mdex", bucketID=local.getBuckets.bucketID})>
						</cfcase>
						<cfcase value="List Messages">
							<cfset local.arrBuckets.append({name="lyris", bucketID=local.getBuckets.bucketID})>
						</cfcase>
						<cfcase value="Similar Searches">
							<cfset local.arrBuckets.append({name="searches", bucketID=local.getBuckets.bucketID})>
						</cfcase>
						<cfcase value="Briefs & Motions">
							<cfset local.arrBuckets.append({name="courtdocs", bucketID=local.getBuckets.bucketID})>
						</cfcase>
					</cfswitch>
				</cfloop>

				<cfloop query="local.qryNames">
					<cfset local.thisItemID = local.qryNames.itemID>
					<cfset local.thisItemNameID = local.qryNames.nameID>
					<cfset local.thisItemFirstName = local.qryNames.firstname>
					<cfset local.thisItemLastName = local.qryNames.lastname>

					<!--- item must still be in the grabbedForProcessing state for this job. else skip it. 												--->
					<!--- this is here because we have a check in place that will attempt to clear items "stuck" in the grabbedForProcessing state and 	--->
					<!--- those items may be grabbed by another job, causing it to possible be processed twice.	--->
					<cfquery name="local.checkItemID" datasource="#application.dsn.platformQueue.dsn#">
						select count(qi.itemID) as itemCount
						from dbo.queue_depoCrawlerIndex as qi
						inner join dbo.tblQueueStatuses as qs on qs.queueStatusID = qi.statusID 
						where qi.itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisItemID#">
						and qs.queueStatus = 'grabbedForProcessing'
					</cfquery>

					<cfif local.checkItemID.itemCount>	
						<cfquery name="local.updateToProcessing" datasource="#application.dsn.platformQueue.dsn#">
							set nocount on;

							declare @queueTypeID int, @statusProcessing int;
							select @queueTypeID = queueTypeID from dbo.tblQueueTypes where queueType = 'depoCrawlerIndex';
							select @statusProcessing = queueStatusID from dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'processingItem';

							UPDATE dbo.queue_depoCrawlerIndex
							SET statusID = @statusProcessing,
								dateUpdated = getdate()
							WHERE itemID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.thisItemID#">;
						</cfquery>

						<cfset local.arrSearchResults = ArrayNew(1)>
						<cfloop array="#local.arrBuckets#" item="local.thisBucket"> 
							<cfset local.arrSearchResults.append({ 
								name = local.thisBucket.name, 
								bucketID = local.thisBucket.bucketID, 
								count = local.objBuckets[local.thisBucket.name].getResultsCountForSearchIndex(siteid=local.siteID, bucketid=local.thisBucket.bucketID, firstname=local.thisItemFirstName, lastname=local.thisItemLastName).itemCount 
							})>
						</cfloop>

						<cfquery name="local.qryLogCounts" datasource="#application.dsn.tlasites_search.dsn#">
							SET NOCOUNT ON;

							DECLARE @nameID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.thisItemNameID#">, @sumDiff int;
							DECLARE @tblNewCounts TABLE (bucketID int, newCount int);
							DECLARE @tblCounts TABLE (bucketID int, oldCount int, newCount int, diffCount int);
							DECLARE @nextRunDate datetime = DATEADD(month,1,GETDATE());

							<cfloop array="#local.arrSearchResults#" item="local.thisResult"> 
								insert into @tblNewCounts (bucketID, newCount)
								values (<cfqueryparam cfsqltype="cf_sql_integer" value="#local.thisResult.bucketID#">, <cfqueryparam cfsqltype="cf_sql_integer" value="#local.thisResult.count#">);
							</cfloop>

							INSERT INTO @tblCounts (bucketID, oldCount, newCount, diffCount)
							SELECT bucketID, count, 0, 0
							FROM dbo.tblSearchEngineIndexBucketCounts 
							WHERE nameID = @nameID;

							MERGE @tblCounts AS target 
								USING @tblNewCounts AS source ON source.bucketID = target.bucketID
							WHEN MATCHED THEN 
								UPDATE SET target.newCount = source.newCount
							WHEN NOT MATCHED THEN
								INSERT (bucketID, oldCount, newCount) 
								VALUES (source.bucketID, 0, source.newCount);

							UPDATE @tblCounts
							SET diffCount = 1
							WHERE oldCount <> newCount;

							SELECT @sumDiff = sum(diffCount) FROM @tblCounts;

							IF @sumDiff > 0 BEGIN
								delete from dbo.tblSearchEngineIndexBucketCounts 
								where nameID = @nameID;

								insert into dbo.tblSearchEngineIndexBucketCounts (nameID, bucketID, count)
								SELECT @nameID, bucketID, newCount
								FROM @tblCounts;

								update dbo.tblSearchEngineIndex
								set dateBucketCountsLastUpdated = getdate(),
									dateNextRun = @nextRunDate
								where nameID = @nameID;
							END ELSE 
								update dbo.tblSearchEngineIndex
								set dateNextRun = @nextRunDate
								where nameID = @nameID;

							DELETE FROM platformQueue.dbo.queue_depoCrawlerIndex
							WHERE itemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisItemID#">;
						</cfquery>
					</cfif>
				</cfloop>
			</cfif>

			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
				<cfset local.success = false>
			</cfcatch>
		</cftry>	

		<cfreturn local.success>
	</cffunction>

	<cffunction name="getQueueItemCount" access="private" output="false" returntype="numeric">
		<cfset var qryQueueItems = "">

		<cfquery name="qryQueueItems" datasource="#application.dsn.platformQueue.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT COUNT(itemID) AS itemCount
			FROM dbo.queue_depoCrawlerIndex;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryQueueItems.itemCount>
	</cffunction>

</cfcomponent>