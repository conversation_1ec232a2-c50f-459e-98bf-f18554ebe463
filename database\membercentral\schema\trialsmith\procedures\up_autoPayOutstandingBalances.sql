ALTER PROC dbo.up_autoPayOutstandingBalances
@itemCount int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

    declare @now datetime, @queueTypeID int, @statusReady int, 
		@applicationTypeID int = membercentral.dbo.fn_getApplicationTypeIDFromName('BuyNow'),
		@siteID int = membercentral.dbo.fn_getSiteIDFromSiteCode('MC'), @settingsXML xml;

	set @now = getdate();
	SET @itemCount = 0;
	select @queueTypeID = queueTypeID from platformQueue.dbo.tblQueueTypes where queueType = 'payTSBalance';
	select @statusReady = queuestatusID from platformQueue.dbo.tblQueueStatuses where queueTypeID = @queueTypeID and queueStatus = 'readyToProcess';

	SELECT @settingsXML = settingsXML
	FROM membercentral.dbo.cms_applicationTypeSettings
	WHERE siteID = @siteID
	AND applicationTypeID = @applicationTypeID;

	SELECT @settingsXML = ISNULL(@settingsXML,'<settings />');

	-- get the depomembers tied to orgs -- we wont run these
	IF OBJECT_ID('tempdb..#tmpOrgs') IS NOT NULL 
		DROP TABLE #tmpOrgs;
	CREATE TABLE #tmpOrgs (DepoMemberDataID int PRIMARY KEY);

	insert into #tmpOrgs (DepoMemberDataID)
	select distinct DepoMemberDataID 
	from dbo.membercentralBilling 
	where DepoMemberDataID is not null;


	-- get non-org depomembers with an outstanding balance, set to C, and has a non-declined card on file
	IF OBJECT_ID('tempdb..#tmpDepos') IS NOT NULL 
		DROP TABLE #tmpDepos;
	CREATE TABLE #tmpDepos (depoMemberDataID int PRIMARY KEY, payProfileID int, totalDue decimal(18,2));

	INSERT INTO #tmpDepos (depoMemberDataID, payProfileID, totalDue)
	select d.depomemberdataID, TSCC.payProfileID, SUM(t.AmountBilled + t.salesTaxAmount) AS TotalDue
	from dbo.depoMemberData as d
	inner join dbo.depoTransactions as t on t.depomemberdataID = d.depomemberdataID
	INNER JOIN dbo.ccMemberPaymentProfiles as TSCC on TSCC.depomemberdataID = d.depomemberdataID 
		and TSCC.orgcode = 'TS'
		and TSCC.declined = 0
	where d.paymentType = 'C'
	AND NOT EXISTS (select depoMemberDataID from #tmpOrgs where depomemberdataID = d.depomemberdataID)
	AND NOT EXISTS (select depoMemberDataID from platformQueue.dbo.queue_payTSBalance where depomemberdataID = d.depomemberdataID)
	group by d.depomemberdataID, TSCC.payProfileID
	HAVING (SUM(t.AmountBilled + t.salesTaxAmount) > 0);
	
	IF @@ROWCOUNT > 0 BEGIN
		INSERT INTO platformQueue.dbo.queue_payTSBalance (depoMemberDataID, payProfileID, paymentAmount, statusID, dateAdded, dateUpdated)
		SELECT depoMemberDataID, payProfileID, totalDue, @statusReady, @now, @now
		FROM #tmpDepos;
		SET @itemCount = @@ROWCOUNT;

		EXEC membercentral.dbo.sched_resumeTask @name='Process Pay TS Balance Queue', @engine='MCLuceeLinux';
	END

	IF OBJECT_ID('tempdb..#tmpOrgs') IS NOT NULL 
		DROP TABLE #tmpOrgs;
	IF OBJECT_ID('tempdb..#tmpDepos') IS NOT NULL 
		DROP TABLE #tmpDepos;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
