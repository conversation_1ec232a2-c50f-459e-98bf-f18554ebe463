<cfcomponent output="false">

	<cffunction name="getFile" access="public" output="false" returntype="query">
		<cfargument name="fileID" type="numeric" required="yes">

		<cfset var qryFile = "">
		
		<cfstoredproc procedure="sw_getFile" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.fileID#">
			<cfprocresult name="qryFile" resultset="1">
		</cfstoredproc>
		
		<cfreturn qryFile>
	</cffunction>
	
	<cffunction name="getFileTypes" access="public" output="false" returntype="query">
		<cfset var qryFileTypes = "">

		<cfstoredproc procedure="sw_getFileTypes" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocresult name="qryFileTypes" resultset="1">
		</cfstoredproc>

		<cfreturn qryFileTypes>
	</cffunction>

	<cffunction name="saveFile" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="mcproxy_sitecode" type="string" required="yes">
		<cfargument name="titleID" type="numeric" required="yes">
		<cfargument name="fileID" type="numeric" required="yes">
		<cfargument name="fileTypeID" type="numeric" required="yes">
		<cfargument name="fileName" type="string" required="yes">
		<cfargument name="fileTitle" type="string" required="yes">
		<cfargument name="fileDesc" type="string" required="yes">
		<cfargument name="addPVR" type="boolean" required="no" default="0">
		<cfargument name="formatsAvailable" type="string" required="no" default="">

		<cfset var local = structNew()>
		
		<cftry>
			<cfif NOT hasSWFileRights(siteID=arguments.mcproxy_siteID, titleID=arguments.titleID, action='saveFile', checkLockSettings=true)>
				<cfthrow message="invalid request">
			</cfif>

			<cfif arguments.fileID gt 0>
				<cfset updateFile(fileID=arguments.fileID, fileTypeID=arguments.fileTypeID, fileName=arguments.fileName, 
									fileTitle=arguments.fileTitle, fileDesc=arguments.fileDesc)>
			<cfelse>
				<cfset local.data.fileID = addFile(orgcode=arguments.mcproxy_sitecode, titleID=arguments.titleID, fileTypeID=arguments.fileTypeID, 
									fileName=arguments.fileName, fileTitle=arguments.fileTitle, fileDesc=arguments.fileDesc, addPVR=arguments.addPVR,
									formatsAvailable=arguments.formatsAvailable)>
			</cfif>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="addFile" access="private" output="false" returntype="numeric">
		<cfargument name="orgcode" type="string" required="yes">
		<cfargument name="titleID" type="numeric" required="yes">
		<cfargument name="fileTypeID" type="numeric" required="yes">
		<cfargument name="fileName" type="string" required="yes">
		<cfargument name="fileTitle" type="string" required="yes">
		<cfargument name="fileDesc" type="string" required="yes">
		<cfargument name="addPVR" type="boolean" required="no" default="0">
		<cfargument name="formatsAvailable" type="string" required="no" default="">
		
		<cfset var local = structnew()>
		<cfset local.fileID = 0>
		
		<cfstoredproc procedure="sw_addFile" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.orgcode#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.titleID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.fileTypeID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.fileName#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.fileTitle#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.fileDesc#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.addPVR#">
			<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.fileID">
		</cfstoredproc>

		<cfif application.MCEnvironment NEQ 'production'>			
			<cfquery name="qryUpdateFormats" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				UPDATE dbo.tblFiles
				SET formatsAvailable = CAST(<cfqueryparam value="#arguments.formatsAvailable#" cfsqltype="CF_SQL_LONGVARCHAR"> as xml)
				WHERE fileID = <cfqueryparam value="#local.fileID#" cfsqltype="CF_SQL_INTEGER">
			</cfquery>
		</cfif>
		
		<cfreturn local.fileID>
	</cffunction>
	
	<cffunction name="updateFile" access="private" output="false" returntype="void">
		<cfargument name="fileID" type="numeric" required="yes">
		<cfargument name="fileTypeID" type="numeric" required="yes">
		<cfargument name="fileName" type="string" required="yes">
		<cfargument name="fileTitle" type="string" required="yes">
		<cfargument name="fileDesc" type="string" required="yes">

		<cfset var qryUpdateFile = "">
		
		<cfquery name="qryUpdateFile" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			UPDATE dbo.tblFiles
			SET fileTypeID = <cfqueryparam value="#arguments.fileTypeID#" cfsqltype="CF_SQL_INTEGER">,
				fileName = <cfqueryparam value="#arguments.fileName#" cfsqltype="CF_SQL_VARCHAR">,
				fileTitle = <cfqueryparam value="#arguments.fileTitle#" cfsqltype="CF_SQL_VARCHAR">,
				fileDesc = <cfqueryparam value="#arguments.fileDesc#" cfsqltype="CF_SQL_VARCHAR">
			WHERE fileID = <cfqueryparam value="#arguments.fileID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>
	</cffunction>

	<cffunction name="deleteTitleFile" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="titleID" type="numeric" required="yes">
		<cfargument name="fileID" type="numeric" required="yes">

		<cfset var local = structNew()>
		<cfsetting requesttimeout="200">

		<cftry>
			<cfif NOT hasSWFileRights(siteID=arguments.mcproxy_siteID, titleID=arguments.titleID, action='deleteTitleFile', checkLockSettings=true)>
				<cfthrow message="invalid request">
			</cfif>

			<!--- 
				tl 4/2008: 	even though files can technically be linked to more than 1 title/seminar,
							we dont support it. the application logic assumes a file is only linked to 
							one title/seminar. so deleting it here really should delete it from disk 
							as well since it wont be reused.
				
			--->

			<!--- get file info --->
			<cfquery name="local.qryFileInfo" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				select f.fileID, f.participantID, p.orgcode
				from dbo.tblFiles as f
				inner join dbo.tblParticipants as p on p.participantID = f.participantID
				where f.fileID = <cfqueryparam value="#arguments.fileID#" cfsqltype="CF_SQL_INTEGER">
				and f.isDeleted = 0
			</cfquery>

			<cfif local.qryFileInfo.recordcount is 1>
				<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=local.qryFileInfo.orgcode)>
				
				<!--- get objects from S3 and write objectKeys to a file for importing --->
				<cfif application.MCEnvironment eq "production">
					<cfset local.prefixS = lcase("swod/#local.qryFileInfo.orgcode#/#local.qryFileInfo.participantID#/#local.qryFileInfo.fileID#.")>
					<cfset application.objS3.listObjects(bucket="seminarweb", prefix=local.prefixS, delimiter="", maxResults=0, writeToFilePath="#local.strFolder.folderPath#/sw_formats_S.txt")>
				
					<cfset local.prefixD = lcase("swoddownloads/#local.qryFileInfo.orgcode#/#local.qryFileInfo.participantID#/#local.qryFileInfo.fileID#.")>
					<cfset application.objS3.listObjects(bucket="seminarweb", prefix=local.prefixD, delimiter="", maxResults=0, writeToFilePath="#local.strFolder.folderPath#/sw_formats_D.txt")>
				</cfif>

				<!--- mark file as deleted and add to the delete queue --->
				<cfstoredproc procedure="sw_deleteFile" datasource="#application.dsn.tlasites_seminarweb.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryFileInfo.fileID#">
					<cfif application.MCEnvironment eq "production">
						<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.strFolder.folderPathUNC#\sw_formats_S.txt">
						<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.strFolder.folderPathUNC#\sw_formats_D.txt">
					<cfelse>
						<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="true">
        				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="true">
					</cfif>
				</cfstoredproc>
			</cfif>
			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="deleteAllFiles" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="titleID" type="numeric" required="yes">

		<cfset var local = structNew()>
		<cfsetting requesttimeout="200">

		<cftry>
			<cfif NOT hasSWFileRights(siteID=arguments.mcproxy_siteID, titleID=arguments.titleID, action='deleteTitleFile', checkLockSettings=true)>
				<cfthrow message="invalid request">
			</cfif>

			<!--- 
				tl 4/2008: 	even though files can technically be linked to more than 1 title/seminar,
							we dont support it. the application logic assumes a file is only linked to 
							one title/seminar. so deleting it here really should delete it from disk 
							as well since it wont be reused.
				
			--->

			<!--- get file info --->
			<cfquery name="local.qryFileInfo" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				select f.fileID, f.participantID, p.orgcode
				from dbo.tblFiles as f
				inner join dbo.tblParticipants as p on p.participantID = f.participantID
				inner join dbo.tblTitlesAndFiles AS taf on taf.fileID = f.fileID 
				where taf.titleID = <cfqueryparam value="#arguments.titleID#" cfsqltype="CF_SQL_INTEGER">
				and f.isDeleted = 0
			</cfquery>

			<cfloop query="#local.qryFileInfo#">
				<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=local.qryFileInfo.orgcode)>
				
				<!--- get objects from S3 and write objectKeys to a file for importing --->
				<cfif application.MCEnvironment eq "production">
					<cfset local.prefixS = lcase("swod/#local.qryFileInfo.orgcode#/#local.qryFileInfo.participantID#/#local.qryFileInfo.fileID#.")>
					<cfset application.objS3.listObjects(bucket="seminarweb", prefix=local.prefixS, delimiter="", maxResults=0, writeToFilePath="#local.strFolder.folderPath#/sw_formats_S.txt")>

					<cfset local.prefixD = lcase("swoddownloads/#local.qryFileInfo.orgcode#/#local.qryFileInfo.participantID#/#local.qryFileInfo.fileID#.")>
					<cfset application.objS3.listObjects(bucket="seminarweb", prefix=local.prefixD, delimiter="", maxResults=0, writeToFilePath="#local.strFolder.folderPath#/sw_formats_D.txt")>
				</cfif>

				<!--- mark file as deleted and add to the delete queue --->
				<cfstoredproc procedure="sw_deleteFile" datasource="#application.dsn.tlasites_seminarweb.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryFileInfo.fileID#">
					<cfif application.MCEnvironment eq "production">
						<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.strFolder.folderPathUNC#\sw_formats_S.txt">
						<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.strFolder.folderPathUNC#\sw_formats_D.txt">
					<cfelse>
						<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="true">
						<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="true">
					</cfif>
				</cfstoredproc>
			</cfloop>
			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="moveTitleFile" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="titleID" type="numeric" required="yes">
		<cfargument name="orderData" type="string" required="yes">

		<cfset var local = structNew()>

		<cftry>
			<cfif NOT hasSWFileRights(siteID=arguments.mcproxy_siteID, titleID=arguments.titleID, action='moveTitleFile', checkLockSettings=true)>
				<cfthrow message="invalid request">
			</cfif>
			<cfset local.orderData = DeserializeJSON(arguments.orderData)>
			<cfif isArray(local.orderData) and arrayLen(local.orderData) GT 1>
				<cfloop array="#local.orderData#" index="local.thisData">
					<cfquery datasource="#application.dsn.tlasites_seminarweb.dsn#" name="local.qryUpdateData">
						UPDATE taf
						SET taf.fileOrder = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisData.newOrder + 1#">
						FROM dbo.tblTitlesAndFiles AS taf
						INNER JOIN dbo.tblFiles AS f ON taf.fileID = f.fileID
						INNER JOIN dbo.tblFilesTypes AS ft ON f.fileTypeID = ft.filetypeID
						INNER JOIN dbo.tblParticipants AS p ON f.participantID = p.participantID
						WHERE taf.titleID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.titleID#">
						AND f.isDeleted = 0
						AND taf.fileID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisData.id#">
					</cfquery>
				</cfloop>
			</cfif>	
			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="refreshFiles" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="titleID" type="numeric" required="yes">
		<cfargument name="fileID" type="numeric" required="yes">
		<cfargument name="pathToFile" type="string" required="yes">

		<cfset var local = structNew()>

		<cfif NOT hasSWFileRights(siteID=arguments.mcproxy_siteID, titleID=arguments.titleID, action='refreshFiles', checkLockSettings=true)>
			<cfreturn { "success":false }>
		</cfif>

		<cfhttp url="#application.paths.backendPlatform.internalURL#?event=SWProductionUtils.syncFileFormats&fileID=#arguments.fileID#&pathtofile=#lCase(arguments.pathToFile)#" method="get" throwonerror="Yes"></cfhttp>

		<cfset local.data.success = true>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="updateTitleFiles" access="public"output="false" returntype="void">
		<cfargument name="titleID" type="numeric" required="yes">
		<cfargument name="arrFiles" type="array" required="yes">

		<cfset var qryUpdateTitleFiles = "">
		<cftry>
			<cfquery name="qryUpdateTitleFiles" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @titleID int;
					SET @titleID = <cfqueryparam value="#arguments.titleID#" cfsqltype="CF_SQL_INTEGER">;

					BEGIN TRAN;
						<cfloop array="#arguments.arrFiles#" index="local.thisFile">
							UPDATE dbo.tblFiles
							SET fileTitle = <cfqueryparam value="#local.thisFile.fileTitle#" cfsqltype="CF_SQL_VARCHAR">
							WHERE fileID = <cfqueryparam value="#local.thisFile.fileID#" cfsqltype="CF_SQL_INTEGER">
						</cfloop>
					COMMIT TRAN;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>
	</cffunction>

	<cffunction name="getFilesFromSeminarTitles" access="public" output="false" returntype="struct">
		<cfargument name="participantID" type="numeric" required="yes">
		<cfargument name="seminarID" type="numeric" required="yes">
		<cfargument name="ovLayoutID" type="numeric" required="false" default="0">

		<cfset var local = structNew()>
		<cfset local.returnData = {success=true, allTitlesHaveMediaFiles=false, titlesWithNoMediaFiles='', allTitlesHaveDownloadFiles=false, titlesWithNoDownloadFiles=''}>
		<cftry>
			<cfstoredproc procedure="sw_getTitleFilesFromSeminarID" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">
				<cfprocresult name="local.qryTitleFilesFromSeminarID" resultset="1">
			</cfstoredproc>

			<cfif arguments.ovLayoutID eq 0>
				<cfstoredproc procedure="general_issuesReport" datasource="#application.dsn.tlasites_seminarweb.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.participantID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">
					<cfprocresult name="local.qrySetupIssues">
				</cfstoredproc>
			<cfelse>
				<cfquery datasource="#application.dsn.tlasites_seminarweb.dsn#" name="local.qrySetupIssues">
					SET XACT_ABORT, NOCOUNT ON;
					BEGIN TRY
						DECLARE @seminarID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">,
							@ovLayoutID int =  <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.ovLayoutID#">

						IF OBJECT_ID('tempdb..##tmpSetupIssues') IS NOT NULL
							DROP TABLE ##tmpSetupIssues;
						IF OBJECT_ID('tempdb..##tmpSeminarsSWODOv') IS NOT NULL
							DROP TABLE ##tmpSeminarsSWODOv;
						CREATE TABLE ##tmpSetupIssues (autoID int identity(1,1), checkID tinyint, checkContent varchar(max), programID int, titleID int);
						CREATE TABLE ##tmpSeminarsSWODOv (seminarID int PRIMARY KEY, layoutID int);

						/* overriding value to be used inside general_issuesReport*/
						INSERT INTO ##tmpSeminarsSWODOv (seminarID, layoutID)
						VALUES(@seminarID, @ovLayoutID);

						INSERT INTO ##tmpSetupIssues (checkID, checkContent, programID, titleID)
						EXEC dbo.general_issuesReport @participantID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.participantID#">,
							@seminarID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">;
						
						SELECT checkID, checkContent, programID, titleID
						FROM ##tmpSetupIssues
						ORDER BY checkID, programID, titleID;
						
						IF OBJECT_ID('tempdb..##tmpSetupIssues') IS NOT NULL
							DROP TABLE ##tmpSetupIssues;
						IF OBJECT_ID('tempdb..##tmpSeminarsSWODOv') IS NOT NULL
							DROP TABLE ##tmpSeminarsSWODOv;

					END TRY
					BEGIN CATCH
						IF @@trancount > 0 ROLLBACK TRANSACTION;
						EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
					END CATCH
				</cfquery>
			</cfif>

			<!--- Get a list of all titleIDs in the result set --->
			<cfset local.qryFilteredMediaFiles = queryFilter(local.qryTitleFilesFromSeminarID, function(row) {
				return row.fileType == 'video';
			})>
			<cfset local.qryFilteredDownloadFiles = queryFilter(local.qryTitleFilesFromSeminarID, function(row) {
				return row.fileMode == 'download';
			})>
			<cfset local.titleIDsWithMedia = ValueList(local.qryFilteredMediaFiles.titleID)>
			<cfset local.titleIDsWithDownloads = ValueList(local.qryFilteredDownloadFiles.titleID)>
			<!---<cfset local.videoFileCount = arrayLen(local.qryFilteredMediaFiles)>--->
			<!--- Get all titles associated with the seminar --->
			<cfquery name="local.qrySeminarTitlesWithoutMedia" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				SELECT titleID 
				FROM tblSeminarsAndTitles 
				WHERE seminarID =  <cfqueryparam value="#arguments.seminarID#" cfsqltype="cf_sql_integer">
				<cfif  LEN(local.titleIDsWithMedia)>
					AND titleID NOT IN (<cfqueryparam value="#local.titleIDsWithMedia#" list="true" cfsqltype="cf_sql_integer">)
				</cfif>
			</cfquery>
			<cfquery name="local.qrySeminarTitlesWithoutDownload" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				SELECT titleID 
				FROM tblSeminarsAndTitles 
				WHERE seminarID =  <cfqueryparam value="#arguments.seminarID#" cfsqltype="cf_sql_integer">
				<cfif  LEN(local.titleIDsWithDownloads)>
					AND titleID NOT IN (<cfqueryparam value="#local.titleIDsWithDownloads#" list="true" cfsqltype="cf_sql_integer">)
				</cfif>
			</cfquery>
			<!--- Check if there are any titles without media files --->
			<cfif local.qrySeminarTitlesWithoutMedia.recordCount EQ 0>
				<cfset local.returnData.allTitlesHaveMediaFiles = true>
			<cfelse>
				<cfset local.returnData.titlesWithNoMediaFiles = ValueList(local.qrySeminarTitlesWithoutMedia.titleID)>
			</cfif>
			<cfif local.qrySeminarTitlesWithoutDownload.recordCount EQ 0>
				<cfset local.returnData.allTitlesHaveDownloadFiles = true>
			<cfelse>
				<cfset local.returnData.titlesWithNoDownloadFiles = ValueList(local.qrySeminarTitlesWithoutDownload.titleID)>
			</cfif>
			
			<!--- Return the result --->
			<cfset local.returnData.success = true>
			<cfset local.returnData.data = local.qryFilteredMediaFiles>
			<cfset local.returnData.qrySetupIssues = local.qrySetupIssues>
		
			<cfreturn local.returnData>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.returnData.success = false>
		</cfcatch>
		</cftry>
	</cffunction>
	
	<cffunction name="getUploadFileSetUp" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="mcproxy_sitecode" type="string" required="yes">
		<cfargument name="titleID" type="numeric" required="yes">
		<cfargument name="fileID" type="numeric" required="yes">
		<cfargument name="filemode" type="string" required="yes">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>

		<cfif NOT hasSWFileRights(siteID=arguments.mcproxy_siteID, titleID=arguments.titleID, action='getUploadFileSetUp', checkLockSettings=false)>
			<cfreturn { "success":false }>
		</cfif>

		<cfset local.qryFile = getFile(fileID=arguments.fileID)>
		
		<cfif listFindNoCase("stream,download,paper",arguments.filemode)>
			<cfset local.returnStruct['fileid'] = local.qryFile.fileID>
			<cfset local.returnStruct['filetitle'] = local.qryFile.fileTitle>
			<cfset local.returnStruct['participantid'] = local.qryFile.participantID>
		
			<cfset local.bucket = "seminarweb">
			<cfset local.returnStruct['accesskey'] =  "********************">
			<cfset local.secretKey = "UJZ/JsekvjTNCJOK3UmUUPIAJwEwsoiqnq2jybdX">

			<cfset local.http = "http://">
			<cfif application.objPlatform.isRequestSecure()>
				<cfset local.http = "https://">
			</cfif>

			<cfset local.returnStruct['uploadurl'] = "#local.http##local.bucket#.s3.amazonaws.com">

			<cfswitch expression="#arguments.filemode#">
				<cfcase value="stream">
					<cfset local.returnStruct['objectkey'] = lcase("swod/#arguments.mcproxy_sitecode#/#local.qryFile.participantID#/")>
					<cfset local.returnStruct['fileuploadheading'] = "Upload Streaming Formats">
				</cfcase>
				<cfcase value="download">
					<cfset local.returnStruct['objectkey'] = lcase("swoddownloads/#arguments.mcproxy_sitecode#/#local.qryFile.participantID#/")>
					<cfset local.returnStruct['fileuploadheading'] = "Upload Downloadable Formats">
				</cfcase>
				<cfcase value="paper">
					<cfset local.returnStruct['objectkey'] = lcase("swoddownloads/#arguments.mcproxy_sitecode#/#local.qryFile.participantID#/#arguments.fileID#.pvr/")>
					<cfset local.returnStruct['fileuploadheading'] = "Upload Paper Images">
				</cfcase>
			</cfswitch>

			<!--- generating policy & signature here due to the difference in aws access & secret key @application.objS3 --->
			<cfset local.dateString = DateAdd("d",1,Now())>

			<cfsavecontent variable="local.stringPolicy">
				<cfoutput>
				{
				  "expiration": "#DateFormat(local.dateString,"yyyy-mm-dd")#T12:00:00.000Z",
				  "conditions": [
				    {"bucket": "#local.bucket#" },
				    {"acl": "public-read" },
				    ["starts-with", "$key", "#left(local.returnStruct.objectKey,len(local.returnStruct.objectKey)-1)#"],
				    ["starts-with", "$content-type", ""],
				    ["starts-with", "$name", ""],
				    ["starts-with", "$filename", ""]
				  ]
				}
				</cfoutput>
			</cfsavecontent>
			
			<!--- Replace "\n" with chr(10) to get a correct digest --->
			<cfset local.fixedData = replace(local.stringPolicy,"\n",chr(10),"all")>
			<cfset local.returnStruct['policy'] = ToBase64(local.fixedData)>

			<!--- Replace "\n" with chr(10) to get a correct digest --->
			<cfset local.fixedData = replace(local.returnStruct.policy,"\n",chr(10),"all")>

			<!--- Calculate the hash of the information --->
			<cfset local.signingKey = createObject("java","javax.crypto.spec.SecretKeySpec").init(local.secretKey.getBytes(),'HmacSHA1')>
			<cfset local.mac = createObject("java","javax.crypto.Mac").getInstance('HmacSHA1')>
			<cfset local.mac.init(local.signingKey)>
			<cfset local.returnStruct['policysignature'] = toBase64(mac.doFinal(local.returnStruct.policy.getBytes()))>

			<cfset local.returnStruct['success'] = true>
		<cfelse>
			<cfset local.returnStruct['success'] = false>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getVideoPreviewLinkFromFileID" access="public" output="false" returnType="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="fileID" type="string" required="yes">

		<cfset var local = StructNew()>
		<cfset local.data = { "success":true, "videoPreviewLink":"" }>
		<cfset local.s3bucket = "seminarweb">
		<cfset local.s3requesttype = "vhost">
		<cfset local.s3region = "us-east-1">
		<cfif application.objPlatform.isRequestSecure()>
			<cfset local.s3protocol = "https">
		<cfelse>
			<cfset local.s3protocol = "http">
		</cfif>
		<cfset local.s3expire = 30>

		<cfquery name="local.qryFileDetails" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT f.fileID, f.fileName, f.fileTitle, p.participantID, t.titleID, p.orgcode as participantOrgCode
			FROM dbo.tblFiles as f
			INNER JOIN dbo.tblTitlesAndFiles as taf on taf.fileID = f.fileID
			INNER JOIN dbo.tblTitles as t on t.titleid = taf.titleID
			INNER JOIN dbo.tblParticipants as p on p.participantID = t.participantID
			WHERE f.fileID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.fileID#">
			AND f.isDeleted = 0
			AND t.isDeleted = 0
			AND f.formatsAvailable.exist('/formats/format[@accesstype=''S'' and @ext=''mp4'']') = 1;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfif NOT hasSWFileRights(siteID=arguments.mcproxy_siteID, titleID=local.qryFileDetails.titleID, action='getVideoPreviewLinkFromFileID', checkLockSettings=false)>
			<cfreturn { "success":false }>
		</cfif>
		
		<cfif local.qryFileDetails.recordCount>
			<cfset local.objectKey = lcase("swod/#local.qryFileDetails.participantOrgCode#/#local.qryFileDetails.participantID#/#local.qryFileDetails.fileID#.mp4")>
			<cfif application.objS3.s3FileExists(bucket=local.s3bucket, objectKey=local.objectKey, requestType=local.s3requesttype, region=local.s3region)>
				<!--- Clean display name needed for S3 --->
				<cfset local.displayName = ReReplaceNoCase(local.qryFileDetails.fileName,'[^A-Z0-9 \-_.!\()]','','ALL')> 

				<cfset local.arrAmzHeaders = arrayNew(1)>
				<cfset local.tmpStr = { key="response-content-disposition", value="inline; filename=""#local.displayName#""; filename*=UTF-8''#urlEncodedFormat(local.displayName)#" }>
				
				<cfset arrayAppend(local.arrAmzHeaders,local.tmpStr)>
				<cfset local.data.videoPreviewLink = application.objS3.s3Url(bucket=local.s3bucket, objectKey=local.objectKey, requestType=local.s3requesttype, expireInMinutes=local.s3expire, canonicalizedAmzHeaders=local.arrAmzHeaders, region=local.s3region, protocol=local.s3protocol)>
			</cfif>
		</cfif>

		<cfif NOT len(local.data.videoPreviewLink)>
			<cfset local.data.success = false>
		</cfif>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getDownloadLinkFromFileID" access="public" output="false" returnType="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="mcproxy_sitecode" type="string" required="yes">
		<cfargument name="fileID" type="numeric" required="yes">
		<cfargument name="titleID" type="numeric" required="yes">
		<cfargument name="fileMode" type="string" required="yes">
		<cfargument name="fileExt" type="string" required="yes">
	
		<cfset var local = StructNew()>
		<cfset local.data = { "success":true, "s3filelink":"" }>
	
		<cfif NOT hasSWFileRights(siteID=arguments.mcproxy_siteID, titleID=arguments.titleID, action='getDownloadLinkFromFileID', checkLockSettings=false)>
			<cfreturn { "success":false, "s3filelink":"" }>
		</cfif>
		
		<cfset local.s3bucket = "seminarweb">
		<cfset local.s3requesttype = "vhost">
		<cfset local.s3region = "us-east-1">
		<cfif application.objPlatform.isRequestSecure()>
			<cfset local.s3protocol = "https">
		<cfelse>
			<cfset local.s3protocol = "http">
		</cfif>
		<cfset local.s3expire = 30>
	
		<cfset local.qryFile = getFile(fileID=arguments.fileID)>
		
		<cfif local.qryFile.recordCount>
			<cfswitch expression="#arguments.filemode#">
				<cfcase value="stream">
					<cfset local.objectKey = lcase("swod/#arguments.mcproxy_sitecode#/#local.qryFile.participantID#/#arguments.fileID#.#arguments.fileExt#")>
				</cfcase>
				<cfcase value="download">
					<cfset local.objectKey = lcase("swoddownloads/#arguments.mcproxy_sitecode#/#local.qryFile.participantID#/#arguments.fileID#.#arguments.fileExt#")>
				</cfcase>
			</cfswitch>
			
			<cfif application.objS3.s3FileExists(bucket=local.s3bucket, objectKey=local.objectKey, requestType=local.s3requesttype, region=local.s3region)>
				<!--- Clean display name needed for S3 --->
				<cfset local.displayName = ReReplaceNoCase("#local.qryFile.fileName#.#arguments.fileExt#",'[^A-Z0-9 \-_.!\()]','','ALL')> 
				
				<cfset local.arrAmzHeaders = arrayNew(1)>
				<cfset local.tmpStr = { key="response-content-disposition", value="attachment; filename=""#local.displayName#""; filename*=UTF-8''#urlEncodedFormat(local.displayName)#" }>
				<cfset arrayAppend(local.arrAmzHeaders,local.tmpStr)>
	
				<!--- get mime from extension --->
				<cfswitch expression="#ListLast(local.displayName,'.')#">
					<cfcase value="doc"><cfset local.mime = "application/msword"></cfcase>
					<cfcase value="docx"><cfset local.mime = "application/vnd.openxmlformats-officedocument.wordprocessingml.document"></cfcase>
					<cfcase value="xls"><cfset local.mime = "application/vnd.ms-excel"></cfcase>
					<cfcase value="xlsx"><cfset local.mime = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"></cfcase>
					<cfcase value="m4v"><cfset local.mime = "video/x-m4v"></cfcase>
					<cfcase value="mp3"><cfset local.mime = "audio/mpeg"></cfcase>
					<cfcase value="mp4"><cfset local.mime = "audio/mp4"></cfcase>
					<cfcase value="pdf"><cfset local.mime = "application/pdf"></cfcase>
					<cfcase value="ppt"><cfset local.mime = "application/vnd.ms-powerpoint"></cfcase>
					<cfcase value="iif"><cfset local.mime = "text/iif"></cfcase>
					<cfcase value="ach"><cfset local.mime = "text/ach"></cfcase>
					<cfdefaultcase><cfset local.mime = "application/octet-stream"></cfdefaultcase>
				</cfswitch>
				<cfset local.tmpStr = { key="response-content-type", value="#local.mime#" }>
				<cfset arrayAppend(local.arrAmzHeaders,local.tmpStr)>
				
				<cfset local.data.s3filelink = application.objS3.s3Url(bucket=local.s3bucket, objectKey=local.objectKey, requestType=local.s3requesttype, expireInMinutes=local.s3expire, canonicalizedAmzHeaders=local.arrAmzHeaders, region=local.s3region, protocol=local.s3protocol)>
			</cfif>
		</cfif>

		<cfif NOT len(local.data.s3filelink)>
			<cfset local.data.success = false>
		</cfif>
	
		<cfreturn local.data>
	</cffunction>

	<cffunction name="hasSWFileRights" access="private" returntype="boolean" output="no">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="titleID" type="numeric" required="true">
		<cfargument name="action" type="string" required="true">
		<cfargument name="checkLockSettings" type="boolean" required="false" default="0">

		<cfset var local = structNew()>
		<cfset local.hasRights = false>

		<cfquery datasource="#application.dsn.tlasites_seminarweb.dsn#" name="local.qrySWInfo">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @siteID int, @siteCode varchar(10), @publisherOrgCode varchar(10) = '', @lockSettings bit;

			SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
			SELECT @siteCode = memberCentral.dbo.fn_getSiteCodeFromSiteID(@siteID);
			
			SELECT @publisherOrgCode = p.orgcode, @lockSettings = s.lockSettings
			FROM dbo.tblTitles AS t
			INNER JOIN dbo.tblParticipants AS p ON p.participantID = t.participantID
			INNER JOIN dbo.tblSeminarsAndTitles AS sat ON sat.titleID = t.titleID
			INNER JOIN dbo.tblSeminars AS s ON sat.seminarID = s.seminarID
			WHERE t.titleID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.titleID#">
			AND t.isDeleted = 0;

			SELECT isPublisher = CASE WHEN @publisherOrgCode = @siteCode THEN 1 ELSE 0 END, @lockSettings as lockSettings;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.SeminarWebAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='SeminarWebAdmin',siteID=arguments.siteID)>
		<cfset local.tmpRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.SeminarWebAdminSRID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.siteID)>
		<cfset local.hasRights = (local.tmpRights.editSWODProgramAll OR local.tmpRights.editSWODProgramPublish) AND local.qrySWInfo.isPublisher EQ 1>

		<cfreturn local.hasRights AND (arguments.checkLockSettings ? NOT local.qrySWInfo.lockSettings : true)>
	</cffunction>

	<cffunction name="deleteTitleFileFormat" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="titleID" type="numeric" required="yes">
		<cfargument name="fileID" type="numeric" required="yes">
		<cfargument name="fileExt" type="string" required="yes">
		<cfargument name="fileMode" type="string" required="yes">

		<cfset var local = structNew()>
		<cfset local.data = {}>

		<cfsetting requesttimeout="200">

		<cftry>
			<cfif NOT hasSWFileRights(siteID=arguments.mcproxy_siteID, titleID=arguments.titleID, action='deleteTitleFormat', checkLockSettings=true)>
				<cfthrow message="invalid request">
			</cfif>

			<!--- get file info --->
			<cfquery name="local.qryFileInfo" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				select f.fileID, f.participantID, p.orgcode
				from dbo.tblFiles as f
				inner join dbo.tblParticipants as p on p.participantID = f.participantID
				where f.fileID = <cfqueryparam value="#arguments.fileID#" cfsqltype="CF_SQL_INTEGER">
				and f.isDeleted = 0
			</cfquery>

			<cfif local.qryFileInfo.recordcount is 1>
				<cfswitch expression="#arguments.fileMode#">
					<cfcase value="stream">
						<cfset local.objectKey = lcase("swod/#local.qryFileInfo.orgcode#/#local.qryFileInfo.participantID#/#arguments.fileID#.#arguments.fileExt#")>
						<cfset local.fileModeAbbr = "S">
					</cfcase>
					<cfcase value="download">
						<cfset local.objectKey = lcase("swoddownloads/#local.qryFileInfo.orgcode#/#local.qryFileInfo.participantID#/#arguments.fileID#.#arguments.fileExt#")>
						<cfset local.fileModeAbbr = "D">
					</cfcase>
				</cfswitch>
				
				<!--- mark file as deleted and add to the delete queue --->
				<cfstoredproc procedure="sw_deleteFileFormat" datasource="#application.dsn.tlasites_seminarweb.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryFileInfo.fileID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.fileExt#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.fileModeAbbr#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.objectKey#">
				</cfstoredproc>

				<cfset local.data.success = true>
			<cfelse>
				<cfthrow message="invalid request">
			</cfif>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getTitleFileInfo" access="public" output="false" returntype="query">
		<cfargument name="titleID" type="numeric" required="yes">
		<cfargument name="fileID" type="numeric" required="yes">

		<cfset qryTitleFileInfo = "">

		<cfquery name="qryTitleFileInfo" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @fileID int, @hasSyncPoints bit = 0;
			SET @fileID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.fileID#">;

			IF EXISTS (SELECT 1 FROM dbo.tblSeminarAndFilesSyncPoints WHERE fileID = @fileID)
				SET @hasSyncPoints = 1;

			SELECT f.fileID, f.fileName, f.fileTitle, t.titleID, t.titleName, @hasSyncPoints AS hasSyncPoints
			FROM dbo.tblFiles as f
			INNER JOIN dbo.tblTitlesAndFiles as taf on taf.fileID = f.fileID
			INNER JOIN dbo.tblTitles as t on t.titleid = taf.titleID
			WHERE f.fileID = @fileID
			AND t.titleID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.titleID#">
			AND f.isDeleted = 0
			AND t.isDeleted = 0;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryTitleFileInfo>
	</cffunction>

	<cffunction name="replaceFile" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">
		<cfargument name="seminarID" type="numeric" required="yes">
		<cfargument name="titleID" type="numeric" required="yes">
		<cfargument name="newFileID" type="numeric" required="yes">
		<cfargument name="oldFileID" type="numeric" required="yes">
		<cfargument name="fileMode" type="string" required="yes">
		<cfargument name="carryOverProgress" type="boolean" required="no" default="1">

		<cfset var local = structNew()>
		<cfset local.data = {}>

		<cfsetting requesttimeout="200">

		<cftry>
			<cfif NOT hasSWFileRights(siteID=arguments.mcproxy_siteID, titleID=arguments.titleID, action='replaceFile', checkLockSettings=true)>
				<cfthrow message="invalid request">
			</cfif>

			<!--- get file info --->
			<cfquery name="local.qryFileInfo" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				select f.fileID
				from dbo.tblFiles as f
				inner join dbo.tblParticipants as p on p.participantID = f.participantID
				where f.fileID = <cfqueryparam value="#arguments.oldFileID#" cfsqltype="CF_SQL_INTEGER">
				and f.isDeleted = 0
			</cfquery>

			<cfif local.qryFileInfo.recordcount is 1>
				<cfset local.participantID = CreateObject("component","seminarWebSWCommon").getParticipantIDFromSiteCode(sitecode=arguments.mcproxy_siteCode)>

				<cfswitch expression="#arguments.fileMode#">
					<cfcase value="stream">
						<cfif application.MCEnvironment EQ 'production' AND arguments.carryOverProgress>
							<cfquery name="local.qryPopulateQueue" datasource="#application.dsn.platformQueue.dsn#">
								SET NOCOUNT ON;
								
								DECLARE @queueTypeID int, @statusWaiting int, @nowDate datetime;
								SELECT @queueTypeID = queueTypeID FROM dbo.tblQueueTypes WHERE queueType = 'SWReplaceMediaFile';
								SELECT @statusWaiting = queueStatusID FROM dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID AND queueStatus = 'waitingToProcess';
								SET @nowDate = getdate();
				
								INSERT INTO dbo.queue_SWReplaceMediaFile (orgID, siteID, participantID, seminarID, titleID, oldFileID, newFileID, recordedByMemberID, statusID, dateAdded, dateUpdated)
								VALUES (
									<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">,
									<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">,
									<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.participantID#">,
									<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.seminarID#">,
									<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.titleID#">,
									<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.oldFileID#">,
									<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.newFileID#">,
									<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">,
									@statusWaiting,
									@nowDate,
									@nowDate
								);
								
								EXEC membercentral.dbo.sched_resumeTask @name='Process SeminarWeb Replace Media File Queue', @engine='BERLinux';
							</cfquery>
						</cfif>
						<cfset deleteTitleFile(mcproxy_siteID=arguments.mcproxy_siteID, titleID=arguments.titleID, fileID=arguments.oldFileID)>
					</cfcase>
					<cfcase value="download">
						<cfset deleteTitleFile(mcproxy_siteID=arguments.mcproxy_siteID, titleID=arguments.titleID, fileID=arguments.oldFileID)>
					</cfcase>
				</cfswitch>

				<cfif application.MCEnvironment EQ 'production'>
					<cfset local.refreshFilesResult = refreshFiles(mcproxy_siteID=arguments.mcproxy_siteID, titleID=arguments.titleID, fileID=arguments.newFileID, pathToFile="#lCase(arguments.mcproxy_siteCode)#.#local.participantID#")>
				</cfif>

				<cfquery name="local.qryAuditLog" datasource="#application.dsn.platformQueue.dsn#">
					INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
					VALUES ('{ "c":"auditLog", "d": {
						"AUDITCODE":"SW",
						"ORGID":' + <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.mcproxy_orgID#"> + ',
						"SITEID":' + <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.mcproxy_siteID#"> + ',
						"ACTORMEMBERID":' + <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#session.cfcuser.memberdata.memberID#"> + ',
						"ACTIONDATE":"' + convert(varchar(20),GETDATE(),120) + '",
						"MESSAGE":"' + replace(memberCentral.dbo.fn_cleanInvalidXMLChars('File [' + <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.newFileID#"> + '] has replaced the old file [' + <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.oldFileID#"> + '].'),'"','\"') + '" } }');
				</cfquery>

				<cfset local.data.success = true>
			<cfelse>
				<cfthrow message="invalid request">
			</cfif>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>
</cfcomponent>