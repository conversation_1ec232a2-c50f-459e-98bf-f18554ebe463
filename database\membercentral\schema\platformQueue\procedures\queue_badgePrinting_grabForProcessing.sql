CREATE PROC dbo.queue_badgePrinting_grabForProcessing
@batchSize int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @queueTypeID int, @statusReady int, @statusGrabbed int;
	SELECT @queueTypeID = queueTypeID FROM dbo.tblQueueTypes WHERE queueType = 'badgePrinting';
	SELECT @statusReady = queuestatusID FROM dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID and queueStatus = 'readyToProcess';
	SELECT @statusGrabbed = queuestatusID FROM dbo.tblQueueStatuses WHERE queueTypeID = @queueTypeID and queueStatus = 'grabbedForProcessing';

	IF OBJECT_ID('tempdb..#tmpRegBadges') IS NOT NULL
		DROP TABLE #tmpRegBadges;
	CREATE TABLE #tmpRegBadges (itemID int);

	-- dequeue in order of dateAdded. get @batchsize entries
	UPDATE qid WITH (UPDLOCK, READPAST)
	SET qid.statusID = @statusGrabbed,
		qid.dateUpdated = getdate()
		OUTPUT inserted.itemID
		INTO #tmpRegBadges
	FROM dbo.queue_badgePrinting AS qid
	INNER JOIN (
		SELECT TOP (@batchSize) itemID 
		FROM dbo.queue_badgePrinting
		WHERE statusID = @statusReady
		ORDER BY dateAdded, itemID
	) AS batch ON batch.itemID = qid.itemID
	WHERE qid.statusID = @statusReady;

	SELECT qid.itemID, s.sitecode, qid.siteID, qid.registrantID, qid.deviceID, etc.rawContent AS templateContent
	FROM #tmpRegBadges AS tmp
	INNER JOIN dbo.queue_badgePrinting AS qid ON qid.itemID = tmp.itemID
	INNER JOIN membercentral.dbo.sites AS s ON s.siteID = qid.siteID
	INNER JOIN membercentral.dbo.badge_templates AS bt ON bt.templateID = qid.templateID
	CROSS APPLY membercentral.dbo.fn_getContent(bt.contentID,1) AS etc
	ORDER BY qid.itemID;

	IF OBJECT_ID('tempdb..#tmpRegBadges') IS NOT NULL
		DROP TABLE #tmpRegBadges;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
