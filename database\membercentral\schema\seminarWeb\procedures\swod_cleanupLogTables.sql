ALTER PROC dbo.swod_cleanupLogTables
@itemCount int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @awsS3prefix varchar(100), @s3bucket varchar(100), @s3UploadReadyStatusID int, @seminarwebfiles varchar(100), @pathToLogs varchar(400);
	set @awsS3prefix = 'swodaccesslogs/';
	set @s3bucket = 'seminarweb';
	SET @itemCount = 1;
	SELECT @seminarwebfiles = seminarwebfiles FROM membercentral.dbo.fn_getServerSettings();
	SET @pathToLogs = @seminarwebfiles + 'SWODAccessLogs\';
			

	/* *********************************************************************************** */
	/* Write out activityLog, debugLog older than 1 day as text files and clear from table */
	/* *********************************************************************************** */
	IF OBJECT_ID('tempdb..#tmpLogAccess') IS NOT NULL 
		DROP TABLE #tmpLogAccess;
	CREATE TABLE #tmpLogAccess (logAccessID int PRIMARY KEY, folder char(4), debug bit DEFAULT(0), activity bit DEFAULT(0),
		debugLogPath varchar(400), activityLogPath varchar(400), debugLogS3Key varchar(400), activityLogS3Key varchar(400));

	-- limit to this range so the query runs faster
	declare @oneDayAgo datetime, @twoDaysAgo datetime;
	set @oneDayAgo = dateadd(d,-1,getdate());
	set @twoDaysAgo = dateadd(d,-2,getdate());

	select @s3UploadReadyStatusID = qs.queueStatusID
	from platformQueue.dbo.tblQueueTypes as qt
	inner join platformQueue.dbo.tblQueueStatuses as qs on qs.queueTypeID = qt.queueTypeID
	where qt.queueType = 's3Upload'
	and qs.queueStatus = 'readyToProcess';

	insert into #tmpLogAccess (logAccessID, folder)
	select logaccessid, right('0000' + cast(logAccessID%1000 as varchar(4)),4)
	from dbo.tbllogaccessswod
	where datelastmodified between @twoDaysAgo and @oneDayAgo;

	update tmp set 
	    tmp.debug = trialsmith.dbo.fn_writefile(@pathToLogs + tmp.folder + '\' + cast(tmp.logAccessID as varchar(10)) + '-debug.txt',tbl.debuglog,1),
	    tmp.debugLogPath = @pathToLogs + tmp.folder + '\' + cast(tmp.logAccessID as varchar(10)) + '-debug.txt',
	    tmp.debugLogS3Key = @awsS3prefix + tmp.folder + '/' + cast(tmp.logAccessID as varchar(10)) + '-debug.txt'
	from #tmpLogAccess as tmp
	inner join dbo.tbllogaccessswod as tbl on tbl.logAccessID = tmp.logAccessID
	where tbl.debuglog is not null;

	update tmp set 
	    tmp.activity = trialsmith.dbo.fn_writefile(@pathToLogs + tmp.folder + '\' + cast(tmp.logAccessID as varchar(10)) + '-activity.txt',tbl.activityLog,1),
	    tmp.activityLogPath = @pathToLogs + tmp.folder + '\' + cast(tmp.logAccessID as varchar(10)) + '-activity.txt',
	    tmp.activityLogS3Key = @awsS3prefix + tmp.folder + '/' + cast(tmp.logAccessID as varchar(10)) + '-activity.txt'
	from #tmpLogAccess as tmp
	inner join dbo.tbllogaccessswod as tbl on tbl.logAccessID = tmp.logAccessID
	where tbl.activityLog is not null;

	-- add to Amazon S3 upload queue
	insert into platformQueue.dbo.queue_S3Upload (statusID, s3bucketName, objectKey, filePath, deleteOnSuccess, dateAdded, dateUpdated)
	select @s3UploadReadyStatusID, @s3bucket, debugLogS3Key, debugLogPath, 1, getdate(), getdate()
	from #tmpLogAccess as tmp
	where tmp.debug = 1;

	insert into platformQueue.dbo.queue_S3Upload (statusID, s3bucketName, objectKey, filePath, deleteOnSuccess, dateAdded, dateUpdated)
	select @s3UploadReadyStatusID, @s3bucket, activityLogS3Key, activityLogPath, 1, getdate(), getdate()
	from #tmpLogAccess as tmp
	where tmp.activity = 1;

	update tbl
	set tbl.debuglog = null
	from #tmpLogAccess as tmp
	inner join dbo.tbllogaccessswod as tbl on tbl.logAccessID = tmp.logAccessID
	where tmp.debug = 1;

	update tbl
	set tbl.activityLog = null
	from #tmpLogAccess as tmp
	inner join dbo.tbllogaccessswod as tbl on tbl.logAccessID = tmp.logAccessID
	where tmp.activity = 1;

	IF OBJECT_ID('tempdb..#tmpLogAccess') IS NOT NULL 
		DROP TABLE #tmpLogAccess;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
