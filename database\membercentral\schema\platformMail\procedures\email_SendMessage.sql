ALTER PROC dbo.email_SendMessage
@fromName varchar(200),
@fromEmail varchar(200),
@toEmailList varchar(max),
@replyToEmail varchar(200),
@subject varchar(400),
@title varchar(400),
@messageContent nvarchar(max),
@attachmentsList varchar(max),
@siteID int,
@memberID int,
@messageTypeID int,
@sendingSiteResourceID int,
@referenceType varchar(20),
@referenceID int,
@doWrapEmail bit,
@environmentName varchar(50),
@messageID int OUTPUT,
@recipientIDList varchar(max) OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	
	DECLARE @resourceTypeID int, @contentID int, @siteResourceID int, @contentVersionID int, 
		@sendOnDate datetime = getdate(), @orgID int, @emailTagTypeID int, @emailTypeID int, @recipientID int,
		@messageStatusIDQueued int, @attachmentID int, @s3keyMod varchar(4), @objectKey varchar(400), 
		@attachmentFileName varchar(400), @filePathForS3Upload varchar(400), @s3bucketName varchar(100) = 'platformmail-membercentral-com',
		@contentTitle varchar(100) = left(@title,100), @s3UploadReadyStatusID int, @nowDate datetime = getdate(),
		@thisToEmail varchar(255), @autoID int, @tier varchar(12), @backendTempPath varchar(400), @emailRegex varchar(max),
		@changedEmailsList varchar(max), @injectedNote varchar(max), @sendToAddress varchar(255), @fileName varchar(400),
		@folderPath varchar(400), @sharedTempNoWebPath varchar(40), @sharedTempNoWebS3UploaderPath varchar(60);

	DECLARE @tblRecipients table (recipientID int PRIMARY KEY);
	DECLARE @tblToEmails table (autoID int, email varchar(255));
	DECLARE @tblAttachments table (autoID int, [fileName] varchar(400), folderPath varchar(400));

	SET @messageID = NULL;
	SET @recipientIDList = NULL;
	SET @emailRegex = '^[a-zA-Z_0-9-''''\&\+~]+(\.[a-zA-Z_0-9-''''\&\+~]+)*@([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,63}$';
	SET @resourceTypeID = membercentral.dbo.fn_getResourceTypeID('ApplicationCreatedContent');
	SELECT @orgID = orgID FROM membercentral.dbo.sites WHERE siteID = @siteID;
	SELECT @emailTagTypeID = emailTagTypeID FROM membercentral.dbo.ams_memberEmailTagTypes WHERE orgID = @orgID AND emailTagType = 'Primary';
	SELECT @emailTypeID = emailTypeID FROM membercentral.dbo.ams_memberEmailTags WHERE orgID = @orgID and memberID = @memberID AND emailTagTypeID = @emailTagTypeID;
	SELECT @messageStatusIDQueued = statusID FROM dbo.email_statuses WHERE statusCode = 'Q';

	SELECT @s3UploadReadyStatusID = qs.queueStatusID
	FROM platformQueue.dbo.tblQueueTypes as qt
	INNER JOIN platformQueue.dbo.tblQueueStatuses as qs on qs.queueTypeID = qt.queueTypeID
	WHERE qt.queueType = 's3Upload'
	AND qs.queueStatus = 'readyToProcess';

	-- validate the to emails and kick out if no valid ones left & trim the emails.
	SET @toEmailList = REPLACE(REPLACE(@toEmailList,',',';'),' ','');

	INSERT INTO @tblToEmails (autoID, email)
	SELECT autoID, listItem
	FROM membercentral.dbo.fn_varCharListToTable(@toEmailList,';');

	DELETE FROM @tblToEmails
	WHERE email = ''
	OR LEN(email) > 200
	OR membercentral.dbo.fn_RegExReplace(email,@emailRegex,'') <> ''

	IF NOT EXISTS (SELECT 1 FROM @tblToEmails)
		RAISERROR('No Valid To Email Provided.', 16, 1);

	-- sendgrid API requires only one reply-to address. so pick first one in case there is a list.
	SELECT TOP 1 @replyToEmail = LTRIM(RTRIM(listItem))
	FROM membercentral.dbo.fn_varCharListToTable(REPLACE(@replyToEmail,',',';'),';')
	ORDER BY autoID;

	-- replace line feeds with html br
	SET @messageContent = replace(@messageContent,char(13) + char(10),'<br/>');

	IF @doWrapEmail = 1 BEGIN
		DECLARE @emailContentHTML varchar(max), @customHeadContent varchar(max), @titleHTML varchar(500) = '';

		SELECT @customHeadContent = customHead.rawContent
		FROM membercentral.dbo.sites AS s
		CROSS APPLY membercentral.dbo.fn_getContent(s.customHeadContentID,1) AS customHead
		WHERE siteID = @siteID;

		SET @emailContentHTML = '
			<table cellspacing="0" cellpadding="0" width="100%" border="0">
			<tr>
				<td style="background-color:#eee;padding:10px 20px 20px 20px;">
					<table style="background-color:#eee;" cellspacing="0" cellpadding="0" width="100%" border="0">
					<tr><td style="font:bold 18px Verdana,Helvetica,Arial,sans-serif;color:#069;padding-left:20px;padding-bottom:8px;">'+@title+'</td></tr>
					<tr><td style="background-color:#fff;font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:#333;padding:20px;">'+@messageContent+'</td></tr>
					</table>
				</td>
			</tr>
			</table>';

		IF LEN(@title) > 0
			SET @titleHTML = '<title>'+ @title +'</title>';

		SET @messageContent = '<!DOCTYPE html>
			<html>
			<head>
				'+@titleHTML+'
				<meta http-equiv="Content-Type" content="text/html; charset=utf8" />
				'+ @customHeadContent +'
			</head>
			<body>'+ @emailContentHTML +'</body>
			</html>';
	END

	SELECT @tier=tier FROM membercentral.dbo.fn_getServerSettings();

	-- email sending queue will always override emails when not in production, but this allows us to inject the note at the top of the message
	IF @environmentName <> 'production' BEGIN
		SET @sendToAddress = '<EMAIL>';

		SELECT @changedEmailsList = COALESCE(@changedEmailsList + ';', '') + email
		FROM @tblToEmails
		WHERE email <> @sendToAddress;

		IF LEN(@changedEmailsList) > 0 OR LEN(@replyToEmail) > 0 BEGIN
			SET @injectedNote = '';

			IF LEN(@changedEmailsList) > 0
				SET @injectedNote += '<b><span style="color:blue;">TO</span></b> changed from <span style="color:blue;">'+ @changedEmailsList +'</span> to <span style="color:blue;">'+ @sendToAddress +'</span><br/>';

			IF LEN(@replyToEmail) > 0
				SET @injectedNote += '<b><span style="color:blue;">REPLYTO</span></b> changed from <span style="color:blue;">'+ @replyToEmail +'</span> to <span style="color:blue;">-blank-</span><br/>';

			SET @injectedNote = '<table width="100%" border="0" cellspacing="0" cellpadding="4" style="border:1px solid #999;border-collapse:collapse;">
				<tr bgcolor="#DEDEDE">
					<td style="background-color:#fff;font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:#333;padding:10px;">
						<b><span style="color:red;">NOTE:</span></b> The recipients of this email have been modified.<br/>
						'+ @injectedNote +'
					</td>
				</tr>
				</table><br/>';

			SET @messageContent = replace(@messageContent,'<body>','<body>' + @injectedNote);
		END

		SET @toEmailList = @sendToAddress;
		SET @replyToEmail = '';
	END

	-- attachments: folderpath1|filename1.jpg***folderpath2|filename2.jpg***folderpath3|filename3.jpg
	IF LEN(@attachmentsList) > 0 BEGIN
		INSERT INTO @tblAttachments(autoID, [fileName], folderPath)
		select eachfileset.autoid, 
			CASE WHEN CHARINDEX('|',listItem) > 0 THEN LTRIM(RTRIM(LEFT(listItem, CHARINDEX('|',listItem) - 1))) ELSE '' END,
			CASE WHEN CHARINDEX('|',listItem) > 0 THEN LTRIM(RTRIM(RIGHT(listItem, LEN(listItem) - CHARINDEX('|',listItem) - 2))) ELSE '' END
		FROM membercentral.dbo.fn_varCharListToTable(@attachmentsList,'***') as eachfileset;

		DELETE FROM @tblAttachments WHERE [fileName] = '' OR folderPath = '';
	END

	BEGIN TRAN;
		EXEC membercentral.dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@resourceTypeID, 
			@parentSiteResourceID=@sendingSiteResourceID, @siteResourceStatusID=1, @isHTML=1,
			@languageID=1, @isActive=1, @contentTitle=@contentTitle, @contentDesc='', @rawContent=@messageContent,
			@memberID=@memberID, @contentID=@contentID OUTPUT, @siteResourceID=@siteResourceID OUTPUT;

		SELECT @contentVersionID = contentVersionID
		FROM membercentral.dbo.cms_contentVersions
		WHERE siteID = @siteID
		AND contentID = @contentID;

		EXEC dbo.email_insertMessage @messageTypeID=@messageTypeID, @siteID=@siteID, 
			@sendingSiteResourceID=@sendingSiteResourceID, @isTestMessage=0, @sendOnDate=@sendOnDate, @recordedByMemberID=@memberID,
			@fromName=@fromName, @fromEmail=@fromEmail, @replyToEmail=@replyToEmail, @senderEmail='',
			@subject=@subject, @contentVersionID=@contentVersionID, @messageWrapper='',
			@referenceType=@referenceType, @referenceID=@referenceID, @consentListIDs=null, @messageID=@messageID OUTPUT;

		-- add recipients as I (not ready to be queued yet)
		SELECT @autoID = MIN(autoID) FROM @tblToEmails;
		WHILE @autoID IS NOT NULL BEGIN
			SELECT @thisToEmail = email FROM @tblToEmails WHERE autoID = @autoID;

			EXEC dbo.email_insertMessageRecipientHistory @messageID=@messageID, @memberID=@memberID, 
				@toName='', @toEmail=@thisToEmail, @emailTypeID=@emailTypeID, @statusCode='I', @siteID=@siteID,
				@recipientID=@recipientID OUTPUT;

			INSERT INTO @tblRecipients (recipientID)
			VALUES (@recipientID);

			SELECT @autoID = MIN(autoID) FROM @tblToEmails WHERE autoID > @autoID;
		END

		-- adding attachment entries
		SELECT @sharedTempNoWebPath=sharedTempNoWebPath, @sharedTempNoWebS3UploaderPath=sharedTempNoWebS3UploaderPath 
		FROM membercentral.dbo.fn_getServerSettings();
		
		SELECT @autoID = MIN(autoID) FROM @tblAttachments;
		WHILE @autoID IS NOT NULL BEGIN
			SELECT @fileName=[fileName], @folderPath=folderPath 
			FROM @tblAttachments 
			WHERE autoID = @autoID;
			
			SET @filePathForS3Upload = REPLACE(REPLACE(@folderPath + '/' + @fileName, @sharedTempNoWebPath, @sharedTempNoWebS3UploaderPath),'/','\');

			IF membercentral.dbo.fn_FileExists(@filePathForS3Upload) = 1 BEGIN
				EXEC dbo.email_insertAttachment @fileName=@fileName, @localDirectory=@folderPath, @attachmentID=@attachmentID OUTPUT;

				INSERT INTO dbo.email_messageRecipientAttachments (recipientID, attachmentID)
				SELECT recipientID, @attachmentID
				FROM @tblRecipients;

				-- insert to s3 upload queue
				SET @s3keyMod = FORMAT(@attachmentID % 1000, '0000');
				SET @objectKey = LOWER(@environmentName + '/outgoing/' + @s3keyMod + '/' + CAST(@attachmentID as varchar(10)) + '/' + @fileName);

				-- set deleteOnSuccess=0 because we may need to send the same actual attachment in multiple messages. It can be cleaned up by the directory cleanup scripts.
				IF NOT EXISTS (select 1 from platformQueue.dbo.queue_S3Upload where s3bucketName = @s3bucketName and objectKey = @objectKey)
					INSERT INTO platformQueue.dbo.queue_S3Upload (statusID, s3bucketName, objectKey, filePath, deleteOnSuccess, dateAdded, dateUpdated)
					VALUES (@s3UploadReadyStatusID, @s3bucketName, @objectKey, @filePathForS3Upload, 0, @nowDate, @nowDate);
			END

			SELECT @autoID = MIN(autoID) FROM @tblAttachments WHERE autoID > @autoID;
		END

		-- mark recipients as queued
		UPDATE mrh
		SET mrh.emailStatusID = @messageStatusIDQueued
		FROM @tblRecipients as tmp
		INNER JOIN dbo.email_messageRecipientHistory as mrh on mrh.recipientID = tmp.recipientID;
	COMMIT TRAN;

	SELECT @recipientIDList = COALESCE(@recipientIDList + ',', '') + CAST(recipientID as varchar(10))
	FROM @tblRecipients;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
