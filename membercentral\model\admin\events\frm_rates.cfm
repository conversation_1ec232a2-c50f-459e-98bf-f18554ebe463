<cfsavecontent variable="local.ratesJS">
	<cfoutput>
	<script language="javascript">
	function validateRateForm() {
		mca_hideAlert('ev_rate_err_div');
		rateChanged($('##rate').val());

		var arrReq = new Array();
		var retValue = true;
		var arrQty = [];
		var frmElem = document.forms['frmRate'].elements;

		if ($('##rateName').val() == '') arrReq[arrReq.length] = 'Enter the name of this rate.';
		if ($('##rateUID').val() == '') arrReq[arrReq.length] = 'Enter the API ID.';
		if ($('input[name="scheduleID"]:checked').length == 0) arrReq[arrReq.length] = 'At least one Rate Availability schedule must be selected. Schedules can be managed in the Registration Schedule section of the Registration tab.';

		for (i=0; i < frmElem.length; i++) {
			if ((frmElem[i].name.indexOf('newBulkRate_qty_') != -1) || (frmElem[i].name.indexOf('existBulkRate_qty_') != -1)) {
				elemVal = parseInt(frmElem[i].value);
				if (elemVal <= 1) {
					arrReq[arrReq.length] = 'Please validate that all bulk rate quantities are greater than 1.';
					retValue = false;
					break;
				}
				else {
					for (x=0; x < arrQty.length; x++) {
						if (arrQty[x] == elemVal) {
							arrReq[arrReq.length] = 'Please validate that all bulk rate quantities have unique values.';
							retValue = false;
							break;
						}
					}
					arrQty.push(elemVal);
				}
			}
			if (!retValue)
				break;
		}

		if (arrReq.length > 0) {
			mca_showAlert('ev_rate_err_div', arrReq.join('<br/>'), true);
			return false;
		}

		return true;
	}
	function rateChanged(rateVal) {
		$('##rate').val(formatCurrency(rateVal));
		if(parseFloat($('##rate').val().replace(/\$/g, '')) == 0) $('##freeRateDisplayRow').show();
		else $('##freeRateDisplayRow').hide();
	}
	
	<!--- BULK RATE --->
	function formatInt(num) {
		var i = parseInt(num);
		return (isNaN(i)) ? 0 : i;
	}
	var newBulkID = 0;
	function addBulkDiscount() {
		$('##tbodyBulkRates').show();
		var templObj = { newID: newBulkID++ };
		var bulkDiscountTemplateSource = $('##ev_rateBulkDiscount').html();
		var bulkDiscountTemplate = Handlebars.compile(bulkDiscountTemplateSource);
		$( "div##divBulkRates" ).append(bulkDiscountTemplate(templObj));
	}
	function removeNewBulkDiscount(id) {
		$('##divNewBulkRate_' + id).remove();
	}
	function removeExistBulkDiscount(id) {
		var delIDs = $('##frmRate ##delBR').val();
		delIDs += (delIDs.length > 0) ? ',' + id : id;
		$('##frmRate ##delBR').val(delIDs);
		$('##divExistBulkRate_' + id).addClass('d-none');
	}

	<!--- gl account selector functions --->
	function selectGLAccount() {
		$('##divRateForm').hide();
		toggleGLASelectorGridArea(true);
	}
	function selectGLAccountResult(objGL) {
		if (objGL.thepathexpanded.length > 0) {
			$('##GLAccountPath').val(objGL.thepathexpanded);
			$('##glSaveMsg').html('Remember to save!');
			$('##GLAccountID').val(objGL.glaccountid);
		} else { 
			var msg = 'There was a problem selecting the GL Account for this rate.<br/>
				Try again; if the issue persists, contact MemberCentral for assistance.';
			$('##divGLerr').html(msg).show();
		}
		$('##divRateForm').show();
		toggleGLASelectorGridArea(false,true);
	}
	function clearGLAccount() {
		$('##GLAccountPath').val('(No account selected)');
		$('##glSaveMsg').html('Remember to save!');
		$('##GLAccountID').val(0);
	}
	$(function() {
		mca_setupSelect2($('##divRateForm'));
	});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.ratesJS)#">

<cfoutput>
<div id="divRateForm" class="p-3">
	<form name="frmRate" id="frmRate" action="#local.formlink#" method="post" onsubmit="return validateRateForm();">
	<input type="hidden" name="eventID" id="eventID" value="#arguments.event.getValue('eventID')#">
	<input type="hidden" name="rateid" id="rateid" value="#arguments.event.getValue('rateid')#">
	<input type="hidden" name="delBR" id="delBR" value="">

	<div id="ev_rate_err_div" class="alert alert-danger mb-2 d-none"></div>
	
	<div class="form-label-group">
		<input type="text" name="rateName"  id="rateName" value="#arguments.event.getValue('rateName')#" class="form-control" maxlength="100"/>
		<label for="rateName">Name of Rate *</label>
	</div>
	
	<div class="form-label-group">
		<input type="text" name="reportCode"  id="reportCode" value="#arguments.event.getValue('reportCode')#" class="form-control" maxlength="15"/>
		<label for="reportCode">Report Code</label>
		<div class="form-text small text-dim">(used to label this rate in reports)</div>
	</div>
	
	<cfif arguments.event.getValue('rateid') gt 0>
		<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser)>
			<div class="form-label-group">
				<input type="text" name="rateUID" id="rateUID" value="#arguments.event.getValue('rateUID')#" class="form-control" maxlength="60"/>
				<label for="rateName">API ID *</label>
			</div>
		<cfelse>
			<div class="form-label-group">
				<input type="text" value="#arguments.event.getValue('rateUID')#" disabled class="form-control" maxlength="60"/>
				<label for="rateName">API ID *</label>
			</div>
		</cfif>
	</cfif>
	<div class="form-label-group mb-1">
		<div class="input-group">
			<cfif arguments.event.getValue('mc_admintoolInfo.myrights.managePaidRates')>
				<input type="text" name="rate"  id="rate" value="#iif(arguments.event.getValue('rate') neq '',DE(DollarFormat(arguments.event.getValue('rate'))),(''))#" class="form-control" onBlur="rateChanged(this.value);">
			<cfelse>
				<input type="hidden" name="rate"  id="rate" value="#NumberFormat(arguments.event.getValue('rate'),"0.00")#">
				<input type="text" value="#DollarFormat(arguments.event.getValue('rate'))#" class="form-control" disabled>
			</cfif>
			<div class="input-group-append">
				<span class="input-group-text cursor-pointer" data-target="rate">USD</span>
			</div>
			<label for="rate" >Rate *</label>
		</div>
	</div>
	<div class="mb-3">		
		<a href="javascript:addBulkDiscount();">Add Bulk Discount</a>
		<div id="freeRateDisplayRow" class="mt-2" <cfif arguments.event.getValue('rate') neq 0> style="display:none;"</cfif>>							
			<div class="font-weight-bold mb-1">This rate is $0.00. Choose how you'd like this displayed to members.</div>
			<div class="form-input">
				<input type="radio" name="freeRateDisplay" id="freeRateDisplay_free" value="FREE" class="form-input-control" <cfif arguments.event.getValue('freeRateDisplay') eq "FREE">checked</cfif>>
				<label for="freeRateDisplay_free" class="form-input-label">Display amount as FREE</label>
			</div>
			<div class="form-input">
				<input type="radio" name="freeRateDisplay" id="freeRateDisplay_0" value="$0.00" class="form-input-control" <cfif arguments.event.getValue('freeRateDisplay') eq "$0.00">checked</cfif>>
				<label for="freeRateDisplay_0" class="form-input-label">Display amount as $0.00</label>
			</div>
			<div class="form-input">
				<input type="radio" name="freeRateDisplay" id="freeRateDisplay_hide" value="" class="form-input-control" <cfif arguments.event.getValue('freeRateDisplay') eq "">checked</cfif>>
				<label for="freeRateDisplay_hide" class="form-input-label">Hide amount</label>
			</div>
		</div>
	</div>
	<div class="form-label-group">
		<textarea name="rateMessage" id="rateMessage" class="form-control" maxlength="400">#arguments.event.getValue('rateMessage')#</textarea>
		<label for="rateMessage">Rate Message</label>
		<div class="form-text small text-dim">(Optional message to promote value offered to member or other instructions. Message will be shown on-screen and in email confirmations with rate selected.)</div>
	</div>
	<select name="rateMessageDisplay" id="rateMessageDisplay" class="form-control mb-3">
		<option value="1" <cfif arguments.event.getValue('rateMessageDisplay') is 1>selected</cfif>>Always Display Message</option>
		<option value="0" <cfif arguments.event.getValue('rateMessageDisplay') is 0>selected</cfif>>Display Message After Rate Selected</option>
	</select>
	<div class="form-label-group">
		<select name="isHidden" id="isHidden" class="form-control">
			<option value="0" <cfif arguments.event.getValue('isHidden') is not 1>selected</cfif>>This rate is available on front-end registration.</option>
			<option value="1" <cfif arguments.event.getValue('isHidden') is 1>selected</cfif>>This rate is NOT available on front-end registration.</option>
		</select>
		<label for="isHidden">Front-End Availability *</label>
	</div>
	<div class="form-group row mb-3">
		<label class="col-sm-12 col-form-label-sm font-size-md">Rate Availablility *</label>
		<div class="col-sm-12">
			<cfloop query="local.qryRegistrationSchedule">
				<cfset local.startDate = local.objTZ.convertTimeZone(dateToConvert=local.qryRegistrationSchedule.startDate,fromTimeZone='US/Central',toTimeZone=local.regTimeZone)>
				<cfset local.endDate = local.objTZ.convertTimeZone(dateToConvert=local.qryRegistrationSchedule.endDate,fromTimeZone='US/Central',toTimeZone=local.regTimeZone)>
				<div class="custom-control custom-checkbox">
					<input type="checkbox" name="scheduleID" id="scheduleID#local.qryRegistrationSchedule.scheduleID#" value="#local.qryRegistrationSchedule.scheduleID#" class="custom-control-input" <cfif listFind(arguments.event.getValue('ratesAvailableList'),local.qryRegistrationSchedule.scheduleID)>checked</cfif>> 
					<label for="scheduleID#local.qryRegistrationSchedule.scheduleID#" class="custom-control-label">
						#local.qryRegistrationSchedule.rangeName#
						<div class="form-text text-dim">
						(#DateTimeFormat(local.startDate, 'm/d/yyyy hh:nn tt', local.regTimeZone)# to #DateTimeFormat(local.endDate, 'm/d/yyyy hh:nn tt', local.regTimeZone)# #local.regTimeZoneName#)
						</div>
					</label>
				</div>
			</cfloop>
		</div>
	</div>
	<div class="form-label-group">
		<select name="rateGroupingID" id="rateGroupingID" class="form-control">
			<option value="0">Default - No Grouping</option>
			<cfloop query="local.qryGetRateGroupings">
				<option value="#local.qryGetRateGroupings.rateGroupingID#" <cfif local.qryGetRateGroupings.rateGroupingID eq arguments.event.getValue('rateGroupingID')>selected</cfif>>#local.qryGetRateGroupings.rateGrouping#</option>
			</cfloop>
		</select>
		<label for="rateGroupingID">Rate Grouping</label>
	</div>
	<div class="mb-3" id="tbodyBulkRates" style="<cfif (val(arguments.event.getValue('rateid')) eq 0) OR (local.qryBulkRates.recordCount eq 0)>display:none;</cfif>">
		<cfif arguments.event.getValue('bulkCountByRate',0) eq 0>
			<i>Bulk rate quantity applies to all registrants regardless of rate</i>
		<cfelse>
			<i>Bulk rate quantity only applies to registrants with the same rate</i>
		</cfif>
		<div id="divBulkRates">
			<cfif val(arguments.event.getValue('rateid')) neq 0>
				<cfloop query="local.qryBulkRates">
					<div id="divExistBulkRate_#local.qryBulkRates.rateID#" class="input-group mt-1">
						<div class="d-flex align-items-center">
							<div class="form-label-group">
								<input type="text" name="existBulkRate_qty_#local.qryBulkRates.rateID#" id="existBulkRate_qty_#local.qryBulkRates.rateID#" value="#local.qryBulkRates.bulkQty#" class="form-control" onBlur="this.value=formatInt(this.value);">
								<label for="existBulkRate_qty_#local.qryBulkRates.rateID#">Qty:</label>
							</div>
							<span class="ml-2"></span>
							<div class="form-label-group">
								<div class="input-group">
									<input type="text" name="existBulkRate_rate_#local.qryBulkRates.rateID#" id="existBulkRate_rate_#local.qryBulkRates.rateID#" value="#DollarFormat(local.qryBulkRates.rate)#" class="form-control" onBlur="this.value=formatCurrency(this.value);">
									<div class="input-group-append">
										<span class="input-group-text cursor-pointer" data-target="existBulkRate_rate_#local.qryBulkRates.rateID#">#arguments.event.getValue('mc_siteinfo.defaultCurrencyType')#</span>
									</div>
									<label for="existBulkRate_rate_#local.qryBulkRates.rateID#">Rate:</label>
								</div>				
							</div>
							<a class="ml-2" href="javascript:removeExistBulkDiscount(#local.qryBulkRates.rateID#);">Remove</a>
						</div>
					</div>
				</cfloop>
			</cfif>
		</div>
	</div>
	<div id="divGLerr" class="alert alert-danger" style="display:none;"></div>
	<input type="hidden" name="GLAccountID" id="GLAccountID" value="#val(arguments.event.getValue('rateGLAccountID'))#">
	<div class="form-label-group">
		<input type="text" id="GLAccountPath" value="<cfif len(arguments.event.getValue('GLAccountPath'))>#arguments.event.getValue('GLAccountPath')#<cfelse>(No account selected;)</cfif>" class="form-control" maxlength="20" readonly disabled="true">
		<label for="GLAccountPath">Revenue GL Override</label>
		<a href="javascript:selectGLAccount();">Choose GL Account</a> &nbsp; &bull; &nbsp; <a href="javascript:clearGLAccount();">Clear Selected GL Account</a><span id="glSaveMsg" class="font-weight-bold text-danger ml-3"></span>
	</div>
	<cfif local.qryTicketPackagesForRate.recordcount>
		<div class="form-group row mb-3">
			<label class="col-sm-12 col-form-label-sm font-size-md">
				Ticket Packages Included *<br/>
				<i>Enter number of packages included with selection of this rate.</i>
			</label>
			<div class="col-sm-12">
				<cfloop query="local.qryTicketPackagesForRate">
					<div class="d-flex align-items-center mb-1">
						<input type="text" name="packageQty_#local.qryTicketPackagesForRate.ticketPackageID#" id="packageQty_#local.qryTicketPackagesForRate.ticketPackageID#" value="#val(local.qryTicketPackagesForRate.quantity)#" class="form-control form-control-sm" style="width:100px;" onBlur="this.value=formatInt(this.value);">
						<span class="ml-1">#local.qryTicketPackagesForRate.ticketPackageNameExpanded#</span>
					</div>
				</cfloop>
			</div>
		</div>
		<div class="form-label-group">
			<select name="excludeTicketPackages" id="excludeTicketPackages" class="form-control form-control-sm" multiple="true" data-toggle="custom-select2" placeholder="Select Ticket Packages">
				<cfoutput query="local.qryTicketPackagesForRate" group="ticketID">
					<optgroup label="#local.qryTicketPackagesForRate.ticketName#">
						<cfoutput>
							<option value="#local.qryTicketPackagesForRate.ticketPackageID#"<cfif local.qryTicketPackagesForRate.isExcluded> selected</cfif>>#local.qryTicketPackagesForRate.ticketPackageName#</option>
						</cfoutput>
					</optgroup>
				</cfoutput>
			</select>
			<label for="GLAccountPath">Ticket Packages Excluded</label>
		</div>
	</cfif>
	</cfoutput>

	<cfif local.qrySubEvents.recordcount>
		<h4 class="mt-4">Sub-event Rate Mapping</h4>
		<div class="mb-2">Map this rate to the following sub-events that have Force Registration set to Yes:</div>	
		<div class="row mx-0">
			<div class="col">	
				<cfoutput query="local.qrySubEvents" group="subEventID">
					<div class="form-group">
						<div class="font-weight-bold">#dateformat(local.qrySubEvents.startTime,"m/d/yyyy")# - #local.qrySubEvents.eventName#</div>
						<div class="ml-2">
							<select name="rateMap" id="rateMap" class="form-control">
								<option value=""></option>
								<cfoutput>
									<option value="#local.qrySubEvents.subEventID#|#local.qrySubEvents.rateID#" <cfif local.qrySubEvents.mapID neq "">selected</cfif>>#local.qrySubEvents.rateName#</option>
								</cfoutput>
							</select>
						</div>
					</div>
				</cfoutput>
			</div>
		</div>
	</cfif>
	<!--- hidden submit triggered from parent handler --->
	<button type="submit" class="d-none"></button>
	</form>
</div>

<cfoutput>
#local.showGLSelector.data#

<script id="ev_rateBulkDiscount" type="text/x-handlebars-template">
	<div id="divNewBulkRate_{{newID}}" class="input-group mt-1">
		<div class="d-flex align-items-center">
			<div class="form-label-group">
				<input type="text" name="newBulkRate_qty_{{newID}}" id="newBulkRate_qty_{{newID}}" value="0" class="form-control" onBlur="this.value=formatInt(this.value);">
				<label for="newBulkRate_qty_{{newID}}">Qty:</label>
			</div>
			<span class="ml-2"></span>
			<div class="form-label-group">
				<div class="input-group">
					<input type="text" name="newBulkRate_rate_{{newID}}" id="newBulkRate_rate_{{newID}}" value="0.00" class="form-control" onBlur="this.value=formatCurrency(this.value);">
					<div class="input-group-append">
						<span class="input-group-text cursor-pointer" data-target="newBulkRate_rate_{{newID}}">#arguments.event.getValue('mc_siteinfo.defaultCurrencyType')#</span>
					</div>
					<label for="newBulkRate_rate_{{newID}}">Rate:</label>
				</div>				
			</div>
			<a class="ml-2" href="javascript:removeNewBulkDiscount({{newID}});">Remove</a>
		</div>
	</div>
</script>
</cfoutput>